import type { ColumnsType } from 'antd/es/table';
import { JOIN_TYPE } from '@/utils/enum';
import { ModelIcon } from '@/utils/icons';
import SelectionTable from '@/components/table/SelectionTable';

interface ModelField {
  modelId: string;
  modelName: string;
  fieldId: string;
  fieldName: string;
}

export interface RelationsDataType {
  name: string;
  fromField: ModelField;
  isAutoGenerated: boolean;
  type: JOIN_TYPE;
  toField: ModelField;
  properties: Record<string, any>;
}

interface Props {
  columns: ColumnsType<RelationsDataType>;
  dataSource: RelationsDataType[];
  enableRowSelection?: boolean;
  extra?: (
    onCollapseOpen: (
      event: React.MouseEvent<HTMLElement, MouseEvent>,
      key: string,
    ) => void,
  ) => React.ReactNode;
  onChange?: (value: any | null) => void;
  tableTitle: string;
  rowKey: (record: RelationsDataType) => string;
}

export default function ModelRelationSelectionTable(props: Props) {
  return (
    <SelectionTable
      {...props}
      tableHeader={
        <>
          <ModelIcon className="pr-2 text-md" />
          {props.tableTitle}
        </>
      }
    />
  );
}
