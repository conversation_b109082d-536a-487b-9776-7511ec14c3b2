export enum Path {
  Home = '/home',
  HomeDashboard = '/home/<USER>',
  Thread = '/home/<USER>',
  Modeling = '/modeling',
  Onboarding = '/setup',
  OnboardingConnection = '/setup/connection',
  OnboardingModels = '/setup/models',
  OnboardingRelationships = '/setup/relationships',
  Knowledge = '/knowledge',
  KnowledgeQuestionSQLPairs = '/knowledge/question-sql-pairs',
  KnowledgeInstructions = '/knowledge/instructions',
  APIManagement = '/api-management',
  APIManagementHistory = '/api-management/history',
}
