import styled from 'styled-components';
import { Columns } from '@styled-icons/fa-solid';
import {
  Calendar,
  Text,
  InfoCircle,
  Cube,
  LineChart,
  IdCard,
} from '@styled-icons/boxicons-regular';
import { Braces, Map2 } from '@styled-icons/remix-line';
import {
  SortNumerically,
  SortAlphabetically,
  Tick,
} from '@styled-icons/typicons';
import {
  VpnKey,
  CenterFocusWeak,
  Refresh,
  Pageview,
  Explore,
  Translate,
  OpenInNew,
} from '@styled-icons/material-outlined';
import FieldBinaryOutlined from '@ant-design/icons/FieldBinaryOutlined';
import MonitorOutlined from '@ant-design/icons/MonitorOutlined';
import SwapOutlined from '@ant-design/icons/SwapOutlined';
import ShareAltOutlined from '@ant-design/icons/ShareAltOutlined';
import { Binoculars, LightningCharge } from '@styled-icons/bootstrap';
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import { Sparkles } from '@styled-icons/ionicons-outline';
import { Discord, Github } from '@styled-icons/fa-brands';

export const NumericIcon = styled(SortNumerically)`
  height: 1em;
`;
export const TickIcon = styled(Tick)`
  height: 1em;
`;
export const StringIcon = styled(SortAlphabetically)`
  height: 1em;
`;
export const TextIcon = styled(Text)`
  height: 1em;
`;
export const CalendarIcon = styled(Calendar)`
  height: 1em;
`;
export const IdIcon = styled(IdCard)`
  height: 1em;
`;
export const JsonBracesIcon = styled(Braces)`
  height: 1em;
`;
export const BinaryIcon = styled(FieldBinaryOutlined)`
  height: 1em;
`;
export const ColumnsIcon = styled(Columns)`
  height: 1em;
`;
export const InfoIcon = styled(InfoCircle)`
  height: 1em;
`;
export const PrimaryKeyIcon = styled(VpnKey)`
  height: 1em;
`;
export const ModelIcon = styled(Cube)`
  height: 1em;
`;
export const FocusIcon = styled(CenterFocusWeak)`
  height: 1em;
`;
export const MapIcon = styled(Map2)`
  height: 1em;
`;
export const RelationshipIcon = styled(SwapOutlined)`
  height: 1em;
`;
export const MonitorIcon = styled(MonitorOutlined)`
  height: 1em;
`;
export const RefreshIcon = styled(Refresh)``;
export const MetricIcon = styled(LineChart)`
  height: 1em;
`;
export const ShareIcon = styled(ShareAltOutlined)``;
export const LightningIcon = styled(LightningCharge)`
  height: 1em;
`;
export const MoreIcon = styled(MoreOutlined)``;
export const ViewIcon = styled(Pageview)`
  height: 1em;
`;
export const ExploreIcon = styled(Explore)`
  height: 1em;
`;
export const SparklesIcon = styled(Sparkles)`
  height: 1em;
`;

export const BinocularsIcon = styled(Binoculars)`
  height: 16px;
  width: 14px;
`;

export const DiscordIcon = styled(Discord)`
  height: 1em;
`;

export const GithubIcon = styled(Github)`
  height: 1em;
`;

export const TranslateIcon = styled(Translate)`
  height: 1em;
`;

export const OpenInNewIcon = styled(OpenInNew)`
  height: 1em;
`;
