export const CopilotSVG = ({
  fillCurrentColor = true,
  className,
}: {
  fillCurrentColor?: boolean;
  className?: string;
}) => (
  <svg
    className={className}
    width="14"
    height="14"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.6562 11.7478L13.9791 10.0247L12.2522 5.34375C12.1201 4.98542 11.8813 4.67621 11.5679 4.45781C11.2546 4.2394 10.8819 4.12231 10.5 4.12231C10.1181 4.12231 9.74535 4.2394 9.43205 4.45781C9.11875 4.67621 8.87993 4.98542 8.74781 5.34375L7.02469 10.0247L2.34375 11.7478C1.98542 11.8799 1.67621 12.1187 1.45781 12.432C1.2394 12.7454 1.12231 13.1181 1.12231 13.5C1.12231 13.8819 1.2394 14.2546 1.45781 14.568C1.67621 14.8813 1.98542 15.1201 2.34375 15.2522L7.02094 16.9753L8.74781 21.6562C8.87993 22.0146 9.11875 22.3238 9.43205 22.5422C9.74535 22.7606 10.1181 22.8777 10.5 22.8777C10.8819 22.8777 11.2546 22.7606 11.5679 22.5422C11.8813 22.3238 12.1201 22.0146 12.2522 21.6562L13.9753 16.9791L18.6562 15.2522C19.0146 15.1201 19.3238 14.8813 19.5422 14.568C19.7606 14.2546 19.8777 13.8819 19.8777 13.5C19.8777 13.1181 19.7606 12.7454 19.5422 12.432C19.3238 12.1187 19.0146 11.8799 18.6562 11.7478ZM13.0312 14.9259C12.7777 15.0192 12.5475 15.1664 12.3565 15.3574C12.1655 15.5484 12.0183 15.7787 11.925 16.0322L10.5 19.9012L9.07406 16.0312C8.98079 15.778 8.83365 15.548 8.64282 15.3572C8.45198 15.1663 8.222 15.0192 7.96875 14.9259L4.09875 13.5L7.96875 12.0741C8.222 11.9808 8.45198 11.8337 8.64282 11.6428C8.83365 11.452 8.98079 11.222 9.07406 10.9688L10.5 7.09875L11.9259 10.9688C12.0192 11.2223 12.1664 11.4525 12.3574 11.6435C12.5484 11.8345 12.7787 11.9817 13.0322 12.075L16.9012 13.5L13.0312 14.9259ZM13.125 3.75C13.125 3.45163 13.2435 3.16548 13.4545 2.9545C13.6655 2.74353 13.9516 2.625 14.25 2.625H15.375V1.5C15.375 1.20163 15.4935 0.915483 15.7045 0.704505C15.9155 0.493526 16.2016 0.375 16.5 0.375C16.7984 0.375 17.0845 0.493526 17.2955 0.704505C17.5065 0.915483 17.625 1.20163 17.625 1.5V2.625H18.75C19.0484 2.625 19.3345 2.74353 19.5455 2.9545C19.7565 3.16548 19.875 3.45163 19.875 3.75C19.875 4.04837 19.7565 4.33452 19.5455 4.5455C19.3345 4.75647 19.0484 4.875 18.75 4.875H17.625V6C17.625 6.29837 17.5065 6.58452 17.2955 6.7955C17.0845 7.00647 16.7984 7.125 16.5 7.125C16.2016 7.125 15.9155 7.00647 15.7045 6.7955C15.4935 6.58452 15.375 6.29837 15.375 6V4.875H14.25C13.9516 4.875 13.6655 4.75647 13.4545 4.5455C13.2435 4.33452 13.125 4.04837 13.125 3.75ZM23.625 8.25C23.625 8.54837 23.5065 8.83452 23.2955 9.0455C23.0845 9.25647 22.7984 9.375 22.5 9.375H22.125V9.75C22.125 10.0484 22.0065 10.3345 21.7955 10.5455C21.5845 10.7565 21.2984 10.875 21 10.875C20.7016 10.875 20.4155 10.7565 20.2045 10.5455C19.9935 10.3345 19.875 10.0484 19.875 9.75V9.375H19.5C19.2016 9.375 18.9155 9.25647 18.7045 9.0455C18.4935 8.83452 18.375 8.54837 18.375 8.25C18.375 7.95163 18.4935 7.66548 18.7045 7.4545C18.9155 7.24353 19.2016 7.125 19.5 7.125H19.875V6.75C19.875 6.45163 19.9935 6.16548 20.2045 5.9545C20.4155 5.74353 20.7016 5.625 21 5.625C21.2984 5.625 21.5845 5.74353 21.7955 5.9545C22.0065 6.16548 22.125 6.45163 22.125 6.75V7.125H22.5C22.7984 7.125 23.0845 7.24353 23.2955 7.4545C23.5065 7.66548 23.625 7.95163 23.625 8.25Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
  </svg>
);
