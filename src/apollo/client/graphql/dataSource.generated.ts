import * as Types from './__types__';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type StartSampleDatasetMutationVariables = Types.Exact<{
  data: Types.SampleDatasetInput;
}>;


export type StartSampleDatasetMutation = { __typename?: 'Mutation', startSampleDataset: any };

export type ListDataSourceTablesQueryVariables = Types.Exact<{ [key: string]: never; }>;


export type ListDataSourceTablesQuery = { __typename?: 'Query', listDataSourceTables: Array<{ __typename?: 'CompactTable', name: string, columns: Array<{ __typename?: 'CompactColumn', name: string, type: string }> }> };

export type AutoGeneratedRelationsQueryVariables = Types.Exact<{ [key: string]: never; }>;


export type AutoGeneratedRelationsQuery = { __typename?: 'Query', autoGenerateRelation: Array<{ __typename?: 'RecommendRelations', id: number, displayName: string, referenceName: string, relations: Array<{ __typename?: 'Relation', fromModelId: number, fromModelReferenceName: string, fromColumnId: number, fromColumnReferenceName: string, toModelId: number, toModelReferenceName: string, toColumnId: number, toColumnReferenceName: string, type: Types.RelationType, name: string } | null> }> };

export type SaveDataSourceMutationVariables = Types.Exact<{
  data: Types.DataSourceInput;
}>;


export type SaveDataSourceMutation = { __typename?: 'Mutation', saveDataSource: { __typename?: 'DataSource', type: Types.DataSourceName, properties: any } };

export type UpdateDataSourceMutationVariables = Types.Exact<{
  data: Types.UpdateDataSourceInput;
}>;


export type UpdateDataSourceMutation = { __typename?: 'Mutation', updateDataSource: { __typename?: 'DataSource', type: Types.DataSourceName, properties: any } };

export type SaveTablesMutationVariables = Types.Exact<{
  data: Types.SaveTablesInput;
}>;


export type SaveTablesMutation = { __typename?: 'Mutation', saveTables: any };

export type SaveRelationsMutationVariables = Types.Exact<{
  data: Types.SaveRelationInput;
}>;


export type SaveRelationsMutation = { __typename?: 'Mutation', saveRelations: any };

export type SchemaChangeQueryVariables = Types.Exact<{ [key: string]: never; }>;


export type SchemaChangeQuery = { __typename?: 'Query', schemaChange: { __typename?: 'SchemaChange', lastSchemaChangeTime?: string | null, deletedTables?: Array<{ __typename?: 'DetailedChangeTable', sourceTableName: string, displayName: string, columns: Array<{ __typename?: 'DetailedChangeColumn', sourceColumnName: string, displayName: string, type: string }>, relationships: Array<{ __typename?: 'DetailedAffectedRelationships', displayName: string, referenceName: string }>, calculatedFields: Array<{ __typename?: 'DetailedAffectedCalculatedFields', displayName: string, referenceName: string, type: string }> }> | null, deletedColumns?: Array<{ __typename?: 'DetailedChangeTable', sourceTableName: string, displayName: string, columns: Array<{ __typename?: 'DetailedChangeColumn', sourceColumnName: string, displayName: string, type: string }>, relationships: Array<{ __typename?: 'DetailedAffectedRelationships', displayName: string, referenceName: string }>, calculatedFields: Array<{ __typename?: 'DetailedAffectedCalculatedFields', displayName: string, referenceName: string, type: string }> }> | null, modifiedColumns?: Array<{ __typename?: 'DetailedChangeTable', sourceTableName: string, displayName: string, columns: Array<{ __typename?: 'DetailedChangeColumn', sourceColumnName: string, displayName: string, type: string }>, relationships: Array<{ __typename?: 'DetailedAffectedRelationships', displayName: string, referenceName: string }>, calculatedFields: Array<{ __typename?: 'DetailedAffectedCalculatedFields', displayName: string, referenceName: string, type: string }> }> | null } };

export type TriggerDataSourceDetectionMutationVariables = Types.Exact<{ [key: string]: never; }>;


export type TriggerDataSourceDetectionMutation = { __typename?: 'Mutation', triggerDataSourceDetection: boolean };

export type ResolveSchemaChangeMutationVariables = Types.Exact<{
  where: Types.ResolveSchemaChangeWhereInput;
}>;


export type ResolveSchemaChangeMutation = { __typename?: 'Mutation', resolveSchemaChange: boolean };


export const StartSampleDatasetDocument = gql`
    mutation StartSampleDataset($data: SampleDatasetInput!) {
  startSampleDataset(data: $data)
}
    `;
export type StartSampleDatasetMutationFn = Apollo.MutationFunction<StartSampleDatasetMutation, StartSampleDatasetMutationVariables>;

/**
 * __useStartSampleDatasetMutation__
 *
 * To run a mutation, you first call `useStartSampleDatasetMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useStartSampleDatasetMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [startSampleDatasetMutation, { data, loading, error }] = useStartSampleDatasetMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useStartSampleDatasetMutation(baseOptions?: Apollo.MutationHookOptions<StartSampleDatasetMutation, StartSampleDatasetMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<StartSampleDatasetMutation, StartSampleDatasetMutationVariables>(StartSampleDatasetDocument, options);
      }
export type StartSampleDatasetMutationHookResult = ReturnType<typeof useStartSampleDatasetMutation>;
export type StartSampleDatasetMutationResult = Apollo.MutationResult<StartSampleDatasetMutation>;
export type StartSampleDatasetMutationOptions = Apollo.BaseMutationOptions<StartSampleDatasetMutation, StartSampleDatasetMutationVariables>;
export const ListDataSourceTablesDocument = gql`
    query ListDataSourceTables {
  listDataSourceTables {
    name
    columns {
      name
      type
    }
  }
}
    `;

/**
 * __useListDataSourceTablesQuery__
 *
 * To run a query within a React component, call `useListDataSourceTablesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListDataSourceTablesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListDataSourceTablesQuery({
 *   variables: {
 *   },
 * });
 */
export function useListDataSourceTablesQuery(baseOptions?: Apollo.QueryHookOptions<ListDataSourceTablesQuery, ListDataSourceTablesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListDataSourceTablesQuery, ListDataSourceTablesQueryVariables>(ListDataSourceTablesDocument, options);
      }
export function useListDataSourceTablesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListDataSourceTablesQuery, ListDataSourceTablesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListDataSourceTablesQuery, ListDataSourceTablesQueryVariables>(ListDataSourceTablesDocument, options);
        }
export type ListDataSourceTablesQueryHookResult = ReturnType<typeof useListDataSourceTablesQuery>;
export type ListDataSourceTablesLazyQueryHookResult = ReturnType<typeof useListDataSourceTablesLazyQuery>;
export type ListDataSourceTablesQueryResult = Apollo.QueryResult<ListDataSourceTablesQuery, ListDataSourceTablesQueryVariables>;
export const AutoGeneratedRelationsDocument = gql`
    query AutoGeneratedRelations {
  autoGenerateRelation {
    id
    displayName
    referenceName
    relations {
      fromModelId
      fromModelReferenceName
      fromColumnId
      fromColumnReferenceName
      toModelId
      toModelReferenceName
      toColumnId
      toColumnReferenceName
      type
      name
    }
  }
}
    `;

/**
 * __useAutoGeneratedRelationsQuery__
 *
 * To run a query within a React component, call `useAutoGeneratedRelationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAutoGeneratedRelationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAutoGeneratedRelationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useAutoGeneratedRelationsQuery(baseOptions?: Apollo.QueryHookOptions<AutoGeneratedRelationsQuery, AutoGeneratedRelationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AutoGeneratedRelationsQuery, AutoGeneratedRelationsQueryVariables>(AutoGeneratedRelationsDocument, options);
      }
export function useAutoGeneratedRelationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AutoGeneratedRelationsQuery, AutoGeneratedRelationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AutoGeneratedRelationsQuery, AutoGeneratedRelationsQueryVariables>(AutoGeneratedRelationsDocument, options);
        }
export type AutoGeneratedRelationsQueryHookResult = ReturnType<typeof useAutoGeneratedRelationsQuery>;
export type AutoGeneratedRelationsLazyQueryHookResult = ReturnType<typeof useAutoGeneratedRelationsLazyQuery>;
export type AutoGeneratedRelationsQueryResult = Apollo.QueryResult<AutoGeneratedRelationsQuery, AutoGeneratedRelationsQueryVariables>;
export const SaveDataSourceDocument = gql`
    mutation SaveDataSource($data: DataSourceInput!) {
  saveDataSource(data: $data) {
    type
    properties
  }
}
    `;
export type SaveDataSourceMutationFn = Apollo.MutationFunction<SaveDataSourceMutation, SaveDataSourceMutationVariables>;

/**
 * __useSaveDataSourceMutation__
 *
 * To run a mutation, you first call `useSaveDataSourceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveDataSourceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveDataSourceMutation, { data, loading, error }] = useSaveDataSourceMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useSaveDataSourceMutation(baseOptions?: Apollo.MutationHookOptions<SaveDataSourceMutation, SaveDataSourceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SaveDataSourceMutation, SaveDataSourceMutationVariables>(SaveDataSourceDocument, options);
      }
export type SaveDataSourceMutationHookResult = ReturnType<typeof useSaveDataSourceMutation>;
export type SaveDataSourceMutationResult = Apollo.MutationResult<SaveDataSourceMutation>;
export type SaveDataSourceMutationOptions = Apollo.BaseMutationOptions<SaveDataSourceMutation, SaveDataSourceMutationVariables>;
export const UpdateDataSourceDocument = gql`
    mutation UpdateDataSource($data: UpdateDataSourceInput!) {
  updateDataSource(data: $data) {
    type
    properties
  }
}
    `;
export type UpdateDataSourceMutationFn = Apollo.MutationFunction<UpdateDataSourceMutation, UpdateDataSourceMutationVariables>;

/**
 * __useUpdateDataSourceMutation__
 *
 * To run a mutation, you first call `useUpdateDataSourceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateDataSourceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateDataSourceMutation, { data, loading, error }] = useUpdateDataSourceMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateDataSourceMutation(baseOptions?: Apollo.MutationHookOptions<UpdateDataSourceMutation, UpdateDataSourceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateDataSourceMutation, UpdateDataSourceMutationVariables>(UpdateDataSourceDocument, options);
      }
export type UpdateDataSourceMutationHookResult = ReturnType<typeof useUpdateDataSourceMutation>;
export type UpdateDataSourceMutationResult = Apollo.MutationResult<UpdateDataSourceMutation>;
export type UpdateDataSourceMutationOptions = Apollo.BaseMutationOptions<UpdateDataSourceMutation, UpdateDataSourceMutationVariables>;
export const SaveTablesDocument = gql`
    mutation SaveTables($data: SaveTablesInput!) {
  saveTables(data: $data)
}
    `;
export type SaveTablesMutationFn = Apollo.MutationFunction<SaveTablesMutation, SaveTablesMutationVariables>;

/**
 * __useSaveTablesMutation__
 *
 * To run a mutation, you first call `useSaveTablesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveTablesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveTablesMutation, { data, loading, error }] = useSaveTablesMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useSaveTablesMutation(baseOptions?: Apollo.MutationHookOptions<SaveTablesMutation, SaveTablesMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SaveTablesMutation, SaveTablesMutationVariables>(SaveTablesDocument, options);
      }
export type SaveTablesMutationHookResult = ReturnType<typeof useSaveTablesMutation>;
export type SaveTablesMutationResult = Apollo.MutationResult<SaveTablesMutation>;
export type SaveTablesMutationOptions = Apollo.BaseMutationOptions<SaveTablesMutation, SaveTablesMutationVariables>;
export const SaveRelationsDocument = gql`
    mutation SaveRelations($data: SaveRelationInput!) {
  saveRelations(data: $data)
}
    `;
export type SaveRelationsMutationFn = Apollo.MutationFunction<SaveRelationsMutation, SaveRelationsMutationVariables>;

/**
 * __useSaveRelationsMutation__
 *
 * To run a mutation, you first call `useSaveRelationsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveRelationsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveRelationsMutation, { data, loading, error }] = useSaveRelationsMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useSaveRelationsMutation(baseOptions?: Apollo.MutationHookOptions<SaveRelationsMutation, SaveRelationsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SaveRelationsMutation, SaveRelationsMutationVariables>(SaveRelationsDocument, options);
      }
export type SaveRelationsMutationHookResult = ReturnType<typeof useSaveRelationsMutation>;
export type SaveRelationsMutationResult = Apollo.MutationResult<SaveRelationsMutation>;
export type SaveRelationsMutationOptions = Apollo.BaseMutationOptions<SaveRelationsMutation, SaveRelationsMutationVariables>;
export const SchemaChangeDocument = gql`
    query SchemaChange {
  schemaChange {
    deletedTables {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    deletedColumns {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    modifiedColumns {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    lastSchemaChangeTime
  }
}
    `;

/**
 * __useSchemaChangeQuery__
 *
 * To run a query within a React component, call `useSchemaChangeQuery` and pass it any options that fit your needs.
 * When your component renders, `useSchemaChangeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSchemaChangeQuery({
 *   variables: {
 *   },
 * });
 */
export function useSchemaChangeQuery(baseOptions?: Apollo.QueryHookOptions<SchemaChangeQuery, SchemaChangeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<SchemaChangeQuery, SchemaChangeQueryVariables>(SchemaChangeDocument, options);
      }
export function useSchemaChangeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<SchemaChangeQuery, SchemaChangeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<SchemaChangeQuery, SchemaChangeQueryVariables>(SchemaChangeDocument, options);
        }
export type SchemaChangeQueryHookResult = ReturnType<typeof useSchemaChangeQuery>;
export type SchemaChangeLazyQueryHookResult = ReturnType<typeof useSchemaChangeLazyQuery>;
export type SchemaChangeQueryResult = Apollo.QueryResult<SchemaChangeQuery, SchemaChangeQueryVariables>;
export const TriggerDataSourceDetectionDocument = gql`
    mutation TriggerDataSourceDetection {
  triggerDataSourceDetection
}
    `;
export type TriggerDataSourceDetectionMutationFn = Apollo.MutationFunction<TriggerDataSourceDetectionMutation, TriggerDataSourceDetectionMutationVariables>;

/**
 * __useTriggerDataSourceDetectionMutation__
 *
 * To run a mutation, you first call `useTriggerDataSourceDetectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTriggerDataSourceDetectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [triggerDataSourceDetectionMutation, { data, loading, error }] = useTriggerDataSourceDetectionMutation({
 *   variables: {
 *   },
 * });
 */
export function useTriggerDataSourceDetectionMutation(baseOptions?: Apollo.MutationHookOptions<TriggerDataSourceDetectionMutation, TriggerDataSourceDetectionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<TriggerDataSourceDetectionMutation, TriggerDataSourceDetectionMutationVariables>(TriggerDataSourceDetectionDocument, options);
      }
export type TriggerDataSourceDetectionMutationHookResult = ReturnType<typeof useTriggerDataSourceDetectionMutation>;
export type TriggerDataSourceDetectionMutationResult = Apollo.MutationResult<TriggerDataSourceDetectionMutation>;
export type TriggerDataSourceDetectionMutationOptions = Apollo.BaseMutationOptions<TriggerDataSourceDetectionMutation, TriggerDataSourceDetectionMutationVariables>;
export const ResolveSchemaChangeDocument = gql`
    mutation ResolveSchemaChange($where: ResolveSchemaChangeWhereInput!) {
  resolveSchemaChange(where: $where)
}
    `;
export type ResolveSchemaChangeMutationFn = Apollo.MutationFunction<ResolveSchemaChangeMutation, ResolveSchemaChangeMutationVariables>;

/**
 * __useResolveSchemaChangeMutation__
 *
 * To run a mutation, you first call `useResolveSchemaChangeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useResolveSchemaChangeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [resolveSchemaChangeMutation, { data, loading, error }] = useResolveSchemaChangeMutation({
 *   variables: {
 *      where: // value for 'where'
 *   },
 * });
 */
export function useResolveSchemaChangeMutation(baseOptions?: Apollo.MutationHookOptions<ResolveSchemaChangeMutation, ResolveSchemaChangeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ResolveSchemaChangeMutation, ResolveSchemaChangeMutationVariables>(ResolveSchemaChangeDocument, options);
      }
export type ResolveSchemaChangeMutationHookResult = ReturnType<typeof useResolveSchemaChangeMutation>;
export type ResolveSchemaChangeMutationResult = Apollo.MutationResult<ResolveSchemaChangeMutation>;
export type ResolveSchemaChangeMutationOptions = Apollo.BaseMutationOptions<ResolveSchemaChangeMutation, ResolveSchemaChangeMutationVariables>;