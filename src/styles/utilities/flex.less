.justify {
  &-start {
    justify-content: flex-start !important;
  }
  &-end {
    justify-content: flex-end !important;
  }
  &-center {
    justify-content: center !important;
  }
  &-space-between {
    justify-content: space-between !important;
  }
}

.align {
  &-start {
    align-items: flex-start !important;
  }
  &-end {
    align-items: flex-end !important;
  }
  &-center {
    align-items: center !important;
  }
  &-baseline {
    align-items: baseline !important;
  }
}

.flex {
  &-shrink-0 {
    flex-shrink: 0 !important;
  }

  &-shrink-1 {
    flex-shrink: 1 !important;
  }

  &-grow-0 {
    flex-grow: 0 !important;
  }

  &-grow-1 {
    flex-grow: 1 !important;
  }

  &-row {
    flex-direction: row !important;
  }

  &-column {
    flex-direction: column !important;
  }
}
