/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.alterTable('thread_response', function (table) {
    table.dropColumn('summary');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable('thread_response', function (table) {
    table
      .string('summary')
      .nullable()
      .comment('the summary of the thread response');
  });
};
