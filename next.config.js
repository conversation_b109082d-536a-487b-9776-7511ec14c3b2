/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const withLess = require('next-with-less');
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const resolveAlias = {
  antd$: path.resolve(__dirname, 'src/import/antd'),
};

/** @type {import('next').NextConfig} */
const nextConfig = withLess({
  output: 'standalone',
  staticPageGenerationTimeout: 1000,
  compiler: {
    // Enables the styled-components SWC transform
    styledComponents: {
      displayName: true,
      ssr: true,
    },
  },
  lessLoaderOptions: {
    additionalData: `@import "@/styles/antd-variables.less";`,
  },
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      ...resolveAlias,
    };
    return config;
  },
  // routes redirect
  async redirects() {
    return [
      {
        source: '/setup',
        destination: '/setup/connection',
        permanent: true,
      },
    ];
  },
});

module.exports = withBundleAnalyzer(nextConfig);



// // =======================================================================
// const path = require('path');
// const withLess = require('next-with-less');
// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: process.env.ANALYZE === 'true',
// });

// const resolveAlias = {
//   antd$: path.resolve(__dirname, 'src/import/antd'),
//   '@/services/askingService$': path.resolve(__dirname, 'src/apollo/server/services/askingService.ts'),
//   '@': path.resolve(__dirname, 'src'),
// };

// /** @type {import('next').NextConfig} */
// const nextConfig = withLess({
//   output: 'standalone',
//   staticPageGenerationTimeout: 1000,
//   compiler: {
//     styledComponents: { displayName: true, ssr: true },
//   },
//   lessLoaderOptions: {
//     additionalData: `@import "@/styles/antd-variables.less";`,
//   },
//   webpack: (config, { isServer }) => {
//     config.resolve.alias = { ...config.resolve.alias, ...resolveAlias };

//     // 仅开发环境处理循环依赖
//     if (process.env.NODE_ENV === 'development') {
//       config.module.rules.push({
//         test: /apollo\/server\/services\/.*\.ts$/,
//         use: [path.resolve(__dirname, 'loaders/module-wrapper-loader.js')],
//         include: [
//           path.resolve(__dirname, 'src/apollo/server/services/askingService.ts')
//         ]
//       });

//       config.cache = {
//         type: 'filesystem',
//         cacheDirectory: path.resolve(__dirname, '.next/cache/webpack'),
//         buildDependencies: { config: [__filename] },
//       };
//     }

//     return config;
//   },
//   async redirects() {
//     return [/* 原有重定向配置 */];
//   },
// });

// module.exports = withBundleAnalyzer(nextConfig);