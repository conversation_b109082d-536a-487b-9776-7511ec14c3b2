"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[623],{77491:function(n,e,t){t.d(e,{$4:function(){return V},DC:function(){return nF},Gf:function(){return nc},HT:function(){return ny},MG:function(){return nb},Ok:function(){return nk},Qk:function(){return nr},Rf:function(){return nE},Rh:function(){return ne},T_:function(){return nJ},WL:function(){return nH},Xy:function(){return ns},Y:function(){return n$},Z0:function(){return nD},Zy:function(){return nZ},dP:function(){return nX},e8:function(){return no},eO:function(){return nQ},hC:function(){return nS},j8:function(){return nY},jv:function(){return ng},kY:function(){return ni},tT:function(){return np},vi:function(){return nN},vt:function(){return nh},wK:function(){return nP},wU:function(){return nl},xF:function(){return nj}});var r=t(82729),u=t(68806),o=t(6812),s=t(73359),a=t(50319);function i(){let n=(0,r._)(["\n    fragment CommonError on Error {\n  code\n  shortMessage\n  message\n  stacktrace\n}\n    "]);return i=function(){return n},n}function d(){let n=(0,r._)(["\n    fragment CommonBreakdownDetail on ThreadResponseBreakdownDetail {\n  queryId\n  status\n  description\n  steps {\n    summary\n    sql\n    cteName\n  }\n  error {\n    ...CommonError\n  }\n}\n    ",""]);return d=function(){return n},n}function c(){let n=(0,r._)(["\n    fragment CommonAnswerDetail on ThreadResponseAnswerDetail {\n  queryId\n  status\n  content\n  numRowsUsedInLLM\n  error {\n    ...CommonError\n  }\n}\n    ",""]);return c=function(){return n},n}function m(){let n=(0,r._)(["\n    fragment CommonChartDetail on ThreadResponseChartDetail {\n  queryId\n  status\n  description\n  chartType\n  chartSchema\n  error {\n    ...CommonError\n  }\n  adjustment\n}\n    ",""]);return m=function(){return n},n}function l(){let n=(0,r._)(["\n    fragment CommonAskingTask on AskingTask {\n  status\n  type\n  candidates {\n    sql\n    type\n    view {\n      id\n      name\n      statement\n      displayName\n    }\n    sqlPair {\n      id\n      question\n      sql\n      projectId\n    }\n  }\n  error {\n    ...CommonError\n  }\n  rephrasedQuestion\n  intentReasoning\n  sqlGenerationReasoning\n  retrievedTables\n  invalidSql\n  traceId\n  queryId\n}\n    ",""]);return l=function(){return n},n}function f(){let n=(0,r._)(["\n    fragment CommonResponse on ThreadResponse {\n  id\n  threadId\n  question\n  sql\n  view {\n    id\n    name\n    statement\n    displayName\n  }\n  breakdownDetail {\n    ...CommonBreakdownDetail\n  }\n  answerDetail {\n    ...CommonAnswerDetail\n  }\n  chartDetail {\n    ...CommonChartDetail\n  }\n  askingTask {\n    ...CommonAskingTask\n  }\n  adjustment {\n    type\n    payload\n  }\n  adjustmentTask {\n    queryId\n    status\n    error {\n      ...CommonError\n    }\n    sql\n    traceId\n    invalidSql\n  }\n}\n    ","\n","\n","\n","\n",""]);return f=function(){return n},n}function h(){let n=(0,r._)(["\n    fragment CommonRecommendedQuestionsTask on RecommendedQuestionsTask {\n  status\n  questions {\n    question\n    category\n    sql\n  }\n  error {\n    ...CommonError\n  }\n}\n    ",""]);return h=function(){return n},n}function C(){let n=(0,r._)(["\n    query SuggestedQuestions {\n  suggestedQuestions {\n    questions {\n      label\n      question\n    }\n  }\n}\n    "]);return C=function(){return n},n}function p(){let n=(0,r._)(["\n    query AskingTask($taskId: String!) {\n  askingTask(taskId: $taskId) {\n    ...CommonAskingTask\n  }\n}\n    ",""]);return p=function(){return n},n}function I(){let n=(0,r._)(["\n    query Threads {\n  threads {\n    id\n    summary\n  }\n}\n    "]);return I=function(){return n},n}function k(){let n=(0,r._)(["\n    query Thread($threadId: Int!) {\n  thread(threadId: $threadId) {\n    id\n    responses {\n      ...CommonResponse\n    }\n  }\n}\n    ",""]);return k=function(){return n},n}function T(){let n=(0,r._)(["\n    query ThreadResponse($responseId: Int!) {\n  threadResponse(responseId: $responseId) {\n    ...CommonResponse\n  }\n}\n    ",""]);return T=function(){return n},n}function $(){let n=(0,r._)(["\n    mutation CreateAskingTask($data: AskingTaskInput!) {\n  createAskingTask(data: $data) {\n    id\n  }\n}\n    "]);return $=function(){return n},n}function R(){let n=(0,r._)(["\n    mutation CancelAskingTask($taskId: String!) {\n  cancelAskingTask(taskId: $taskId)\n}\n    "]);return R=function(){return n},n}function g(){let n=(0,r._)(["\n    mutation RerunAskingTask($responseId: Int!) {\n  rerunAskingTask(responseId: $responseId) {\n    id\n  }\n}\n    "]);return g=function(){return n},n}function w(){let n=(0,r._)(["\n    mutation CreateThread($data: CreateThreadInput!) {\n  createThread(data: $data) {\n    id\n  }\n}\n    "]);return w=function(){return n},n}function P(){let n=(0,r._)(["\n    mutation CreateThreadResponse($threadId: Int!, $data: CreateThreadResponseInput!) {\n  createThreadResponse(threadId: $threadId, data: $data) {\n    ...CommonResponse\n  }\n}\n    ",""]);return P=function(){return n},n}function _(){let n=(0,r._)(["\n    mutation UpdateThread($where: ThreadUniqueWhereInput!, $data: UpdateThreadInput!) {\n  updateThread(where: $where, data: $data) {\n    id\n    summary\n  }\n}\n    "]);return _=function(){return n},n}function D(){let n=(0,r._)(["\n    mutation UpdateThreadResponse($where: ThreadResponseUniqueWhereInput!, $data: UpdateThreadResponseInput!) {\n  updateThreadResponse(where: $where, data: $data) {\n    ...CommonResponse\n  }\n}\n    ",""]);return D=function(){return n},n}function q(){let n=(0,r._)(["\n    mutation AdjustThreadResponse($responseId: Int!, $data: AdjustThreadResponseInput!) {\n  adjustThreadResponse(responseId: $responseId, data: $data) {\n    ...CommonResponse\n  }\n}\n    ",""]);return q=function(){return n},n}function y(){let n=(0,r._)(["\n    mutation DeleteThread($where: ThreadUniqueWhereInput!) {\n  deleteThread(where: $where)\n}\n    "]);return y=function(){return n},n}function v(){let n=(0,r._)(["\n    mutation PreviewData($where: PreviewDataInput!) {\n  previewData(where: $where)\n}\n    "]);return v=function(){return n},n}function j(){let n=(0,r._)(["\n    mutation PreviewBreakdownData($where: PreviewDataInput!) {\n  previewBreakdownData(where: $where)\n}\n    "]);return j=function(){return n},n}function A(){let n=(0,r._)(["\n    query GetNativeSQL($responseId: Int!) {\n  nativeSql(responseId: $responseId)\n}\n    "]);return A=function(){return n},n}function Q(){let n=(0,r._)(["\n    mutation CreateInstantRecommendedQuestions($data: InstantRecommendedQuestionsInput!) {\n  createInstantRecommendedQuestions(data: $data) {\n    id\n  }\n}\n    "]);return Q=function(){return n},n}function L(){let n=(0,r._)(["\n    query InstantRecommendedQuestions($taskId: String!) {\n  instantRecommendedQuestions(taskId: $taskId) {\n    ...CommonRecommendedQuestionsTask\n  }\n}\n    ",""]);return L=function(){return n},n}function S(){let n=(0,r._)(["\n    query GetThreadRecommendationQuestions($threadId: Int!) {\n  getThreadRecommendationQuestions(threadId: $threadId) {\n    ...CommonRecommendedQuestionsTask\n  }\n}\n    ",""]);return S=function(){return n},n}function M(){let n=(0,r._)(["\n    query GetProjectRecommendationQuestions {\n  getProjectRecommendationQuestions {\n    ...CommonRecommendedQuestionsTask\n  }\n}\n    ",""]);return M=function(){return n},n}function E(){let n=(0,r._)(["\n    mutation GenerateProjectRecommendationQuestions {\n  generateProjectRecommendationQuestions\n}\n    "]);return E=function(){return n},n}function G(){let n=(0,r._)(["\n    mutation GenerateThreadRecommendationQuestions($threadId: Int!) {\n  generateThreadRecommendationQuestions(threadId: $threadId)\n}\n    "]);return G=function(){return n},n}function b(){let n=(0,r._)(["\n    mutation GenerateThreadResponseAnswer($responseId: Int!) {\n  generateThreadResponseAnswer(responseId: $responseId) {\n    ...CommonResponse\n  }\n}\n    ",""]);return b=function(){return n},n}function U(){let n=(0,r._)(["\n    mutation GenerateThreadResponseChart($responseId: Int!) {\n  generateThreadResponseChart(responseId: $responseId) {\n    ...CommonResponse\n  }\n}\n    ",""]);return U=function(){return n},n}function N(){let n=(0,r._)(["\n    mutation AdjustThreadResponseChart($responseId: Int!, $data: AdjustThreadResponseChartInput!) {\n  adjustThreadResponseChart(responseId: $responseId, data: $data) {\n    ...CommonResponse\n  }\n}\n    ",""]);return N=function(){return n},n}function x(){let n=(0,r._)(["\n    query AdjustmentTask($taskId: String!) {\n  adjustmentTask(taskId: $taskId) {\n    queryId\n    status\n    error {\n      code\n      shortMessage\n      message\n      stacktrace\n    }\n    sql\n    traceId\n    invalidSql\n  }\n}\n    "]);return x=function(){return n},n}function Z(){let n=(0,r._)(["\n    mutation CancelAdjustmentTask($taskId: String!) {\n  cancelAdjustmentTask(taskId: $taskId)\n}\n    "]);return Z=function(){return n},n}function B(){let n=(0,r._)(["\n    mutation RerunAdjustmentTask($responseId: Int!) {\n  rerunAdjustmentTask(responseId: $responseId)\n}\n    "]);return B=function(){return n},n}let H={},W=(0,u.Ps)(i()),Y=(0,u.Ps)(d(),W),O=(0,u.Ps)(c(),W),F=(0,u.Ps)(m(),W),K=(0,u.Ps)(l(),W),X=(0,u.Ps)(f(),Y,O,F,K,W),z=(0,u.Ps)(h(),W),J=(0,u.Ps)(C());function V(n){let e={...H,...n};return o.aM(J,e)}let nn=(0,u.Ps)(p(),K);function ne(n){let e={...H,...n};return s.t(nn,e)}let nt=(0,u.Ps)(I());function nr(n){let e={...H,...n};return o.aM(nt,e)}let nu=(0,u.Ps)(k(),X);function no(n){let e={...H,...n};return o.aM(nu,e)}function ns(n){let e={...H,...n};return s.t(nu,e)}let na=(0,u.Ps)(T(),X);function ni(n){let e={...H,...n};return s.t(na,e)}let nd=(0,u.Ps)($());function nc(n){let e={...H,...n};return a.D(nd,e)}let nm=(0,u.Ps)(R());function nl(n){let e={...H,...n};return a.D(nm,e)}let nf=(0,u.Ps)(g());function nh(n){let e={...H,...n};return a.D(nf,e)}let nC=(0,u.Ps)(w());function np(n){let e={...H,...n};return a.D(nC,e)}let nI=(0,u.Ps)(P(),X);function nk(n){let e={...H,...n};return a.D(nI,e)}let nT=(0,u.Ps)(_());function n$(n){let e={...H,...n};return a.D(nT,e)}let nR=(0,u.Ps)(D(),X);function ng(n){let e={...H,...n};return a.D(nR,e)}let nw=(0,u.Ps)(q(),X);function nP(n){let e={...H,...n};return a.D(nw,e)}let n_=(0,u.Ps)(y());function nD(n){let e={...H,...n};return a.D(n_,e)}let nq=(0,u.Ps)(v());function ny(n){let e={...H,...n};return a.D(nq,e)}(0,u.Ps)(j());let nv=(0,u.Ps)(A());function nj(n){let e={...H,...n};return s.t(nv,e)}let nA=(0,u.Ps)(Q());function nQ(n){let e={...H,...n};return a.D(nA,e)}let nL=(0,u.Ps)(L(),z);function nS(n){let e={...H,...n};return s.t(nL,e)}let nM=(0,u.Ps)(S(),z);function nE(n){let e={...H,...n};return s.t(nM,e)}let nG=(0,u.Ps)(M(),z);function nb(n){let e={...H,...n};return s.t(nG,e)}let nU=(0,u.Ps)(E());function nN(n){let e={...H,...n};return a.D(nU,e)}let nx=(0,u.Ps)(G());function nZ(n){let e={...H,...n};return a.D(nx,e)}let nB=(0,u.Ps)(b(),X);function nH(n){let e={...H,...n};return a.D(nB,e)}let nW=(0,u.Ps)(U(),X);function nY(n){let e={...H,...n};return a.D(nW,e)}let nO=(0,u.Ps)(N(),X);function nF(n){let e={...H,...n};return a.D(nO,e)}(0,u.Ps)(x());let nK=(0,u.Ps)(Z());function nX(n){let e={...H,...n};return a.D(nK,e)}let nz=(0,u.Ps)(B());function nJ(n){let e={...H,...n};return a.D(nz,e)}},87021:function(n,e,t){t.d(e,{T:function(){return u}});var r=t(85893);let u=n=>{let{color:e="var(--gray-9)",size:t=30}=n;return(0,r.jsxs)("svg",{style:{width:t,height:"auto"},viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",shapeRendering:"geometricPrecision",children:[(0,r.jsx)("path",{d:"M15.8023 7.82981C16.2779 8.98701 16.0102 10.1784 15.2043 10.491C14.3983 10.8035 13.3594 10.1187 12.8838 8.96153C12.4082 7.80433 12.676 6.61289 13.4819 6.30038C14.2878 5.98786 15.3267 6.67261 15.8023 7.82981Z",fill:e}),(0,r.jsx)("path",{d:"M29.2498 21.952C29.6972 22.4662 30.06 23.0215 29.9917 23.685C29.9234 24.3486 29.3086 24.614 28.8987 24.6804C28.4888 24.7467 20.4276 25.8084 19.5396 25.8748C18.7537 25.9335 18.6097 25.8363 18.303 25.6293C18.1647 25.5398 15.0158 22.5446 13.4586 21.0582C12.69 20.502 11.9941 20.0605 11.5031 19.8762C11.0511 19.7065 10.8708 19.0927 11.2918 18.7366L12.3395 17.8503C12.6148 17.6175 13.0379 17.6319 13.2922 17.9027C13.5897 18.1624 14.5664 19.2307 15.0176 19.7324C16.4453 21.0988 18.1849 22.7674 19.1297 23.685C19.8811 23.9504 21.6801 23.6187 22.0672 23.486C22.0672 23.486 19.4312 20.2141 18.8919 19.8065C18.3525 19.3989 17.8676 19.0849 17.4905 18.9339C16.7022 18.5185 17.059 17.9764 17.336 17.7573L18.4758 16.9463C18.7522 16.7496 19.1457 16.7781 19.3852 17.0359C20.803 18.8099 23.7888 22.4906 24.3899 23.0215C25.1414 23.2869 26.8948 22.9994 27.3274 22.8224L21.8995 16.0762C21.0284 15.3386 20.2227 14.7346 19.6677 14.5037C19.2263 14.3201 19.0551 13.6941 19.5023 13.3497L20.6563 12.4606C20.8517 12.3422 21.3174 12.1979 21.6163 12.5686L22.4088 13.5326L29.2498 21.952Z",fill:e}),(0,r.jsx)("path",{d:"M11.8478 1.99067C10.5707 1.99067 9.16387 2.29138 7.65774 3.03904C4.50493 4.60413 2.85603 7.82981 2.33475 10.202C1.60286 13.5326 2.02008 17.0359 3.22442 19.2504C4.07463 20.8137 4.76646 21.6232 6.14326 22.773C8.22894 24.5149 9.81294 25.2866 12.5342 25.6205C13.3997 25.7267 14.7668 25.6858 14.7668 25.6858C15.0555 25.6943 15.3305 25.807 15.5383 26.0018L16.946 27.322L18.5323 28.7087C18.8115 28.9528 18.6338 29.4028 18.2581 29.4028H16.4788C16.1959 29.4028 15.9415 29.1595 15.8496 29.0378C15.2849 28.4856 14.3617 27.6654 14.3617 27.6654L12.5949 27.6134L12.0702 29.601C12.0082 29.8358 11.7904 30 11.5408 30H10.8359C10.4826 30 10.2222 29.6793 10.3044 29.3456L10.7754 27.4341C10.4111 27.3614 10.0218 27.2603 9.62389 27.1204L8.89011 29.615C8.82307 29.8429 8.60863 30 8.36462 30H7.57716C7.21754 30 6.95595 29.6684 7.04944 29.3312L7.88561 26.3144C6.21075 25.5469 4.73704 24.2212 4.73704 24.2212C3.61206 23.3518 2.40208 21.6625 1.93772 20.9265C0.758197 18.9403 0.394862 17.7438 0.0906286 15.4688C-0.12877 13.8281 0.0906362 11.06 0.319969 9.83744C1.19531 6.30038 2.88436 3.2418 6.58906 1.34044C8.46832 0.375947 10.2771 0 11.8478 0C13.447 0 15.5446 0.508003 17.1081 1.30947C18.6578 2.10386 19.4247 2.82829 20.4677 3.84627C20.795 4.1666 21.6271 4.79496 22.3369 4.74577C22.7866 4.80486 24.0471 4.75887 25.4919 4.10215C26.2394 3.83912 26.7594 4.35638 26.9213 4.63196C27.103 4.91761 27.3006 5.6137 26.6372 6.11287L21.7843 9.76476C21.5609 10.1265 21.3962 10.4625 21.3325 10.7478C21.1055 11.6585 20.3487 11.25 20.057 11.06C19.7653 10.87 19.0996 9.90218 19.0996 9.90218C18.8562 9.60785 18.9128 9.17176 19.2335 8.94832C19.5878 8.70146 20.7613 7.88917 21.6518 7.34629L22.4617 6.73683C21.868 6.78175 20.3598 6.55944 19.0768 5.3108C19.0768 5.3108 17.441 3.72084 16.1516 3.07001C14.6097 2.29178 13.0964 1.99067 11.8478 1.99067Z",fill:e})]})}},83777:function(n,e,t){t.d(e,{Z:function(){return a}});var r=t(67294),u=t(11163),o=t(84908),s=t(77491);function a(){let n=(0,u.useRouter)(),{data:e,refetch:t}=(0,s.Qk)({fetchPolicy:"cache-and-network"}),[a]=(0,s.Y)(),[i]=(0,s.Z0)();return{data:{threads:(0,r.useMemo)(()=>((null==e?void 0:e.threads)||[]).map(n=>({id:n.id.toString(),name:n.summary})),[e])},onSelect:e=>{n.push("".concat(o.y$.Home,"/").concat(e[0]))},onRename:async(n,e)=>{await a({variables:{where:{id:Number(n)},data:{summary:e}}}),t()},onDelete:async n=>{await i({variables:{where:{id:Number(n)}}}),t()},refetch:t}}}}]);