"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[858],{46140:function(e,n,t){var r,a,l,i,s,o,d,u,c,m,E,f,I,N,h,p,R,g,S,T,D,A,C,_,x,O,b,y,L,P,j,v,U,M,w,F,Q,q,Z,G,B,k;t.d(n,{DN:function(){return s},Ii:function(){return r},Jq:function(){return m},L6:function(){return i},Tj:function(){return I},Vr:function(){return D},Y0:function(){return p},a_:function(){return g},fz:function(){return T},i_:function(){return c},j:function(){return a},oB:function(){return l},oX:function(){return o},ri:function(){return u},uT:function(){return N},uo:function(){return E},wR:function(){return S},y9:function(){return f}}),(A=r||(r={})).GENERATE_SQL="GENERATE_SQL",A.GENERATE_VEGA_CHART="GENERATE_VEGA_CHART",A.RUN_SQL="RUN_SQL",(C=a||(a={})).CORRECTING="CORRECTING",C.FAILED="FAILED",C.FINISHED="FINISHED",C.GENERATING="GENERATING",C.PLANNING="PLANNING",C.SEARCHING="SEARCHING",C.STOPPED="STOPPED",C.UNDERSTANDING="UNDERSTANDING",(_=l||(l={})).GENERAL="GENERAL",_.MISLEADING_QUERY="MISLEADING_QUERY",_.TEXT_TO_SQL="TEXT_TO_SQL",(x=i||(i={})).FRI="FRI",x.MON="MON",x.SAT="SAT",x.SUN="SUN",x.THU="THU",x.TUE="TUE",x.WED="WED",(O=s||(s={})).FAILED="FAILED",O.FETCHING="FETCHING",O.FINISHED="FINISHED",O.GENERATING="GENERATING",O.STOPPED="STOPPED",(b=o||(o={})).AREA="AREA",b.BAR="BAR",b.GROUPED_BAR="GROUPED_BAR",b.LINE="LINE",b.MULTI_LINE="MULTI_LINE",b.PIE="PIE",b.STACKED_BAR="STACKED_BAR",(y=d||(d={})).AREA="AREA",y.BAR="BAR",y.GROUPED_BAR="GROUPED_BAR",y.LINE="LINE",y.MULTI_LINE="MULTI_LINE",y.NUMBER="NUMBER",y.PIE="PIE",y.STACKED_BAR="STACKED_BAR",y.TABLE="TABLE",(L=u||(u={})).BIG_QUERY="BIG_QUERY",L.CLICK_HOUSE="CLICK_HOUSE",L.DUCKDB="DUCKDB",L.MSSQL="MSSQL",L.MYSQL="MYSQL",L.POSTGRES="POSTGRES",L.SNOWFLAKE="SNOWFLAKE",L.TRINO="TRINO",(P=c||(c={})).ABS="ABS",P.AVG="AVG",P.CBRT="CBRT",P.CEIL="CEIL",P.CEILING="CEILING",P.COUNT="COUNT",P.COUNT_IF="COUNT_IF",P.EXP="EXP",P.FLOOR="FLOOR",P.LENGTH="LENGTH",P.LN="LN",P.LOG10="LOG10",P.MAX="MAX",P.MIN="MIN",P.REVERSE="REVERSE",P.ROUND="ROUND",P.SIGN="SIGN",P.SUM="SUM",(j=m||(m={})).CALCULATED_FIELD="CALCULATED_FIELD",j.FIELD="FIELD",j.METRIC="METRIC",j.MODEL="MODEL",j.RELATION="RELATION",j.VIEW="VIEW",(v=E||(E={})).DATASOURCE_SAVED="DATASOURCE_SAVED",v.NOT_STARTED="NOT_STARTED",v.ONBOARDING_FINISHED="ONBOARDING_FINISHED",v.WITH_SAMPLE_DATASET="WITH_SAMPLE_DATASET",(U=f||(f={})).DE="DE",U.EN="EN",U.ES="ES",U.FR="FR",U.JA="JA",U.KO="KO",U.PT="PT",U.RU="RU",U.ZH_CN="ZH_CN",U.ZH_TW="ZH_TW",(M=I||(I={})).FAILED="FAILED",M.FINISHED="FINISHED",M.GENERATING="GENERATING",M.NOT_STARTED="NOT_STARTED",(w=N||(N={})).MANY_TO_ONE="MANY_TO_ONE",w.ONE_TO_MANY="ONE_TO_MANY",w.ONE_TO_ONE="ONE_TO_ONE",(F=h||(h={})).LLM="LLM",F.SQL_PAIR="SQL_PAIR",F.VIEW="VIEW",(Q=p||(p={})).ECOMMERCE="ECOMMERCE",Q.HR="HR",Q.MUSIC="MUSIC",Q.NBA="NBA",(q=R||(R={})).CUSTOM="CUSTOM",q.DAILY="DAILY",q.NEVER="NEVER",q.WEEKLY="WEEKLY",(Z=g||(g={})).DELETED_COLUMNS="DELETED_COLUMNS",Z.DELETED_TABLES="DELETED_TABLES",Z.MODIFIED_COLUMNS="MODIFIED_COLUMNS",(G=S||(S={})).IN_PROGRESS="IN_PROGRESS",G.SYNCRONIZED="SYNCRONIZED",G.UNSYNCRONIZED="UNSYNCRONIZED",(B=T||(T={})).APPLY_SQL="APPLY_SQL",B.REASONING="REASONING",(k=D||(D={})).FAILED="FAILED",k.FETCHING_DATA="FETCHING_DATA",k.FINISHED="FINISHED",k.INTERRUPTED="INTERRUPTED",k.NOT_STARTED="NOT_STARTED",k.PREPROCESSING="PREPROCESSING",k.STREAMING="STREAMING"},34217:function(e,n,t){t.d(n,{Cu:function(){return S},Jg:function(){return L},Lh:function(){return x},kI:function(){return b},t_:function(){return U},tl:function(){return C},v5:function(){return w},vD:function(){return D},xY:function(){return j},xv:function(){return R}});var r=t(82729),a=t(68806),l=t(50319),i=t(6812);function s(){let e=(0,r._)(["\n    mutation StartSampleDataset($data: SampleDatasetInput!) {\n  startSampleDataset(data: $data)\n}\n    "]);return s=function(){return e},e}function o(){let e=(0,r._)(["\n    query ListDataSourceTables {\n  listDataSourceTables {\n    name\n    columns {\n      name\n      type\n    }\n  }\n}\n    "]);return o=function(){return e},e}function d(){let e=(0,r._)(["\n    query AutoGeneratedRelations {\n  autoGenerateRelation {\n    id\n    displayName\n    referenceName\n    relations {\n      fromModelId\n      fromModelReferenceName\n      fromColumnId\n      fromColumnReferenceName\n      toModelId\n      toModelReferenceName\n      toColumnId\n      toColumnReferenceName\n      type\n      name\n    }\n  }\n}\n    "]);return d=function(){return e},e}function u(){let e=(0,r._)(["\n    mutation SaveDataSource($data: DataSourceInput!) {\n  saveDataSource(data: $data) {\n    type\n    properties\n  }\n}\n    "]);return u=function(){return e},e}function c(){let e=(0,r._)(["\n    mutation UpdateDataSource($data: UpdateDataSourceInput!) {\n  updateDataSource(data: $data) {\n    type\n    properties\n  }\n}\n    "]);return c=function(){return e},e}function m(){let e=(0,r._)(["\n    mutation SaveTables($data: SaveTablesInput!) {\n  saveTables(data: $data)\n}\n    "]);return m=function(){return e},e}function E(){let e=(0,r._)(["\n    mutation SaveRelations($data: SaveRelationInput!) {\n  saveRelations(data: $data)\n}\n    "]);return E=function(){return e},e}function f(){let e=(0,r._)(["\n    query SchemaChange {\n  schemaChange {\n    deletedTables {\n      sourceTableName\n      displayName\n      columns {\n        sourceColumnName\n        displayName\n        type\n      }\n      relationships {\n        displayName\n        referenceName\n      }\n      calculatedFields {\n        displayName\n        referenceName\n        type\n      }\n    }\n    deletedColumns {\n      sourceTableName\n      displayName\n      columns {\n        sourceColumnName\n        displayName\n        type\n      }\n      relationships {\n        displayName\n        referenceName\n      }\n      calculatedFields {\n        displayName\n        referenceName\n        type\n      }\n    }\n    modifiedColumns {\n      sourceTableName\n      displayName\n      columns {\n        sourceColumnName\n        displayName\n        type\n      }\n      relationships {\n        displayName\n        referenceName\n      }\n      calculatedFields {\n        displayName\n        referenceName\n        type\n      }\n    }\n    lastSchemaChangeTime\n  }\n}\n    "]);return f=function(){return e},e}function I(){let e=(0,r._)(["\n    mutation TriggerDataSourceDetection {\n  triggerDataSourceDetection\n}\n    "]);return I=function(){return e},e}function N(){let e=(0,r._)(["\n    mutation ResolveSchemaChange($where: ResolveSchemaChangeWhereInput!) {\n  resolveSchemaChange(where: $where)\n}\n    "]);return N=function(){return e},e}let h={},p=(0,a.Ps)(s());function R(e){let n={...h,...e};return l.D(p,n)}let g=(0,a.Ps)(o());function S(e){let n={...h,...e};return i.aM(g,n)}let T=(0,a.Ps)(d());function D(e){let n={...h,...e};return i.aM(T,n)}let A=(0,a.Ps)(u());function C(e){let n={...h,...e};return l.D(A,n)}let _=(0,a.Ps)(c());function x(e){let n={...h,...e};return l.D(_,n)}let O=(0,a.Ps)(m());function b(e){let n={...h,...e};return l.D(O,n)}let y=(0,a.Ps)(E());function L(e){let n={...h,...e};return l.D(y,n)}let P=(0,a.Ps)(f());function j(e){let n={...h,...e};return i.aM(P,n)}let v=(0,a.Ps)(I());function U(e){let n={...h,...e};return l.D(v,n)}let M=(0,a.Ps)(N());function w(e){let n={...h,...e};return l.D(M,n)}},61030:function(e,n,t){t.d(n,{J$:function(){return c},wC:function(){return E}});var r=t(82729),a=t(68806),l=t(50319),i=t(6812);function s(){let e=(0,r._)(["\n    mutation Deploy {\n  deploy\n}\n    "]);return s=function(){return e},e}function o(){let e=(0,r._)(["\n    query DeployStatus {\n  modelSync {\n    status\n  }\n}\n    "]);return o=function(){return e},e}let d={},u=(0,a.Ps)(s());function c(e){let n={...d,...e};return l.D(u,n)}let m=(0,a.Ps)(o());function E(e){let n={...d,...e};return i.aM(m,n)}},3591:function(e,n,t){t.d(n,{Af:function(){return x},Pw:function(){return D},R5:function(){return b},it:function(){return S},ko:function(){return C}});var r=t(82729),a=t(68806),l=t(6812),i=t(50319);function s(){let e=(0,r._)(["\n    fragment CommonColumn on DetailedColumn {\n  displayName\n  referenceName\n  sourceColumnName\n  type\n  isCalculated\n  notNull\n  properties\n}\n    "]);return s=function(){return e},e}function o(){let e=(0,r._)(["\n    fragment CommonField on FieldInfo {\n  id\n  displayName\n  referenceName\n  sourceColumnName\n  type\n  isCalculated\n  notNull\n  expression\n  properties\n}\n    "]);return o=function(){return e},e}function d(){let e=(0,r._)(["\n    fragment CommonRelation on DetailedRelation {\n  fromModelId\n  fromColumnId\n  toModelId\n  toColumnId\n  type\n  name\n}\n    "]);return d=function(){return e},e}function u(){let e=(0,r._)(["\n    query ListModels {\n  listModels {\n    id\n    displayName\n    referenceName\n    sourceTableName\n    refSql\n    primaryKey\n    cached\n    refreshTime\n    description\n    fields {\n      ...CommonField\n    }\n    calculatedFields {\n      ...CommonField\n    }\n  }\n}\n    ",""]);return u=function(){return e},e}function c(){let e=(0,r._)(["\n    query GetModel($where: ModelWhereInput!) {\n  model(where: $where) {\n    displayName\n    referenceName\n    sourceTableName\n    refSql\n    primaryKey\n    cached\n    refreshTime\n    description\n    fields {\n      ...CommonColumn\n    }\n    calculatedFields {\n      ...CommonColumn\n    }\n    relations {\n      ...CommonRelation\n    }\n    properties\n  }\n}\n    ","\n",""]);return c=function(){return e},e}function m(){let e=(0,r._)(["\n    mutation CreateModel($data: CreateModelInput!) {\n  createModel(data: $data)\n}\n    "]);return m=function(){return e},e}function E(){let e=(0,r._)(["\n    mutation UpdateModel($where: ModelWhereInput!, $data: UpdateModelInput!) {\n  updateModel(where: $where, data: $data)\n}\n    "]);return E=function(){return e},e}function f(){let e=(0,r._)(["\n    mutation DeleteModel($where: ModelWhereInput!) {\n  deleteModel(where: $where)\n}\n    "]);return f=function(){return e},e}function I(){let e=(0,r._)(["\n    mutation PreviewModelData($where: WhereIdInput!) {\n  previewModelData(where: $where)\n}\n    "]);return I=function(){return e},e}let N={},h=(0,a.Ps)(s()),p=(0,a.Ps)(o()),R=(0,a.Ps)(d()),g=(0,a.Ps)(u(),p);function S(e){let n={...N,...e};return l.aM(g,n)}(0,a.Ps)(c(),h,R);let T=(0,a.Ps)(m());function D(e){let n={...N,...e};return i.D(T,n)}let A=(0,a.Ps)(E());function C(e){let n={...N,...e};return i.D(A,n)}let _=(0,a.Ps)(f());function x(e){let n={...N,...e};return i.D(_,n)}let O=(0,a.Ps)(I());function b(e){let n={...N,...e};return i.D(O,n)}},68753:function(e,n,t){t.d(n,{N:function(){return a},q:function(){return l}});var r=t(67294);let a=(0,r.createContext)({});function l(){return(0,r.useContext)(a)}},59709:function(e,n,t){t.d(n,{Z:function(){return M}});var r=t(85893),a=t(61782),l=t(11163),i=t(21367),s=t(4922),o=t(19521),d=t(25675),u=t.n(d);function c(){return(0,r.jsx)(u(),{src:"/images/logo-white-with-text.svg",alt:"Wren AI",width:125,height:30})}var m=t(84908),E=t(67294),f=t(80002),I=t(37031),N=t(17625),h=t.n(N),p=t(63626),R=t.n(p),g=t(30046),S=t.n(g),T=t(46140),D=t(61030),A=t(68753);let{Text:C}=f.default,_=(e,n)=>{let t=e?T.wR.IN_PROGRESS:n;return({[T.wR.IN_PROGRESS]:(0,r.jsxs)(s.default,{size:[4,0],children:[(0,r.jsx)(R(),{className:"mr-1 gray-1"}),(0,r.jsx)(C,{className:"gray-1",children:"Deploying..."})]}),[T.wR.SYNCRONIZED]:(0,r.jsxs)(s.default,{size:[4,0],children:[(0,r.jsx)(h(),{className:"mr-1 green-7"}),(0,r.jsx)(C,{className:"gray-1",children:"Synced"})]}),[T.wR.UNSYNCRONIZED]:(0,r.jsxs)(s.default,{size:[4,0],children:[(0,r.jsx)(S(),{className:"mr-1 gold-6"}),(0,r.jsx)(C,{className:"gray-1",children:"Undeployed changes"})]})})[t]||""};function x(){let{data:e,loading:n,startPolling:t,stopPolling:a}=(0,A.q)(),[l,{data:o,loading:d}]=(0,D.J$)({onCompleted:e=>{var n,t;(null===(n=e.deploy)||void 0===n?void 0:n.status)==="FAILED"&&(console.error("Failed to deploy - ",null===(t=e.deploy)||void 0===t?void 0:t.error),I.default.error("Failed to deploy. Please check the log for more details."))}});(0,E.useEffect)(()=>{var n;(null==o?void 0:null===(n=o.deploy)||void 0===n?void 0:n.status)==="FAILED"&&(null==e?void 0:e.modelSync.status)===T.wR.UNSYNCRONIZED&&a()},[o,e]);let u=null==e?void 0:e.modelSync.status,c=()=>{l(),t(1e3)};(0,E.useEffect)(()=>{u===T.wR.SYNCRONIZED&&a()},[u]);let m=d||n||[T.wR.SYNCRONIZED,T.wR.IN_PROGRESS].includes(u);return(0,r.jsxs)(s.default,{size:[8,0],children:[_(d,u),(0,r.jsx)(i.default,{className:"adm-modeling-header-btn ".concat(m?"":"gray-10"),disabled:m,onClick:()=>c(),size:"small","data-guideid":"deploy-model",children:"Deploy"})]})}let{Header:O}=a.default,b=(0,o.ZP)(i.default).withConfig({displayName:"HeaderBar__StyledButton",componentId:"sc-b9d2d314-0"})(["background:",";font-weight:",";border:none;color:var(--gray-1);&:hover,&:focus{background:",";color:var(--gray-1);}"],e=>e.$isHighlight?"rgba(255, 255, 255, 0.20)":"transparent",e=>e.$isHighlight?"700":"normal",e=>e.$isHighlight?"rgba(255, 255, 255, 0.20)":"rgba(255, 255, 255, 0.05)"),y=(0,o.ZP)(O).withConfig({displayName:"HeaderBar__StyledHeader",componentId:"sc-b9d2d314-1"})(["height:48px;border-bottom:1px solid var(--gray-5);background:var(--gray-10);padding:10px 16px;"]);function L(){let e=(0,l.useRouter)(),{pathname:n}=e,t=!n.startsWith(m.y$.Onboarding),a=n.startsWith(m.y$.Modeling);return(0,r.jsx)(y,{children:(0,r.jsxs)("div",{className:"d-flex justify-space-between align-center",style:{marginTop:-2},children:[(0,r.jsxs)(s.default,{size:[48,0],children:[(0,r.jsx)(c,{}),t&&(0,r.jsxs)(s.default,{size:[16,0],children:[(0,r.jsx)(b,{shape:"round",size:"small",$isHighlight:n.startsWith(m.y$.Home),onClick:()=>e.push(m.y$.Home),children:"Home"}),(0,r.jsx)(b,{shape:"round",size:"small",$isHighlight:n.startsWith(m.y$.Modeling),onClick:()=>e.push(m.y$.Modeling),children:"Modeling"}),(0,r.jsx)(b,{shape:"round",size:"small",$isHighlight:n.startsWith(m.y$.Knowledge),onClick:()=>e.push(m.y$.KnowledgeQuestionSQLPairs),children:"Knowledge"}),(0,r.jsx)(b,{shape:"round",size:"small",$isHighlight:n.startsWith(m.y$.APIManagement),onClick:()=>e.push(m.y$.APIManagementHistory),children:"API"})]})]}),a&&(0,r.jsx)(s.default,{size:[16,0],children:(0,r.jsx)(x,{})})]})})}var P=t(33190),j=t(67783),v=t(90512);let{Content:U}=a.default;function M(e){let{loading:n}=(0,j.V)(),{children:t,loading:l}=e,i=n||l;return(0,r.jsxs)(a.default,{className:(0,v.Z)("adm-main bg-gray-3",{"overflow-hidden":i}),children:[(0,r.jsx)(L,{}),(0,r.jsx)(U,{className:"adm-content",children:t}),(0,r.jsx)(P.ZP,{visible:i})]})}},43053:function(e,n,t){t.d(n,{J:function(){return h},Z:function(){return p}});var r=t(85893),a=t(67294),l=t(41609),i=t.n(l),s=t(13518),o=t(69828),d=t(66590),u=t(59530),c=t(98885);function m(e){let{modelValue:n,fieldValue:t,value:l={},onModelChange:i,onFieldChange:s,onChange:o,modelOptions:u,fieldOptions:m,modelDisabled:E,fieldDisabled:f}=e,[I,N]=(0,a.useState)({model:n,field:t,...l});return(0,a.useEffect)(()=>{(null==I?void 0:I.model)&&(null==I?void 0:I.field)&&o&&o(I)},[I]),(0,r.jsxs)(c.default.Group,{className:"d-flex",compact:!0,children:[(0,r.jsx)(d.default,{style:{width:"35%"},options:u,onChange:e=>{i&&i(e);let n={model:e,field:void 0};N(n),o&&o(n)},placeholder:"Model",value:(null==l?void 0:l.model)||n,disabled:E,showSearch:!0,optionFilterProp:"label","data-testid":"common__models-select"}),(0,r.jsx)(d.default,{className:"flex-grow-1",options:m,onChange:e=>{s&&s(e),N({...I,field:e})},placeholder:"Field",value:(null==l?void 0:l.field)||t,disabled:f,showSearch:!0,optionFilterProp:"label","data-testid":"common__fields-select"})]})}var E=t(84908),f=t(44280),I=t(32235),N=t(45403);let h={FROM_FIELD:"fromField",TO_FIELD:"toField",TYPE:"type"};function p(e){var n;let{defaultValue:t,loading:l,model:c,onClose:p,onSubmit:R,relations:g,visible:S,formMode:T,isRecommendMode:D}=e,[A]=s.Z.useForm(),C=T===E.SD.EDIT,_=(0,N.ZP)({model:c}),x=null===(n=_.modelOptions.find(e=>(0,E.bu)(e.value).referenceName===c))||void 0===n?void 0:n.value,O=null==t?void 0:t.toField.modelName,b=(0,N.ZP)({model:O,excludeModels:[c]});(0,a.useEffect)(()=>{if(!S||i()(t))return;let e=(0,N.tP)(t);A.setFieldsValue(e),b.onModelChange(e.toField.model)},[A,t,S]);let y=Object.keys(E.oD).map(e=>({label:(0,f.I)(e),value:E.oD[e]}));return(0,r.jsx)(o.Z,{title:"".concat(i()(t)?"Add":"Update"," relationship"),width:750,visible:S,okText:"Submit",onOk:()=>{A.validateFields().then(async e=>{await R({...t,...e}),p()}).catch(console.error)},onCancel:p,confirmLoading:l,maskClosable:!1,destroyOnClose:!0,afterClose:()=>A.resetFields(),centered:!0,children:(0,r.jsxs)(s.Z,{form:A,preserve:!1,layout:"vertical",children:[(0,r.jsx)(s.Z.Item,{label:"From",name:h.FROM_FIELD,required:!0,rules:[e=>{let{getFieldValue:n}=e;return{validator:(0,I.xq)(C||D,g,n)}}],children:(0,r.jsx)(m,{modelValue:x,modelDisabled:!0,fieldDisabled:C,onModelChange:_.onModelChange,modelOptions:_.modelOptions,fieldOptions:_.fieldOptions})}),(0,r.jsx)(s.Z.Item,{label:"To",name:h.TO_FIELD,required:!0,rules:[e=>{let{getFieldValue:n}=e;return{validator:(0,I.kq)(C||D,g,n)}}],children:(0,r.jsx)(m,{onModelChange:b.onModelChange,modelOptions:b.modelOptions,fieldOptions:b.fieldOptions,modelDisabled:C,fieldDisabled:C})}),(0,r.jsx)(s.Z.Item,{label:"Type",name:h.TYPE,required:!0,rules:[{required:!0,message:u.q.ADD_RELATION.RELATION_TYPE.REQUIRED}],children:(0,r.jsx)(d.default,{"data-testid":"relationship-form__type-select",options:y,placeholder:"Select a relationship type"})})]})})}},12891:function(e,n,t){t.d(n,{Z:function(){return E}});var r=t(85893),a=t(25675),l=t.n(a),i=t(21367),s=t(19521),o=t(49397);let d=(0,s.ZP)(i.default).withConfig({displayName:"ButtonItem__StyledButton",componentId:"sc-184829de-0"})(["border:2px var(--gray-4) solid;background-color:var(--gray-2);border-radius:4px;width:100%;height:auto;&:focus{border:2px var(--gray-4) solid;background-color:var(--gray-2);}&:hover{border-color:var(--geekblue-6);background-color:var(--gray-2);}&.is-active{border-color:var(--geekblue-6) !important;background-color:var(--gray-2) !important;}&:disabled{opacity:0.5;}.ant-btn-loading-icon .anticon{font-size:24px;}"]),u=(0,s.ZP)(o.Z).withConfig({displayName:"ButtonItem__StyledIcon",componentId:"sc-184829de-1"})(["width:40px;height:40px;font-size:32px;display:inline-flex;justify-content:center;align-items:center;"]),c=s.ZP.div.withConfig({displayName:"ButtonItem__PlainImage",componentId:"sc-184829de-2"})(["border:1px var(--gray-4) solid;background-color:white;width:40px;height:40px;"]),m=s.ZP.div.withConfig({displayName:"ButtonItem__ComingSoon",componentId:"sc-184829de-3"})(["border:1px var(--gray-7) solid;color:var(--gray-7);font-size:8px;padding:2px 6px;border-radius:999px;&:before{content:'COMING SOON';}"]);function E(e){let{value:n,disabled:t,submitting:a,logo:i,IconComponent:s,label:o,onSelect:E,selectedTemplate:f}=e,I=f===n,N=I&&a;return(0,r.jsxs)(d,{className:["px-4 py-2 gray-8 d-flex align-center",N?"flex-start":"justify-space-between",I?"is-active":""].join(" "),disabled:t||a,loading:N,onClick:()=>E(n),children:[(0,r.jsxs)("div",{className:"d-flex align-center",style:{width:"100%"},children:[i?(0,r.jsx)(l(),{className:"mr-2",src:i,alt:o,width:"40",height:"40"}):s?(0,r.jsx)(u,{component:s,className:"mr-2"}):(0,r.jsx)(c,{className:"mr-2"}),o]}),t&&(0,r.jsx)(m,{})]})}},25063:function(e,n,t){t.d(n,{cS:function(){return eD},B5:function(){return eT},QC:function(){return eA},f6:function(){return e_},mt:function(){return eC},m4:function(){return eO},uR:function(){return ex}});var r=t(85893),a=t(82492),l=t.n(a),i=t(75517),s=t.n(i),o=t(88716),d=t.n(o),u=t(84908),c=t(41664),m=t.n(c),E=t(67294),f=t(55041),I=t(80002),N=t(40582),h=t(77840),p=t(12891);let R=e=>(0,r.jsx)(f.default,{span:6,children:(0,r.jsx)(p.Z,{...e})},e.label),g=(0,h.x)(R),S=(0,h.x)(R);var T=t(25675),D=t.n(T),A=t(13518),C=t(62819),_=t(21367),x=t(19521),O=t(39690);let b=(0,x.ZP)(A.Z).withConfig({displayName:"ConnectDataSource__StyledForm",componentId:"sc-6858b8f4-0"})(["border:1px var(--gray-4) solid;border-radius:4px;"]),y=x.ZP.div.withConfig({displayName:"ConnectDataSource__DataSource",componentId:"sc-6858b8f4-1"})(["border:1px var(--gray-4) solid;border-radius:4px;"]);var L=t(59530),P=t(47037),j=t.n(P),v=t(91966),U=t.n(v),M=t(98885),w=t(17579),F=t(78661),Q=t.n(F),q=t(56144);let Z=x.ZP.div.withConfig({displayName:"MultiSelectBox__StyledBox",componentId:"sc-45baf25f-0"})(["border:1px solid var(--gray-5);border-radius:4px;&.multiSelectBox-input-error{border-color:var(--red-5);}.ant-table{border:0;}.ant-table-body,.ant-table-placeholder{height:195px;}"]),G=x.ZP.div.withConfig({displayName:"MultiSelectBox__StyledTotal",componentId:"sc-45baf25f-1"})(["padding:8px 12px;border-bottom:1px var(--gray-3) solid;"]);function B(e){let{columns:n,loading:t,items:a,onChange:l,value:i}=e,[s,o]=(0,E.useState)(new Set(i)),[d,u]=(0,E.useState)(""),{status:c}=(0,E.useContext)(q.FormItemInputContext),m=(0,E.useMemo)(()=>d?a.filter(e=>n.map(n=>e[n.dataIndex]).some(e=>j()(e)&&e.includes(d))):a,[a,d]),f=e=>{let n=new Set(s);n.has(e)?n.delete(e):n.add(e),o(n),l&&l(Array.from(n))},I=0===s.size?a.length:"".concat(s.size,"/").concat(a.length);return(0,r.jsxs)(Z,{className:c?"multiSelectBox-input-".concat(c):void 0,children:[(0,r.jsxs)(G,{children:[I," table(s)"]}),(0,r.jsx)("div",{className:"p-2",children:(0,r.jsx)(M.default,{prefix:(0,r.jsx)(Q(),{}),onChange:e=>{e.persist();let{value:n}=e.target;u(n)},placeholder:"Search here",allowClear:!0})}),(0,r.jsx)(w.Z,{rowSelection:{type:"checkbox",selectedRowKeys:Array.from(s),onSelect:e=>f(e.value),onChange(e){if(0===e.length){let e=m.map(e=>e.value),n=new Set(U()([...s.values()],e));o(n),l&&l(Array.from(n));return}if(e.length===m.length){let n=new Set([...s,...e]);o(n),l&&l(Array.from(n))}}},rowKey:e=>e.value,columns:n,dataSource:m,scroll:{y:195},pagination:!1,loading:t})]})}let{Title:k,Text:H}=I.default,W=[{title:"Table name",dataIndex:"name"}];var Y=t(57557),V=t.n(Y),K=t(4922),$=t(64822),z=t(16711),J=t(81506),X=t.n(J),ee=t(93181),en=t.n(ee),et=t(31682),er=t.n(et),ea=t(39616),el=t(42187);let{Panel:ei}=el.default,es=(0,x.ZP)(el.default).withConfig({displayName:"SelectionTable__StyledCollapse",componentId:"sc-bfcb74be-0"})(["&.ant-collapse.adm-error{border-color:var(--red-5);border-bottom:1px solid var(--red-5);}&.ant-collapse{background-color:white;border-color:var(--gray-4);> .ant-collapse-item > .ant-collapse-header{padding:16px 12px;align-items:center;}> .ant-collapse-item,.ant-collapse-content{border-color:var(--gray-4);}.ant-collapse-content-box{padding:0px;}.ant-table{border:none;.ant-table-thead > tr > th{color:var(--gray-7);background-color:white;}&.ant-table-empty{.ant-empty-normal{margin:16px 0;}}}}"]),eo=(0,x.ZP)(N.default).attrs(e=>({className:"".concat(e.$isRowSelection?"":"ml-1")})).withConfig({displayName:"SelectionTable__StyledRow",componentId:"sc-bfcb74be-1"})([""]);var ed=(0,E.forwardRef)(function(e,n){let{columns:t,dataSource:a,extra:l,enableRowSelection:i,onChange:s,rowKey:o,tableHeader:d,tableTitle:u}=e,{status:c}=(0,E.useContext)(q.FormItemInputContext),m=function(e){let[n,t]=(0,E.useState)([e]),r=e=>t(e);return{collapseDefaultActiveKey:n,onChangeCollapsePanelState:r,onCollapseOpen:(e,t)=>{r([t]),n.includes(t)&&e.stopPropagation()}}}(u),f=!!i;return(0,r.jsx)(es,{className:c?"adm-".concat(c):"",defaultActiveKey:m.collapseDefaultActiveKey,onChange:m.onChangeCollapsePanelState,children:(0,r.jsx)(ei,{extra:l&&l(m.onCollapseOpen),header:(0,r.jsx)(eo,{wrap:!1,gutter:8,align:"middle",$isRowSelection:f,children:d}),showArrow:!1,children:(0,r.jsx)(w.Z,{ref:n,columns:t,dataSource:a,rowKey:o,rowSelection:f?{type:"checkbox",onChange:(e,n)=>{s&&s(n)}}:void 0,pagination:{hideOnSinglePage:!0,pageSize:50,size:"small"}})},u)})});function eu(e){return(0,r.jsx)(ed,{...e,tableHeader:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(ea.ZA,{className:"pr-2 text-md"}),e.tableTitle]})})}var ec=t(44280),em=t(90883),eE=t(43053),ef=t(45403);let{Title:eI,Text:eN}=I.default,eh=["name","isAutoGenerated"];function ep(e){let{index:n,modelName:t,onSetRelation:a,onDeleteRow:l,relations:i,recommendNameMapping:s}=e,o=[{title:"From",dataIndex:"fromField",key:"fromField",render:e=>"".concat(e.modelName,".").concat(e.fieldName),width:"35%"},{title:"To",dataIndex:"toField",key:"toField",render:e=>"".concat(e.modelName,".").concat(e.fieldName),width:"35%"},{title:"Type",dataIndex:"type",key:"type",render:(e,n)=>(0,r.jsxs)(r.Fragment,{children:[(0,ec.I)(e),n.isAutoGenerated&&(0,r.jsx)(eN,{className:"pl-1",type:"secondary",children:"(auto-generated)"})]}),width:"30%"},{title:"",key:"action",width:48,align:"center",render:(e,n)=>(0,r.jsxs)(K.default,{size:[16,0],children:[(0,r.jsx)(en(),{onClick:()=>a({modelName:t,defaultValue:n})}),(0,r.jsx)($.Z,{title:"Confirm to delete?",okText:"Delete",okButtonProps:{danger:!0},onConfirm:()=>l(t,n),children:(0,r.jsx)(X(),{})})]})}];return(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(eu,{columns:o,dataSource:i,tableTitle:s[t],extra:e=>(0,r.jsxs)(_.default,{onClick:n=>{a({modelName:t}),e(n,s[t])},size:"small",title:"Add relationship",children:[(0,r.jsx)(er(),{}),"Add"]}),rowKey:e=>"".concat(t,"-").concat(e.fromField.fieldName,"-").concat(e.toField.modelName,"-").concat(e.toField.fieldName,"-").concat(n)})})}var eR=t(46140),eg=t(94638),eS=t(31069);let eT={[u.ye.STARTER]:{step:0,component:function(e){let{onNext:n,submitting:t}=e,[a,l]=(0,E.useState)(),i=eC(),s=ex();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.default.Title,{level:1,className:"mb-3",children:"Connect a data source"}),(0,r.jsxs)(I.default.Text,{children:["Vote for your favorite data sources on"," ",(0,r.jsx)(m(),{href:"https://github.com/Canner/WrenAI/discussions/327",target:"_blank",rel:"noopener noreferrer",children:"GitHub"}),"."]}),(0,r.jsx)(N.default,{className:"mt-6",gutter:[16,16],children:(0,r.jsx)(g,{data:i,onSelect:e=>{n&&n({dataSource:e})},submitting:t})}),(0,r.jsx)("div",{className:"py-8"}),(0,r.jsx)(I.default.Title,{level:1,className:"mb-3",children:"Play around with sample data"}),(0,r.jsx)(N.default,{className:"mt-6",gutter:[16,16],children:(0,r.jsx)(S,{data:s,onSelect:e=>{l(e),n&&n({template:e})},submitting:t,selectedTemplate:a})}),(0,r.jsx)("div",{className:"py-12"})]})}},[u.ye.CREATE_DATA_SOURCE]:{step:0,component:function(e){let{connectError:n,dataSource:t,submitting:a,onNext:l,onBack:i}=e,[s]=A.Z.useForm(),o=e_(t);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.default.Title,{level:1,className:"mb-3",children:"Connect the data source"}),(0,r.jsxs)(I.default.Text,{children:["Vote for your favorite data sources on"," ",(0,r.jsx)(m(),{href:"https://github.com/Canner/WrenAI/discussions/327",target:"_blank",rel:"noopener noreferrer",children:"GitHub"}),"."]}),(0,r.jsxs)(b,{form:s,layout:"vertical",className:"p-6 my-6",children:[(0,r.jsxs)(N.default,{align:"middle",className:"mb-6",children:[(0,r.jsx)(f.default,{span:12,children:(0,r.jsxs)(y,{className:"d-inline-block px-4 py-2 bg-gray-2 gray-8",children:[(0,r.jsx)(D(),{className:"mr-2",src:o.logo,alt:t,width:"40",height:"40"}),o.label]})}),(0,r.jsxs)(f.default,{className:"text-right",span:12,children:["Learn more information in the ",o.label," ",(0,r.jsx)(m(),{href:o.guide,target:"_blank",rel:"noopener noreferrer",children:"setup guide"}),"."]})]}),(0,r.jsx)(o.component,{})]}),n&&(0,r.jsx)(C.default,{message:n.shortMessage,description:t===O.t.POSTGRES?eO(n):n.message,type:"error",showIcon:!0,className:"my-6"}),(0,r.jsxs)(N.default,{gutter:16,className:"pt-6",children:[(0,r.jsx)(f.default,{span:12,children:(0,r.jsx)(_.default,{onClick:i,size:"large",className:"adm-onboarding-btn",disabled:a,children:"Back"})}),(0,r.jsx)(f.default,{className:"text-right",span:12,children:(0,r.jsx)(_.default,{type:"primary",size:"large",onClick:()=>{s.validateFields().then(e=>{l&&l({properties:e})}).catch(e=>{console.error(e)})},loading:a,className:"adm-onboarding-btn",children:"Next"})})]})]})},maxWidth:960},[u.ye.SELECT_MODELS]:{step:1,component:function(e){let{fetching:n,tables:t,onBack:a,onNext:l,submitting:i}=e,[s]=A.Z.useForm(),o=t.map(e=>({...e,value:e.name}));return(0,r.jsxs)("div",{children:[(0,r.jsx)(k,{level:1,className:"mb-3",children:"Select tables to create data models"}),(0,r.jsxs)(H,{children:["We will create data models based on selected tables to help AI better understand your data.",(0,r.jsx)("br",{}),(0,r.jsx)(m(),{href:"https://docs.getwren.ai/oss/guide/modeling/overview",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})," ","about data models."]}),(0,r.jsx)("div",{className:"my-6",children:(0,r.jsx)(A.Z,{form:s,layout:"vertical",style:{marginTop:8},children:(0,r.jsx)(A.Z.Item,{name:"tables",rules:[{required:!0,message:L.q.SETUP_MODEL.TABLE.REQUIRED}],children:(0,r.jsx)(B,{columns:W,items:o,loading:n})})})}),(0,r.jsxs)(N.default,{gutter:16,className:"pt-6",children:[(0,r.jsx)(f.default,{span:12,children:(0,r.jsx)(_.default,{onClick:a,size:"large",className:"adm-onboarding-btn",disabled:i,children:"Back"})}),(0,r.jsx)(f.default,{className:"text-right",span:12,children:(0,r.jsx)(_.default,{type:"primary",size:"large",onClick:()=>{s.validateFields().then(e=>{l&&l({selectedTables:e.tables})}).catch(e=>{console.error(e)})},className:"adm-onboarding-btn",loading:i,children:"Next"})})]})]})},maxWidth:960},[u.ye.DEFINE_RELATIONS]:{step:2,component:function(e){let{fetching:n,recommendRelations:t,recommendNameMapping:a,onBack:l,onNext:i,onSkip:s,submitting:o}=e,[d,u]=(0,E.useState)(t),[c,m]=(0,E.useState)(null),[I,h]=(0,E.useState)(!1);(0,E.useEffect)(()=>{u(t);let e=Object.values(t);0!==e.length&&h(e.every(e=>0===e.length))},[t]);let p=(0,em.Z)(),R=(e,n)=>{let r=(t[e]||[]).find(e=>JSON.stringify(V()(e,eh))===JSON.stringify(V()(n,eh)));return(null==r?void 0:r.isAutoGenerated)||!1},g=e=>{let n=(0,ef.T9)(e),t=n.fromField.modelName,r=R(t,n);u({...d,[t]:[...d[t]||[],{...n,isAutoGenerated:r}]})},S=(e,n)=>{u({...d,[e]:d[e].filter(e=>JSON.stringify(e)!==JSON.stringify(n))})},T=e=>{m(e),p.openModal()},D=(e,n,t)=>{let r=(0,ef.T9)(t),a=R(e,r);u({...d,[e]:d[e].map(e=>JSON.stringify(e)===JSON.stringify(n)?{...r,isAutoGenerated:a}:e)})};return(0,r.jsxs)("div",{children:[(0,r.jsx)(eI,{level:1,className:"mb-3",children:"Define relationships"}),(0,r.jsx)(eN,{children:"You can create relationships between selected tables. We provide suggested relationships based on primary and foreign keys defined in your data source. The relationships are then added to data models."}),I&&(0,r.jsx)(C.default,{message:"No recommended relationships",description:"No relationships are recommended because no primary or foreign keys were detected.",type:"info",showIcon:!0,className:"my-6"}),(0,r.jsxs)("div",{className:"my-6 text-center",children:[Object.entries(d).map((e,n)=>{let[t,l=[]]=e;return(0,r.jsx)(ep,{index:n,modelName:t,relations:l,onSetRelation:T,onDeleteRow:S,recommendNameMapping:a},"".concat(t,"-").concat(l.length))}),(0,r.jsx)(z.default,{spinning:n,tip:"Loading...",className:"my-15"})]}),(0,r.jsxs)(N.default,{gutter:16,className:"pt-6",children:[(0,r.jsx)(f.default,{span:12,children:(0,r.jsx)(_.default,{onClick:l,size:"large",className:"adm-onboarding-btn",children:"Back"})}),(0,r.jsxs)(f.default,{className:"text-right",span:12,children:[(0,r.jsx)(_.default,{className:"mr-4 gray-7 adm-onboarding-btn",type:"text",size:"large",onClick:s,disabled:o,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_skip_define_relationship",children:"Skip this step"}),(0,r.jsx)(_.default,{type:"primary",size:"large",onClick:()=>{i&&i({relations:d})},className:"adm-onboarding-btn",loading:o,disabled:n,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_finish_define_relationship",children:"Finish"})]})]}),(0,r.jsx)(eE.Z,{...p.state,model:null==c?void 0:c.modelName,onSubmit:async e=>{(null==c?void 0:c.defaultValue)?D(c.modelName,c.defaultValue,e):g(e),m(null)},onClose:()=>{m(null),p.closeModal()},defaultValue:(null==c?void 0:c.defaultValue)?V()(c.defaultValue,eh):void 0,relations:d,isRecommendMode:!!(null==c?void 0:c.defaultValue)})]})}}},eD={[u.tW.BIG_QUERY]:{...(0,eS.Mh)(u.tW.BIG_QUERY),guide:"https://docs.getwren.ai/oss/guide/connect/bigquery",disabled:!1},[u.tW.DUCKDB]:{...(0,eS.Mh)(u.tW.DUCKDB),guide:"https://docs.getwren.ai/oss/guide/connect/duckdb",disabled:!1},[u.tW.POSTGRES]:{...(0,eS.Mh)(u.tW.POSTGRES),guide:"https://docs.getwren.ai/oss/guide/connect/postgresql",disabled:!1},[u.tW.MYSQL]:{...(0,eS.Mh)(u.tW.MYSQL),guide:"https://docs.getwren.ai/oss/guide/connect/mysql",disabled:!1},[u.tW.MSSQL]:{...(0,eS.Mh)(u.tW.MSSQL),guide:"https://docs.getwren.ai/oss/guide/connect/sqlserver",disabled:!1},[u.tW.CLICK_HOUSE]:{...(0,eS.Mh)(u.tW.CLICK_HOUSE),guide:"https://docs.getwren.ai/oss/guide/connect/clickhouse",disabled:!1},[u.tW.TRINO]:{...(0,eS.Mh)(u.tW.TRINO),guide:"https://docs.getwren.ai/oss/guide/connect/trino",disabled:!1},[u.tW.SNOWFLAKE]:{...(0,eS.Mh)(u.tW.SNOWFLAKE),guide:"https://docs.getwren.ai/oss/guide/connect/snowflake",disabled:!1}},eA={[eR.Y0.ECOMMERCE]:{label:"E-commerce",IconComponent:s(),guide:"https://docs.getwren.ai/oss/getting_started/sample_data/ecommerce"},[eR.Y0.HR]:{label:"Human Resource",IconComponent:d(),guide:"https://docs.getwren.ai/oss/getting_started/sample_data/hr"}},eC=()=>Object.values(eD),e_=e=>l()(eD[e],(0,eS.pA)(e)),ex=()=>Object.keys(eA).map(e=>({...eA[e],value:e})),eO=e=>e.code===eg.O1.CONNECTION_REFUSED?(0,r.jsxs)("div",{children:[e.message,". ",(0,r.jsx)("br",{}),"If you are having trouble connecting to your PostgreSQL database, please refer to our"," ",(0,r.jsx)("a",{href:"https://docs.getwren.ai/oss/guide/connect/postgresql#connect",target:"_blank",rel:"noopener noreferrer",children:"documentation"})," ","for detailed instructions."]}):e.message},67783:function(e,n,t){t.d(n,{Z:function(){return N},V:function(){return I}});var r=t(67294),a=t(11163),l=t(82729),i=t(68806),s=t(6812);function o(){let e=(0,l._)(["\n    query OnboardingStatus {\n  onboardingStatus {\n    status\n  }\n}\n    "]);return o=function(){return e},e}let d={},u=(0,i.Ps)(o());function c(e){let n={...d,...e};return s.aM(u,n)}var m=t(46140),E=t(84908);let f={[m.uo.DATASOURCE_SAVED]:E.y$.OnboardingModels,[m.uo.NOT_STARTED]:E.y$.OnboardingConnection,[m.uo.ONBOARDING_FINISHED]:E.y$.Modeling,[m.uo.WITH_SAMPLE_DATASET]:E.y$.Modeling},I=()=>{var e;let n=(0,a.useRouter)(),{data:t,loading:l}=c(),i=null==t?void 0:null===(e=t.onboardingStatus)||void 0===e?void 0:e.status;return(0,r.useEffect)(()=>{if(i){let e=f[i],t=n.pathname;if(e&&e!==E.y$.Modeling){if(e===t||n.pathname.startsWith(E.y$.Onboarding)&&i!==m.uo.ONBOARDING_FINISHED)return;n.push(e);return}if("/"===t||t===E.y$.OnboardingRelationships&&i===m.uo.WITH_SAMPLE_DATASET||[E.y$.OnboardingConnection,E.y$.OnboardingModels].includes(t)){n.push(e);return}}},[i,n.pathname]),{loading:l,onboardingStatus:i}};function N(){var e;let{data:n,loading:t,error:r,refetch:a}=c();return{loading:t,error:r,refetch:a,onboardingStatus:null==n?void 0:null===(e=n.onboardingStatus)||void 0===e?void 0:e.status}}},45403:function(e,n,t){t.d(n,{T9:function(){return o},ZP:function(){return u},tP:function(){return d}});var r=t(67294),a=t(3591),l=t(84908);let i=["id","referenceName"],s=["id","referenceName"],o=e=>{let n=(0,l.bu)(e.fromField.model),t=(0,l.bu)(e.fromField.field),r=(0,l.bu)(e.toField.model),a=(0,l.bu)(e.toField.field);return{...e,fromField:{modelId:n.id,modelName:n.referenceName,fieldId:t.id,fieldName:t.referenceName},toField:{modelId:r.id,modelName:r.referenceName,fieldId:a.id,fieldName:a.referenceName}}},d=e=>{let n={model:{id:e.fromField.modelId,referenceName:e.fromField.modelName},field:{id:e.fromField.fieldId,referenceName:e.fromField.fieldName}},t={model:{id:e.toField.modelId,referenceName:e.toField.modelName},field:{id:e.toField.fieldId,referenceName:e.toField.fieldName}};return{fromField:{model:(0,l.Q_)(n.model,i),field:(0,l.Q_)(n.field,s)},toField:{model:(0,l.Q_)(t.model,i),field:(0,l.Q_)(t.field,s)},type:e.type}};function u(e){let{model:n,excludeModels:t}=e,[o,d]=(0,r.useState)(n||"");(0,r.useEffect)(()=>d(n),[n]);let{data:u}=(0,a.it)({fetchPolicy:"cache-and-network"}),c=(0,r.useMemo)(()=>u?u.listModels.map(e=>({id:e.id,referenceName:e.referenceName,displayName:e.displayName,fields:e.fields})):[],[u]),m=(0,r.useMemo)(()=>c.filter(e=>!(t&&t.includes(e.referenceName))),[t,o,u]),E=(0,r.useMemo)(()=>m.map(e=>({label:e.displayName,value:(0,l.Q_)(e,i),"data-testid":"common__models__select-option"})),[m]),f=(0,r.useMemo)(()=>m.find(e=>e.referenceName===o),[E,o]);return{modelOptions:E,fieldOptions:(0,r.useMemo)(()=>((null==f?void 0:f.fields)||[]).map(e=>({label:e.displayName,value:(0,l.Q_)(e,s),"data-testid":"common__fields__select-option"})),[f]),onModelChange:e=>{d((0,l.bu)(e).referenceName)}}}},90883:function(e,n,t){t.d(n,{Z:function(){return l}});var r=t(67294),a=t(84908);function l(){let[e,n]=(0,r.useState)(!1),[t,l]=(0,r.useState)(a.SD.CREATE),[i,s]=(0,r.useState)(null),[o,d]=(0,r.useState)(null);return{state:{visible:e,formMode:t,defaultValue:o,payload:i},openModal:(e,t)=>{t&&s(t),e&&d(e),e&&l(a.SD.EDIT),n(!0)},closeModal:()=>{n(!1),s(null),d(null),l(a.SD.CREATE)}}}},49397:function(e,n,t){var r=t(5152);let a=t.n(r)()(()=>t.e(36).then(t.bind(t,91036)),{loadableGenerated:{webpack:()=>[91036]},ssr:!1});n.Z=a},44280:function(e,n,t){t.d(n,{X:function(){return s},I:function(){return i}});var r=t(46140),a=t(84908);let l="Unknown",i=e=>({[a.oD.MANY_TO_ONE]:"Many-to-one",[a.oD.ONE_TO_MANY]:"One-to-many",[a.oD.ONE_TO_ONE]:"One-to-one"})[e]||l,s=e=>({[r.i_.AVG]:{name:"Average",syntax:"avg(column)",description:"Returns the average of the values in the column."},[r.i_.COUNT]:{name:"Count",syntax:"count(column)",description:"Returns the count of non-null rows (also known as records) in the selected data."},[r.i_.MAX]:{name:"Max",syntax:"max(column)",description:"Returns the largest value found in the column."},[r.i_.MIN]:{name:"Min",syntax:"min(column)",description:"Returns the smallest value found in the column."},[r.i_.SUM]:{name:"Sum",syntax:"sum(column)",description:"Adds up all the values of the column."},[r.i_.ABS]:{name:"Absolute",syntax:"abs(column)",description:"Returns the absolute (positive) value of the specified column."},[r.i_.CBRT]:{name:"Cube root",syntax:"cbrt(column)",description:"Returns the cube root of the number."},[r.i_.CEIL]:{name:"Ceil",syntax:"ceil(column)",description:"Rounds a decimal up (ceil as in ceiling)."},[r.i_.EXP]:{name:"Exponential",syntax:"exp(column)",description:"Returns Euler’s number, e, raised to the power of the supplied number."},[r.i_.FLOOR]:{name:"Floor",syntax:"floor(column)",description:"Rounds a decimal number down."},[r.i_.LN]:{name:"Natural logarithm",syntax:"ln(column)",description:"Returns the natural logarithm of the number."},[r.i_.LOG10]:{name:"Log10",syntax:"log10(column)",description:"Returns the base 10 log of the number."},[r.i_.ROUND]:{name:"Round",syntax:"round(column)",description:"Rounds a decimal number either up or down to the nearest integer value."},[r.i_.SIGN]:{name:"Signum",syntax:"sign(column)",description:"Returns the signum function of the number."},[r.i_.LENGTH]:{name:"Length",syntax:"length(column)",description:"Returns the number of characters in string."},[r.i_.REVERSE]:{name:"Reverse",syntax:"reverse(column)",description:"Returns string with the characters in reverse order."}})[e]||{name:l,syntax:l,description:l}},31069:function(e,n,t){t.d(n,{Mh:function(){return M},pA:function(){return w},Ur:function(){return j},J9:function(){return v}});var r=t(84908),a=t(85893),l=t(67294),i=t(6468),s=t(21367),o=t(13518),d=t(98885),u=t(72647),c=t.n(u),m=t(59530);let E=e=>{let{onChange:n,value:t}=e,[r,o]=(0,l.useState)([]);(0,l.useEffect)(()=>{t||o([])},[t]);let d=(e,n)=>{let t=new FileReader;t.onloadend=e=>{let r=t.result;r&&n(JSON.parse(String(r)))},t.readAsText(e)};return(0,a.jsx)(i.Z,{accept:".json",fileList:r,onChange:e=>{let{file:t,fileList:r}=e;if(r.length){let e=r[0];d(t.originFileObj,e=>{n&&n(e)}),o([e])}},onRemove:()=>{o([]),n&&n(void 0)},maxCount:1,children:(0,a.jsx)(s.default,{icon:(0,a.jsx)(c(),{}),children:"Click to upload JSON key file"})})};function f(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",required:!0,name:"displayName",rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"Our BigQuery"})}),(0,a.jsx)(o.Z.Item,{label:"Project ID",required:!0,name:"projectId",rules:[{required:!t,message:m.q.CONNECTION.PROJECT_ID.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"The GCP project ID",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Dataset ID",required:!0,name:"datasetId",rules:[{required:!t,message:m.q.CONNECTION.DATASET_ID.REQUIRED}],children:(0,a.jsx)(d.default,{disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Credentials",required:!t,name:"credentials",rules:[{required:!t,message:m.q.CONNECTION.CREDENTIAL.REQUIRED}],children:(0,a.jsx)(E,{})})]})}var I=t(41664),N=t.n(I),h=t(40582),p=t(55041),R=t(81506),g=t.n(R),S=t(31682),T=t.n(S);let{TextArea:D}=d.default;function A(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"DuckDB"})}),(0,a.jsx)(o.Z.Item,{label:"Initial SQL statements",name:"initSql",extra:"These statements are meant to be executed only once during initialization.",rules:[{required:!0,message:m.q.CONNECTION.INIT_SQL.REQUIRED}],children:(0,a.jsx)(D,{placeholder:"CREATE TABLE new_tbl AS SELECT * FROM read_csv('input.csv');",rows:4})}),(0,a.jsx)(o.Z.Item,{label:"Configuration options",extra:(0,a.jsxs)(a.Fragment,{children:["DuckDB offers various configuration options that can modify the system's behavior."," ",(0,a.jsx)(N(),{href:"https://duckdb.org/docs/configuration/overview.html",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})]}),children:(0,a.jsx)(o.Z.List,{name:"configurations",initialValue:[{}],children:(e,n)=>{let{add:t,remove:r}=n;return(0,a.jsxs)(a.Fragment,{children:[e.map(e=>{let{key:n,name:t,...l}=e;return(0,a.jsxs)(h.default,{wrap:!1,gutter:8,children:[(0,a.jsx)(p.default,{flex:"1 0",children:(0,a.jsx)(o.Z.Item,{...l,name:[t,"key"],style:{width:"100%"},rules:[e=>{let{getFieldValue:n}=e;return{validator:(e,r)=>n(["configurations",t,"value"])&&!r?Promise.reject(m.q.CONNECTION.CONFIGURATION.KEY.REQUIRED):Promise.resolve()}}],children:(0,a.jsx)(d.default,{placeholder:"Key"})})}),(0,a.jsx)(p.default,{flex:"1 0",children:(0,a.jsx)(o.Z.Item,{...l,name:[t,"value"],style:{width:"100%"},rules:[e=>{let{getFieldValue:n}=e;return{validator:(e,r)=>n(["configurations",t,"key"])&&!r?Promise.reject(m.q.CONNECTION.CONFIGURATION.VALUE.REQUIRED):Promise.resolve()}}],children:(0,a.jsx)(d.default,{placeholder:"Value"})})}),(0,a.jsx)(p.default,{flex:"none",className:"p-1",children:(0,a.jsx)(g(),{onClick:()=>r(t)})})]},n)}),(0,a.jsx)(o.Z.Item,{noStyle:!0,children:(0,a.jsx)(s.default,{type:"dashed",onClick:()=>t(),block:!0,icon:(0,a.jsx)(T(),{}),children:"Add an option"})})]})}})}),(0,a.jsx)(o.Z.Item,{label:"Extensions",extra:(0,a.jsxs)(a.Fragment,{children:["DuckDB has an extension mechanism that enables the dynamic loading of extensions."," ",(0,a.jsx)(N(),{href:"https://duckdb.org/docs/extensions/overview.html",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})]}),children:(0,a.jsx)(o.Z.List,{name:"extensions",initialValue:[""],children:(e,n)=>{let{add:t,remove:r}=n;return(0,a.jsxs)(a.Fragment,{children:[e.map(e=>{let{key:n,name:t,...l}=e;return(0,a.jsxs)(h.default,{wrap:!1,gutter:8,className:"my-2",children:[(0,a.jsx)(p.default,{flex:"1 0",children:(0,a.jsx)(o.Z.Item,{...l,name:t,noStyle:!0,style:{width:"100%"},children:(0,a.jsx)(d.default,{placeholder:"Extension name"})})}),(0,a.jsx)(p.default,{flex:"none",className:"p-1",children:(0,a.jsx)(g(),{onClick:()=>r(t)})})]},n)}),(0,a.jsx)(o.Z.Item,{noStyle:!0,children:(0,a.jsx)(s.default,{type:"dashed",onClick:()=>t(),block:!0,icon:(0,a.jsx)(T(),{}),children:"Add an extension"})})]})}})})]})}var C=t(32235);function _(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:C.KT}],children:(0,a.jsx)(d.default,{placeholder:"********",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PORT.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"3306",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"user",rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"input password"})}),(0,a.jsx)(o.Z.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DATABASE.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"MySQL database name",disabled:t})})]})}var x=t(19662);function O(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:C.KT}],children:(0,a.jsx)(d.default,{placeholder:"********",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PORT.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"5432",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"user",rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"input password"})}),(0,a.jsx)(o.Z.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DATABASE.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"PostgreSQL database name",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:(0,a.jsx)(x.Z,{})})]})}function b(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:C.KT}],children:(0,a.jsx)(d.default,{placeholder:"********",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PORT.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"1433",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"user",rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"input password"})}),(0,a.jsx)(o.Z.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DATABASE.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"SQL Server database name",disabled:t})}),(0,a.jsx)(o.Z.Item,{extra:"This parameter is used to skip server certificate validation. If you are using a trusted certificate, you can disable it.",label:"Enable Trust Server Certificate",name:"trustServerCertificate",valuePropName:"checked",initialValue:!0,children:(0,a.jsx)(x.Z,{})})]})}function y(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:C.KT}],children:(0,a.jsx)(d.default,{placeholder:"<your_click_house_account>.clickhouse.cloud",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PORT.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"8443",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"user",rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"input password"})}),(0,a.jsx)(o.Z.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DATABASE.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"ClickHouse database name",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:(0,a.jsx)(x.Z,{})})]})}function L(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:C.KT}],children:(0,a.jsx)(d.default,{placeholder:"********",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PORT.REQUIRED}],children:(0,a.jsx)(d.default,{disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Schemas",name:"schemas",required:!0,rules:[{required:!0,message:m.q.CONNECTION.SCHEMAS.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"catalog.schema1, catalog.schema2"})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"username",required:!0,rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!1,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"Input password"})}),(0,a.jsx)(o.Z.Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:(0,a.jsx)(x.Z,{})})]})}function P(e){let{mode:n}=e,t=n===r.SD.EDIT;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Username",name:"user",rules:[{required:!0,message:m.q.CONNECTION.USERNAME.REQUIRED}],children:(0,a.jsx)(d.default,{})}),(0,a.jsx)(o.Z.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:m.q.CONNECTION.PASSWORD.REQUIRED}],children:(0,a.jsx)(d.default.Password,{placeholder:"input password"})}),(0,a.jsx)(o.Z.Item,{label:"Account",name:"account",required:!0,rules:[{required:!0,message:m.q.CONNECTION.ACCOUNT.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"<snowflake_org_id>-<snowflake_user_id>",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:m.q.CONNECTION.DATABASE.REQUIRED}],children:(0,a.jsx)(d.default,{placeholder:"Snowflake database name",disabled:t})}),(0,a.jsx)(o.Z.Item,{label:"Schema",name:"schema",required:!0,rules:[{required:!0,message:m.q.CONNECTION.SCHEMA.REQUIRED}],children:(0,a.jsx)(d.default,{})})]})}let j=e=>{switch(e){case r.tW.BIG_QUERY:return"/images/dataSource/bigQuery.svg";case r.tW.POSTGRES:return"/images/dataSource/postgreSql.svg";case r.tW.MYSQL:return"/images/dataSource/mysql.svg";case r.tW.MSSQL:return"/images/dataSource/sqlserver.svg";case r.tW.CLICK_HOUSE:return"/images/dataSource/clickhouse.svg";case r.tW.DUCKDB:return"/images/dataSource/duckDb.svg";case r.tW.TRINO:return"/images/dataSource/trino.svg";case r.tW.SNOWFLAKE:return"/images/dataSource/snowflake.svg";default:return null}},v=e=>{switch(e){case r.tW.BIG_QUERY:return"BigQuery";case r.tW.POSTGRES:return"PostgreSQL";case r.tW.MYSQL:return"MySQL";case r.tW.MSSQL:return"SQL Server";case r.tW.CLICK_HOUSE:return"ClickHouse";case r.tW.DUCKDB:return"DuckDB";case r.tW.TRINO:return"Trino";case r.tW.SNOWFLAKE:return"Snowflake";default:return""}},U=e=>{switch(e){case r.tW.BIG_QUERY:return f;case r.tW.POSTGRES:return O;case r.tW.MYSQL:return _;case r.tW.MSSQL:return b;case r.tW.CLICK_HOUSE:return y;case r.tW.DUCKDB:return A;case r.tW.TRINO:return L;case r.tW.SNOWFLAKE:return P;default:return null}},M=e=>({label:v(e),logo:j(e),value:r.tW[e]}),w=e=>({component:U(e)||(()=>null)})},39690:function(e,n,t){var r,a;t.d(n,{t:function(){return r}}),(a=r||(r={})).BIG_QUERY="BIG_QUERY",a.DUCKDB="DUCKDB",a.POSTGRES="POSTGRES",a.MYSQL="MYSQL",a.MSSQL="MSSQL",a.CLICK_HOUSE="CLICK_HOUSE",a.TRINO="TRINO",a.SNOWFLAKE="SNOWFLAKE"},84908:function(e,n,t){t.d(n,{HE:function(){return C},BA:function(){return R},tW:function(){return b.t},Hk:function(){return T},SD:function(){return h},oD:function(){return y.uT},E6:function(){return S},mo:function(){return O},dI:function(){return x},QZ:function(){return y.Jq},F4:function(){return A},y$:function(){return g},L6:function(){return _},ye:function(){return p},bu:function(){return a},Q_:function(){return r}}),(I=h||(h={})).CREATE="CREATE",I.EDIT="EDIT";let r=(e,n)=>n.map(n=>"".concat(n,":").concat(e[n]||"")).join("☺"),a=e=>Object.fromEntries(e.split("☺").map(e=>e.split(":")));(N=p||(p={})).STARTER="starter",N.CREATE_DATA_SOURCE="createDataSource",N.SELECT_MODELS="selectModels",N.RECOMMEND_RELATIONS="recommendRelations",N.DEFINE_RELATIONS="defineRelations";var l,i,s,o,d,u,c,m,E,f,I,N,h,p,R,g,S,T,D,A,C,_,x,O,b=t(39690);(l=R||(R={})).BOOLEAN="BOOLEAN",l.TINYINT="TINYINT",l.INT2="INT2",l.SMALLINT="SMALLINT",l.INT4="INT4",l.INTEGER="INTEGER",l.INT8="INT8",l.BIGINT="BIGINT",l.INT64="INT64",l.NUMERIC="NUMERIC",l.DECIMAL="DECIMAL",l.FLOAT4="FLOAT4",l.REAL="REAL",l.FLOAT8="FLOAT8",l.DOUBLE="DOUBLE",l.VARCHAR="VARCHAR",l.CHAR="CHAR",l.BPCHAR="BPCHAR",l.TEXT="TEXT",l.STRING="STRING",l.NAME="NAME",l.TIMESTAMP="TIMESTAMP",l.TIMESTAMPTZ="TIMESTAMP WITH TIME ZONE",l.DATE="DATE",l.INTERVAL="INTERVAL",l.JSON="JSON",l.RECORD="RECORD",l.OID="OID",l.BYTEA="BYTEA",l.VARBINARY="VARBINARY",l.UUID="UUID",l.INET="INET",l.UNKNOWN="UNKNOWN";var y=t(46140);(i=g||(g={})).Home="/home",i.HomeDashboard="/home/<USER>",i.Thread="/home/<USER>",i.Modeling="/modeling",i.Onboarding="/setup",i.OnboardingConnection="/setup/connection",i.OnboardingModels="/setup/models",i.OnboardingRelationships="/setup/relationships",i.Knowledge="/knowledge",i.KnowledgeQuestionSQLPairs="/knowledge/question-sql-pairs",i.KnowledgeInstructions="/knowledge/instructions",i.APIManagement="/api-management",i.APIManagementHistory="/api-management/history",(s=S||(S={})).MANY="many",s.ONE="one",(o=T||(T={})).STEP="step",o.SMOOTHSTEP="smoothstep",o.BEZIER="bezier",o.MODEL="model",o.METRIC="metric",(d=D||(D={})).NONE="none",d.VIEW_SQL="view_sql",d.PREVIEW_DATA="preview_data",(u=A||(A={}))[u.IDLE=0]="IDLE",u[u.UNDERSTANDING=1]="UNDERSTANDING",u[u.SEARCHING=2]="SEARCHING",u[u.PLANNING=3]="PLANNING",u[u.GENERATING=4]="GENERATING",u[u.CORRECTING=5]="CORRECTING",u[u.FINISHED=6]="FINISHED",u[u.FAILED=7]="FAILED",u[u.STOPPED=8]="STOPPED",u[u.NO_RESULT=9]="NO_RESULT",(c=C||(C={})).ANSWER="answer",c.VIEW_SQL="view-sql",c.CHART="chart",(m=_||(_={})).DATA_SOURCE="DATA_SOURCE",m.PROJECT="PROJECT",(E=x||(x={})).EDIT="edit",E.DELETE="delete",E.UPDATE_COLUMNS="update_columns",E.CACHE_SETTINGS="cache_settings",E.REFRESH="refresh",E.HIDE_CATEGORY="hide_category",E.VIEW_SQL_PAIR="view_sql_pair",E.VIEW_INSTRUCTION="view_instruction",E.ADJUST_SQL="adjust_sql",E.ADJUST_STEPS="adjust_steps",(f=O||(O={})).QUESTION_SQL_PAIRS="question-sql-pairs",f.INSTRUCTIONS="instructions",f.API_HISTORY="api-history",f.API_REFERENCE="api-reference"},59530:function(e,n,t){t.d(n,{q:function(){return r}});let r={CONNECTION:{DISPLAY_NAME:{REQUIRED:"Please input display name."},PROJECT_ID:{REQUIRED:"Please input project id."},DATASET_ID:{REQUIRED:"Please input dataset ID."},CREDENTIAL:{REQUIRED:"Please upload credential."},INIT_SQL:{REQUIRED:"Please input initial SQL statements."},CONFIGURATION:{KEY:{REQUIRED:"Please input configuration key."},VALUE:{REQUIRED:"Please input configuration value."}},HOST:{REQUIRED:"Please input host.",INVALID:"Invalid host. Use 'host.docker.internal' on macOS/Windows to connect to the local database."},PORT:{REQUIRED:"Please input port."},USERNAME:{REQUIRED:"Please input username."},PASSWORD:{REQUIRED:"Please input password."},DATABASE:{REQUIRED:"Please input database name."},SCHEMA:{REQUIRED:"Please input schema name."},SCHEMAS:{REQUIRED:"Please input list of catalog.schema separated by comma."},ACCOUNT:{REQUIRED:"Please input account."}},ADD_RELATION:{FROM_FIELD:{REQUIRED:"Please select a field."},TO_FIELD:{REQUIRED:"Please select a field."},RELATION_TYPE:{REQUIRED:"Please select a relationship type."},RELATIONSHIP:{EXIST:"This relationship already exists."}},SETUP_MODEL:{TABLE:{REQUIRED:"Please select at least one table."}},SAVE_AS_VIEW:{NAME:{REQUIRED:"Please input view name."}},MODELING_CREATE_MODEL:{TABLE:{REQUIRED:"Please select a table."},COLUMNS:{REQUIRED:"Please select at least one column."},PRIMARY_KEY:{INVALID:"Please select again, the primary key must be one of the selected columns."}},CALCULATED_FIELD:{NAME:{REQUIRED:"Please input field name."},EXPRESSION:{REQUIRED:"Please select an expression."},LINEAGE:{REQUIRED:"Please select a field.",INVALID_STRING_TYPE:"Please select a string type field.",INVALID_NUMBER_TYPE:"Please select a number type field."}},SQL_PAIR:{SQL:{REQUIRED:"Please input SQL statement."},QUESTION:{REQUIRED:"Please input a matching question.",MAX_LENGTH:"Question must be 300 characters or fewer."}},INSTRUCTION:{DETAILS:{REQUIRED:"Please input an instruction details."},QUESTIONS:{REQUIRED:"Please input a matching question."},IS_DEFAULT_GLOBAL:{REQUIRED:"Please select how to apply this instruction."}},FIX_SQL:{SQL:{REQUIRED:"Please input SQL statement."}},ADJUST_REASONING:{SELECTED_MODELS:{REQUIRED:"Please select at least one model"},STEPS:{REQUIRED:"Please input reasoning steps",MAX_LENGTH:"Reasoning steps must be 3000 characters or fewer."}},IMPORT_DATA_SOURCE_SQL:{SQL:{REQUIRED:"Please input SQL statement."}},CRON:{REQUIRED:"Please input cron expression.",INVALID:"Invalid cron expression."},CACHE_SETTINGS:{DAY:{REQUIRED:"Please select day."},TIME:{REQUIRED:"Please select time."}}}},95462:function(e,n,t){t.d(n,{lV:function(){return i},pQ:function(){return l},yA:function(){return a}});var r=t(46140);let a=[r.i_.AVG,r.i_.COUNT,r.i_.MAX,r.i_.MIN,r.i_.SUM],l=[r.i_.ABS,r.i_.CBRT,r.i_.CEIL,r.i_.EXP,r.i_.FLOOR,r.i_.LN,r.i_.LOG10,r.i_.ROUND,r.i_.SIGN],i=[r.i_.LENGTH,r.i_.REVERSE]},39616:function(e,n,t){t.d(n,{Bk:function(){return J},D7:function(){return et},Du:function(){return $},ET:function(){return er},EU:function(){return Y},F$:function(){return en},NA:function(){return K},ON:function(){return ee},Qu:function(){return G},Rh:function(){return q},Se:function(){return W},T9:function(){return ea},VL:function(){return Z},Vu:function(){return B},Ye:function(){return Q},ZA:function(){return V},cJ:function(){return H},km:function(){return z},nX:function(){return X},r$:function(){return el},r3:function(){return F},tT:function(){return k}});var r=t(19521),a=t(8611),l=t(34005),i=t(54368),s=t(54283),o=t(95612),d=t(39575),u=t(53485),c=t(52330),m=t(3265),E=t(7169),f=t(5064),I=t(66642),N=t(19288),h=t(65038),p=t(10527),R=t(63611),g=t(33667),S=t(16708),T=t(13072),D=t(30884),A=t.n(D),C=t(18113),_=t.n(C),x=t(81730),O=t.n(x),b=t(10677),y=t.n(b),L=t(88253),P=t(68811),j=t(98509),v=t.n(j),U=t(83204),M=t(88061),w=t(44498);let F=(0,r.ZP)(E.v).withConfig({displayName:"icons__NumericIcon",componentId:"sc-1b106fcf-0"})(["height:1em;"]),Q=(0,r.ZP)(f.d).withConfig({displayName:"icons__TickIcon",componentId:"sc-1b106fcf-1"})(["height:1em;"]),q=(0,r.ZP)(I.P).withConfig({displayName:"icons__StringIcon",componentId:"sc-1b106fcf-2"})(["height:1em;"]),Z=(0,r.ZP)(l.x).withConfig({displayName:"icons__TextIcon",componentId:"sc-1b106fcf-3"})(["height:1em;"]),G=(0,r.ZP)(i.f).withConfig({displayName:"icons__CalendarIcon",componentId:"sc-1b106fcf-4"})(["height:1em;"]),B=(0,r.ZP)(s.s).withConfig({displayName:"icons__IdIcon",componentId:"sc-1b106fcf-5"})(["height:1em;"]),k=(0,r.ZP)(c.z).withConfig({displayName:"icons__JsonBracesIcon",componentId:"sc-1b106fcf-6"})(["height:1em;"]),H=(0,r.ZP)(A()).withConfig({displayName:"icons__BinaryIcon",componentId:"sc-1b106fcf-7"})(["height:1em;"]),W=(0,r.ZP)(a.o).withConfig({displayName:"icons__ColumnsIcon",componentId:"sc-1b106fcf-8"})(["height:1em;"]);(0,r.ZP)(o.Z).withConfig({displayName:"icons__InfoIcon",componentId:"sc-1b106fcf-9"})(["height:1em;"]);let Y=(0,r.ZP)(N.s).withConfig({displayName:"icons__PrimaryKeyIcon",componentId:"sc-1b106fcf-10"})(["height:1em;"]),V=(0,r.ZP)(d.X).withConfig({displayName:"icons__ModelIcon",componentId:"sc-1b106fcf-11"})(["height:1em;"]);(0,r.ZP)(h.k).withConfig({displayName:"icons__FocusIcon",componentId:"sc-1b106fcf-12"})(["height:1em;"]),(0,r.ZP)(m.Y).withConfig({displayName:"icons__MapIcon",componentId:"sc-1b106fcf-13"})(["height:1em;"]);let K=(0,r.ZP)(O()).withConfig({displayName:"icons__RelationshipIcon",componentId:"sc-1b106fcf-14"})(["height:1em;"]);(0,r.ZP)(_()).withConfig({displayName:"icons__MonitorIcon",componentId:"sc-1b106fcf-15"})(["height:1em;"]);let $=(0,r.ZP)(p.h).withConfig({displayName:"icons__RefreshIcon",componentId:"sc-1b106fcf-16"})([""]),z=(0,r.ZP)(u.w).withConfig({displayName:"icons__MetricIcon",componentId:"sc-1b106fcf-17"})(["height:1em;"]);(0,r.ZP)(y()).withConfig({displayName:"icons__ShareIcon",componentId:"sc-1b106fcf-18"})([""]);let J=(0,r.ZP)(L.t).withConfig({displayName:"icons__LightningIcon",componentId:"sc-1b106fcf-19"})(["height:1em;"]),X=(0,r.ZP)(v()).withConfig({displayName:"icons__MoreIcon",componentId:"sc-1b106fcf-20"})([""]),ee=(0,r.ZP)(R.z).withConfig({displayName:"icons__ViewIcon",componentId:"sc-1b106fcf-21"})(["height:1em;"]);(0,r.ZP)(g.r).withConfig({displayName:"icons__ExploreIcon",componentId:"sc-1b106fcf-22"})(["height:1em;"]),(0,r.ZP)(U.P).withConfig({displayName:"icons__SparklesIcon",componentId:"sc-1b106fcf-23"})(["height:1em;"]);let en=(0,r.ZP)(P.$).withConfig({displayName:"icons__BinocularsIcon",componentId:"sc-1b106fcf-24"})(["height:16px;width:14px;"]),et=(0,r.ZP)(M.O).withConfig({displayName:"icons__DiscordIcon",componentId:"sc-1b106fcf-25"})(["height:1em;"]),er=(0,r.ZP)(w.E).withConfig({displayName:"icons__GithubIcon",componentId:"sc-1b106fcf-26"})(["height:1em;"]),ea=(0,r.ZP)(S.v).withConfig({displayName:"icons__TranslateIcon",componentId:"sc-1b106fcf-27"})(["height:1em;"]),el=(0,r.ZP)(T._).withConfig({displayName:"icons__OpenInNewIcon",componentId:"sc-1b106fcf-28"})(["height:1em;"])},77840:function(e,n,t){t.d(n,{x:function(){return a}});var r=t(85893);let a=e=>n=>{let{data:t,keyIndex:a="key",...l}=n,i=t.map((n,i)=>{let s="function"==typeof a?a(n):n[a];return(0,r.jsx)(e,{data:t,index:i,...l,...n},"".concat(i,"-").concat(s))});return(0,r.jsx)(r.Fragment,{children:i})}},32235:function(e,n,t){t.d(n,{gZ:function(){return d},r7:function(){return o},rv:function(){return i},kN:function(){return u},xq:function(){return I},kq:function(){return N},bI:function(){return p},qd:function(){return c},x7:function(){return g},KT:function(){return h},gY:function(){return R}});var r=t(84908),a=t(59530),l=t(95462);let i=e=>async(n,t)=>{var r;if(!t)return Promise.reject(a.q.CALCULATED_FIELD.NAME.REQUIRED);let l=await e(t),{valid:i,message:s}=null==l?void 0:null===(r=l.data)||void 0===r?void 0:r.validateCalculatedField;return i?Promise.resolve():Promise.reject(s)},s=(e,n)=>(t,a)=>!([r.QZ.FIELD,r.QZ.CALCULATED_FIELD].includes(a.nodeType)&&e.includes(t))||n.includes(a.type.toLocaleUpperCase()),o=s(l.lV,[r.BA.VARCHAR,r.BA.CHAR,r.BA.BPCHAR,r.BA.TEXT,r.BA.STRING,r.BA.NAME]),d=s(l.pQ,[r.BA.TINYINT,r.BA.INT2,r.BA.SMALLINT,r.BA.INT4,r.BA.INTEGER,r.BA.INT8,r.BA.BIGINT,r.BA.INT64,r.BA.NUMERIC,r.BA.DECIMAL,r.BA.FLOAT4,r.BA.REAL,r.BA.FLOAT8,r.BA.DOUBLE]),u=e=>(n,t)=>{if(!t)return Promise.reject(Error(a.q.CALCULATED_FIELD.LINEAGE.REQUIRED));let l=t[t.length-1];return[r.QZ.FIELD,r.QZ.CALCULATED_FIELD].includes(l.nodeType)?o(e,l)?d(e,l)?Promise.resolve():Promise.reject(Error(a.q.CALCULATED_FIELD.LINEAGE.INVALID_NUMBER_TYPE)):Promise.reject(Error(a.q.CALCULATED_FIELD.LINEAGE.INVALID_STRING_TYPE)):Promise.reject(Error(a.q.CALCULATED_FIELD.LINEAGE.REQUIRED))},c=e=>async(n,t)=>{var r;if(!t)return Promise.reject(a.q.SAVE_AS_VIEW.NAME.REQUIRED);let l=await e({variables:{data:{name:t}}}),{valid:i,message:s}=null==l?void 0:null===(r=l.data)||void 0===r?void 0:r.validateView;return i?Promise.resolve():Promise.reject(s)};var m=t(45403),E=t(43053);let f=(e,n)=>!!e[n.fromField.modelName].find(e=>e.fromField.modelId===n.fromField.modelId&&e.fromField.fieldId===n.fromField.fieldId&&e.toField.modelId===n.toField.modelId&&e.toField.fieldId===n.toField.fieldId)||!!e[n.toField.modelName].find(e=>e.fromField.modelId===n.toField.modelId&&e.fromField.fieldId===n.toField.fieldId&&e.toField.modelId===n.fromField.modelId&&e.toField.fieldId===n.fromField.fieldId),I=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return async(r,l)=>{if(!l||!l.field)return Promise.reject(a.q.ADD_RELATION.FROM_FIELD.REQUIRED);if(!e){let e=t(E.J.TO_FIELD);if(e&&e.model&&e.field&&f(n,(0,m.T9)({fromField:l,toField:e})))return Promise.reject(a.q.ADD_RELATION.RELATIONSHIP.EXIST)}return Promise.resolve()}},N=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return async(r,l)=>{if(!l||!l.field)return Promise.reject(a.q.ADD_RELATION.TO_FIELD.REQUIRED);if(!e){let e=t(E.J.FROM_FIELD);if(e&&e.model&&e.field&&f(n,(0,m.T9)({fromField:e,toField:l})))return Promise.reject(a.q.ADD_RELATION.RELATIONSHIP.EXIST)}return Promise.resolve()}},h=(e,n)=>n?["localhost","127.0.0.1"].includes(n)?Promise.reject(a.q.CONNECTION.HOST.INVALID):Promise.resolve():Promise.reject(a.q.CONNECTION.HOST.REQUIRED),p=e=>async(n,t)=>t&&""!==t.trim()?t.length>300?Promise.reject(e.MAX_LENGTH):Promise.resolve():Promise.reject(e.REQUIRED),R=e=>(null==e?void 0:e.trim().split(" ").length)===5,g=(e,n)=>n?R(n)?Promise.resolve():Promise.reject(a.q.CRON.INVALID):Promise.reject(a.q.CRON.REQUIRED)}}]);