"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[241],{36851:function(e,t,n){n.d(t,{AC:function(){return I},HH:function(){return eU},Ly:function(){return h},OW:function(){return eg},RX:function(){return e_},Rr:function(){return nF},VP:function(){return eP},XQ:function(){return nT},_K:function(){return tS},ll:function(){return nZ},oI:function(){return V},oR:function(){return A},s_:function(){return _},tV:function(){return nI},u5:function(){return en},x$:function(){return nz}});var o,r,a,l,i,s,d,c,u,g,p,h,m=n(67294),f=n(83840),y=n(52464),x=n(76248),S=n(37882),b=n(23838),E=n(46939),w=n(62487),v=n(73935);let M=(0,m.createContext)(null),N=M.Provider,C={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},k=C.error001();function A(e,t){let n=(0,m.useContext)(M);if(null===n)throw Error(k);return(0,y.s)(n,e,t)}let I=()=>{let e=(0,m.useContext)(M);if(null===e)throw Error(k);return(0,m.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},P=e=>e.userSelectionActive?"none":"all";function _({position:e,children:t,className:n,style:o,...r}){let a=A(P),l=`${e}`.split("-");return m.createElement("div",{className:(0,f.Z)(["react-flow__panel",n,...l]),style:{...o,pointerEvents:a},...r},t)}function $({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:m.createElement(_,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},m.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var R=(0,m.memo)(({x:e,y:t,label:n,labelStyle:o={},labelShowBg:r=!0,labelBgStyle:a={},labelBgPadding:l=[2,4],labelBgBorderRadius:i=2,children:s,className:d,...c})=>{let u=(0,m.useRef)(null),[g,p]=(0,m.useState)({x:0,y:0,width:0,height:0}),h=(0,f.Z)(["react-flow__edge-textwrapper",d]);return((0,m.useEffect)(()=>{if(u.current){let e=u.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?m.createElement("g",{transform:`translate(${e-g.width/2} ${t-g.height/2})`,className:h,visibility:g.width?"visible":"hidden",...c},r&&m.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:a,rx:i,ry:i}),m.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:u,style:o},n),s):null});let O=e=>({width:e.offsetWidth,height:e.offsetHeight}),D=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),B=(e={x:0,y:0},t)=>({x:D(e.x,t[0][0],t[1][0]),y:D(e.y,t[0][1],t[1][1])}),z=(e,t,n)=>e<t?D(Math.abs(e-t),1,50)/50:e>n?-D(Math.abs(e-n),1,50)/50:0,L=(e,t)=>[20*z(e.x,35,t.width-35),20*z(e.y,35,t.height-35)],T=e=>e.getRootNode?.()||window?.document,H=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),F=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Z=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),X=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),V=(e,t)=>Z(H(F(e),F(t))),Y=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),K=e=>U(e.width)&&U(e.height)&&U(e.x)&&U(e.y),U=e=>!isNaN(e)&&isFinite(e),W=Symbol.for("internals"),j=["Enter"," ","Escape"],q=(e,t)=>{},G=e=>"nativeEvent"in e;function Q(e){let t=G(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let J=e=>"clientX"in e,ee=(e,t)=>{let n=J(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},et=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,en=({id:e,path:t,labelX:n,labelY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:p=20})=>m.createElement(m.Fragment,null,m.createElement("path",{id:e,style:c,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:u,markerStart:g}),p&&m.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:p,className:"react-flow__edge-interaction"}),r&&U(n)&&U(o)?m.createElement(R,{x:n,y:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d}):null);function eo(e,t,n){return void 0===n?n:o=>{let r=t().edges.find(t=>t.id===e);r&&n(o,{...r})}}function er({sourceX:e,sourceY:t,targetX:n,targetY:o}){let r=Math.abs(n-e)/2,a=Math.abs(o-t)/2;return[n<e?n+r:n-r,o<t?o+a:o-a,r,a]}function ea({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:a,targetControlX:l,targetControlY:i}){let s=.125*e+.375*r+.375*l+.125*n,d=.125*t+.375*a+.375*i+.125*o;return[s,d,Math.abs(s-e),Math.abs(d-t)]}function el({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===h.Left||e===h.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function ei({sourceX:e,sourceY:t,sourcePosition:n=h.Bottom,targetX:o,targetY:r,targetPosition:a=h.Top}){let[l,i]=el({pos:n,x1:e,y1:t,x2:o,y2:r}),[s,d]=el({pos:a,x1:o,y1:r,x2:e,y2:t}),[c,u,g,p]=ea({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:l,sourceControlY:i,targetControlX:s,targetControlY:d});return[`M${e},${t} C${l},${i} ${s},${d} ${o},${r}`,c,u,g,p]}en.displayName="BaseEdge",(o=d||(d={})).Strict="strict",o.Loose="loose",(r=c||(c={})).Free="free",r.Vertical="vertical",r.Horizontal="horizontal",(a=u||(u={})).Partial="partial",a.Full="full",(l=g||(g={})).Bezier="default",l.Straight="straight",l.Step="step",l.SmoothStep="smoothstep",l.SimpleBezier="simplebezier",(i=p||(p={})).Arrow="arrow",i.ArrowClosed="arrowclosed",(s=h||(h={})).Left="left",s.Top="top",s.Right="right",s.Bottom="bottom";let es=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=h.Bottom,targetPosition:a=h.Top,label:l,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:p,markerStart:f,interactionWidth:y})=>{let[x,S,b]=ei({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:a});return m.createElement(en,{path:x,labelX:S,labelY:b,label:l,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:p,markerStart:f,interactionWidth:y})});es.displayName="SimpleBezierEdge";let ed={[h.Left]:{x:-1,y:0},[h.Right]:{x:1,y:0},[h.Top]:{x:0,y:-1},[h.Bottom]:{x:0,y:1}},ec=({source:e,sourcePosition:t=h.Bottom,target:n})=>t===h.Left||t===h.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},eu=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function eg({sourceX:e,sourceY:t,sourcePosition:n=h.Bottom,targetX:o,targetY:r,targetPosition:a=h.Top,borderRadius:l=5,centerX:i,centerY:s,offset:d=20}){let[c,u,g,p,m]=function({source:e,sourcePosition:t=h.Bottom,target:n,targetPosition:o=h.Top,center:r,offset:a}){let l,i;let s=ed[t],d=ed[o],c={x:e.x+s.x*a,y:e.y+s.y*a},u={x:n.x+d.x*a,y:n.y+d.y*a},g=ec({source:c,sourcePosition:t,target:u}),p=0!==g.x?"x":"y",m=g[p],f=[],y={x:0,y:0},x={x:0,y:0},[S,b,E,w]=er({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(s[p]*d[p]==-1){l=r.x||S,i=r.y||b;let e=[{x:l,y:c.y},{x:l,y:u.y}],t=[{x:c.x,y:i},{x:u.x,y:i}];f=s[p]===m?"x"===p?e:t:"x"===p?t:e}else{let r=[{x:c.x,y:u.y}],g=[{x:u.x,y:c.y}];if(f="x"===p?s.x===m?g:r:s.y===m?r:g,t===o){let t=Math.abs(e[p]-n[p]);if(t<=a){let o=Math.min(a-1,a-t);s[p]===m?y[p]=(c[p]>e[p]?-1:1)*o:x[p]=(u[p]>n[p]?-1:1)*o}}if(t!==o){let e="x"===p?"y":"x",t=s[p]===d[e],n=c[e]>u[e],o=c[e]<u[e];(1===s[p]&&(!t&&n||t&&o)||1!==s[p]&&(!t&&o||t&&n))&&(f="x"===p?r:g)}let h={x:c.x+y.x,y:c.y+y.y},S={x:u.x+x.x,y:u.y+x.y};Math.max(Math.abs(h.x-f[0].x),Math.abs(S.x-f[0].x))>=Math.max(Math.abs(h.y-f[0].y),Math.abs(S.y-f[0].y))?(l=(h.x+S.x)/2,i=f[0].y):(l=f[0].x,i=(h.y+S.y)/2)}return[[e,{x:c.x+y.x,y:c.y+y.y},...f,{x:u.x+x.x,y:u.y+x.y},n],l,i,E,w]}({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:a,center:{x:i,y:s},offset:d});return[c.reduce((e,t,n)=>e+(n>0&&n<c.length-1?function(e,t,n,o){let r=Math.min(eu(e,t)/2,eu(t,n)/2,o),{x:a,y:l}=t;if(e.x===a&&a===n.x||e.y===l&&l===n.y)return`L${a} ${l}`;if(e.y===l){let t=e.x<n.x?-1:1,o=e.y<n.y?1:-1;return`L ${a+r*t},${l}Q ${a},${l} ${a},${l+r*o}`}let i=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${a},${l+r*s}Q ${a},${l} ${a+r*i},${l}`}(c[n-1],t,c[n+1],l):`${0===n?"M":"L"}${t.x} ${t.y}`),""),u,g,p,m]}let ep=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,sourcePosition:u=h.Bottom,targetPosition:g=h.Top,markerEnd:p,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[S,b,E]=eg({sourceX:e,sourceY:t,sourcePosition:u,targetX:n,targetY:o,targetPosition:g,borderRadius:y?.borderRadius,offset:y?.offset});return m.createElement(en,{path:S,labelX:b,labelY:E,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:p,markerStart:f,interactionWidth:x})});ep.displayName="SmoothStepEdge";let eh=(0,m.memo)(e=>m.createElement(ep,{...e,pathOptions:(0,m.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));eh.displayName="StepEdge";let em=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:p})=>{let[h,f,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){let[r,a,l,i]=er({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,a,l,i]}({sourceX:e,sourceY:t,targetX:n,targetY:o});return m.createElement(en,{path:h,labelX:f,labelY:y,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:p})});function ef(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function ey({pos:e,x1:t,y1:n,x2:o,y2:r,c:a}){switch(e){case h.Left:return[t-ef(t-o,a),n];case h.Right:return[t+ef(o-t,a),n];case h.Top:return[t,n-ef(n-r,a)];case h.Bottom:return[t,n+ef(r-n,a)]}}function ex({sourceX:e,sourceY:t,sourcePosition:n=h.Bottom,targetX:o,targetY:r,targetPosition:a=h.Top,curvature:l=.25}){let[i,s]=ey({pos:n,x1:e,y1:t,x2:o,y2:r,c:l}),[d,c]=ey({pos:a,x1:o,y1:r,x2:e,y2:t,c:l}),[u,g,p,m]=ea({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:i,sourceControlY:s,targetControlX:d,targetControlY:c});return[`M${e},${t} C${i},${s} ${d},${c} ${o},${r}`,u,g,p,m]}em.displayName="StraightEdge";let eS=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=h.Bottom,targetPosition:a=h.Top,label:l,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:p,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[S,b,E]=ex({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:a,curvature:y?.curvature});return m.createElement(en,{path:S,labelX:b,labelY:E,label:l,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:p,markerStart:f,interactionWidth:x})});eS.displayName="BezierEdge";let eb=(0,m.createContext)(null),eE=eb.Provider;eb.Consumer;let ew=()=>(0,m.useContext)(eb),ev=e=>"id"in e&&"source"in e&&"target"in e,eM=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,eN=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},eC=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),ek=(e,t)=>{let n;return e.source&&e.target?eC(n=ev(e)?{...e}:{...e,id:eM(e)},t)?t:t.concat(n):(q("006",C.error006()),t)},eA=({x:e,y:t},[n,o,r],a,[l,i])=>{let s={x:(e-n)/r,y:(t-o)/r};return a?{x:l*Math.round(s.x/l),y:i*Math.round(s.y/i)}:s},eI=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),eP=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},e_=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:Z(e.reduce((e,n)=>{let{x:o,y:r}=eP(n,t).positionAbsolute;return H(e,F({x:o,y:r,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),e$=(e,t,[n,o,r]=[0,0,1],a=!1,l=!1,i=[0,0])=>{let s={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},d=[];return e.forEach(e=>{let{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(l&&!o||r)return!1;let{positionAbsolute:c}=eP(e,i),u=Y(s,{x:c.x,y:c.y,width:t||0,height:n||0}),g=void 0===t||void 0===n||null===t||null===n,p=(t||0)*(n||0);(g||a&&u>0||u>=p||e.dragging)&&d.push(e)}),d},eR=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},eO=(e,t,n,o,r,a=.1)=>{let l=D(Math.min(t/(e.width*(1+a)),n/(e.height*(1+a))),o,r);return{x:t/2-(e.x+e.width/2)*l,y:n/2-(e.y+e.height/2)*l,zoom:l}},eD=(e,t=0)=>e.transition().duration(t);function eB(e,t,n,o){return(t[n]||[]).reduce((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t),[])}let ez={source:null,target:null,sourceHandle:null,targetHandle:null},eL=()=>({handleDomNode:null,isValid:!1,connection:ez,endHandle:null});function eT(e,t,n,o,r,a,l){let i="target"===r,s=l.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...eL(),handleDomNode:s};if(s){let e=eH(void 0,s),r=s.getAttribute("data-nodeid"),l=s.getAttribute("data-handleid"),u=s.classList.contains("connectable"),g=s.classList.contains("connectableend"),p={source:i?r:n,sourceHandle:i?l:o,target:i?n:r,targetHandle:i?o:l};c.connection=p,u&&g&&(t===d.Strict?i&&"source"===e||!i&&"target"===e:r!==n||l!==o)&&(c.endHandle={nodeId:r,handleId:l,type:e},c.isValid=a(p))}return c}function eH(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function eF(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function eZ({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:a,setState:l,isValidConnection:i,edgeUpdaterType:s,onEdgeUpdateEnd:d}){let c,u;let g=T(e.target),{connectionMode:p,domNode:h,autoPanOnConnect:m,connectionRadius:f,onConnectStart:y,panBy:x,getNodes:S,cancelConnection:b}=a(),E=0,{x:w,y:v}=ee(e),M=eH(s,g?.elementFromPoint(w,v)),N=h?.getBoundingClientRect();if(!N||!M)return;let C=ee(e,N),k=!1,A=null,I=!1,P=null,_=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce((e,r)=>{if(r[W]){let{handleBounds:a}=r[W],l=[],i=[];a&&(l=eB(r,a,"source",`${t}-${n}-${o}`),i=eB(r,a,"target",`${t}-${n}-${o}`)),e.push(...l,...i)}return e},[])}({nodes:S(),nodeId:n,handleId:t,handleType:M}),$=()=>{if(!m)return;let[e,t]=L(C,N);x({x:e,y:t}),E=requestAnimationFrame($)};function R(e){var o,s;let d;let{transform:h}=a();C=ee(e,N);let{handle:m,validHandleResult:y}=function(e,t,n,o,r,a){let{x:l,y:i}=ee(e),s=t.elementsFromPoint(l,i).find(e=>e.classList.contains("react-flow__handle"));if(s){let e=s.getAttribute("data-nodeid");if(e){let t=eH(void 0,s),o=s.getAttribute("data-handleid"),l=a({nodeId:e,id:o,type:t});if(l){let a=r.find(n=>n.nodeId===e&&n.type===t&&n.id===o);return{handle:{id:o,type:t,nodeId:e,x:a?.x||n.x,y:a?.y||n.y},validHandleResult:l}}}}let d=[],c=1/0;if(r.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){let n=a(e);t<=c&&(t<c?d=[{handle:e,validHandleResult:n}]:t===c&&d.push({handle:e,validHandleResult:n}),c=t)}}),!d.length)return{handle:null,validHandleResult:eL()};if(1===d.length)return d[0];let u=d.some(({validHandleResult:e})=>e.isValid),g=d.some(({handle:e})=>"target"===e.type);return d.find(({handle:e,validHandleResult:t})=>g?"target"===e.type:!u||t.isValid)||d[0]}(e,g,eA(C,h,!1,[1,1]),f,_,e=>eT(e,p,n,t,r?"target":"source",i,g));if(c=m,k||($(),k=!0),P=y.handleDomNode,A=y.connection,I=y.isValid,l({connectionPosition:c&&I?eI({x:c.x,y:c.y},h):C,connectionStatus:(o=!!c,d=null,(s=I)?d="valid":o&&!s&&(d="invalid"),d),connectionEndHandle:y.endHandle}),!c&&!I&&!P)return eF(u);A.source!==A.target&&P&&(eF(u),u=P,P.classList.add("connecting","react-flow__handle-connecting"),P.classList.toggle("valid",I),P.classList.toggle("react-flow__handle-valid",I))}function O(e){(c||P)&&A&&I&&o?.(A),a().onConnectEnd?.(e),s&&d?.(e),eF(u),b(),cancelAnimationFrame(E),k=!1,I=!1,A=null,P=null,g.removeEventListener("mousemove",R),g.removeEventListener("mouseup",O),g.removeEventListener("touchmove",R),g.removeEventListener("touchend",O)}l({connectionPosition:C,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:M,connectionStartHandle:{nodeId:n,handleId:t,type:M},connectionEndHandle:null}),y?.(e,{nodeId:n,handleId:t,handleType:M}),g.addEventListener("mousemove",R),g.addEventListener("mouseup",O),g.addEventListener("touchmove",R),g.addEventListener("touchend",O)}let eX=()=>!0,eV=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),eY=(e,t,n)=>o=>{let{connectionStartHandle:r,connectionEndHandle:a,connectionClickStartHandle:l}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||a?.nodeId===e&&a?.handleId===t&&a?.type===n,clickConnecting:l?.nodeId===e&&l?.handleId===t&&l?.type===n}},eK=(0,m.forwardRef)(({type:e="source",position:t=h.Top,isValidConnection:n,isConnectable:o=!0,isConnectableStart:r=!0,isConnectableEnd:a=!0,id:l,onConnect:i,children:s,className:d,onMouseDown:c,onTouchStart:u,...g},p)=>{let y=l||null,S="target"===e,b=I(),E=ew(),{connectOnClick:w,noPanClassName:v}=A(eV,x.X),{connecting:M,clickConnecting:N}=A(eY(E,y,e),x.X);E||b.getState().onError?.("010",C.error010());let k=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=b.getState(),r={...t,...e};if(o){let{edges:e,setEdges:t}=b.getState();t(ek(r,e))}n?.(r),i?.(r)},P=e=>{if(!E)return;let t=J(e);r&&(t&&0===e.button||!t)&&eZ({event:e,handleId:y,nodeId:E,onConnect:k,isTarget:S,getState:b.getState,setState:b.setState,isValidConnection:n||b.getState().isValidConnection||eX}),t?c?.(e):u?.(e)};return m.createElement("div",{"data-handleid":y,"data-nodeid":E,"data-handlepos":t,"data-id":`${E}-${y}-${e}`,className:(0,f.Z)(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",v,d,{source:!S,target:S,connectable:o,connectablestart:r,connectableend:a,connecting:N,connectionindicator:o&&(r&&!M||a&&M)}]),onMouseDown:P,onTouchStart:P,onClick:w?t=>{let{onClickConnectStart:o,onClickConnectEnd:a,connectionClickStartHandle:l,connectionMode:i,isValidConnection:s}=b.getState();if(!E||!l&&!r)return;if(!l){o?.(t,{nodeId:E,handleId:y,handleType:e}),b.setState({connectionClickStartHandle:{nodeId:E,type:e,handleId:y}});return}let d=T(t.target),c=n||s||eX,{connection:u,isValid:g}=eT({nodeId:E,id:y,type:e},i,l.nodeId,l.handleId||null,l.type,c,d);g&&k(u),a?.(t),b.setState({connectionClickStartHandle:null})}:void 0,ref:p,...g},s)});eK.displayName="Handle";var eU=(0,m.memo)(eK);let eW=({data:e,isConnectable:t,targetPosition:n=h.Top,sourcePosition:o=h.Bottom})=>m.createElement(m.Fragment,null,m.createElement(eU,{type:"target",position:n,isConnectable:t}),e?.label,m.createElement(eU,{type:"source",position:o,isConnectable:t}));eW.displayName="DefaultNode";var ej=(0,m.memo)(eW);let eq=({data:e,isConnectable:t,sourcePosition:n=h.Bottom})=>m.createElement(m.Fragment,null,e?.label,m.createElement(eU,{type:"source",position:n,isConnectable:t}));eq.displayName="InputNode";var eG=(0,m.memo)(eq);let eQ=({data:e,isConnectable:t,targetPosition:n=h.Top})=>m.createElement(m.Fragment,null,m.createElement(eU,{type:"target",position:n,isConnectable:t}),e?.label);eQ.displayName="OutputNode";var eJ=(0,m.memo)(eQ);let e0=()=>null;e0.displayName="GroupNode";let e1=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected)}),e2=e=>e.id;function e5(e,t){return(0,x.X)(e.selectedNodes.map(e2),t.selectedNodes.map(e2))&&(0,x.X)(e.selectedEdges.map(e2),t.selectedEdges.map(e2))}let e3=(0,m.memo)(({onSelectionChange:e})=>{let t=I(),{selectedNodes:n,selectedEdges:o}=A(e1,e5);return(0,m.useEffect)(()=>{let r={nodes:n,edges:o};e?.(r),t.getState().onSelectionChange.forEach(e=>e(r))},[n,o,e]),null});e3.displayName="SelectionListener";let e4=e=>!!e.onSelectionChange;function e8({onSelectionChange:e}){let t=A(e4);return e||t?m.createElement(e3,{onSelectionChange:e}):null}let e7=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function e6(e,t){(0,m.useEffect)(()=>{void 0!==e&&t(e)},[e])}function e9(e,t,n){(0,m.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let te=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:r,onConnectStart:a,onConnectEnd:l,onClickConnectStart:i,onClickConnectEnd:s,nodesDraggable:d,nodesConnectable:c,nodesFocusable:u,edgesFocusable:g,edgesUpdatable:p,elevateNodesOnSelect:h,minZoom:f,maxZoom:y,nodeExtent:S,onNodesChange:b,onEdgesChange:E,elementsSelectable:w,connectionMode:v,snapGrid:M,snapToGrid:N,translateExtent:C,connectOnClick:k,defaultEdgeOptions:P,fitView:_,fitViewOptions:$,onNodesDelete:R,onEdgesDelete:O,onNodeDrag:D,onNodeDragStart:B,onNodeDragStop:z,onSelectionDrag:L,onSelectionDragStart:T,onSelectionDragStop:H,noPanClassName:F,nodeOrigin:Z,rfId:X,autoPanOnConnect:V,autoPanOnNodeDrag:Y,onError:K,connectionRadius:U,isValidConnection:W,nodeDragThreshold:j})=>{let{setNodes:q,setEdges:G,setDefaultNodesAndEdges:Q,setMinZoom:J,setMaxZoom:ee,setTranslateExtent:et,setNodeExtent:en,reset:eo}=A(e7,x.X),er=I();return(0,m.useEffect)(()=>(Q(n,o?.map(e=>({...e,...P}))),()=>{eo()}),[]),e9("defaultEdgeOptions",P,er.setState),e9("connectionMode",v,er.setState),e9("onConnect",r,er.setState),e9("onConnectStart",a,er.setState),e9("onConnectEnd",l,er.setState),e9("onClickConnectStart",i,er.setState),e9("onClickConnectEnd",s,er.setState),e9("nodesDraggable",d,er.setState),e9("nodesConnectable",c,er.setState),e9("nodesFocusable",u,er.setState),e9("edgesFocusable",g,er.setState),e9("edgesUpdatable",p,er.setState),e9("elementsSelectable",w,er.setState),e9("elevateNodesOnSelect",h,er.setState),e9("snapToGrid",N,er.setState),e9("snapGrid",M,er.setState),e9("onNodesChange",b,er.setState),e9("onEdgesChange",E,er.setState),e9("connectOnClick",k,er.setState),e9("fitViewOnInit",_,er.setState),e9("fitViewOnInitOptions",$,er.setState),e9("onNodesDelete",R,er.setState),e9("onEdgesDelete",O,er.setState),e9("onNodeDrag",D,er.setState),e9("onNodeDragStart",B,er.setState),e9("onNodeDragStop",z,er.setState),e9("onSelectionDrag",L,er.setState),e9("onSelectionDragStart",T,er.setState),e9("onSelectionDragStop",H,er.setState),e9("noPanClassName",F,er.setState),e9("nodeOrigin",Z,er.setState),e9("rfId",X,er.setState),e9("autoPanOnConnect",V,er.setState),e9("autoPanOnNodeDrag",Y,er.setState),e9("onError",K,er.setState),e9("connectionRadius",U,er.setState),e9("isValidConnection",W,er.setState),e9("nodeDragThreshold",j,er.setState),e6(e,q),e6(t,G),e6(f,J),e6(y,ee),e6(C,et),e6(S,en),null},tt={display:"none"},tn={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},to="react-flow__node-desc",tr="react-flow__edge-desc",ta=e=>e.ariaLiveMessage;function tl({rfId:e}){let t=A(ta);return m.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:tn},t)}function ti({rfId:e,disableKeyboardA11y:t}){return m.createElement(m.Fragment,null,m.createElement("div",{id:`${to}-${e}`,style:tt},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),m.createElement("div",{id:`${tr}-${e}`,style:tt},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&m.createElement(tl,{rfId:e}))}var ts=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,o]=(0,m.useState)(!1),r=(0,m.useRef)(!1),a=(0,m.useRef)(new Set([])),[l,i]=(0,m.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,m.useEffect)(()=>{let n=t?.target||("undefined"!=typeof document?document:null);if(null!==e){let e=e=>{if(r.current=e.ctrlKey||e.metaKey||e.shiftKey,(!r.current||r.current&&!t.actInsideInputWithModifier)&&Q(e))return!1;let n=tc(e.code,i);a.current.add(e[n]),td(l,a.current,!1)&&(e.preventDefault(),o(!0))},s=e=>{if((!r.current||r.current&&!t.actInsideInputWithModifier)&&Q(e))return!1;let n=tc(e.code,i);td(l,a.current,!0)?(o(!1),a.current.clear()):a.current.delete(e[n]),"Meta"===e.key&&a.current.clear(),r.current=!1},d=()=>{a.current.clear(),o(!1)};return n?.addEventListener("keydown",e),n?.addEventListener("keyup",s),window.addEventListener("blur",d),()=>{n?.removeEventListener("keydown",e),n?.removeEventListener("keyup",s),window.removeEventListener("blur",d)}}},[e,o]),n};function td(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function tc(e,t){return t.includes(e)?"code":"key"}function tu(e,t,n){e.forEach(o=>{if(o.parentNode&&!e.has(o.parentNode))throw Error(`Parent node ${o.parentNode} not found`);if(o.parentNode||n?.[o.id]){let{x:r,y:a,z:l}=function e(t,n,o,r){if(!t.parentNode)return o;let a=n.get(t.parentNode),l=eP(a,r);return e(a,n,{x:(o.x??0)+l.x,y:(o.y??0)+l.y,z:(a[W]?.z??0)>(o.z??0)?a[W]?.z??0:o.z??0},r)}(o,e,{...o.position,z:o[W]?.z??0},t);o.positionAbsolute={x:r,y:a},o[W].z=l,n?.[o.id]&&(o[W].isParent=!0)}})}function tg(e,t,n,o){let r=new Map,a={},l=o?1e3:0;return e.forEach(e=>{let n=(U(e.zIndex)?e.zIndex:0)+(e.selected?l:0),o=t.get(e.id),i={width:o?.width,height:o?.height,...e,positionAbsolute:{x:e.position.x,y:e.position.y}};e.parentNode&&(i.parentNode=e.parentNode,a[e.parentNode]=!0),Object.defineProperty(i,W,{enumerable:!1,value:{handleBounds:o?.[W]?.handleBounds,z:n}}),r.set(e.id,i)}),tu(r,n,a),r}function tp(e,t={}){let{getNodes:n,width:o,height:r,minZoom:a,maxZoom:l,d3Zoom:i,d3Selection:s,fitViewOnInitDone:d,fitViewOnInit:c,nodeOrigin:u}=e(),g=t.initial&&!d&&c;if(i&&s&&(g||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),d=e.every(e=>e.width&&e.height);if(e.length>0&&d){let{x:n,y:d,zoom:c}=eO(e_(e,u),o,r,t.minZoom??a,t.maxZoom??l,t.padding??.1),g=S.CR.translate(n,d).scale(c);return"number"==typeof t.duration&&t.duration>0?i.transform(eD(s,t.duration),g):i.transform(s,g),!0}}return!1}function th({changedNodes:e,changedEdges:t,get:n,set:o}){let{nodeInternals:r,edges:a,onNodesChange:l,onEdgesChange:i,hasDefaultNodes:s,hasDefaultEdges:d}=n();e?.length&&(s&&o({nodeInternals:(e.forEach(e=>{let t=r.get(e.id);t&&r.set(t.id,{...t,[W]:t[W],selected:e.selected})}),new Map(r))}),l?.(e)),t?.length&&(d&&o({edges:a.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),i?.(t))}let tm=()=>{},tf={zoomIn:tm,zoomOut:tm,zoomTo:tm,getZoom:()=>1,setViewport:tm,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:tm,fitBounds:tm,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},ty=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),tx=()=>{let e=I(),{d3Zoom:t,d3Selection:n}=A(ty,x.X);return(0,m.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(eD(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(eD(n,e?.duration),1/1.2),zoomTo:(e,o)=>t.scaleTo(eD(n,o?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(o,r)=>{let[a,l,i]=e.getState().transform,s=S.CR.translate(o.x??a,o.y??l).scale(o.zoom??i);t.transform(eD(n,r?.duration),s)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>tp(e.getState,t),setCenter:(o,r,a)=>{let{width:l,height:i,maxZoom:s}=e.getState(),d=void 0!==a?.zoom?a.zoom:s,c=l/2-o*d,u=i/2-r*d,g=S.CR.translate(c,u).scale(d);t.transform(eD(n,a?.duration),g)},fitBounds:(o,r)=>{let{width:a,height:l,minZoom:i,maxZoom:s}=e.getState(),{x:d,y:c,zoom:u}=eO(o,a,l,i,s,r?.padding??.1),g=S.CR.translate(d,c).scale(u);t.transform(eD(n,r?.duration),g)},project:t=>{let{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),eA(t,n,o,r)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:o,snapGrid:r,domNode:a}=e.getState();if(!a)return t;let{x:l,y:i}=a.getBoundingClientRect();return eA({x:t.x-l,y:t.y-i},n,o,r)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:r,y:a}=o.getBoundingClientRect(),l=eI(t,n);return{x:l.x+r,y:l.y+a}},viewportInitialized:!0}:tf,[t,n])};function tS(){let e=tx(),t=I(),n=(0,m.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),o=(0,m.useCallback)(e=>t.getState().nodeInternals.get(e),[]),r=(0,m.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),a=(0,m.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),l=(0,m.useCallback)(e=>{let{getNodes:n,setNodes:o,hasDefaultNodes:r,onNodesChange:a}=t.getState(),l=n(),i="function"==typeof e?e(l):e;r?o(i):a&&a(0===i.length?l.map(e=>({type:"remove",id:e.id})):i.map(e=>({item:e,type:"reset"})))},[]),i=(0,m.useCallback)(e=>{let{edges:n=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:a}=t.getState(),l="function"==typeof e?e(n):e;r?o(l):a&&a(0===l.length?n.map(e=>({type:"remove",id:e.id})):l.map(e=>({item:e,type:"reset"})))},[]),s=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:a,onNodesChange:l}=t.getState();a?r([...o(),...n]):l&&l(n.map(e=>({item:e,type:"add"})))},[]),d=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:a,onEdgesChange:l}=t.getState();a?r([...o,...n]):l&&l(n.map(e=>({item:e,type:"add"})))},[]),c=(0,m.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:o}=t.getState(),[r,a,l]=o;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:r,y:a,zoom:l}}},[]),u=(0,m.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:o,getNodes:r,edges:a,hasDefaultNodes:l,hasDefaultEdges:i,onNodesDelete:s,onEdgesDelete:d,onNodesChange:c,onEdgesChange:u}=t.getState(),g=(e||[]).map(e=>e.id),p=(n||[]).map(e=>e.id),h=r().reduce((e,t)=>{let n=!g.includes(t.id)&&t.parentNode&&e.find(e=>e.id===t.parentNode);return("boolean"!=typeof t.deletable||t.deletable)&&(g.includes(t.id)||n)&&e.push(t),e},[]),m=a.filter(e=>"boolean"!=typeof e.deletable||e.deletable),f=m.filter(e=>p.includes(e.id));if(h||f){let e=[...f,...eR(h,m)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(i||l)&&(i&&t.setState({edges:a.filter(e=>!n.includes(e.id))}),l&&(h.forEach(e=>{o.delete(e.id)}),t.setState({nodeInternals:new Map(o)}))),n.length>0&&(d?.(e),u&&u(n.map(e=>({id:e,type:"remove"})))),h.length>0&&(s?.(h),c&&c(h.map(e=>({id:e.id,type:"remove"}))))}},[]),g=(0,m.useCallback)(e=>{let n=K(e),o=n?null:t.getState().nodeInternals.get(e.id);return n||o?[n?e:X(o),o,n]:[null,null,n]},[]),p=(0,m.useCallback)((e,n=!0,o)=>{let[r,a,l]=g(e);return r?(o||t.getState().getNodes()).filter(e=>{if(!l&&(e.id===a.id||!e.positionAbsolute))return!1;let t=Y(X(e),r);return n&&t>0||t>=r.width*r.height}):[]},[]),h=(0,m.useCallback)((e,t,n=!0)=>{let[o]=g(e);if(!o)return!1;let r=Y(o,t);return n&&r>0||r>=o.width*o.height},[]);return(0,m.useMemo)(()=>({...e,getNodes:n,getNode:o,getEdges:r,getEdge:a,setNodes:l,setEdges:i,addNodes:s,addEdges:d,toObject:c,deleteElements:u,getIntersectingNodes:p,isNodeIntersecting:h}),[e,n,o,r,a,l,i,s,d,c,u,p,h])}let tb={actInsideInputWithModifier:!1};var tE=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=I(),{deleteElements:o}=tS(),r=ts(e,tb),a=ts(t);(0,m.useEffect)(()=>{if(r){let{edges:e,getNodes:t}=n.getState();o({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[r]),(0,m.useEffect)(()=>{n.setState({multiSelectionActive:a})},[a])};let tw={position:"absolute",width:"100%",height:"100%",top:0,left:0},tv=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,tM=e=>({x:e.x,y:e.y,zoom:e.k}),tN=(e,t)=>e.target.closest(`.${t}`),tC=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),tk=e=>{let t=e.ctrlKey&&et()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},tA=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),tI=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:o,zoomOnScroll:r=!0,zoomOnPinch:a=!0,panOnScroll:l=!1,panOnScrollSpeed:i=.5,panOnScrollMode:s=c.Free,zoomOnDoubleClick:d=!0,elementsSelectable:u,panOnDrag:g=!0,defaultViewport:p,translateExtent:h,minZoom:f,maxZoom:y,zoomActivationKeyCode:w,preventScrolling:v=!0,children:M,noWheelClassName:N,noPanClassName:k})=>{let P=(0,m.useRef)(),_=I(),$=(0,m.useRef)(!1),R=(0,m.useRef)(!1),B=(0,m.useRef)(null),z=(0,m.useRef)({x:0,y:0,zoom:0}),{d3Zoom:L,d3Selection:T,d3ZoomHandler:H,userSelectionActive:F}=A(tA,x.X),Z=ts(w),X=(0,m.useRef)(0),V=(0,m.useRef)(!1),Y=(0,m.useRef)();return!function(e){let t=I();(0,m.useEffect)(()=>{let n;let o=()=>{if(!e.current)return;let n=O(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",C.error004()),t.setState({width:n.width||500,height:n.height||500})};return o(),window.addEventListener("resize",o),e.current&&(n=new ResizeObserver(()=>o())).observe(e.current),()=>{window.removeEventListener("resize",o),n&&e.current&&n.unobserve(e.current)}},[])}(B),(0,m.useEffect)(()=>{if(B.current){let e=B.current.getBoundingClientRect(),t=(0,S.sP)().scaleExtent([f,y]).translateExtent(h),n=(0,b.Z)(B.current).call(t),o=S.CR.translate(p.x,p.y).scale(D(p.zoom,f,y)),r=[[0,0],[e.width,e.height]],a=t.constrain()(o,r,h);t.transform(n,a),t.wheelDelta(tk),_.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[a.x,a.y,a.k],domNode:B.current.closest(".react-flow")})}},[]),(0,m.useEffect)(()=>{T&&L&&(!l||Z||F?void 0!==H&&T.on("wheel.zoom",function(e,t){if(!v||tN(e,N))return null;e.preventDefault(),H.call(this,e,t)},{passive:!1}):T.on("wheel.zoom",o=>{if(tN(o,N))return!1;o.preventDefault(),o.stopImmediatePropagation();let r=T.property("__zoom").k||1,l=et();if(o.ctrlKey&&a&&l){let e=(0,E.Z)(o),t=tk(o);L.scaleTo(T,r*Math.pow(2,t),e,o);return}let d=1===o.deltaMode?20:1,u=s===c.Vertical?0:o.deltaX*d,g=s===c.Horizontal?0:o.deltaY*d;!l&&o.shiftKey&&s!==c.Vertical&&(u=o.deltaY*d,g=0),L.translateBy(T,-(u/r)*i,-(g/r)*i,{internal:!0});let p=tM(T.property("__zoom")),{onViewportChangeStart:h,onViewportChange:m,onViewportChangeEnd:f}=_.getState();clearTimeout(Y.current),V.current||(V.current=!0,t?.(o,p),h?.(p)),V.current&&(e?.(o,p),m?.(p),Y.current=setTimeout(()=>{n?.(o,p),f?.(p),V.current=!1},150))},{passive:!1}))},[F,l,s,T,L,H,Z,a,v,N,t,e,n]),(0,m.useEffect)(()=>{L&&L.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;X.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=_.getState(),o=tM(e.transform);$.current=!0,z.current=o,e.sourceEvent?.type==="mousedown"&&_.setState({paneDragging:!0}),n?.(o),t?.(e.sourceEvent,o)})},[L,t]),(0,m.useEffect)(()=>{L&&(F&&!$.current?L.on("zoom",null):F||L.on("zoom",t=>{let{onViewportChange:n}=_.getState();if(_.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),R.current=!!(o&&tC(g,X.current??0)),(e||n)&&!t.sourceEvent?.internal){let o=tM(t.transform);n?.(o),e?.(t.sourceEvent,o)}}))},[F,L,e,g,o]),(0,m.useEffect)(()=>{L&&L.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=_.getState();if($.current=!1,_.setState({paneDragging:!1}),o&&tC(g,X.current??0)&&!R.current&&o(e.sourceEvent),R.current=!1,(n||t)&&tv(z.current,e.transform)){let o=tM(e.transform);z.current=o,clearTimeout(P.current),P.current=setTimeout(()=>{t?.(o),n?.(e.sourceEvent,o)},l?150:0)}})},[L,l,g,n,o]),(0,m.useEffect)(()=>{L&&L.filter(e=>{let t=Z||r,n=a&&e.ctrlKey;if((!0===g||Array.isArray(g)&&g.includes(1))&&1===e.button&&"mousedown"===e.type&&(tN(e,"react-flow__node")||tN(e,"react-flow__edge")))return!0;if(!g&&!t&&!l&&!d&&!a||F||!d&&"dblclick"===e.type||tN(e,N)&&"wheel"===e.type||tN(e,k)&&("wheel"!==e.type||l&&"wheel"===e.type&&!Z)||!a&&e.ctrlKey&&"wheel"===e.type||!t&&!l&&!n&&"wheel"===e.type||!g&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(g)&&!g.includes(e.button)&&("mousedown"===e.type||"touchstart"===e.type))return!1;let o=Array.isArray(g)&&g.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o})},[F,L,r,a,l,d,g,u,Z]),m.createElement("div",{className:"react-flow__renderer",ref:B,style:tw},M)},tP=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function t_(){let{userSelectionActive:e,userSelectionRect:t}=A(tP,x.X);return e&&t?m.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function t$(e,t){let n=e.find(e=>e.id===t.parentNode);if(n){let e=t.position.x+t.width-n.width,o=t.position.y+t.height-n.height;if(e>0||o>0||t.position.x<0||t.position.y<0){if(n.style={...n.style},n.style.width=n.style.width??n.width,n.style.height=n.style.height??n.height,e>0&&(n.style.width+=e),o>0&&(n.style.height+=o),t.position.x<0){let e=Math.abs(t.position.x);n.position.x=n.position.x-e,n.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);n.position.y=n.position.y-e,n.style.height+=e,t.position.y=0}n.width=n.style.width,n.height=n.style.height}}}function tR(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let o=e.filter(e=>e.id===n.id);if(0===o.length)return t.push(n),t;let r={...n};for(let e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&t$(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&t$(t,r);break;case"remove":return t}return t.push(r),t},n)}function tO(e,t){return tR(e,t)}let tD=(e,t)=>({id:e,type:"select",selected:t});function tB(e,t){return e.reduce((e,n)=>{let o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(tD(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(tD(n.id,!1))),e},[])}let tz=(e,t)=>n=>{n.target===t.current&&e?.(n)},tL=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),tT=(0,m.memo)(({isSelecting:e,selectionMode:t=u.Full,panOnDrag:n,onSelectionStart:o,onSelectionEnd:r,onPaneClick:a,onPaneContextMenu:l,onPaneScroll:i,onPaneMouseEnter:s,onPaneMouseMove:d,onPaneMouseLeave:c,children:g})=>{let p=(0,m.useRef)(null),h=I(),y=(0,m.useRef)(0),S=(0,m.useRef)(0),b=(0,m.useRef)(),{userSelectionActive:E,elementsSelectable:w,dragging:v}=A(tL,x.X),M=()=>{h.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,S.current=0},N=e=>{a?.(e),h.getState().resetSelectedElements(),h.setState({nodesSelectionActive:!1})},C=w&&(e||E);return m.createElement("div",{className:(0,f.Z)(["react-flow__pane",{dragging:v,selection:e}]),onClick:C?void 0:tz(N,p),onContextMenu:tz(e=>{if(Array.isArray(n)&&n?.includes(2)){e.preventDefault();return}l?.(e)},p),onWheel:tz(i?e=>i(e):void 0,p),onMouseEnter:C?void 0:s,onMouseDown:C?t=>{let{resetSelectedElements:n,domNode:r}=h.getState();if(b.current=r?.getBoundingClientRect(),!w||!e||0!==t.button||t.target!==p.current||!b.current)return;let{x:a,y:l}=ee(t,b.current);n(),h.setState({userSelectionRect:{width:0,height:0,startX:a,startY:l,x:a,y:l}}),o?.(t)}:void 0,onMouseMove:C?n=>{let{userSelectionRect:o,nodeInternals:r,edges:a,transform:l,onNodesChange:i,onEdgesChange:s,nodeOrigin:d,getNodes:c}=h.getState();if(!e||!b.current||!o)return;h.setState({userSelectionActive:!0,nodesSelectionActive:!1});let g=ee(n,b.current),p=o.startX??0,m=o.startY??0,f={...o,x:g.x<p?g.x:p,y:g.y<m?g.y:m,width:Math.abs(g.x-p),height:Math.abs(g.y-m)},x=c(),E=e$(r,f,l,t===u.Partial,!0,d),w=eR(E,a).map(e=>e.id),v=E.map(e=>e.id);if(y.current!==v.length){y.current=v.length;let e=tB(x,v);e.length&&i?.(e)}if(S.current!==w.length){S.current=w.length;let e=tB(a,w);e.length&&s?.(e)}h.setState({userSelectionRect:f})}:d,onMouseUp:C?e=>{if(0!==e.button)return;let{userSelectionRect:t}=h.getState();!E&&t&&e.target===p.current&&N?.(e),h.setState({nodesSelectionActive:y.current>0}),M(),r?.(e)}:void 0,onMouseLeave:C?e=>{E&&(h.setState({nodesSelectionActive:y.current>0}),r?.(e)),M()}:c,ref:p,style:tw},g,m.createElement(t_,null))});function tH(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)break;o=o.parentElement}while(o);return!1}function tF(e,t,n,o,r=[0,0],a){var l;let i=(l=e.extent||o)&&"parent"!==l?[l[0],[l[1][0]-(e.width||0),l[1][1]-(e.height||0)]]:l,s=i;if("parent"!==e.extent||e.expandParent){if(e.extent&&e.parentNode&&"parent"!==e.extent){let{x:t,y:o}=eP(n.get(e.parentNode),r).positionAbsolute;s=[[e.extent[0][0]+t,e.extent[0][1]+o],[e.extent[1][0]+t,e.extent[1][1]+o]]}}else if(e.parentNode&&e.width&&e.height){let t=n.get(e.parentNode),{x:o,y:a}=eP(t,r).positionAbsolute;s=t&&U(o)&&U(a)&&U(t.width)&&U(t.height)?[[o+e.width*r[0],a+e.height*r[1]],[o+t.width-e.width+e.width*r[0],a+t.height-e.height+e.height*r[1]]]:s}else a?.("005",C.error005()),s=i;let d={x:0,y:0};e.parentNode&&(d=eP(n.get(e.parentNode),r).positionAbsolute);let c=s&&"parent"!==s?B(t,s):t;return{position:{x:c.x-d.x,y:c.y-d.y},positionAbsolute:c}}function tZ({nodeId:e,dragItems:t,nodeInternals:n}){let o=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?o.find(t=>t.id===e):o[0],o]}tT.displayName="Pane";let tX=(e,t,n,o)=>{let r=t.querySelectorAll(e);if(!r||!r.length)return null;let a=Array.from(r),l=t.getBoundingClientRect(),i={x:l.width*o[0],y:l.height*o[1]};return a.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-l.left-i.x)/n,y:(t.top-l.top-i.y)/n,...O(e)}})};function tV(e,t,n){return void 0===n?n:o=>{let r=t().nodeInternals.get(e);r&&n(o,{...r})}}function tY({id:e,store:t,unselect:n=!1,nodeRef:o}){let{addSelectedNodes:r,unselectNodesAndEdges:a,multiSelectionActive:l,nodeInternals:i,onError:s}=t.getState(),d=i.get(e);if(!d){s?.("012",C.error012(e));return}t.setState({nodesSelectionActive:!1}),d.selected?(n||d.selected&&l)&&(a({nodes:[d],edges:[]}),requestAnimationFrame(()=>o?.current?.blur())):r([e])}function tK(e){return(t,n,o)=>e?.(t,o)}function tU({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:o,nodeId:r,isSelectable:a,selectNodesOnDrag:l}){let i=I(),[s,d]=(0,m.useState)(!1),c=(0,m.useRef)([]),u=(0,m.useRef)({x:null,y:null}),g=(0,m.useRef)(0),p=(0,m.useRef)(null),h=(0,m.useRef)({x:0,y:0}),f=(0,m.useRef)(null),y=(0,m.useRef)(!1),x=(0,m.useRef)(!1),S=function(){let e=I();return(0,m.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),a=t.touches?t.touches[0].clientX:t.clientX,l=t.touches?t.touches[0].clientY:t.clientY,i={x:(a-n[0])/n[2],y:(l-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(i.x/o[0]):i.x,ySnapped:r?o[1]*Math.round(i.y/o[1]):i.y,...i}},[])}();return(0,m.useEffect)(()=>{if(e?.current){let s=(0,b.Z)(e.current),m=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:o,onSelectionDrag:a,updateNodePositions:l,nodeExtent:s,snapGrid:g,snapToGrid:p,nodeOrigin:h,onError:m}=i.getState();u.current={x:e,y:t};let y=!1,x={x:0,y:0,x2:0,y2:0};if(c.current.length>1&&s&&(x=F(e_(c.current,h))),c.current=c.current.map(o=>{let r={x:e-o.distance.x,y:t-o.distance.y};p&&(r.x=g[0]*Math.round(r.x/g[0]),r.y=g[1]*Math.round(r.y/g[1]));let a=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];c.current.length>1&&s&&!o.extent&&(a[0][0]=o.positionAbsolute.x-x.x+s[0][0],a[1][0]=o.positionAbsolute.x+(o.width??0)-x.x2+s[1][0],a[0][1]=o.positionAbsolute.y-x.y+s[0][1],a[1][1]=o.positionAbsolute.y+(o.height??0)-x.y2+s[1][1]);let l=tF(o,r,n,a,h,m);return y=y||o.position.x!==l.position.x||o.position.y!==l.position.y,o.position=l.position,o.positionAbsolute=l.positionAbsolute,o}),!y)return;l(c.current,!0,!0),d(!0);let S=r?o:tK(a);if(S&&f.current){let[e,t]=tZ({nodeId:r,dragItems:c.current,nodeInternals:n});S(f.current,e,t)}},E=()=>{if(!p.current)return;let[e,t]=L(h.current,p.current);if(0!==e||0!==t){let{transform:n,panBy:o}=i.getState();u.current.x=(u.current.x??0)-e/n[2],u.current.y=(u.current.y??0)-t/n[2],o({x:e,y:t})&&m(u.current)}g.current=requestAnimationFrame(E)},v=t=>{let{nodeInternals:n,multiSelectionActive:o,nodesDraggable:s,unselectNodesAndEdges:d,onNodeDragStart:g,onSelectionDragStart:p}=i.getState();x.current=!0;let h=r?g:tK(p);l&&a||o||!r||n.get(r)?.selected||d(),r&&a&&l&&tY({id:r,store:i,nodeRef:e});let m=S(t);if(u.current=m,c.current=Array.from(n.values()).filter(e=>(e.selected||e.id===r)&&(!e.parentNode||!function e(t,n){if(!t.parentNode)return!1;let o=n.get(t.parentNode);return!!o&&(!!o.selected||e(o,n))}(e,n))&&(e.draggable||s&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:m.x-(e.positionAbsolute?.x??0),y:m.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode,width:e.width,height:e.height,expandParent:e.expandParent})),h&&c.current){let[e,o]=tZ({nodeId:r,dragItems:c.current,nodeInternals:n});h(t.sourceEvent,e,o)}};if(t)s.on(".drag",null);else{let t=(0,w.Z)().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=i.getState();0===n&&v(e);let o=S(e);u.current=o,p.current=t?.getBoundingClientRect()||null,h.current=ee(e.sourceEvent,p.current)}).on("drag",e=>{let t=S(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=i.getState();if(!y.current&&x.current&&n&&(y.current=!0,E()),!x.current){let n=t.xSnapped-(u?.current?.x??0),r=t.ySnapped-(u?.current?.y??0);Math.sqrt(n*n+r*r)>o&&v(e)}(u.current.x!==t.xSnapped||u.current.y!==t.ySnapped)&&c.current&&x.current&&(f.current=e.sourceEvent,h.current=ee(e.sourceEvent,p.current),m(t))}).on("end",e=>{if(x.current&&(d(!1),y.current=!1,x.current=!1,cancelAnimationFrame(g.current),c.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:a}=i.getState(),l=r?o:tK(a);if(t(c.current,!1,!1),l){let[t,o]=tZ({nodeId:r,dragItems:c.current,nodeInternals:n});l(e.sourceEvent,t,o)}}}).filter(t=>{let r=t.target;return!t.button&&(!n||!tH(r,`.${n}`,e))&&(!o||tH(r,o,e))});return s.call(t),()=>{s.on(".drag",null)}}}},[e,t,n,o,a,i,r,l,S]),s}function tW(){let e=I();return(0,m.useCallback)(t=>{let{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:a,snapToGrid:l,snapGrid:i,onError:s,nodesDraggable:d}=e.getState(),c=a().filter(e=>e.selected&&(e.draggable||d&&void 0===e.draggable)),u=l?i[0]:5,g=l?i[1]:5,p=t.isShiftPressed?4:1,h=t.x*u*p,m=t.y*g*p;r(c.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+h,y:e.positionAbsolute.y+m};l&&(t.x=i[0]*Math.round(t.x/i[0]),t.y=i[1]*Math.round(t.y/i[1]));let{positionAbsolute:r,position:a}=tF(e,t,n,o,void 0,s);e.position=a,e.positionAbsolute=r}return e}),!0,!1)},[])}let tj={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var tq=e=>{let t=({id:t,type:n,data:o,xPos:r,yPos:a,xPosOrigin:l,yPosOrigin:i,selected:s,onClick:d,onMouseEnter:c,onMouseMove:u,onMouseLeave:g,onContextMenu:p,onDoubleClick:h,style:y,className:x,isDraggable:S,isSelectable:b,isConnectable:E,isFocusable:w,selectNodesOnDrag:v,sourcePosition:M,targetPosition:N,hidden:C,resizeObserver:k,dragHandle:A,zIndex:P,isParent:_,noDragClassName:$,noPanClassName:R,initialized:O,disableKeyboardA11y:D,ariaLabel:B,rfId:z})=>{let L=I(),T=(0,m.useRef)(null),H=(0,m.useRef)(M),F=(0,m.useRef)(N),Z=(0,m.useRef)(n),X=b||S||d||c||u||g,V=tW(),Y=tV(t,L.getState,c),K=tV(t,L.getState,u),U=tV(t,L.getState,g),W=tV(t,L.getState,p),q=tV(t,L.getState,h);(0,m.useEffect)(()=>{if(T.current&&!C){let e=T.current;return k?.observe(e),()=>k?.unobserve(e)}},[C]),(0,m.useEffect)(()=>{let e=Z.current!==n,o=H.current!==M,r=F.current!==N;T.current&&(e||o||r)&&(e&&(Z.current=n),o&&(H.current=M),r&&(F.current=N),L.getState().updateNodeDimensions([{id:t,nodeElement:T.current,forceUpdate:!0}]))},[t,n,M,N]);let G=tU({nodeRef:T,disabled:C||!S,noDragClassName:$,handleSelector:A,nodeId:t,isSelectable:b,selectNodesOnDrag:v});return C?null:m.createElement("div",{className:(0,f.Z)(["react-flow__node",`react-flow__node-${n}`,{[R]:S},x,{selected:s,selectable:b,parent:_,dragging:G}]),ref:T,style:{zIndex:P,transform:`translate(${l}px,${i}px)`,pointerEvents:X?"all":"none",visibility:O?"visible":"hidden",...y},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:Y,onMouseMove:K,onMouseLeave:U,onContextMenu:W,onClick:e=>{let{nodeDragThreshold:n}=L.getState();if(b&&(!v||!S||n>0)&&tY({id:t,store:L,nodeRef:T}),d){let n=L.getState().nodeInternals.get(t);n&&d(e,{...n})}},onDoubleClick:q,onKeyDown:w?e=>{!Q(e)&&(j.includes(e.key)&&b?tY({id:t,store:L,unselect:"Escape"===e.key,nodeRef:T}):!D&&S&&s&&Object.prototype.hasOwnProperty.call(tj,e.key)&&(L.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~r}, y: ${~~a}`}),V({x:tj[e.key].x,y:tj[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:w?0:void 0,role:w?"button":void 0,"aria-describedby":D?void 0:`${to}-${z}`,"aria-label":B},m.createElement(eE,{value:t},m.createElement(e,{id:t,data:o,type:n,xPos:r,yPos:a,selected:s,isConnectable:E,sourcePosition:M,targetPosition:N,dragging:G,dragHandle:A,zIndex:P})))};return t.displayName="NodeWrapper",(0,m.memo)(t)};let tG=e=>({...e_(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var tQ=(0,m.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let o=I(),{width:r,height:a,x:l,y:i,transformString:s,userSelectionActive:d}=A(tG,x.X),c=tW(),u=(0,m.useRef)(null);return((0,m.useEffect)(()=>{n||u.current?.focus({preventScroll:!0})},[n]),tU({nodeRef:u}),!d&&r&&a)?m.createElement("div",{className:(0,f.Z)(["react-flow__nodesselection","react-flow__container",t]),style:{transform:s}},m.createElement("div",{ref:u,className:"react-flow__nodesselection-rect",onContextMenu:e?t=>{e(t,o.getState().getNodes().filter(e=>e.selected))}:void 0,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(tj,e.key)&&c({x:tj[e.key].x,y:tj[e.key].y,isShiftPressed:e.shiftKey})},style:{width:r,height:a,top:i,left:l}})):null});let tJ=e=>e.nodesSelectionActive,t0=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,deleteKeyCode:i,onMove:s,onMoveStart:d,onMoveEnd:c,selectionKeyCode:u,selectionOnDrag:g,selectionMode:p,onSelectionStart:h,onSelectionEnd:f,multiSelectionKeyCode:y,panActivationKeyCode:x,zoomActivationKeyCode:S,elementsSelectable:b,zoomOnScroll:E,zoomOnPinch:w,panOnScroll:v,panOnScrollSpeed:M,panOnScrollMode:N,zoomOnDoubleClick:C,panOnDrag:k,defaultViewport:I,translateExtent:P,minZoom:_,maxZoom:$,preventScrolling:R,onSelectionContextMenu:O,noWheelClassName:D,noPanClassName:B,disableKeyboardA11y:z})=>{let L=A(tJ),T=ts(u),H=ts(x),F=H||k,Z=H||v,X=T||g&&!0!==F;return tE({deleteKeyCode:i,multiSelectionKeyCode:y}),m.createElement(tI,{onMove:s,onMoveStart:d,onMoveEnd:c,onPaneContextMenu:a,elementsSelectable:b,zoomOnScroll:E,zoomOnPinch:w,panOnScroll:Z,panOnScrollSpeed:M,panOnScrollMode:N,zoomOnDoubleClick:C,panOnDrag:!T&&F,defaultViewport:I,translateExtent:P,minZoom:_,maxZoom:$,zoomActivationKeyCode:S,preventScrolling:R,noWheelClassName:D,noPanClassName:B},m.createElement(tT,{onSelectionStart:h,onSelectionEnd:f,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,panOnDrag:F,isSelecting:!!X,selectionMode:p},e,L&&m.createElement(tQ,{onSelectionContextMenu:O,noPanClassName:B,disableKeyboardA11y:z})))};t0.displayName="FlowRenderer";var t1=(0,m.memo)(t0);function t2(e){let t={input:tq(e.input||eG),default:tq(e.default||ej),output:tq(e.output||eJ),group:tq(e.group||e0)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=tq(e[n]||ej),t),{});return{...t,...n}}let t5=({x:e,y:t,width:n,height:o,origin:r})=>!n||!o||r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]},t3=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),t4=e=>{var t;let{nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:a,updateNodeDimensions:l,onError:i}=A(t3,x.X),s=(t=e.onlyRenderVisibleElements,A((0,m.useCallback)(e=>t?e$(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes(),[t]))),d=(0,m.useRef)(),c=(0,m.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{l(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return d.current=e,e},[]);return(0,m.useEffect)(()=>()=>{d?.current?.disconnect()},[]),m.createElement("div",{className:"react-flow__nodes",style:tw},s.map(t=>{let l=t.type||"default";e.nodeTypes[l]||(i?.("003",C.error003(l)),l="default");let s=e.nodeTypes[l]||e.nodeTypes.default,d=!!(t.draggable||n&&void 0===t.draggable),u=!!(t.selectable||a&&void 0===t.selectable),g=!!(t.connectable||o&&void 0===t.connectable),p=!!(t.focusable||r&&void 0===t.focusable),f=e.nodeExtent?B(t.positionAbsolute,e.nodeExtent):t.positionAbsolute,y=f?.x??0,x=f?.y??0,S=t5({x:y,y:x,width:t.width??0,height:t.height??0,origin:e.nodeOrigin});return m.createElement(s,{key:t.id,id:t.id,className:t.className,style:t.style,type:l,data:t.data,sourcePosition:t.sourcePosition||h.Bottom,targetPosition:t.targetPosition||h.Top,hidden:t.hidden,xPos:y,yPos:x,xPosOrigin:S.x,yPosOrigin:S.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!t.selected,isDraggable:d,isSelectable:u,isConnectable:g,isFocusable:p,resizeObserver:c,dragHandle:t.dragHandle,zIndex:t[W]?.z??0,isParent:!!t[W]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!t.width&&!!t.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:t.ariaLabel})}))};t4.displayName="NodeRenderer";var t8=(0,m.memo)(t4);let t7=(e,t,n)=>n===h.Left?e-t:n===h.Right?e+t:e,t6=(e,t,n)=>n===h.Top?e-t:n===h.Bottom?e+t:e,t9="react-flow__edgeupdater",ne=({position:e,centerX:t,centerY:n,radius:o=10,onMouseDown:r,onMouseEnter:a,onMouseOut:l,type:i})=>m.createElement("circle",{onMouseDown:r,onMouseEnter:a,onMouseOut:l,className:(0,f.Z)([t9,`${t9}-${i}`]),cx:t7(t,o,e),cy:t6(n,o,e),r:o,stroke:"transparent",fill:"transparent"}),nt=()=>!0;var nn=e=>{let t=({id:t,className:n,type:o,data:r,onClick:a,onEdgeDoubleClick:l,selected:i,animated:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:h,style:y,source:x,target:S,sourceX:b,sourceY:E,targetX:w,targetY:v,sourcePosition:M,targetPosition:N,elementsSelectable:C,hidden:k,sourceHandleId:A,targetHandleId:P,onContextMenu:_,onMouseEnter:$,onMouseMove:R,onMouseLeave:O,edgeUpdaterRadius:D,onEdgeUpdate:B,onEdgeUpdateStart:z,onEdgeUpdateEnd:L,markerEnd:T,markerStart:H,rfId:F,ariaLabel:Z,isFocusable:X,isUpdatable:V,pathOptions:Y,interactionWidth:K})=>{let U=(0,m.useRef)(null),[W,q]=(0,m.useState)(!1),[G,Q]=(0,m.useState)(!1),J=I(),ee=(0,m.useMemo)(()=>`url(#${eN(H,F)})`,[H,F]),et=(0,m.useMemo)(()=>`url(#${eN(T,F)})`,[T,F]);if(k)return null;let en=eo(t,J.getState,l),er=eo(t,J.getState,_),ea=eo(t,J.getState,$),el=eo(t,J.getState,R),ei=eo(t,J.getState,O),es=(e,n)=>{if(0!==e.button)return;let{edges:o,isValidConnection:r}=J.getState(),a=n?S:x,l=(n?P:A)||null,i=n?"target":"source",s=r||nt,d=o.find(e=>e.id===t);Q(!0),z?.(e,d,i),eZ({event:e,handleId:l,nodeId:a,onConnect:e=>B?.(d,e),isTarget:n,getState:J.getState,setState:J.setState,isValidConnection:s,edgeUpdaterType:i,onEdgeUpdateEnd:e=>{Q(!1),L?.(e,d,i)}})},ed=()=>q(!0),ec=()=>q(!1);return m.createElement("g",{className:(0,f.Z)(["react-flow__edge",`react-flow__edge-${o}`,n,{selected:i,animated:s,inactive:!C&&!a,updating:W}]),onClick:e=>{let{edges:n,addSelectedEdges:o,unselectNodesAndEdges:r,multiSelectionActive:l}=J.getState(),i=n.find(e=>e.id===t);i&&(C&&(J.setState({nodesSelectionActive:!1}),i.selected&&l?(r({nodes:[],edges:[i]}),U.current?.blur()):o([t])),a&&a(e,i))},onDoubleClick:en,onContextMenu:er,onMouseEnter:ea,onMouseMove:el,onMouseLeave:ei,onKeyDown:X?e=>{if(j.includes(e.key)&&C){let{unselectNodesAndEdges:n,addSelectedEdges:o,edges:r}=J.getState();"Escape"===e.key?(U.current?.blur(),n({edges:[r.find(e=>e.id===t)]})):o([t])}}:void 0,tabIndex:X?0:void 0,role:X?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===Z?void 0:Z||`Edge from ${x} to ${S}`,"aria-describedby":X?`${tr}-${F}`:void 0,ref:U},!G&&m.createElement(e,{id:t,source:x,target:S,selected:i,animated:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:h,data:r,style:y,sourceX:b,sourceY:E,targetX:w,targetY:v,sourcePosition:M,targetPosition:N,sourceHandleId:A,targetHandleId:P,markerStart:ee,markerEnd:et,pathOptions:Y,interactionWidth:K}),V&&m.createElement(m.Fragment,null,("source"===V||!0===V)&&m.createElement(ne,{position:M,centerX:b,centerY:E,radius:D,onMouseDown:e=>es(e,!0),onMouseEnter:ed,onMouseOut:ec,type:"source"}),("target"===V||!0===V)&&m.createElement(ne,{position:N,centerX:w,centerY:v,radius:D,onMouseDown:e=>es(e,!1),onMouseEnter:ed,onMouseOut:ec,type:"target"})))};return t.displayName="EdgeWrapper",(0,m.memo)(t)};function no(e){let t={default:nn(e.default||eS),straight:nn(e.bezier||em),step:nn(e.step||eh),smoothstep:nn(e.step||ep),simplebezier:nn(e.simplebezier||es)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=nn(e[n]||eS),t),{});return{...t,...n}}function nr(e,t,n=null){let o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,a=n?.width||t.width,l=n?.height||t.height;switch(e){case h.Top:return{x:o+a/2,y:r};case h.Right:return{x:o+a,y:r+l/2};case h.Bottom:return{x:o+a/2,y:r+l};case h.Left:return{x:o,y:r+l/2}}}function na(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let nl=(e,t,n,o,r,a)=>{let l=nr(n,e,t),i=nr(a,o,r);return{sourceX:l.x,sourceY:l.y,targetX:i.x,targetY:i.y}};function ni(e){let t=e?.[W]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let ns=[{level:0,isMaxLevel:!0,edges:[]}],nd={[p.Arrow]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[p.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},nc=({id:e,type:t,color:n,width:o=12.5,height:r=12.5,markerUnits:a="strokeWidth",strokeWidth:l,orient:i="auto-start-reverse"})=>{let s=function(e){let t=I();return(0,m.useMemo)(()=>Object.prototype.hasOwnProperty.call(nd,e)?nd[e]:(t.getState().onError?.("009",C.error009(e)),null),[e])}(t);return s?m.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${o}`,markerHeight:`${r}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:i,refX:"0",refY:"0"},m.createElement(s,{color:n,strokeWidth:l})):null},nu=({defaultColor:e,rfId:t})=>n=>{let o=[];return n.edges.reduce((n,r)=>([r.markerStart,r.markerEnd].forEach(r=>{if(r&&"object"==typeof r){let a=eN(r,t);o.includes(a)||(n.push({id:a,color:r.color||e,...r}),o.push(a))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},ng=({defaultColor:e,rfId:t})=>{let n=A((0,m.useCallback)(nu({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return m.createElement("defs",null,n.map(e=>m.createElement(nc,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};ng.displayName="MarkerDefinitions";var np=(0,m.memo)(ng);let nh=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),nm=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:o,edgeTypes:r,noPanClassName:a,onEdgeUpdate:l,onEdgeContextMenu:i,onEdgeMouseEnter:s,onEdgeMouseMove:c,onEdgeMouseLeave:u,onEdgeClick:g,edgeUpdaterRadius:p,onEdgeDoubleClick:y,onEdgeUpdateStart:S,onEdgeUpdateEnd:b,children:E})=>{let{edgesFocusable:w,edgesUpdatable:v,elementsSelectable:M,width:N,height:k,connectionMode:I,nodeInternals:P,onError:_}=A(nh,x.X),$=function(e,t,n=!1){let o=-1,r=Object.entries(e.reduce((e,r)=>{let a=U(r.zIndex),l=a?r.zIndex:0;if(n){let e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,i=Math.max(n?.[W]?.z||0,e?.[W]?.z||0,1e3);l=(a?r.zIndex:0)+(o?i:0)}return e[l]?e[l].push(r):e[l]=[r],o=l>o?l:o,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===o}});return 0===r.length?ns:r}(A((0,m.useCallback)(e=>t?e.edges.filter(t=>{let n=P.get(t.source),o=P.get(t.target);return n?.width&&n?.height&&o?.width&&o?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:a,width:l,height:i,transform:s}){let d={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+a)};d.x===d.x2&&(d.x2+=1),d.y===d.y2&&(d.y2+=1);let c=F({x:(0-s[0])/s[2],y:(0-s[1])/s[2],width:l/s[2],height:i/s[2]});return Math.ceil(Math.max(0,Math.min(c.x2,d.x2)-Math.max(c.x,d.x))*Math.max(0,Math.min(c.y2,d.y2)-Math.max(c.y,d.y)))>0}({sourcePos:n.positionAbsolute||{x:0,y:0},targetPos:o.positionAbsolute||{x:0,y:0},sourceWidth:n.width,sourceHeight:n.height,targetWidth:o.width,targetHeight:o.height,width:e.width,height:e.height,transform:e.transform})}):e.edges,[t,P])),P,n);return N?m.createElement(m.Fragment,null,$.map(({level:t,edges:n,isMaxLevel:x})=>m.createElement("svg",{key:t,style:{zIndex:t},width:N,height:k,className:"react-flow__edges react-flow__container"},x&&m.createElement(np,{defaultColor:e,rfId:o}),m.createElement("g",null,n.map(e=>{let[t,n,x]=ni(P.get(e.source)),[E,N,k]=ni(P.get(e.target));if(!x||!k)return null;let A=e.type||"default";r[A]||(_?.("011",C.error011(A)),A="default");let $=r[A]||r.default,R=I===d.Strict?N.target:(N.target??[]).concat(N.source??[]),O=na(n.source,e.sourceHandle),D=na(R,e.targetHandle),B=O?.position||h.Bottom,z=D?.position||h.Top,L=!!(e.focusable||w&&void 0===e.focusable),T=void 0!==l&&(e.updatable||v&&void 0===e.updatable);if(!O||!D)return _?.("008",C.error008(O,e)),null;let{sourceX:H,sourceY:F,targetX:Z,targetY:X}=nl(t,O,B,E,D,z);return m.createElement($,{key:e.id,id:e.id,className:(0,f.Z)([e.className,a]),type:A,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:H,sourceY:F,targetX:Z,targetY:X,sourcePosition:B,targetPosition:z,elementsSelectable:M,onEdgeUpdate:l,onContextMenu:i,onMouseEnter:s,onMouseMove:c,onMouseLeave:u,onClick:g,edgeUpdaterRadius:p,onEdgeDoubleClick:y,onEdgeUpdateStart:S,onEdgeUpdateEnd:b,rfId:o,ariaLabel:e.ariaLabel,isFocusable:L,isUpdatable:T,pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth})})))),E):null};nm.displayName="EdgeRenderer";var nf=(0,m.memo)(nm);let ny=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function nx({children:e}){let t=A(ny);return m.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let nS={[h.Left]:h.Right,[h.Right]:h.Left,[h.Top]:h.Bottom,[h.Bottom]:h.Top},nb=({nodeId:e,handleType:t,style:n,type:o=g.Bezier,CustomComponent:r,connectionStatus:a})=>{let{fromNode:l,handleId:i,toX:s,toY:c,connectionMode:u}=A((0,m.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),x.X),p=l?.[W]?.handleBounds,h=p?.[t];if(u===d.Loose&&(h=h||p?.["source"===t?"target":"source"]),!l||!h)return null;let f=i?h.find(e=>e.id===i):h[0],y=f?f.x+f.width/2:(l.width??0)/2,S=f?f.y+f.height/2:l.height??0,b=(l.positionAbsolute?.x??0)+y,E=(l.positionAbsolute?.y??0)+S,w=f?.position,v=w?nS[w]:null;if(!w||!v)return null;if(r)return m.createElement(r,{connectionLineType:o,connectionLineStyle:n,fromNode:l,fromHandle:f,fromX:b,fromY:E,toX:s,toY:c,fromPosition:w,toPosition:v,connectionStatus:a});let M="",N={sourceX:b,sourceY:E,sourcePosition:w,targetX:s,targetY:c,targetPosition:v};return o===g.Bezier?[M]=ex(N):o===g.Step?[M]=eg({...N,borderRadius:0}):o===g.SmoothStep?[M]=eg(N):o===g.SimpleBezier?[M]=ei(N):M=`M${b},${E} ${s},${c}`,m.createElement("path",{d:M,fill:"none",className:"react-flow__connection-path",style:n})};nb.displayName="ConnectionLine";let nE=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function nw({containerStyle:e,style:t,type:n,component:o}){let{nodeId:r,handleType:a,nodesConnectable:l,width:i,height:s,connectionStatus:d}=A(nE,x.X);return r&&a&&i&&l?m.createElement("svg",{style:e,width:i,height:s,className:"react-flow__edges react-flow__connectionline react-flow__container"},m.createElement("g",{className:(0,f.Z)(["react-flow__connection",d])},m.createElement(nb,{nodeId:r,handleType:a,style:t,type:n,CustomComponent:o,connectionStatus:d}))):null}function nv(e,t){return(0,m.useRef)(null),I(),(0,m.useMemo)(()=>t(e),[e])}let nM=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:o,onMoveEnd:r,onInit:a,onNodeClick:l,onEdgeClick:i,onNodeDoubleClick:s,onEdgeDoubleClick:d,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:p,onSelectionContextMenu:h,onSelectionStart:f,onSelectionEnd:y,connectionLineType:x,connectionLineStyle:S,connectionLineComponent:b,connectionLineContainerStyle:E,selectionKeyCode:w,selectionOnDrag:v,selectionMode:M,multiSelectionKeyCode:N,panActivationKeyCode:C,zoomActivationKeyCode:k,deleteKeyCode:A,onlyRenderVisibleElements:I,elementsSelectable:P,selectNodesOnDrag:_,defaultViewport:$,translateExtent:R,minZoom:O,maxZoom:D,preventScrolling:B,defaultMarkerColor:z,zoomOnScroll:L,zoomOnPinch:T,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:Z,zoomOnDoubleClick:X,panOnDrag:V,onPaneClick:Y,onPaneMouseEnter:K,onPaneMouseMove:U,onPaneMouseLeave:W,onPaneScroll:j,onPaneContextMenu:q,onEdgeUpdate:G,onEdgeContextMenu:Q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:et,edgeUpdaterRadius:en,onEdgeUpdateStart:eo,onEdgeUpdateEnd:er,noDragClassName:ea,noWheelClassName:el,noPanClassName:ei,elevateEdgesOnSelect:es,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})=>{let ep=nv(e,t2),eh=nv(t,no);return!function(e){let t=tS(),n=(0,m.useRef)(!1);(0,m.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(a),m.createElement(t1,{onPaneClick:Y,onPaneMouseEnter:K,onPaneMouseMove:U,onPaneMouseLeave:W,onPaneContextMenu:q,onPaneScroll:j,deleteKeyCode:A,selectionKeyCode:w,selectionOnDrag:v,selectionMode:M,onSelectionStart:f,onSelectionEnd:y,multiSelectionKeyCode:N,panActivationKeyCode:C,zoomActivationKeyCode:k,elementsSelectable:P,onMove:n,onMoveStart:o,onMoveEnd:r,zoomOnScroll:L,zoomOnPinch:T,zoomOnDoubleClick:X,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:Z,panOnDrag:V,defaultViewport:$,translateExtent:R,minZoom:O,maxZoom:D,onSelectionContextMenu:h,preventScrolling:B,noDragClassName:ea,noWheelClassName:el,noPanClassName:ei,disableKeyboardA11y:ed},m.createElement(nx,null,m.createElement(nf,{edgeTypes:eh,onEdgeClick:i,onEdgeDoubleClick:d,onEdgeUpdate:G,onlyRenderVisibleElements:I,onEdgeContextMenu:Q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:et,onEdgeUpdateStart:eo,onEdgeUpdateEnd:er,edgeUpdaterRadius:en,defaultMarkerColor:z,noPanClassName:ei,elevateEdgesOnSelect:!!es,disableKeyboardA11y:ed,rfId:eg},m.createElement(nw,{style:S,type:x,component:b,containerStyle:E})),m.createElement("div",{className:"react-flow__edgelabel-renderer"}),m.createElement(t8,{nodeTypes:ep,onNodeClick:l,onNodeDoubleClick:s,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:p,selectNodesOnDrag:_,onlyRenderVisibleElements:I,noPanClassName:ei,noDragClassName:ea,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})))};nM.displayName="GraphView";var nN=(0,m.memo)(nM);let nC=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nk={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:nC,nodeExtent:nC,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:d.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:q,isValidConnection:void 0},nA=()=>(0,y.F)((e,t)=>({...nk,setNodes:n=>{let{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:a}=t();e({nodeInternals:tg(n,o,r,a)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:o={}}=t();e({edges:n.map(e=>({...o,...e}))})},setDefaultNodesAndEdges:(n,o)=>{let r=void 0!==n,a=void 0!==o;e({nodeInternals:r?tg(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:a?o:[],hasDefaultNodes:r,hasDefaultEdges:a})},updateNodeDimensions:n=>{let{onNodesChange:o,nodeInternals:r,fitViewOnInit:a,fitViewOnInitDone:l,fitViewOnInitOptions:i,domNode:s,nodeOrigin:d}=t(),c=s?.querySelector(".react-flow__viewport");if(!c)return;let u=window.getComputedStyle(c),{m22:g}=new window.DOMMatrixReadOnly(u.transform),p=n.reduce((e,t)=>{let n=r.get(t.id);if(n){let o=O(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[W]:{...n[W],handleBounds:{source:tX(".source",t.nodeElement,g,d),target:tX(".target",t.nodeElement,g,d)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e},[]);tu(r,d);let h=l||a&&!l&&tp(t,{initial:!0,...i});e({nodeInternals:new Map(r),fitViewOnInitDone:h}),p?.length>0&&o?.(p)},updateNodePositions:(e,n=!0,o=!1)=>{let{triggerNodeChanges:r}=t();r(e.map(e=>{let t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:o,nodeInternals:r,hasDefaultNodes:a,nodeOrigin:l,getNodes:i,elevateNodesOnSelect:s}=t();n?.length&&(a&&e({nodeInternals:tg(tO(n,i()),r,l,s)}),o?.(n))},addSelectedNodes:n=>{let o;let{multiSelectionActive:r,edges:a,getNodes:l}=t(),i=null;r?o=n.map(e=>tD(e,!0)):(o=tB(l(),n),i=tB(a,[])),th({changedNodes:o,changedEdges:i,get:t,set:e})},addSelectedEdges:n=>{let o;let{multiSelectionActive:r,edges:a,getNodes:l}=t(),i=null;r?o=n.map(e=>tD(e,!0)):(o=tB(a,n),i=tB(l(),[])),th({changedNodes:i,changedEdges:o,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{let{edges:r,getNodes:a}=t();th({changedNodes:(n||a()).map(e=>(e.selected=!1,tD(e.id,!1))),changedEdges:(o||r).map(e=>tD(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:o}=t();th({changedNodes:o().filter(e=>e.selected).map(e=>tD(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>tD(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:o}=t();o.forEach(e=>{e.positionAbsolute=B(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{let{transform:n,width:o,height:r,d3Zoom:a,d3Selection:l,translateExtent:i}=t();if(!a||!l||!e.x&&!e.y)return!1;let s=S.CR.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),d=a?.constrain()(s,[[0,0],[o,r]],i);return a.transform(l,d),n[0]!==d.x||n[1]!==d.y||n[2]!==d.k},cancelConnection:()=>e({connectionNodeId:nk.connectionNodeId,connectionHandleId:nk.connectionHandleId,connectionHandleType:nk.connectionHandleType,connectionStatus:nk.connectionStatus,connectionStartHandle:nk.connectionStartHandle,connectionEndHandle:nk.connectionEndHandle}),reset:()=>e({...nk})}),Object.is),nI=({children:e})=>{let t=(0,m.useRef)(null);return t.current||(t.current=nA()),m.createElement(N,{value:t.current},e)};nI.displayName="ReactFlowProvider";let nP=({children:e})=>(0,m.useContext)(M)?m.createElement(m.Fragment,null,e):m.createElement(nI,null,e);nP.displayName="ReactFlowWrapper";let n_={input:eG,default:ej,output:eJ,group:e0},n$={default:eS,straight:em,step:eh,smoothstep:ep,simplebezier:es},nR=[0,0],nO=[15,15],nD={x:0,y:0,zoom:1},nB={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},nz=(0,m.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,className:r,nodeTypes:a=n_,edgeTypes:l=n$,onNodeClick:i,onEdgeClick:s,onInit:p,onMove:h,onMoveStart:y,onMoveEnd:x,onConnect:S,onConnectStart:b,onConnectEnd:E,onClickConnectStart:w,onClickConnectEnd:v,onNodeMouseEnter:M,onNodeMouseMove:N,onNodeMouseLeave:C,onNodeContextMenu:k,onNodeDoubleClick:A,onNodeDragStart:I,onNodeDrag:P,onNodeDragStop:_,onNodesDelete:R,onEdgesDelete:O,onSelectionChange:D,onSelectionDragStart:B,onSelectionDrag:z,onSelectionDragStop:L,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,connectionMode:Z=d.Strict,connectionLineType:X=g.Bezier,connectionLineStyle:V,connectionLineComponent:Y,connectionLineContainerStyle:K,deleteKeyCode:U="Backspace",selectionKeyCode:W="Shift",selectionOnDrag:j=!1,selectionMode:q=u.Full,panActivationKeyCode:G="Space",multiSelectionKeyCode:Q=et()?"Meta":"Control",zoomActivationKeyCode:J=et()?"Meta":"Control",snapToGrid:ee=!1,snapGrid:en=nO,onlyRenderVisibleElements:eo=!1,selectNodesOnDrag:er=!0,nodesDraggable:ea,nodesConnectable:el,nodesFocusable:ei,nodeOrigin:es=nR,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,defaultViewport:eg=nD,minZoom:ep=.5,maxZoom:eh=2,translateExtent:em=nC,preventScrolling:ef=!0,nodeExtent:ey,defaultMarkerColor:ex="#b1b1b7",zoomOnScroll:eS=!0,zoomOnPinch:eb=!0,panOnScroll:eE=!1,panOnScrollSpeed:ew=.5,panOnScrollMode:ev=c.Free,zoomOnDoubleClick:eM=!0,panOnDrag:eN=!0,onPaneClick:eC,onPaneMouseEnter:ek,onPaneMouseMove:eA,onPaneMouseLeave:eI,onPaneScroll:eP,onPaneContextMenu:e_,children:e$,onEdgeUpdate:eR,onEdgeContextMenu:eO,onEdgeDoubleClick:eD,onEdgeMouseEnter:eB,onEdgeMouseMove:ez,onEdgeMouseLeave:eL,onEdgeUpdateStart:eT,onEdgeUpdateEnd:eH,edgeUpdaterRadius:eF=10,onNodesChange:eZ,onEdgesChange:eX,noDragClassName:eV="nodrag",noWheelClassName:eY="nowheel",noPanClassName:eK="nopan",fitView:eU=!1,fitViewOptions:eW,connectOnClick:ej=!0,attributionPosition:eq,proOptions:eG,defaultEdgeOptions:eQ,elevateNodesOnSelect:eJ=!0,elevateEdgesOnSelect:e0=!1,disableKeyboardA11y:e1=!1,autoPanOnConnect:e2=!0,autoPanOnNodeDrag:e5=!0,connectionRadius:e3=20,isValidConnection:e4,onError:e7,style:e6,id:e9,nodeDragThreshold:tt,...tn},to)=>{let tr=e9||"1";return m.createElement("div",{...tn,style:{...e6,...nB},ref:to,className:(0,f.Z)(["react-flow",r]),"data-testid":"rf__wrapper",id:e9},m.createElement(nP,null,m.createElement(nN,{onInit:p,onMove:h,onMoveStart:y,onMoveEnd:x,onNodeClick:i,onEdgeClick:s,onNodeMouseEnter:M,onNodeMouseMove:N,onNodeMouseLeave:C,onNodeContextMenu:k,onNodeDoubleClick:A,nodeTypes:a,edgeTypes:l,connectionLineType:X,connectionLineStyle:V,connectionLineComponent:Y,connectionLineContainerStyle:K,selectionKeyCode:W,selectionOnDrag:j,selectionMode:q,deleteKeyCode:U,multiSelectionKeyCode:Q,panActivationKeyCode:G,zoomActivationKeyCode:J,onlyRenderVisibleElements:eo,selectNodesOnDrag:er,defaultViewport:eg,translateExtent:em,minZoom:ep,maxZoom:eh,preventScrolling:ef,zoomOnScroll:eS,zoomOnPinch:eb,zoomOnDoubleClick:eM,panOnScroll:eE,panOnScrollSpeed:ew,panOnScrollMode:ev,panOnDrag:eN,onPaneClick:eC,onPaneMouseEnter:ek,onPaneMouseMove:eA,onPaneMouseLeave:eI,onPaneScroll:eP,onPaneContextMenu:e_,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,onEdgeUpdate:eR,onEdgeContextMenu:eO,onEdgeDoubleClick:eD,onEdgeMouseEnter:eB,onEdgeMouseMove:ez,onEdgeMouseLeave:eL,onEdgeUpdateStart:eT,onEdgeUpdateEnd:eH,edgeUpdaterRadius:eF,defaultMarkerColor:ex,noDragClassName:eV,noWheelClassName:eY,noPanClassName:eK,elevateEdgesOnSelect:e0,rfId:tr,disableKeyboardA11y:e1,nodeOrigin:es,nodeExtent:ey}),m.createElement(te,{nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:S,onConnectStart:b,onConnectEnd:E,onClickConnectStart:w,onClickConnectEnd:v,nodesDraggable:ea,nodesConnectable:el,nodesFocusable:ei,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,elevateNodesOnSelect:eJ,minZoom:ep,maxZoom:eh,nodeExtent:ey,onNodesChange:eZ,onEdgesChange:eX,snapToGrid:ee,snapGrid:en,connectionMode:Z,translateExtent:em,connectOnClick:ej,defaultEdgeOptions:eQ,fitView:eU,fitViewOptions:eW,onNodesDelete:R,onEdgesDelete:O,onNodeDragStart:I,onNodeDrag:P,onNodeDragStop:_,onSelectionDrag:z,onSelectionDragStart:B,onSelectionDragStop:L,noPanClassName:eK,nodeOrigin:es,rfId:tr,autoPanOnConnect:e2,autoPanOnNodeDrag:e5,onError:e7,connectionRadius:e3,isValidConnection:e4,nodeDragThreshold:tt}),m.createElement(e8,{onSelectionChange:D}),e$,m.createElement($,{proOptions:eG,position:eq}),m.createElement(ti,{rfId:tr,disableKeyboardA11y:e1})))});nz.displayName="ReactFlow";let nL=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");function nT({children:e}){let t=A(nL);return t?(0,v.createPortal)(e,t):null}function nH(e){return t=>{let[n,o]=(0,m.useState)(t),r=(0,m.useCallback)(t=>o(n=>e(t,n)),[]);return[n,o,r]}}let nF=nH(tO),nZ=nH(function(e,t){return tR(e,t)})}}]);