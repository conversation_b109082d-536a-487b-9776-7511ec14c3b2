"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[36],{91036:function(e,t,n){var r=n(64836),a=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(42122)),l=r(n(38416)),u=r(n(70215)),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=p(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&({}).hasOwnProperty.call(e,l)){var u=o?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(r,l,u):r[l]=e[l]}return r.default=e,n&&n.set(e,r),r}(n(67294)),c=r(n(93967)),d=r(n(79872)),f=n(89019),s=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"];function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}var v=i.forwardRef(function(e,t){var n=e.className,r=e.component,a=e.viewBox,p=e.spin,v=e.rotate,m=e.tabIndex,w=e.onClick,h=e.children,y=(0,u.default)(e,s);(0,f.warning)(!!(r||h),"Should have `component` prop or `children`."),(0,f.useInsertStyles)();var _=i.useContext(d.default),b=_.prefixCls,g=void 0===b?"anticon":b,k=_.rootClassName,x=(0,c.default)(k,g,n),C=(0,c.default)((0,l.default)({},"".concat(g,"-spin"),!!p)),N=(0,o.default)((0,o.default)({},f.svgBaseProps),{},{className:C,style:v?{msTransform:"rotate(".concat(v,"deg)"),transform:"rotate(".concat(v,"deg)")}:void 0,viewBox:a});a||delete N.viewBox;var O=m;return void 0===O&&w&&(O=-1),i.createElement("span",(0,o.default)((0,o.default)({role:"img"},y),{},{ref:t,tabIndex:O,onClick:w,className:x}),r?i.createElement(r,(0,o.default)({},N),h):h?((0,f.warning)(!!a||1===i.Children.count(h)&&i.isValidElement(h)&&"use"===i.Children.only(h).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),i.createElement("svg",(0,o.default)((0,o.default)({},N),{},{viewBox:a}),h)):null)});v.displayName="AntdIcon",t.default=v}}]);