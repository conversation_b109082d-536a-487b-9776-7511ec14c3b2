(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[711,775],{45652:function(e,i,t){var n=t(88668),l=t(47443),o=t(1196),r=t(74757),s=t(23593),a=t(21814);e.exports=function(e,i,t){var d=-1,u=l,c=e.length,f=!0,h=[],v=h;if(t)f=!1,u=o;else if(c>=200){var p=i?null:s(e);if(p)return a(p);f=!1,u=r,v=new n}else v=i?[]:h;e:for(;++d<c;){var g=e[d],m=i?i(g):g;if(g=t||0!==g?g:0,f&&m==m){for(var x=v.length;x--;)if(v[x]===m)continue e;i&&v.push(m),h.push(g)}else u(v,m,t)||(v!==h&&v.push(m),h.push(g))}return h}},23593:function(e,i,t){var n=t(58525),l=t(50308),o=t(21814),r=n&&1/o(new n([,-0]))[1]==1/0?function(e){return new n(e)}:l;e.exports=r},50361:function(e,i,t){var n=t(85990);e.exports=function(e){return n(e,5)}},14293:function(e){e.exports=function(e){return null==e}},81763:function(e,i,t){var n=t(44239),l=t(37005);e.exports=function(e){return"number"==typeof e||l(e)&&"[object Number]"==n(e)}},50308:function(e){e.exports=function(){}},44908:function(e,i,t){var n=t(45652);e.exports=function(e){return e&&e.length?n(e):[]}},79421:function(e,i,t){"use strict";t.d(i,{C9:function(){return S},Cf:function(){return w},ZP:function(){return T}});var n,l,o=t(46140),r=t(14293),s=t.n(r),a=t(50361),d=t.n(a),u=t(44908),c=t.n(u),f=t(89734),h=t.n(f),v=t(57557),p=t.n(v),g=t(81763),m=t.n(g);(n=l||(l={})).ARC="arc",n.AREA="area",n.BAR="bar",n.BOXPLOT="boxplot",n.CIRCLE="circle",n.ERRORBAND="errorband",n.ERRORBAR="errorbar",n.IMAGE="image",n.LINE="line",n.POINT="point",n.RECT="rect",n.RULE="rule",n.SQUARE="square",n.TEXT="text",n.TICK="tick",n.TRAIL="trail";let x="#434343",y="#65676c",C=["#7763CF","#444CE7","#1570EF","#0086C9","#3E4784","#E31B54","#EC4A0A","#EF8D0C","#EBC405","#5381AD"],b=[C[4],C[5],C[8],C[3],C[0]],k=C[2],E={mark:{tooltip:!0},font:"Roboto, Arial, Noto Sans, sans-serif",padding:{top:30,bottom:20,left:0,right:0},title:{color:"#262626",fontSize:14},axis:{labelPadding:0,labelOffset:0,labelFontSize:10,gridColor:"#d9d9d9",titleColor:x,labelColor:y,labelFont:" Roboto, Arial, Noto Sans, sans-serif"},axisX:{labelAngle:-45},line:{color:k},bar:{color:k},legend:{symbolLimit:15,columns:1,labelFontSize:10,labelColor:y,titleColor:x,titleFontSize:14},range:{category:C,ordinal:C,diverging:C,symbol:C,heatmap:C,ramp:C},point:{size:60,color:k}};class T{getChartSpec(){let e=this.getAllCategories(this.encoding);return e.length>this.options.categoriesLimit?null:(e.length<=5&&(this.encoding.color={...this.encoding.color,scale:{range:b}}),this.options.isHideLegend&&(this.encoding.color={...this.encoding.color,legend:null}),this.options.isHideTitle&&(this.title=null),this.data=this.transformDataValues(this.data,this.encoding),{$schema:this.$schema,title:this.title,data:this.data,mark:this.mark,width:this.options.width,height:this.options.height,autosize:this.autosize,encoding:this.encoding,params:this.params,transform:this.transform})}parseSpec(e){if(this.$schema=e.$schema,this.title=e.title,this.transform=e.transform,"mark"in e){let i="string"==typeof e.mark?{type:e.mark}:e.mark;this.addMark(i)}if("encoding"in e){var i;if(null===(i=this.options)||void 0===i?void 0:i.isShowTopCategories){let i=this.filterTopCategories(e.encoding);i&&(this.data=i)}this.addEncoding(e.encoding)}}addMark(e){let i={};"line"===e.type?i={point:this.options.point,tooltip:!0}:"arc"===e.type&&(i={innerRadius:this.options.donutInner}),this.mark={type:e.type,...i}}addEncoding(e){if(this.encoding=e,s()(this.encoding.color)){let i=["x","y"].find(i=>{var t;return(null===(t=e[i])||void 0===t?void 0:t.type)==="nominal"});if(i){let t=e[i];this.encoding.color={field:t.field,type:t.type}}}if("bar"===this.mark.type&&("stack"in this.encoding.y&&(this.encoding.y.stack=this.options.stack),"xOffset"in this.encoding)){let e=this.encoding.xOffset,i=null==e?void 0:e.title;i||(i=this.findFieldTitleInEncoding(this.encoding,null==e?void 0:e.field)),this.encoding.xOffset={...e,title:i}}this.addHoverHighlight(this.encoding)}addHoverHighlight(e){var i;let t=(null===(i=e.color)||void 0===i?void 0:i.condition)?e.color.condition:e.color;if(!(null==t?void 0:t.field)||!(null==t?void 0:t.type))return;this.params&&(null==t?void 0:t.field)&&(this.params[0].select.fields=[t.field]),this.encoding.opacity={condition:{param:"hover",value:1},value:.3};let n=null==t?void 0:t.title;n||(n=this.findFieldTitleInEncoding(this.encoding,null==t?void 0:t.field));let l={title:n,field:null==t?void 0:t.field,type:null==t?void 0:t.type,scale:{range:C}};this.encoding.color={...l,condition:{param:"hover",...p()(l,"scale")}}}filterTopCategories(e){let i=["xOffset","color","x","y"].filter(i=>{var t;return(null===(t=e[i])||void 0===t?void 0:t.type)==="nominal"}),t=["theta","x","y"].filter(i=>{var t;return(null===(t=e[i])||void 0===t?void 0:t.type)==="quantitative"});if(!i.length||!t.length)return;let n=d()(this.data.values),l=e[t[0]],o=h()(n,e=>{let i=e[l.field];return m()(i)?-i:0}),r=[];for(let t of i){let i=e[t];if(r.some(e=>e.field===i.field))continue;let n=o.map(e=>e[i.field]),l=c()(n).slice(0,this.options.categoriesLimit);r.push({field:i.field,values:l})}return{values:n.filter(e=>r.every(i=>i.values.includes(e[i.field])))}}getAllCategories(e){let i=["xOffset","color","x","y"].find(i=>{var t;return(null===(t=e[i])||void 0===t?void 0:t.type)==="nominal"});if(!i)return[];let t=e[i],n=this.data.values.map(e=>e[t.field]);return c()(n)}findFieldTitleInEncoding(e,i){var t;return(null===(t=e[["x","y","xOffset","color"].find(t=>{var n,l;return(null===(n=e[t])||void 0===n?void 0:n.field)===i&&(null===(l=e[t])||void 0===l?void 0:l.title)})])||void 0===t?void 0:t.title)||void 0}transformDataValues(e,i){var t,n;if((null==i?void 0:null===(t=i.x)||void 0===t?void 0:t.type)==="temporal"){let t=e.values.map(e=>({...e,[i.x.field]:this.transformTemporalValue(e[i.x.field])}));return{...e,values:t}}if((null==i?void 0:null===(n=i.y)||void 0===n?void 0:n.type)==="temporal"){let t=e.values.map(e=>({...e,[i.y.field]:this.transformTemporalValue(e[i.y.field])}));return{...e,values:t}}return e}transformTemporalValue(e){if(null==e)return e;let i="string"==typeof e?e:String(e);return i.includes("UTC")?i.replace(/\s+UTC([+-][0-9]+)?(:[0-9]+)?/,""):i}constructor(e,i){this.config=E,this.data=e.data,this.autosize={type:"fit",contains:"padding"},this.params=[{name:"hover",select:{type:"point",on:"mouseover",clear:"mouseout"}}],this.options={width:s()(null==i?void 0:i.width)?"container":i.width,height:s()(null==i?void 0:i.height)?"container":i.height,stack:s()(null==i?void 0:i.stack)?"zero":i.stack,point:!!s()(null==i?void 0:i.point)||i.point,donutInner:s()(null==i?void 0:i.donutInner)?60:i.donutInner,categoriesLimit:s()(null==i?void 0:i.categoriesLimit)?25:i.categoriesLimit,isShowTopCategories:!s()(null==i?void 0:i.isShowTopCategories)&&(null==i?void 0:i.isShowTopCategories),isHideLegend:!s()(null==i?void 0:i.isHideLegend)&&i.isHideLegend,isHideTitle:!s()(null==i?void 0:i.isHideTitle)&&i.isHideTitle};let t=d()(e);this.parseSpec(t)}}let R=(e,i)=>{if("bar"===e){var t,n;if(null==i?void 0:i.xOffset)return o.oX.GROUPED_BAR;if(!s()(null==i?void 0:null===(t=i.y)||void 0===t?void 0:t.stack)||!s()(null==i?void 0:null===(n=i.x)||void 0===n?void 0:n.stack))return o.oX.STACKED_BAR}else if("arc"===e)return o.oX.PIE;return e?e.toUpperCase():null},w=e=>{let i=null==e?void 0:e.chartSchema,t=(null==e?void 0:e.chartType)||null,n=null,l=null,o=null,r=null,s=null;if(i&&"encoding"in i){var a,d,u,c,f;let e=i.encoding;n=(null==e?void 0:null===(a=e.x)||void 0===a?void 0:a.field)||null,l=(null==e?void 0:null===(d=e.y)||void 0===d?void 0:d.field)||null,o=(null==e?void 0:null===(u=e.color)||void 0===u?void 0:u.field)||null,r=(null==e?void 0:null===(c=e.xOffset)||void 0===c?void 0:c.field)||null,s=(null==e?void 0:null===(f=e.theta)||void 0===f?void 0:f.field)||null,null===t&&(t=R("string"==typeof i.mark?i.mark:i.mark.type,e))}return{chartType:t,xAxis:n,yAxis:l,color:o,xOffset:r,theta:s}},S=e=>e?["x","y","xOffset","color"].reduce((i,t)=>{let n=e[t];return(null==n?void 0:n.field)&&(null==n?void 0:n.title)&&(i[null==n?void 0:n.field]=null==n?void 0:n.title),i},{}):{}},37711:function(e,i,t){"use strict";t.r(i),t.d(i,{default:function(){return E}});var n=t(85893),l=t(67294),o=t(90512),r=t(41609),s=t.n(r),a=t(62819),d=t(21367),u=t(29060),c=t(18496),f=t(6977),h=t(79421),v=t(7337),p=t.n(v),g=t(93181),m=t.n(g),x=t(92870),y=t.n(x),C=t(93646),b=t.n(C);let k={mode:"vega-lite",renderer:"svg",tooltip:{theme:"custom"},actions:{export:!0,editor:!1}};function E(e){let{className:i,spec:t,values:r,width:v=600,height:g=320,autoFilter:x,hideActions:C,hideTitle:E,hideLegend:T,forceUpdate:R,onReload:w,onEdit:S,onPin:j}=e,[A,L]=(0,l.useState)(null),[O,H]=(0,l.useState)(!1),I=(0,l.useRef)(null),N=(0,l.useRef)(null),z=(0,l.useMemo)(()=>{var e;if(!t||!r)return;let i=new h.ZP({...t,data:{values:r}},{donutInner:A,isShowTopCategories:x||O,isHideLegend:T,isHideTitle:E}),n=i.getChartSpec();return s()(null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.values)?null:(0,c.compile)(n,{config:i.config}).spec},[t,r,O,A,R]);return((0,l.useEffect)(()=>(N.current&&z&&(0,f.ZP)(N.current,z,k).then(e=>{I.current=e}),()=>{I.current&&I.current.finalize()}),[z,R]),(0,l.useEffect)(()=>{N.current&&L(.15*N.current.clientHeight)},[R]),null===z)?0===r.length?(0,n.jsx)("div",{children:"No available data"}):(0,n.jsx)(a.default,{className:"mt-6 mb-4 mx-4",message:(0,n.jsxs)("div",{className:"d-flex align-center justify-space-between",children:[(0,n.jsx)("div",{children:"There are too many categories to display effectively. Click 'Show top 25' to view the top results, or ask a follow-up question to focus on a specific group or filter results."}),(0,n.jsx)(d.default,{size:"small",icon:(0,n.jsx)(y(),{}),onClick:()=>{H(!O)},children:"Show top 25"})]}),type:"warning"}):(0,n.jsxs)("div",{className:(0,o.Z)("adm-chart",{"adm-chart--no-actions":C},i),style:{width:v},children:[(!!w||!!S||!!j)&&(0,n.jsxs)("div",{className:"adm-chart-additional d-flex justify-content-between align-center",children:[!!w&&(0,n.jsx)(u.default,{title:"Regenerate chart",children:(0,n.jsx)("button",{onClick:w,children:(0,n.jsx)(p(),{})})}),!!S&&(0,n.jsx)(u.default,{title:"Edit chart",children:(0,n.jsx)("button",{onClick:S,children:(0,n.jsx)(m(),{})})}),!!j&&(0,n.jsx)(u.default,{title:"Pin chart to dashboard",children:(0,n.jsx)("button",{onClick:j,children:(0,n.jsx)(b(),{})})})]}),(0,n.jsx)("div",{style:{width:v,height:g},ref:N})]})}}}]);