(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[518],{35208:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2a7.63 7.63 0 01-2.9-6.1V351c0-6.8 7.9-10.5 13.1-6.1l192 160.9c3.9 3.2 3.9 9.1 0 12.3zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"}}]},name:"code",theme:"filled"}},31499:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r,i=(r=n(20685))&&r.__esModule?r:{default:r};e.default=i,t.exports=i},20685:function(t,e,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=r(n(42122)),u=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=i(t)&&"function"!=typeof t)return{default:t};var n=c(void 0);if(n&&n.has(t))return n.get(t);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&({}).hasOwnProperty.call(t,u)){var a=o?Object.getOwnPropertyDescriptor(t,u):null;a&&(a.get||a.set)?Object.defineProperty(r,u,a):r[u]=t[u]}return r.default=t,n&&n.set(t,r),r}(n(67294)),a=r(n(35208)),s=r(n(3247));function c(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(c=function(t){return t?n:e})(t)}var l=u.forwardRef(function(t,e){return u.createElement(s.default,(0,o.default)((0,o.default)({},t),{},{ref:e,icon:a.default}))});e.default=l},16755:function(t,e,n){"use strict";var r=n(64836),i=n(18698);e.Z=void 0;var o=r(n(10434)),u=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!=typeof t)return{default:t};var n=f(void 0);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var a=o?Object.getOwnPropertyDescriptor(t,u):null;a&&(a.get||a.set)?Object.defineProperty(r,u,a):r[u]=t[u]}return r.default=t,n&&n.set(t,r),r}(n(67294)),a=r(n(29060)),s=n(31407),c=n(6456),l=n(43368);function f(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(f=function(t){return t?n:e})(t)}var h=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)0>e.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n},p=u.forwardRef(function(t,e){var n=t.prefixCls,r=t.title,i=t.content,f=h(t,["prefixCls","title","content"]),p=u.useContext(s.ConfigContext).getPrefixCls,d=p("popover",n),v=p();return u.createElement(a.default,(0,o.default)({},f,{prefixCls:d,ref:e,overlay:function(t){if(r||i)return u.createElement(u.Fragment,null,r&&u.createElement("div",{className:"".concat(t,"-title")},(0,c.getRenderPropValue)(r)),u.createElement("div",{className:"".concat(t,"-inner-content")},(0,c.getRenderPropValue)(i)))}(d),transitionName:(0,l.getTransitionName)(v,"zoom-big",f.transitionName)}))});p.displayName="Popover",p.defaultProps={placement:"top",trigger:"hover",mouseEnterDelay:.1,mouseLeaveDelay:.1,overlayStyle:{}},e.Z=p},73242:function(){},83:function(t,e,n){"use strict";var r=n(67294),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=r.useState,u=r.useEffect,a=r.useLayoutEffect,s=r.useDebugValue;function c(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!i(t,n)}catch(t){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),r=o({inst:{value:n,getSnapshot:e}}),i=r[0].inst,l=r[1];return a(function(){i.value=n,i.getSnapshot=e,c(i)&&l({inst:i})},[t,n,e]),u(function(){return c(i)&&l({inst:i}),t(function(){c(i)&&l({inst:i})})},[t]),s(n),n};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:l},26251:function(t,e,n){"use strict";var r=n(67294),i=n(61688),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},u=i.useSyncExternalStore,a=r.useRef,s=r.useEffect,c=r.useMemo,l=r.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,n,r,i){var f=a(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var p=u(t,(f=c(function(){function t(t){if(!s){if(s=!0,u=t,t=r(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return a=e}return a=t}if(e=a,o(u,t))return e;var n=r(t);return void 0!==i&&i(e,n)?(u=t,e):(u=t,a=n)}var u,a,s=!1,c=void 0===n?null:n;return[function(){return t(e())},null===c?void 0:function(){return t(c())}]},[e,n,r,i]))[0],f[1]);return s(function(){h.hasValue=!0,h.value=p},[p]),l(p),p}},61688:function(t,e,n){"use strict";t.exports=n(83)},52798:function(t,e,n){"use strict";t.exports=n(26251)},59819:function(t,e,n){"use strict";n.d(e,{A:function(){return v}});var r,i,o=n(67294),u=n(83840),a=n(36851),s=n(76248);function c({color:t,dimensions:e,lineWidth:n}){return o.createElement("path",{stroke:t,strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})}function l({color:t,radius:e}){return o.createElement("circle",{cx:e,cy:e,r:e,fill:t})}(r=i||(i={})).Lines="lines",r.Dots="dots",r.Cross="cross";let f={[i.Dots]:"#91919a",[i.Lines]:"#eee",[i.Cross]:"#e2e2e2"},h={[i.Dots]:1,[i.Lines]:1,[i.Cross]:6},p=t=>({transform:t.transform,patternId:`pattern-${t.rfId}`});function d({id:t,variant:e=i.Dots,gap:n=20,size:r,lineWidth:d=1,offset:v=2,color:m,style:y,className:_}){let g=(0,o.useRef)(null),{transform:w,patternId:b}=(0,a.oR)(p,s.X),x=m||f[e],E=r||h[e],z=e===i.Dots,Z=e===i.Cross,S=Array.isArray(n)?n:[n,n],A=[S[0]*w[2]||1,S[1]*w[2]||1],k=E*w[2],P=Z?[k,k]:A,M=z?[k/v,k/v]:[P[0]/v,P[1]/v];return o.createElement("svg",{className:(0,u.Z)(["react-flow__background",_]),style:{...y,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:g,"data-testid":"rf__background"},o.createElement("pattern",{id:b+t,x:w[0]%A[0],y:w[1]%A[1],width:A[0],height:A[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${M[0]},-${M[1]})`},z?o.createElement(l,{color:x,radius:k/v}):o.createElement(c,{dimensions:P,color:x,lineWidth:d})),o.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${b+t})`}))}d.displayName="Background";var v=(0,o.memo)(d)},24885:function(t,e,n){"use strict";n.d(e,{B:function(){return h},Z:function(){return v}});var r=n(67294),i=n(83840),o=n(76248),u=n(36851);function a(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},r.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function s(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},r.createElement("path",{d:"M0 0h32v4.2H0z"}))}function c(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},r.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function l(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function f(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let h=({children:t,className:e,...n})=>r.createElement("button",{type:"button",className:(0,i.Z)(["react-flow__controls-button",e]),...n},t);h.displayName="ControlButton";let p=t=>({isInteractive:t.nodesDraggable||t.nodesConnectable||t.elementsSelectable,minZoomReached:t.transform[2]<=t.minZoom,maxZoomReached:t.transform[2]>=t.maxZoom}),d=({style:t,showZoom:e=!0,showFitView:n=!0,showInteractive:d=!0,fitViewOptions:v,onZoomIn:m,onZoomOut:y,onFitView:_,onInteractiveChange:g,className:w,children:b,position:x="bottom-left"})=>{let E=(0,u.AC)(),[z,Z]=(0,r.useState)(!1),{isInteractive:S,minZoomReached:A,maxZoomReached:k}=(0,u.oR)(p,o.X),{zoomIn:P,zoomOut:M,fitView:C}=(0,u._K)();return((0,r.useEffect)(()=>{Z(!0)},[]),z)?r.createElement(u.s_,{className:(0,i.Z)(["react-flow__controls",w]),position:x,style:t,"data-testid":"rf__controls"},e&&r.createElement(r.Fragment,null,r.createElement(h,{onClick:()=>{P(),m?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:k},r.createElement(a,null)),r.createElement(h,{onClick:()=>{M(),y?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:A},r.createElement(s,null))),n&&r.createElement(h,{className:"react-flow__controls-fitview",onClick:()=>{C(v),_?.()},title:"fit view","aria-label":"fit view"},r.createElement(c,null)),d&&r.createElement(h,{className:"react-flow__controls-interactive",onClick:()=>{E.setState({nodesDraggable:!S,nodesConnectable:!S,elementsSelectable:!S}),g?.(!S)},title:"toggle interactivity","aria-label":"toggle interactivity"},S?r.createElement(f,null):r.createElement(l,null)),b):null};d.displayName="Controls";var v=(0,r.memo)(d)},70866:function(t,e,n){"use strict";n.d(e,{a:function(){return _}});var r=n(67294),i=n(83840),o=n(76248),u=n(37882),a=n(23838),s=n(46939),c=n(36851);let l=({id:t,x:e,y:n,width:o,height:u,style:a,color:s,strokeColor:c,strokeWidth:l,className:f,borderRadius:h,shapeRendering:p,onClick:d,selected:v})=>{let{background:m,backgroundColor:y}=a||{};return r.createElement("rect",{className:(0,i.Z)(["react-flow__minimap-node",{selected:v},f]),x:e,y:n,rx:h,ry:h,width:o,height:u,fill:s||m||y,stroke:c,strokeWidth:l,shapeRendering:p,onClick:d?e=>d(e,t):void 0})};l.displayName="MiniMapNode";var f=(0,r.memo)(l);let h=t=>t.nodeOrigin,p=t=>t.getNodes().filter(t=>!t.hidden&&t.width&&t.height),d=t=>t instanceof Function?t:()=>t;var v=(0,r.memo)(function({nodeStrokeColor:t="transparent",nodeColor:e="#e2e2e2",nodeClassName:n="",nodeBorderRadius:i=5,nodeStrokeWidth:u=2,nodeComponent:a=f,onClick:s}){let l=(0,c.oR)(p,o.X),v=(0,c.oR)(h),m=d(e),y=d(t),_=d(n),g="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return r.createElement(r.Fragment,null,l.map(t=>{let{x:e,y:n}=(0,c.VP)(t,v).positionAbsolute;return r.createElement(a,{key:t.id,x:e,y:n,width:t.width,height:t.height,style:t.style,selected:t.selected,className:_(t),color:m(t),borderRadius:i,strokeColor:y(t),strokeWidth:u,shapeRendering:g,onClick:s,id:t.id})}))});let m=t=>{let e=t.getNodes(),n={x:-t.transform[0]/t.transform[2],y:-t.transform[1]/t.transform[2],width:t.width/t.transform[2],height:t.height/t.transform[2]};return{viewBB:n,boundingRect:e.length>0?(0,c.oI)((0,c.RX)(e,t.nodeOrigin),n):n,rfId:t.rfId}};function y({style:t,className:e,nodeStrokeColor:n="transparent",nodeColor:l="#e2e2e2",nodeClassName:f="",nodeBorderRadius:h=5,nodeStrokeWidth:p=2,nodeComponent:d,maskColor:y="rgb(240, 240, 240, 0.6)",maskStrokeColor:_="none",maskStrokeWidth:g=1,position:w="bottom-right",onClick:b,onNodeClick:x,pannable:E=!1,zoomable:z=!1,ariaLabel:Z="React Flow mini map",inversePan:S=!1,zoomStep:A=10,offsetScale:k=5}){let P=(0,c.AC)(),M=(0,r.useRef)(null),{boundingRect:C,viewBB:N,rfId:O}=(0,c.oR)(m,o.X),T=t?.width??200,j=t?.height??150,D=Math.max(C.width/T,C.height/j),V=D*T,R=D*j,B=k*D,$=C.x-(V-C.width)/2-B,H=C.y-(R-C.height)/2-B,X=V+2*B,Y=R+2*B,I=`react-flow__minimap-desc-${O}`,L=(0,r.useRef)(0);L.current=D,(0,r.useEffect)(()=>{if(M.current){let t=(0,a.Z)(M.current),e=(0,u.sP)().on("zoom",E?t=>{let{transform:e,d3Selection:n,d3Zoom:r,translateExtent:i,width:o,height:a}=P.getState();if("mousemove"!==t.sourceEvent.type||!n||!r)return;let s=L.current*Math.max(1,e[2])*(S?-1:1),c={x:e[0]-t.sourceEvent.movementX*s,y:e[1]-t.sourceEvent.movementY*s},l=u.CR.translate(c.x,c.y).scale(e[2]),f=r.constrain()(l,[[0,0],[o,a]],i);r.transform(n,f)}:null).on("zoom.wheel",z?t=>{let{transform:e,d3Selection:n,d3Zoom:r}=P.getState();if("wheel"!==t.sourceEvent.type||!n||!r)return;let i=-t.sourceEvent.deltaY*(1===t.sourceEvent.deltaMode?.05:t.sourceEvent.deltaMode?1:.002)*A,o=e[2]*Math.pow(2,i);r.scaleTo(n,o)}:null);return t.call(e),()=>{t.on("zoom",null)}}},[E,z,S,A]);let W=b?t=>{let e=(0,s.Z)(t);b(t,{x:e[0],y:e[1]})}:void 0;return r.createElement(c.s_,{position:w,style:t,className:(0,i.Z)(["react-flow__minimap",e]),"data-testid":"rf__minimap"},r.createElement("svg",{width:T,height:j,viewBox:`${$} ${H} ${X} ${Y}`,role:"img","aria-labelledby":I,ref:M,onClick:W},Z&&r.createElement("title",{id:I},Z),r.createElement(v,{onClick:x?(t,e)=>{x(t,P.getState().nodeInternals.get(e))}:void 0,nodeColor:l,nodeStrokeColor:n,nodeBorderRadius:h,nodeClassName:f,nodeStrokeWidth:p,nodeComponent:d}),r.createElement("path",{className:"react-flow__minimap-mask",d:`M${$-B},${H-B}h${X+2*B}v${Y+2*B}h${-X-2*B}z
        M${N.x},${N.y}h${N.width}v${N.height}h${-N.width}z`,fill:y,fillRule:"evenodd",stroke:_,strokeWidth:g,pointerEvents:"none"})))}y.displayName="MiniMap";var _=(0,r.memo)(y)},83840:function(t,e,n){"use strict";n.d(e,{Z:function(){return function t(e){if("string"==typeof e||"number"==typeof e)return""+e;let n="";if(Array.isArray(e))for(let r=0,i;r<e.length;r++)""!==(i=t(e[r]))&&(n+=(n&&" ")+i);else for(let t in e)e[t]&&(n+=(n&&" ")+t);return n}}})},62487:function(t,e,n){"use strict";n.d(e,{Z:function(){return d}});var r=n(96057),i=n(23838),o=n(46939),u=n(22718),a=n(25860),s=t=>()=>t;function c(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:o,x:u,y:a,dx:s,dy:c,dispatch:l}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:l}})}function l(t){return!t.ctrlKey&&!t.button}function f(){return this.parentNode}function h(t,e){return null==e?{x:t.x,y:t.y}:e}function p(){return navigator.maxTouchPoints||"ontouchstart"in this}function d(){var t,e,n,d,v=l,m=f,y=h,_=p,g={},w=(0,r.Z)("start","drag","end"),b=0,x=0;function E(t){t.on("mousedown.drag",z).filter(_).on("touchstart.drag",A).on("touchmove.drag",k,a.Q7).on("touchend.drag touchcancel.drag",P).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function z(r,o){if(!d&&v.call(this,r,o)){var s=M(this,m.call(this,r,o),r,o,"mouse");s&&((0,i.Z)(r.view).on("mousemove.drag",Z,a.Dd).on("mouseup.drag",S,a.Dd),(0,u.Z)(r.view),(0,a.rG)(r),n=!1,t=r.clientX,e=r.clientY,s("start",r))}}function Z(r){if((0,a.ZP)(r),!n){var i=r.clientX-t,o=r.clientY-e;n=i*i+o*o>x}g.mouse("drag",r)}function S(t){(0,i.Z)(t.view).on("mousemove.drag mouseup.drag",null),(0,u.D)(t.view,n),(0,a.ZP)(t),g.mouse("end",t)}function A(t,e){if(v.call(this,t,e)){var n,r,i=t.changedTouches,o=m.call(this,t,e),u=i.length;for(n=0;n<u;++n)(r=M(this,o,t,e,i[n].identifier,i[n]))&&((0,a.rG)(t),r("start",t,i[n]))}}function k(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=g[r[e].identifier])&&((0,a.ZP)(t),n("drag",t,r[e]))}function P(t){var e,n,r=t.changedTouches,i=r.length;for(d&&clearTimeout(d),d=setTimeout(function(){d=null},500),e=0;e<i;++e)(n=g[r[e].identifier])&&((0,a.rG)(t),n("end",t,r[e]))}function M(t,e,n,r,i,u){var a,s,l,f=w.copy(),h=(0,o.Z)(u||n,e);if(null!=(l=y.call(t,new c("beforestart",{sourceEvent:n,target:E,identifier:i,active:b,x:h[0],y:h[1],dx:0,dy:0,dispatch:f}),r)))return a=l.x-h[0]||0,s=l.y-h[1]||0,function n(u,p,d){var v,m=h;switch(u){case"start":g[i]=n,v=b++;break;case"end":delete g[i],--b;case"drag":h=(0,o.Z)(d||p,e),v=b}f.call(u,t,new c(u,{sourceEvent:p,subject:l,target:E,identifier:i,active:v,x:h[0]+a,y:h[1]+s,dx:h[0]-m[0],dy:h[1]-m[1],dispatch:f}),r)}}return E.filter=function(t){return arguments.length?(v="function"==typeof t?t:s(!!t),E):v},E.container=function(t){return arguments.length?(m="function"==typeof t?t:s(t),E):m},E.subject=function(t){return arguments.length?(y="function"==typeof t?t:s(t),E):y},E.touchable=function(t){return arguments.length?(_="function"==typeof t?t:s(!!t),E):_},E.on=function(){var t=w.on.apply(w,arguments);return t===w?E:t},E.clickDistance=function(t){return arguments.length?(x=(t=+t)*t,E):Math.sqrt(x)},E}c.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},22718:function(t,e,n){"use strict";n.d(e,{D:function(){return u},Z:function(){return o}});var r=n(23838),i=n(25860);function o(t){var e=t.document.documentElement,n=(0,r.Z)(t).on("dragstart.drag",i.ZP,i.Dd);"onselectstart"in e?n.on("selectstart.drag",i.ZP,i.Dd):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function u(t,e){var n=t.document.documentElement,o=(0,r.Z)(t).on("dragstart.drag",null);e&&(o.on("click.drag",i.ZP,i.Dd),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}},25860:function(t,e,n){"use strict";n.d(e,{Dd:function(){return i},Q7:function(){return r},ZP:function(){return u},rG:function(){return o}});let r={passive:!1},i={capture:!0,passive:!1};function o(t){t.stopImmediatePropagation()}function u(t){t.preventDefault(),t.stopImmediatePropagation()}},24421:function(t,e,n){"use strict";function r(t){return function(){return this.matches(t)}}function i(t){return function(e){return e.matches(t)}}n.d(e,{P:function(){return i},Z:function(){return r}})},31663:function(t,e,n){"use strict";n.d(e,{Z:function(){return i}});var r=n(91226);function i(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),r.Z.hasOwnProperty(e)?{space:r.Z[e],local:t}:t}},91226:function(t,e,n){"use strict";n.d(e,{P:function(){return r}});var r="http://www.w3.org/1999/xhtml";e.Z={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},46939:function(t,e,n){"use strict";function r(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}n.d(e,{Z:function(){return r}})},23838:function(t,e,n){"use strict";n.d(e,{Z:function(){return i}});var r=n(21680);function i(t){return"string"==typeof t?new r.Y1([[document.querySelector(t)]],[document.documentElement]):new r.Y1([[t]],r.Jz)}},21680:function(t,e,n){"use strict";n.d(e,{Y1:function(){return R},ZP:function(){return $},Jz:function(){return V}});var r=n(83010),i=n(19701),o=n(24421),u=Array.prototype.find;function a(){return this.firstElementChild}var s=Array.prototype.filter;function c(){return Array.from(this.children)}function l(t){return Array(t.length)}function f(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function h(t,e,n,r,i,o){for(var u,a=0,s=e.length,c=o.length;a<c;++a)(u=e[a])?(u.__data__=o[a],r[a]=u):n[a]=new f(t,o[a]);for(;a<s;++a)(u=e[a])&&(i[a]=u)}function p(t,e,n,r,i,o,u){var a,s,c,l=new Map,h=e.length,p=o.length,d=Array(h);for(a=0;a<h;++a)(s=e[a])&&(d[a]=c=u.call(s,s.__data__,a,e)+"",l.has(c)?i[a]=s:l.set(c,s));for(a=0;a<p;++a)c=u.call(t,o[a],a,o)+"",(s=l.get(c))?(r[a]=s,s.__data__=o[a],l.delete(c)):n[a]=new f(t,o[a]);for(a=0;a<h;++a)(s=e[a])&&l.get(d[a])===s&&(i[a]=s)}function d(t){return t.__data__}function v(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}f.prototype={constructor:f,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var m=n(31663),y=n(52627);function _(t){return t.trim().split(/^|\s+/)}function g(t){return t.classList||new w(t)}function w(t){this._node=t,this._names=_(t.getAttribute("class")||"")}function b(t,e){for(var n=g(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function x(t,e){for(var n=g(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function E(){this.textContent=""}function z(){this.innerHTML=""}function Z(){this.nextSibling&&this.parentNode.appendChild(this)}function S(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}w.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var A=n(91226);function k(t){var e=(0,m.Z)(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===A.P&&e.documentElement.namespaceURI===A.P?e.createElement(t):e.createElementNS(n,t)}})(e)}function P(){return null}function M(){var t=this.parentNode;t&&t.removeChild(this)}function C(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function N(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function O(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function T(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var u=0,a=i.length;u<a;++u)if((r=i[u]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}var j=n(89920);function D(t,e,n){var r=(0,j.Z)(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}var V=[null];function R(t,e){this._groups=t,this._parents=e}function B(){return new R([[document.documentElement]],V)}R.prototype=B.prototype={constructor:R,select:function(t){"function"!=typeof t&&(t=(0,r.Z)(t));for(var e=this._groups,n=e.length,i=Array(n),o=0;o<n;++o)for(var u,a,s=e[o],c=s.length,l=i[o]=Array(c),f=0;f<c;++f)(u=s[f])&&(a=t.call(u,u.__data__,f,s))&&("__data__"in u&&(a.__data__=u.__data__),l[f]=a);return new R(i,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.Z)(t);for(var n=this._groups,r=n.length,o=[],u=[],a=0;a<r;++a)for(var s,c=n[a],l=c.length,f=0;f<l;++f)(s=c[f])&&(o.push(t.call(s,s.__data__,f,c)),u.push(s));return new R(o,u)},selectChild:function(t){var e;return this.select(null==t?a:(e="function"==typeof t?t:(0,o.P)(t),function(){return u.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?c:(e="function"==typeof t?t:(0,o.P)(t),function(){return s.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.Z)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var u,a=e[i],s=a.length,c=r[i]=[],l=0;l<s;++l)(u=a[l])&&t.call(u,u.__data__,l,a)&&c.push(u);return new R(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,d);var n=e?p:h,r=this._parents,i=this._groups;"function"!=typeof t&&(b=t,t=function(){return b});for(var o=i.length,u=Array(o),a=Array(o),s=Array(o),c=0;c<o;++c){var l=r[c],f=i[c],v=f.length,m="object"==typeof(w=t.call(l,l&&l.__data__,c,r))&&"length"in w?w:Array.from(w),y=m.length,_=a[c]=Array(y),g=u[c]=Array(y);n(l,f,_,g,s[c]=Array(v),m,e);for(var w,b,x,E,z=0,Z=0;z<y;++z)if(x=_[z]){for(z>=Z&&(Z=z+1);!(E=g[Z])&&++Z<y;);x._next=E||null}}return(u=new R(u,r))._enter=a,u._exit=s,u},enter:function(){return new R(this._enter||this._groups.map(l),this._parents)},exit:function(){return new R(this._exit||this._groups.map(l),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,u=Math.min(i,o),a=Array(i),s=0;s<u;++s)for(var c,l=n[s],f=r[s],h=l.length,p=a[s]=Array(h),d=0;d<h;++d)(c=l[d]||f[d])&&(p[d]=c);for(;s<i;++s)a[s]=n[s];return new R(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=v);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var u,a=n[o],s=a.length,c=i[o]=Array(s),l=0;l<s;++l)(u=a[l])&&(c[l]=u);c.sort(e)}return new R(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,e){var n=(0,m.Z)(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:y.Z,property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=_(t+"");if(arguments.length<2){for(var r=g(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?b:x)(this,t)}}:e?function(t){return function(){b(this,t)}}:function(t){return function(){x(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?E:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?z:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(Z)},lower:function(){return this.each(S)},append:function(t){var e="function"==typeof t?t:k(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:k(t),i=null==e?P:"function"==typeof e?e:(0,r.Z)(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(M)},clone:function(t){return this.select(t?N:C)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),u=o.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var s,c=0,l=a.length;c<l;++c)for(r=0,s=a[c];r<u;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,a=e?T:O;r<u;++r)this.each(a(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return D(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return D(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var $=B},52627:function(t,e,n){"use strict";n.d(e,{S:function(){return o},Z:function(){return i}});var r=n(89920);function i(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):o(this.node(),t)}function o(t,e){return t.style.getPropertyValue(e)||(0,r.Z)(t).getComputedStyle(t,null).getPropertyValue(e)}},83010:function(t,e,n){"use strict";function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}n.d(e,{Z:function(){return i}})},19701:function(t,e,n){"use strict";function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}n.d(e,{Z:function(){return i}})},89920:function(t,e,n){"use strict";function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}n.d(e,{Z:function(){return r}})},37882:function(t,e,n){"use strict";n.d(e,{sP:function(){return G},CR:function(){return B}});var r=n(96057),i=n(22718),o=n(8167),u=n(23838),a=n(46939),s=n(21680),c=n(91739);function l(t,e,n){var r=new c.B7;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}var f=(0,r.Z)("start","end","cancel","interrupt"),h=[];function p(t,e,n,r,i,o){var u=t.__transition;if(u){if(n in u)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(s){var c,f,h,p;if(1!==n.state)return a();for(c in i)if((p=i[c]).name===n.name){if(3===p.state)return l(o);4===p.state?(p.state=6,p.timer.stop(),p.on.call("interrupt",t,t.__data__,p.index,p.group),delete i[c]):+c<e&&(p.state=6,p.timer.stop(),p.on.call("cancel",t,t.__data__,p.index,p.group),delete i[c])}if(l(function(){3===n.state&&(n.state=4,n.timer.restart(u,n.delay,n.time),u(s))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(c=0,n.state=3,r=Array(h=n.tween.length),f=-1;c<h;++c)(p=n.tween[c].value.call(t,t.__data__,n.index,n.group))&&(r[++f]=p);r.length=f+1}}function u(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(a),n.state=5,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),a())}function a(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=(0,c.HT)(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:f,tween:h,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function d(t,e){var n=m(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function v(t,e){var n=m(t,e);if(n.state>3)throw Error("too late; already running");return n}function m(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function y(t,e){var n,r,i,o=t.__transition,u=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){u=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}u&&delete t.__transition}}var _=n(99321),g=n(31663);function w(t,e,n){var r=t._id;return t.each(function(){var t=v(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return m(t,r).value[e]}}var b=n(4447),x=n(68063),E=n(6354),z=n(16773);function Z(t,e){var n;return("number"==typeof e?x.Z:e instanceof b.ZP?E.ZP:(n=(0,b.ZP)(e))?(e=n,E.ZP):z.Z)(t,e)}var S=n(24421),A=n(83010),k=n(19701),P=s.ZP.prototype.constructor,M=n(52627);function C(t){return function(){this.style.removeProperty(t)}}var N=0;function O(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var T=s.ZP.prototype;O.prototype=(function(t){return(0,s.ZP)().transition(t)}).prototype={constructor:O,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,A.Z)(t));for(var r=this._groups,i=r.length,o=Array(i),u=0;u<i;++u)for(var a,s,c=r[u],l=c.length,f=o[u]=Array(l),h=0;h<l;++h)(a=c[h])&&(s=t.call(a,a.__data__,h,c))&&("__data__"in a&&(s.__data__=a.__data__),f[h]=s,p(f[h],e,n,h,f,m(a,n)));return new O(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,k.Z)(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var s,c=r[a],l=c.length,f=0;f<l;++f)if(s=c[f]){for(var h,d=t.call(s,s.__data__,f,c),v=m(s,n),y=0,_=d.length;y<_;++y)(h=d[y])&&p(h,e,n,y,d,v);o.push(d),u.push(s)}return new O(o,u,e,n)},selectChild:T.selectChild,selectChildren:T.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,S.Z)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,u=e[i],a=u.length,s=r[i]=[],c=0;c<a;++c)(o=u[c])&&t.call(o,o.__data__,c,u)&&s.push(o);return new O(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),u=Array(r),a=0;a<o;++a)for(var s,c=e[a],l=n[a],f=c.length,h=u[a]=Array(f),p=0;p<f;++p)(s=c[p]||l[p])&&(h[p]=s);for(;a<r;++a)u[a]=e[a];return new O(u,this._parents,this._name,this._id)},selection:function(){return new P(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++N,r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,c=0;c<s;++c)if(u=a[c]){var l=m(u,e);p(u,t,n,c,a,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new O(r,this._parents,t,n)},call:T.call,nodes:T.nodes,node:T.node,size:T.size,empty:T.empty,each:T.each,on:function(t,e){var n,r,i,o=this._id;return arguments.length<2?m(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?d:v,function(){var u=i(this,o),a=u.on;a!==n&&(r=(n=a).copy()).on(t,e),u.on=r}))},attr:function(t,e){var n=(0,g.Z)(t),r="transform"===n?_.w:Z;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var u,a,s=n(this);return null==s?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=e(r=u,s))}}:function(t,e,n){var r,i,o;return function(){var u,a,s=n(this);return null==s?void this.removeAttribute(t):(u=this.getAttribute(t))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=e(r=u,s))}})(n,r,w(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=e(r=u,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=e(r=u,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=(0,g.Z)(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,u,a,s,c,l,f,h,p,d,m,y,g,b,x,E,z,S,A,k="transform"==(t+="")?_.Y:Z;return null==e?this.styleTween(t,(r=t,function(){var t=(0,M.S)(this,r),e=(this.style.removeProperty(r),(0,M.S)(this,r));return t===e?null:t===i&&e===o?u:u=k(i=t,o=e)})).on("end.style."+t,C(t)):"function"==typeof e?this.styleTween(t,(a=t,s=w(this,"style."+t,e),function(){var t=(0,M.S)(this,a),e=s(this),n=e+"";return null==e&&(this.style.removeProperty(a),n=e=(0,M.S)(this,a)),t===n?null:t===c&&n===l?f:(l=n,f=k(c=t,e))})).each((h=this._id,x="end."+(b="style."+(p=t)),function(){var t=v(this,h),e=t.on,n=null==t.value[b]?g||(g=C(p)):void 0;(e!==d||y!==n)&&(m=(d=e).copy()).on(x,y=n),t.on=m})):this.styleTween(t,(E=t,A=e+"",function(){var t=(0,M.S)(this,E);return t===A?null:t===z?S:S=k(z=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=w(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=m(this.node(),n).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=v(this,t),o=i.tween;if(o!==n){r=n=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===e){(r=r.slice()).splice(u,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=v(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:e,value:n},s=0,c=i.length;s<c;++s)if(i[s].name===e){i[s]=a;break}s===c&&i.push(a)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){d(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e=+e,function(){d(this,t).delay=e}})(e,t)):m(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){v(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e=+e,function(){v(this,t).duration=e}})(e,t)):m(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){v(this,t).ease=e}}(e,t)):m(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();v(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,u){var a={value:u},s={value:function(){0==--i&&o()}};n.each(function(){var n=v(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(a),e._.interrupt.push(a),e._.end.push(s)),n.on=e}),0===i&&o()})},[Symbol.iterator]:T[Symbol.iterator]};var j={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};s.ZP.prototype.interrupt=function(t){return this.each(function(){y(this,t)})},s.ZP.prototype.transition=function(t){var e,n;t instanceof O?(e=t._id,t=t._name):(e=++N,(n=j).time=(0,c.zO)(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)(u=a[l])&&p(u,t,e,l,a,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(u,e));return new O(r,this._parents,t,e)};var D=t=>()=>t;function V(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function R(t,e,n){this.k=t,this.x=e,this.y=n}R.prototype={constructor:R,scale:function(t){return 1===t?this:new R(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new R(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var B=new R(1,0,0);function $(t){t.stopImmediatePropagation()}function H(t){t.preventDefault(),t.stopImmediatePropagation()}function X(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function Y(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function I(){return this.__zoom||B}function L(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function W(){return navigator.maxTouchPoints||"ontouchstart"in this}function q(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],u=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function G(){var t,e,n,s=X,c=Y,l=q,f=L,h=W,p=[0,1/0],d=[[-1/0,-1/0],[1/0,1/0]],v=250,m=o.Z,_=(0,r.Z)("start","zoom","end"),g=0,w=10;function b(t){t.property("__zoom",I).on("wheel.zoom",k,{passive:!1}).on("mousedown.zoom",P).on("dblclick.zoom",M).filter(h).on("touchstart.zoom",C).on("touchmove.zoom",N).on("touchend.zoom touchcancel.zoom",O).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,e){return(e=Math.max(p[0],Math.min(p[1],e)))===t.k?t:new R(e,t.x,t.y)}function E(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new R(t.k,r,i)}function z(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function Z(t,e,n,r){t.on("start.zoom",function(){S(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){S(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=S(this,t).event(r),o=c.apply(this,t),u=null==n?z(o):"function"==typeof n?n.apply(this,t):n,a=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),s=this.__zoom,l="function"==typeof e?e.apply(this,t):e,f=m(s.invert(u).concat(a/s.k),l.invert(u).concat(a/l.k));return function(t){if(1===t)t=l;else{var e=f(t),n=a/e[2];t=new R(n,u[0]-e[0]*n,u[1]-e[1]*n)}i.zoom(null,t)}})}function S(t,e,n){return!n&&t.__zooming||new A(t,e)}function A(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=c.apply(t,e),this.taps=0}function k(t,...e){if(s.apply(this,arguments)){var n=S(this,e).event(t),r=this.__zoom,i=Math.max(p[0],Math.min(p[1],r.k*Math.pow(2,f.apply(this,arguments)))),o=(0,a.Z)(t);if(n.wheel)(n.mouse[0][0]!==o[0]||n.mouse[0][1]!==o[1])&&(n.mouse[1]=r.invert(n.mouse[0]=o)),clearTimeout(n.wheel);else{if(r.k===i)return;n.mouse=[o,r.invert(o)],y(this),n.start()}H(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",l(E(x(r,i),n.mouse[0],n.mouse[1]),n.extent,d))}}function P(t,...e){if(!n&&s.apply(this,arguments)){var r=t.currentTarget,o=S(this,e,!0).event(t),c=(0,u.Z)(t.view).on("mousemove.zoom",function(t){if(H(t),!o.moved){var e=t.clientX-h,n=t.clientY-p;o.moved=e*e+n*n>g}o.event(t).zoom("mouse",l(E(o.that.__zoom,o.mouse[0]=(0,a.Z)(t,r),o.mouse[1]),o.extent,d))},!0).on("mouseup.zoom",function(t){c.on("mousemove.zoom mouseup.zoom",null),(0,i.D)(t.view,o.moved),H(t),o.event(t).end()},!0),f=(0,a.Z)(t,r),h=t.clientX,p=t.clientY;(0,i.Z)(t.view),$(t),o.mouse=[f,this.__zoom.invert(f)],y(this),o.start()}}function M(t,...e){if(s.apply(this,arguments)){var n=this.__zoom,r=(0,a.Z)(t.changedTouches?t.changedTouches[0]:t,this),i=n.invert(r),o=n.k*(t.shiftKey?.5:2),f=l(E(x(n,o),r,i),c.apply(this,e),d);H(t),v>0?(0,u.Z)(this).transition().duration(v).call(Z,f,r,t):(0,u.Z)(this).call(b.transform,f,r,t)}}function C(n,...r){if(s.apply(this,arguments)){var i,o,u,c,l=n.touches,f=l.length,h=S(this,r,n.changedTouches.length===f).event(n);for($(n),o=0;o<f;++o)u=l[o],c=[c=(0,a.Z)(u,this),this.__zoom.invert(c),u.identifier],h.touch0?h.touch1||h.touch0[2]===c[2]||(h.touch1=c,h.taps=0):(h.touch0=c,i=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(h.taps<2&&(e=c[0],t=setTimeout(function(){t=null},500)),y(this),h.start())}}function N(t,...e){if(this.__zooming){var n,r,i,o,u=S(this,e).event(t),s=t.changedTouches,c=s.length;for(H(t),n=0;n<c;++n)r=s[n],i=(0,a.Z)(r,this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var f=u.touch0[0],h=u.touch0[1],p=u.touch1[0],v=u.touch1[1],m=(m=p[0]-f[0])*m+(m=p[1]-f[1])*m,y=(y=v[0]-h[0])*y+(y=v[1]-h[1])*y;r=x(r,Math.sqrt(m/y)),i=[(f[0]+p[0])/2,(f[1]+p[1])/2],o=[(h[0]+v[0])/2,(h[1]+v[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],o=u.touch0[1]}u.zoom("touch",l(E(r,i,o),u.extent,d))}}function O(t,...r){if(this.__zooming){var i,o,s=S(this,r).event(t),c=t.changedTouches,l=c.length;for($(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),i=0;i<l;++i)o=c[i],s.touch0&&s.touch0[2]===o.identifier?delete s.touch0:s.touch1&&s.touch1[2]===o.identifier&&delete s.touch1;if(s.touch1&&!s.touch0&&(s.touch0=s.touch1,delete s.touch1),s.touch0)s.touch0[1]=this.__zoom.invert(s.touch0[0]);else if(s.end(),2===s.taps&&(o=(0,a.Z)(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<w)){var f=(0,u.Z)(this).on("dblclick.zoom");f&&f.apply(this,arguments)}}}return b.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",I),t!==i?Z(t,e,n,r):i.interrupt().each(function(){S(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},b.scaleBy=function(t,e,n,r){b.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},b.scaleTo=function(t,e,n,r){b.transform(t,function(){var t=c.apply(this,arguments),r=this.__zoom,i=null==n?z(t):"function"==typeof n?n.apply(this,arguments):n,o=r.invert(i),u="function"==typeof e?e.apply(this,arguments):e;return l(E(x(r,u),i,o),t,d)},n,r)},b.translateBy=function(t,e,n,r){b.transform(t,function(){return l(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),c.apply(this,arguments),d)},null,r)},b.translateTo=function(t,e,n,r,i){b.transform(t,function(){var t=c.apply(this,arguments),i=this.__zoom,o=null==r?z(t):"function"==typeof r?r.apply(this,arguments):r;return l(B.translate(o[0],o[1]).scale(i.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,d)},r,i)},A.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=(0,u.Z)(this.that).datum();_.call(t,this.that,new V(t,{sourceEvent:this.sourceEvent,target:b,type:t,transform:this.that.__zoom,dispatch:_}),e)}},b.wheelDelta=function(t){return arguments.length?(f="function"==typeof t?t:D(+t),b):f},b.filter=function(t){return arguments.length?(s="function"==typeof t?t:D(!!t),b):s},b.touchable=function(t){return arguments.length?(h="function"==typeof t?t:D(!!t),b):h},b.extent=function(t){return arguments.length?(c="function"==typeof t?t:D([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),b):c},b.scaleExtent=function(t){return arguments.length?(p[0]=+t[0],p[1]=+t[1],b):[p[0],p[1]]},b.translateExtent=function(t){return arguments.length?(d[0][0]=+t[0][0],d[1][0]=+t[1][0],d[0][1]=+t[0][1],d[1][1]=+t[1][1],b):[[d[0][0],d[0][1]],[d[1][0],d[1][1]]]},b.constrain=function(t){return arguments.length?(l=t,b):l},b.duration=function(t){return arguments.length?(v=+t,b):v},b.interpolate=function(t){return arguments.length?(m=t,b):m},b.on=function(){var t=_.on.apply(_,arguments);return t===_?b:t},b.clickDistance=function(t){return arguments.length?(g=(t=+t)*t,b):Math.sqrt(g)},b.tapDistance=function(t){return arguments.length?(w=+t,b):w},b}R.prototype},76248:function(t,e,n){"use strict";function r(t,e){if(Object.is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(let[n,r]of t)if(!Object.is(r,e.get(n)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}let n=Object.keys(t);if(n.length!==Object.keys(e).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(e,r)||!Object.is(t[r],e[r]))return!1;return!0}n.d(e,{X:function(){return r}})},52464:function(t,e,n){"use strict";n.d(e,{F:function(){return h},s:function(){return l}});var r=n(67294),i=n(52798);let o=t=>{let e;let n=new Set,r=(t,r)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},e,i),n.forEach(n=>n(e,t))}},i=()=>e,o={setState:r,getState:i,getInitialState:()=>u,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=e=t(r,i,o);return o},u=t=>t?o(t):o,{useDebugValue:a}=r,{useSyncExternalStoreWithSelector:s}=i,c=t=>t;function l(t,e=c,n){let r=s(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return a(r),r}let f=(t,e)=>{let n=u(t),r=(t,r=e)=>l(n,t,r);return Object.assign(r,n),r},h=(t,e)=>t?f(t,e):f}}]);