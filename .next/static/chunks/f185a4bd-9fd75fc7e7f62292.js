"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[225],{68565:function(e,t){let r;Object.defineProperty(t,"__esModule",{value:!0});class n extends Error{}class i extends n{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class s extends n{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class a extends n{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class o extends n{}class l extends n{constructor(e){super(`Invalid unit ${e}`)}}class u extends n{}class c extends n{constructor(){super("Zone is an abstract class")}}let h="numeric",d="short",m="long",f={year:h,month:h,day:h},y={year:h,month:d,day:h},g={year:h,month:d,day:h,weekday:d},w={year:h,month:m,day:h},p={year:h,month:m,day:h,weekday:m},k={hour:h,minute:h},v={hour:h,minute:h,second:h},T={hour:h,minute:h,second:h,timeZoneName:d},S={hour:h,minute:h,second:h,timeZoneName:m},N={hour:h,minute:h,hourCycle:"h23"},O={hour:h,minute:h,second:h,hourCycle:"h23"},b={hour:h,minute:h,second:h,hourCycle:"h23",timeZoneName:d},D={hour:h,minute:h,second:h,hourCycle:"h23",timeZoneName:m},M={year:h,month:h,day:h,hour:h,minute:h},I={year:h,month:h,day:h,hour:h,minute:h,second:h},V={year:h,month:d,day:h,hour:h,minute:h},E={year:h,month:d,day:h,hour:h,minute:h,second:h},x={year:h,month:d,day:h,weekday:d,hour:h,minute:h},F={year:h,month:m,day:h,hour:h,minute:h,timeZoneName:d},C={year:h,month:m,day:h,hour:h,minute:h,second:h,timeZoneName:d},$={year:h,month:m,day:h,weekday:m,hour:h,minute:h,timeZoneName:m},Z={year:h,month:m,day:h,weekday:m,hour:h,minute:h,second:h,timeZoneName:m};class W{get type(){throw new c}get name(){throw new c}get ianaName(){return this.name}get isUniversal(){throw new c}offsetName(e,t){throw new c}formatOffset(e,t){throw new c}offset(e){throw new c}equals(e){throw new c}get isValid(){throw new c}}let L=null;class z extends W{static get instance(){return null===L&&(L=new z),L}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return e3(e,t,r)}formatOffset(e,t){return e7(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return"system"===e.type}get isValid(){return!0}}let q=new Map,A={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6},j=new Map;class _ extends W{static create(e){let t=j.get(e);return void 0===t&&j.set(e,t=new _(e)),t}static resetCache(){j.clear(),q.clear()}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(e){return!1}}constructor(e){super(),this.zoneName=e,this.valid=_.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return e3(e,t,r,this.name)}formatOffset(e,t){return e7(this.offset(e),t)}offset(e){var t;let r;if(!this.valid)return NaN;let n=new Date(e);if(isNaN(n))return NaN;let i=(t=this.name,void 0===(r=q.get(t))&&(r=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),q.set(t,r)),r),[s,a,o,l,u,c,h]=i.formatToParts?function(e,t){let r=e.formatToParts(t),n=[];for(let e=0;e<r.length;e++){let{type:t,value:i}=r[e],s=A[t];"era"===t?n[s]=i:eW(s)||(n[s]=parseInt(i,10))}return n}(i,n):function(e,t){let r=e.format(t).replace(/\u200E/g,""),[,n,i,s,a,o,l,u]=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(r);return[s,n,i,a,o,l,u]}(i,n);"BC"===l&&(s=-Math.abs(s)+1);let d=eX({year:s,month:a,day:o,hour:24===u?0:u,minute:c,second:h,millisecond:0}),m=+n,f=m%1e3;return(d-(m-=f>=0?f:1e3+f))/6e4}equals(e){return"iana"===e.type&&e.name===this.name}get isValid(){return this.valid}}let U={},Y=new Map;function H(e,t={}){let r=JSON.stringify([e,t]),n=Y.get(r);return void 0===n&&(n=new Intl.DateTimeFormat(e,t),Y.set(r,n)),n}let R=new Map,P=new Map,J=null,G=new Map;function B(e){let t=G.get(e);return void 0===t&&(t=new Intl.DateTimeFormat(e).resolvedOptions(),G.set(e,t)),t}let Q=new Map;function K(e,t,r,n){let i=e.listingMode();return"error"===i?null:"en"===i?r(t):n(t)}class X{constructor(e,t,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:n,floor:i,...s}=r;if(!t||Object.keys(s).length>0){let t={useGrouping:!1,...r};r.padTo>0&&(t.minimumIntegerDigits=r.padTo),this.inf=function(e,t={}){let r=JSON.stringify([e,t]),n=R.get(r);return void 0===n&&(n=new Intl.NumberFormat(e,t),R.set(r,n)),n}(e,t)}}format(e){if(!this.inf)return eH(this.floor?Math.floor(e):eG(e,3),this.padTo);{let t=this.floor?Math.floor(e):e;return this.inf.format(t)}}}class ee{constructor(e,t,r){let n;if(this.opts=r,this.originalZone=void 0,this.opts.timeZone)this.dt=e;else if("fixed"===e.zone.type){let t=-(e.offset/60*1),r=t>=0?`Etc/GMT+${t}`:`Etc/GMT${t}`;0!==e.offset&&_.create(r).valid?(n=r,this.dt=e):(n="UTC",this.dt=0===e.offset?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else"system"===e.zone.type?this.dt=e:"iana"===e.zone.type?(this.dt=e,n=e.zone.name):(n="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||n,this.dtf=H(t,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(e=>{if("timeZoneName"!==e.type)return e;{let t=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...e,value:t}}}):e}resolvedOptions(){return this.dtf.resolvedOptions()}}class et{constructor(e,t,r){this.opts={style:"long",...r},!t&&eq()&&(this.rtf=function(e,t={}){let{base:r,...n}=t,i=JSON.stringify([e,n]),s=P.get(i);return void 0===s&&(s=new Intl.RelativeTimeFormat(e,t),P.set(i,s)),s}(e,r))}format(e,t){return this.rtf?this.rtf.format(e,t):function(e,t,r="always",n=!1){let i={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},s=-1===["hours","minutes","seconds"].indexOf(e);if("auto"===r&&s){let r="days"===e;switch(t){case 1:return r?"tomorrow":`next ${i[e][0]}`;case -1:return r?"yesterday":`last ${i[e][0]}`;case 0:return r?"today":`this ${i[e][0]}`}}let a=Object.is(t,-0)||t<0,o=Math.abs(t),l=1===o,u=i[e],c=n?l?u[1]:u[2]||u[1]:l?i[e][0]:e;return a?`${o} ${c} ago`:`in ${o} ${c}`}(t,e,this.opts.numeric,"long"!==this.opts.style)}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}}let er={firstDay:1,minimalDays:4,weekend:[6,7]};class en{static fromOpts(e){return en.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,r,n,i=!1){let s=e||eT.defaultLocale,a=s||(i?"en-US":J||(J=new Intl.DateTimeFormat().resolvedOptions().locale));return new en(a,t||eT.defaultNumberingSystem,r||eT.defaultOutputCalendar,eU(n)||eT.defaultWeekSettings,s)}static resetCache(){J=null,Y.clear(),R.clear(),P.clear(),G.clear(),Q.clear()}static fromObject({locale:e,numberingSystem:t,outputCalendar:r,weekSettings:n}={}){return en.create(e,t,r,n)}constructor(e,t,r,n,i){var s,a,o;let[l,u,c]=function(e){let t=e.indexOf("-x-");-1!==t&&(e=e.substring(0,t));let r=e.indexOf("-u-");if(-1===r)return[e];{let t,n;try{t=H(e).resolvedOptions(),n=e}catch(s){let i=e.substring(0,r);t=H(i).resolvedOptions(),n=i}let{numberingSystem:i,calendar:s}=t;return[n,i,s]}}(e);this.locale=l,this.numberingSystem=t||u||null,this.outputCalendar=r||c||null,this.weekSettings=n,this.intl=(s=this.locale,a=this.numberingSystem,((o=this.outputCalendar)||a)&&(s.includes("-u-")||(s+="-u"),o&&(s+=`-ca-${o}`),a&&(s+=`-nu-${a}`)),s),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return null==this.fastNumbersCached&&(this.fastNumbersCached=(!this.numberingSystem||"latn"===this.numberingSystem)&&("latn"===this.numberingSystem||!this.locale||this.locale.startsWith("en")||"latn"===B(this.locale).numberingSystem)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),t=(null===this.numberingSystem||"latn"===this.numberingSystem)&&(null===this.outputCalendar||"gregory"===this.outputCalendar);return e&&t?"en":"intl"}clone(e){return e&&0!==Object.getOwnPropertyNames(e).length?en.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,eU(e.weekSettings)||this.weekSettings,e.defaultToEN||!1):this}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return K(this,e,tr,()=>{let r=t?{month:e,day:"numeric"}:{month:e},n=t?"format":"standalone";return this.monthsCache[n][e]||(this.monthsCache[n][e]=function(e){let t=[];for(let r=1;r<=12;r++){let n=rH.utc(2009,r,1);t.push(e(n))}return t}(e=>this.extract(e,r,"month"))),this.monthsCache[n][e]})}weekdays(e,t=!1){return K(this,e,ta,()=>{let r=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},n=t?"format":"standalone";return this.weekdaysCache[n][e]||(this.weekdaysCache[n][e]=function(e){let t=[];for(let r=1;r<=7;r++){let n=rH.utc(2016,11,13+r);t.push(e(n))}return t}(e=>this.extract(e,r,"weekday"))),this.weekdaysCache[n][e]})}meridiems(){return K(this,void 0,()=>to,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[rH.utc(2016,11,13,9),rH.utc(2016,11,13,19)].map(t=>this.extract(t,e,"dayperiod"))}return this.meridiemCache})}eras(e){return K(this,e,th,()=>{let t={era:e};return this.eraCache[e]||(this.eraCache[e]=[rH.utc(-40,1,1),rH.utc(2017,1,1)].map(e=>this.extract(e,t,"era"))),this.eraCache[e]})}extract(e,t,r){let n=this.dtFormatter(e,t).formatToParts().find(e=>e.type.toLowerCase()===r);return n?n.value:null}numberFormatter(e={}){return new X(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new ee(e,this.intl,t)}relFormatter(e={}){return new et(this.intl,this.isEnglish(),e)}listFormatter(e={}){return function(e,t={}){let r=JSON.stringify([e,t]),n=U[r];return n||(n=new Intl.ListFormat(e,t),U[r]=n),n}(this.intl,e)}isEnglish(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||B(this.intl).locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:eA()?function(e){let t=Q.get(e);if(!t){let r=new Intl.Locale(e);"minimalDays"in(t="getWeekInfo"in r?r.getWeekInfo():r.weekInfo)||(t={...er,...t}),Q.set(e,t)}return t}(this.locale):er}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}let ei=null;class es extends W{static get utcInstance(){return null===ei&&(ei=new es(0)),ei}static instance(e){return 0===e?es.utcInstance:new es(e)}static parseSpecifier(e){if(e){let t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new es(e6(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return 0===this.fixed?"UTC":`UTC${e7(this.fixed,"narrow")}`}get ianaName(){return 0===this.fixed?"Etc/UTC":`Etc/GMT${e7(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return e7(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return"fixed"===e.type&&e.fixed===this.fixed}get isValid(){return!0}}class ea extends W{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function eo(e,t){if(eW(e)||null===e)return t;if(e instanceof W)return e;if("string"==typeof e){let r=e.toLowerCase();return"default"===r?t:"local"===r||"system"===r?z.instance:"utc"===r||"gmt"===r?es.utcInstance:es.parseSpecifier(r)||_.create(e)}return eL(e)?es.instance(e):"object"==typeof e&&"offset"in e&&"function"==typeof e.offset?e:new ea(e)}let el={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},eu={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},ec=el.hanidec.replace(/[\[|\]]/g,"").split(""),eh=new Map;function ed({numberingSystem:e},t=""){let r=e||"latn",n=eh.get(r);void 0===n&&(n=new Map,eh.set(r,n));let i=n.get(t);return void 0===i&&(i=RegExp(`${el[r]}${t}`),n.set(t,i)),i}let em=()=>Date.now(),ef="system",ey=null,eg=null,ew=null,ep=60,ek,ev=null;class eT{static get now(){return em}static set now(e){em=e}static set defaultZone(e){ef=e}static get defaultZone(){return eo(ef,z.instance)}static get defaultLocale(){return ey}static set defaultLocale(e){ey=e}static get defaultNumberingSystem(){return eg}static set defaultNumberingSystem(e){eg=e}static get defaultOutputCalendar(){return ew}static set defaultOutputCalendar(e){ew=e}static get defaultWeekSettings(){return ev}static set defaultWeekSettings(e){ev=eU(e)}static get twoDigitCutoffYear(){return ep}static set twoDigitCutoffYear(e){ep=e%100}static get throwOnInvalid(){return ek}static set throwOnInvalid(e){ek=e}static resetCaches(){en.resetCache(),_.resetCache(),rH.resetCache(),eh.clear()}}class eS{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}let eN=[0,31,59,90,120,151,181,212,243,273,304,334],eO=[0,31,60,91,121,152,182,213,244,274,305,335];function eb(e,t){return new eS("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function eD(e,t,r){let n=new Date(Date.UTC(e,t-1,r));e<100&&e>=0&&n.setUTCFullYear(n.getUTCFullYear()-1900);let i=n.getUTCDay();return 0===i?7:i}function eM(e,t){let r=eB(e)?eO:eN,n=r.findIndex(e=>e<t),i=t-r[n];return{month:n+1,day:i}}function eI(e,t){return(e-t+7)%7+1}function eV(e,t=4,r=1){let{year:n,month:i,day:s}=e,a=s+(eB(n)?eO:eN)[i-1],o=eI(eD(n,i,s),r),l=Math.floor((a-o+14-t)/7),u;return l<1?l=e1(u=n-1,t,r):l>e1(n,t,r)?(u=n+1,l=1):u=n,{weekYear:u,weekNumber:l,weekday:o,...e9(e)}}function eE(e,t=4,r=1){let{weekYear:n,weekNumber:i,weekday:s}=e,a=eI(eD(n,1,t),r),o=eQ(n),l=7*i+s-a-7+t,u;l<1?l+=eQ(u=n-1):l>o?(u=n+1,l-=eQ(n)):u=n;let{month:c,day:h}=eM(u,l);return{year:u,month:c,day:h,...e9(e)}}function ex(e){let{year:t,month:r,day:n}=e,i=n+(eB(t)?eO:eN)[r-1];return{year:t,ordinal:i,...e9(e)}}function eF(e){let{year:t,ordinal:r}=e,{month:n,day:i}=eM(t,r);return{year:t,month:n,day:i,...e9(e)}}function eC(e,t){if(!(!eW(e.localWeekday)||!eW(e.localWeekNumber)||!eW(e.localWeekYear)))return{minDaysInFirstWeek:4,startOfWeek:1};if(!eW(e.weekday)||!eW(e.weekNumber)||!eW(e.weekYear))throw new o("Cannot mix locale-based week fields with ISO-based week fields");return eW(e.localWeekday)||(e.weekday=e.localWeekday),eW(e.localWeekNumber)||(e.weekNumber=e.localWeekNumber),eW(e.localWeekYear)||(e.weekYear=e.localWeekYear),delete e.localWeekday,delete e.localWeekNumber,delete e.localWeekYear,{minDaysInFirstWeek:t.getMinDaysInFirstWeek(),startOfWeek:t.getStartOfWeek()}}function e$(e){let t=ez(e.year),r=eY(e.month,1,12),n=eY(e.day,1,eK(e.year,e.month));return t?r?!n&&eb("day",e.day):eb("month",e.month):eb("year",e.year)}function eZ(e){let{hour:t,minute:r,second:n,millisecond:i}=e,s=eY(t,0,23)||24===t&&0===r&&0===n&&0===i,a=eY(r,0,59),o=eY(n,0,59),l=eY(i,0,999);return s?a?o?!l&&eb("millisecond",i):eb("second",n):eb("minute",r):eb("hour",t)}function eW(e){return void 0===e}function eL(e){return"number"==typeof e}function ez(e){return"number"==typeof e&&e%1==0}function eq(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function eA(){try{return"undefined"!=typeof Intl&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(e){return!1}}function ej(e,t,r){if(0!==e.length)return e.reduce((e,n)=>{let i=[t(n),n];return e&&r(e[0],i[0])===e[0]?e:i},null)[1]}function e_(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function eU(e){if(null==e)return null;if("object"!=typeof e)throw new u("Week settings must be an object");if(!eY(e.firstDay,1,7)||!eY(e.minimalDays,1,7)||!Array.isArray(e.weekend)||e.weekend.some(e=>!eY(e,1,7)))throw new u("Invalid week settings");return{firstDay:e.firstDay,minimalDays:e.minimalDays,weekend:Array.from(e.weekend)}}function eY(e,t,r){return ez(e)&&e>=t&&e<=r}function eH(e,t=2){return e<0?"-"+(""+-e).padStart(t,"0"):(""+e).padStart(t,"0")}function eR(e){if(!eW(e)&&null!==e&&""!==e)return parseInt(e,10)}function eP(e){if(!eW(e)&&null!==e&&""!==e)return parseFloat(e)}function eJ(e){if(!eW(e)&&null!==e&&""!==e)return Math.floor(1e3*parseFloat("0."+e))}function eG(e,t,r=!1){let n=10**t;return(r?Math.trunc:Math.round)(e*n)/n}function eB(e){return e%4==0&&(e%100!=0||e%400==0)}function eQ(e){return eB(e)?366:365}function eK(e,t){var r;let n=(r=t-1)-12*Math.floor(r/12)+1;return 2===n?eB(e+(t-n)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function eX(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t)).setUTCFullYear(e.year,e.month-1,e.day),+t}function e0(e,t,r){return-eI(eD(e,1,t),r)+t-1}function e1(e,t=4,r=1){let n=e0(e,t,r),i=e0(e+1,t,r);return(eQ(e)-n+i)/7}function e2(e){return e>99?e:e>eT.twoDigitCutoffYear?1900+e:2e3+e}function e3(e,t,r,n=null){let i=new Date(e),s={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};n&&(s.timeZone=n);let a={timeZoneName:t,...s},o=new Intl.DateTimeFormat(r,a).formatToParts(i).find(e=>"timezonename"===e.type.toLowerCase());return o?o.value:null}function e6(e,t){let r=parseInt(e,10);Number.isNaN(r)&&(r=0);let n=parseInt(t,10)||0,i=r<0||Object.is(r,-0)?-n:n;return 60*r+i}function e4(e){let t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new u(`Invalid unit value ${e}`);return t}function e5(e,t){let r={};for(let n in e)if(e_(e,n)){let i=e[n];if(null==i)continue;r[t(n)]=e4(i)}return r}function e7(e,t){let r=Math.trunc(Math.abs(e/60)),n=Math.trunc(Math.abs(e%60)),i=e>=0?"+":"-";switch(t){case"short":return`${i}${eH(r,2)}:${eH(n,2)}`;case"narrow":return`${i}${r}${n>0?`:${n}`:""}`;case"techie":return`${i}${eH(r,2)}${eH(n,2)}`;default:throw RangeError(`Value format ${t} is out of range for property format`)}}function e9(e){return["hour","minute","second","millisecond"].reduce((t,r)=>(t[r]=e[r],t),{})}let e8=["January","February","March","April","May","June","July","August","September","October","November","December"],te=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tt=["J","F","M","A","M","J","J","A","S","O","N","D"];function tr(e){switch(e){case"narrow":return[...tt];case"short":return[...te];case"long":return[...e8];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}let tn=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],ti=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],ts=["M","T","W","T","F","S","S"];function ta(e){switch(e){case"narrow":return[...ts];case"short":return[...ti];case"long":return[...tn];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}let to=["AM","PM"],tl=["Before Christ","Anno Domini"],tu=["BC","AD"],tc=["B","A"];function th(e){switch(e){case"narrow":return[...tc];case"short":return[...tu];case"long":return[...tl];default:return null}}function td(e,t){let r="";for(let n of e)n.literal?r+=n.val:r+=t(n.val);return r}let tm={D:f,DD:y,DDD:w,DDDD:p,t:k,tt:v,ttt:T,tttt:S,T:N,TT:O,TTT:b,TTTT:D,f:M,ff:V,fff:F,ffff:$,F:I,FF:E,FFF:C,FFFF:Z};class tf{static create(e,t={}){return new tf(e,t)}static parseFormat(e){let t=null,r="",n=!1,i=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);"'"===a?(r.length>0&&i.push({literal:n||/^\s+$/.test(r),val:r}),t=null,r="",n=!n):n?r+=a:a===t?r+=a:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=a,t=a)}return r.length>0&&i.push({literal:n||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return tm[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return eH(e,t);let r={...this.opts};return t>0&&(r.padTo=t),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,t){let r="en"===this.loc.listingMode(),n=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,i=(t,r)=>this.loc.extract(e,t,r),s=t=>e.isOffsetFixed&&0===e.offset&&t.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,t.format):"",a=()=>r?to[e.hour<12?0:1]:i({hour:"numeric",hourCycle:"h12"},"dayperiod"),o=(t,n)=>r?tr(t)[e.month-1]:i(n?{month:t}:{month:t,day:"numeric"},"month"),l=(t,n)=>r?ta(t)[e.weekday-1]:i(n?{weekday:t}:{weekday:t,month:"long",day:"numeric"},"weekday"),u=t=>{let r=tf.macroTokenToFormatOpts(t);return r?this.formatWithSystemDefault(e,r):t},c=t=>r?th(t)[e.year<0?0:1]:i({era:t},"era");return td(tf.parseFormat(t),t=>{switch(t){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12==0?12:e.hour%12);case"hh":return this.num(e.hour%12==0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return s({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return s({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return s({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return n?i({day:"numeric"},"day"):this.num(e.day);case"dd":return n?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":case"E":return this.num(e.weekday);case"ccc":return l("short",!0);case"cccc":return l("long",!0);case"ccccc":return l("narrow",!0);case"EEE":return l("short",!1);case"EEEE":return l("long",!1);case"EEEEE":return l("narrow",!1);case"L":return n?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return n?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return o("short",!0);case"LLLL":return o("long",!0);case"LLLLL":return o("narrow",!0);case"M":return n?i({month:"numeric"},"month"):this.num(e.month);case"MM":return n?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return o("short",!1);case"MMMM":return o("long",!1);case"MMMMM":return o("narrow",!1);case"y":return n?i({year:"numeric"},"year"):this.num(e.year);case"yy":return n?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return n?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return n?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return c("short");case"GG":return c("long");case"GGGGG":return c("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return u(t)}})}formatDurationFromString(e,t){let r;let n=e=>{switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},i=tf.parseFormat(t),s=i.reduce((e,{literal:t,val:r})=>t?e:e.concat(r),[]);return td(i,(r=e.shiftTo(...s.map(n).filter(e=>e)),e=>{let t=n(e);return t?this.num(r.get(t),e.length):e}))}}let ty=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function tg(...e){let t=e.reduce((e,t)=>e+t.source,"");return RegExp(`^${t}$`)}function tw(...e){return t=>e.reduce(([e,r,n],i)=>{let[s,a,o]=i(t,n);return[{...e,...s},a||r,o]},[{},null,1]).slice(0,2)}function tp(e,...t){if(null==e)return[null,null];for(let[r,n]of t){let t=r.exec(e);if(t)return n(t)}return[null,null]}function tk(...e){return(t,r)=>{let n;let i={};for(n=0;n<e.length;n++)i[e[n]]=eR(t[r+n]);return[i,null,r+n]}}let tv=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,tT=`(?:${tv.source}?(?:\\[(${ty.source})\\])?)?`,tS=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,tN=RegExp(`${tS.source}${tT}`),tO=RegExp(`(?:T${tN.source})?`),tb=tk("weekYear","weekNumber","weekDay"),tD=tk("year","ordinal"),tM=RegExp(`${tS.source} ?(?:${tv.source}|(${ty.source}))?`),tI=RegExp(`(?: ${tM.source})?`);function tV(e,t,r){let n=e[t];return eW(n)?r:eR(n)}function tE(e,t){return[{hours:tV(e,t,0),minutes:tV(e,t+1,0),seconds:tV(e,t+2,0),milliseconds:eJ(e[t+3])},null,t+4]}function tx(e,t){let r=!e[t]&&!e[t+1],n=e6(e[t+1],e[t+2]);return[{},r?null:es.instance(n),t+3]}function tF(e,t){return[{},e[t]?_.create(e[t]):null,t+1]}let tC=RegExp(`^T?${tS.source}$`),t$=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function tZ(e){let[t,r,n,i,s,a,o,l,u]=e,c="-"===t[0],h=l&&"-"===l[0],d=(e,t=!1)=>void 0!==e&&(t||e&&c)?-e:e;return[{years:d(eP(r)),months:d(eP(n)),weeks:d(eP(i)),days:d(eP(s)),hours:d(eP(a)),minutes:d(eP(o)),seconds:d(eP(l),"-0"===l),milliseconds:d(eJ(u),h)}]}let tW={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function tL(e,t,r,n,i,s,a){let o={year:2===t.length?e2(eR(t)):eR(t),month:te.indexOf(r)+1,day:eR(n),hour:eR(i),minute:eR(s)};return a&&(o.second=eR(a)),e&&(o.weekday=e.length>3?tn.indexOf(e)+1:ti.indexOf(e)+1),o}let tz=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function tq(e){let[,t,r,n,i,s,a,o,l,u,c,h]=e;return[tL(t,i,n,r,s,a,o),new es(l?tW[l]:u?0:e6(c,h))]}let tA=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,tj=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,t_=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function tU(e){let[,t,r,n,i,s,a,o]=e;return[tL(t,i,n,r,s,a,o),es.utcInstance]}function tY(e){let[,t,r,n,i,s,a,o]=e;return[tL(t,o,r,n,i,s,a),es.utcInstance]}let tH=tg(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,tO),tR=tg(/(\d{4})-?W(\d\d)(?:-?(\d))?/,tO),tP=tg(/(\d{4})-?(\d{3})/,tO),tJ=tg(tN),tG=tw(function(e,t){return[{year:tV(e,t),month:tV(e,t+1,1),day:tV(e,t+2,1)},null,t+3]},tE,tx,tF),tB=tw(tb,tE,tx,tF),tQ=tw(tD,tE,tx,tF),tK=tw(tE,tx,tF),tX=tw(tE),t0=tg(/(\d{4})-(\d\d)-(\d\d)/,tI),t1=tg(tM),t2=tw(tE,tx,tF),t3="Invalid Duration",t6={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},t4={years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6},...t6},t5={years:{quarters:4,months:12,weeks:52.1775,days:365.2425,hours:8765.82,minutes:525949.2,seconds:31556952,milliseconds:31556952e3},quarters:{months:3,weeks:13.044375,days:91.310625,hours:2191.455,minutes:131487.3,seconds:7889238,milliseconds:7889238e3},months:{weeks:30.436875/7,days:30.436875,hours:730.485,minutes:43829.1,seconds:2629746,milliseconds:2629746e3},...t6},t7=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],t9=t7.slice(0).reverse();function t8(e,t,r=!1){return new rr({values:r?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix})}function re(e,t){var r;let n=null!=(r=t.milliseconds)?r:0;for(let r of t9.slice(1))t[r]&&(n+=t[r]*e[r].milliseconds);return n}function rt(e,t){let r=0>re(e,t)?-1:1;t7.reduceRight((n,i)=>{if(eW(t[i]))return n;if(n){let s=t[n]*r,a=e[i][n],o=Math.floor(s/a);t[i]+=o*r,t[n]-=o*a*r}return i},null),t7.reduce((r,n)=>{if(eW(t[n]))return r;if(r){let i=t[r]%1;t[r]-=i,t[n]+=i*e[r][n]}return n},null)}class rr{constructor(e){let t="longterm"===e.conversionAccuracy,r=t?t5:t4;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||en.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,t){return rr.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(null==e||"object"!=typeof e)throw new u(`Duration.fromObject: argument expected to be an object, got ${null===e?"null":typeof e}`);return new rr({values:e5(e,rr.normalizeUnit),loc:en.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(eL(e))return rr.fromMillis(e);if(rr.isDuration(e))return e;if("object"==typeof e)return rr.fromObject(e);throw new u(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){let[r]=tp(e,[t$,tZ]);return r?rr.fromObject(r,t):rr.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){let[r]=tp(e,[tC,tX]);return r?rr.fromObject(r,t):rr.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the Duration is invalid");let r=e instanceof eS?e:new eS(e,t);if(!eT.throwOnInvalid)return new rr({invalid:r});throw new a(r)}static normalizeUnit(e){let t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e?e.toLowerCase():e];if(!t)throw new l(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){let r={...t,floor:!1!==t.round&&!1!==t.floor};return this.isValid?tf.create(this.loc,r).formatDurationFromString(this,e):t3}toHuman(e={}){if(!this.isValid)return t3;let t=t7.map(t=>{let r=this.values[t];return eW(r)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:t.slice(0,-1)}).format(r)}).filter(e=>e);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return 0!==this.years&&(e+=this.years+"Y"),(0!==this.months||0!==this.quarters)&&(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),(0!==this.hours||0!==this.minutes||0!==this.seconds||0!==this.milliseconds)&&(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),(0!==this.seconds||0!==this.milliseconds)&&(e+=eG(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},rH.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?re(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let t=rr.fromDurationLike(e),r={};for(let e of t7)(e_(t.values,e)||e_(this.values,e))&&(r[e]=t.get(e)+this.get(e));return t8(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let t=rr.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;let t={};for(let r of Object.keys(this.values))t[r]=e4(e(this.values[r],r));return t8(this,{values:t},!0)}get(e){return this[rr.normalizeUnit(e)]}set(e){return this.isValid?t8(this,{values:{...this.values,...e5(e,rr.normalizeUnit)}}):this}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:r,matrix:n}={}){return t8(this,{loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:n,conversionAccuracy:r})}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return rt(this.matrix,e),t8(this,{values:e},!0)}rescale(){return this.isValid?t8(this,{values:function(e){let t={};for(let[r,n]of Object.entries(e))0!==n&&(t[r]=n);return t}(this.normalize().shiftToAll().toObject())},!0):this}shiftTo(...e){let t;if(!this.isValid||0===e.length)return this;e=e.map(e=>rr.normalizeUnit(e));let r={},n={},i=this.toObject();for(let s of t7)if(e.indexOf(s)>=0){t=s;let e=0;for(let t in n)e+=this.matrix[t][s]*n[t],n[t]=0;eL(i[s])&&(e+=i[s]);let a=Math.trunc(e);r[s]=a,n[s]=(1e3*e-1e3*a)/1e3}else eL(i[s])&&(n[s]=i[s]);for(let e in n)0!==n[e]&&(r[t]+=e===t?n[e]:n[e]/this.matrix[t][e]);return rt(this.matrix,r),t8(this,{values:r},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let t of Object.keys(this.values))e[t]=0===this.values[t]?0:-this.values[t];return t8(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;for(let n of t7){var t,r;if(t=this.values[n],r=e.values[n],void 0===t||0===t?void 0!==r&&0!==r:t!==r)return!1}return!0}}let rn="Invalid Interval";class ri{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the Interval is invalid");let r=e instanceof eS?e:new eS(e,t);if(!eT.throwOnInvalid)return new ri({invalid:r});throw new s(r)}static fromDateTimes(e,t){let r=rR(e),n=rR(t),i=r&&r.isValid?n&&n.isValid?n<r?ri.invalid("end before start",`The end of an interval must be after its start, but you had start=${r.toISO()} and end=${n.toISO()}`):null:ri.invalid("missing or invalid end"):ri.invalid("missing or invalid start");return null==i?new ri({start:r,end:n}):i}static after(e,t){let r=rr.fromDurationLike(t),n=rR(e);return ri.fromDateTimes(n,n.plus(r))}static before(e,t){let r=rr.fromDurationLike(t),n=rR(e);return ri.fromDateTimes(n.minus(r),n)}static fromISO(e,t){let[r,n]=(e||"").split("/",2);if(r&&n){let e,i,s,a;try{i=(e=rH.fromISO(r,t)).isValid}catch(e){i=!1}try{a=(s=rH.fromISO(n,t)).isValid}catch(e){a=!1}if(i&&a)return ri.fromDateTimes(e,s);if(i){let r=rr.fromISO(n,t);if(r.isValid)return ri.after(e,r)}else if(a){let e=rr.fromISO(r,t);if(e.isValid)return ri.before(s,e)}}return ri.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get lastDateTime(){return this.isValid&&this.e?this.e.minus(1):null}get isValid(){return null===this.invalidReason}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){let r;if(!this.isValid)return NaN;let n=this.start.startOf(e,t);return Math.floor((r=(r=null!=t&&t.useLocaleWeeks?this.end.reconfigure({locale:n.locale}):this.end).startOf(e,t)).diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())}hasSame(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return!!this.isValid&&this.s>e}isBefore(e){return!!this.isValid&&this.e<=e}contains(e){return!!this.isValid&&this.s<=e&&this.e>e}set({start:e,end:t}={}){return this.isValid?ri.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];let t=e.map(rR).filter(e=>this.contains(e)).sort((e,t)=>e.toMillis()-t.toMillis()),r=[],{s:n}=this,i=0;for(;n<this.e;){let e=t[i]||this.e,s=+e>+this.e?this.e:e;r.push(ri.fromDateTimes(n,s)),n=s,i+=1}return r}splitBy(e){let t=rr.fromDurationLike(e);if(!this.isValid||!t.isValid||0===t.as("milliseconds"))return[];let{s:r}=this,n=1,i,s=[];for(;r<this.e;){let e=this.start.plus(t.mapUnits(e=>e*n));i=+e>+this.e?this.e:e,s.push(ri.fromDateTimes(r,i)),r=i,n+=1}return s}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return!!this.isValid&&+this.e==+e.s}abutsEnd(e){return!!this.isValid&&+e.e==+this.s}engulfs(e){return!!this.isValid&&this.s<=e.s&&this.e>=e.e}equals(e){return!!this.isValid&&!!e.isValid&&this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let t=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return t>=r?null:ri.fromDateTimes(t,r)}union(e){if(!this.isValid)return this;let t=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return ri.fromDateTimes(t,r)}static merge(e){let[t,r]=e.sort((e,t)=>e.s-t.s).reduce(([e,t],r)=>t?t.overlaps(r)||t.abutsStart(r)?[e,t.union(r)]:[e.concat([t]),r]:[e,r],[[],null]);return r&&t.push(r),t}static xor(e){let t=null,r=0,n=[],i=e.map(e=>[{time:e.s,type:"s"},{time:e.e,type:"e"}]);for(let e of Array.prototype.concat(...i).sort((e,t)=>e.time-t.time))1===(r+="s"===e.type?1:-1)?t=e.time:(t&&+t!=+e.time&&n.push(ri.fromDateTimes(t,e.time)),t=null);return ri.merge(n)}difference(...e){return ri.xor([this].concat(e)).map(e=>this.intersection(e)).filter(e=>e&&!e.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:rn}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=f,t={}){return this.isValid?tf.create(this.s.loc.clone(t),e).formatInterval(this):rn}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:rn}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:rn}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:rn}toFormat(e,{separator:t=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:rn}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):rr.invalid(this.invalidReason)}mapEndpoints(e){return ri.fromDateTimes(e(this.s),e(this.e))}}class rs{static hasDST(e=eT.defaultZone){let t=rH.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return _.isValidZone(e)}static normalizeZone(e){return eo(e,eT.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||en.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||en.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||en.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null,outputCalendar:i="gregory"}={}){return(n||en.create(t,r,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null,outputCalendar:i="gregory"}={}){return(n||en.create(t,r,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null}={}){return(n||en.create(t,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null}={}){return(n||en.create(t,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return en.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return en.create(t,null,"gregory").eras(e)}static features(){return{relative:eq(),localeWeek:eA()}}}function ra(e,t){let r=e=>e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),n=r(t)-r(e);return Math.floor(rr.fromMillis(n).as("days"))}function ro(e,t=e=>e){return{regex:e,deser:([e])=>t(function(e){let t=parseInt(e,10);if(!isNaN(t))return t;t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if(-1!==e[r].search(el.hanidec))t+=ec.indexOf(e[r]);else for(let e in eu){let[r,i]=eu[e];n>=r&&n<=i&&(t+=n-r)}}return parseInt(t,10)}(e))}}let rl=String.fromCharCode(160),ru=`[ ${rl}]`,rc=RegExp(ru,"g");function rh(e){return e.replace(/\./g,"\\.?").replace(rc,ru)}function rd(e){return e.replace(/\./g,"").replace(rc," ").toLowerCase()}function rm(e,t){return null===e?null:{regex:RegExp(e.map(rh).join("|")),deser:([r])=>e.findIndex(e=>rd(r)===rd(e))+t}}function rf(e,t){return{regex:e,deser:([,e,t])=>e6(e,t),groups:t}}function ry(e){return{regex:e,deser:([e])=>e}}let rg={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}},rw=null;function rp(e,t){return Array.prototype.concat(...e.map(e=>(function(e,t){if(e.literal)return e;let r=rT(tf.macroTokenToFormatOpts(e.val),t);return null==r||r.includes(void 0)?e:r})(e,t)))}class rk{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=rp(tf.parseFormat(t),e),this.units=this.tokens.map(t=>(function(e,t){let r=ed(t),n=ed(t,"{2}"),i=ed(t,"{3}"),s=ed(t,"{4}"),a=ed(t,"{6}"),o=ed(t,"{1,2}"),l=ed(t,"{1,3}"),u=ed(t,"{1,6}"),c=ed(t,"{1,9}"),h=ed(t,"{2,4}"),d=ed(t,"{4,6}"),m=e=>({regex:RegExp(e.val.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")),deser:([e])=>e,literal:!0}),f=(f=>{if(e.literal)return m(f);switch(f.val){case"G":return rm(t.eras("short"),0);case"GG":return rm(t.eras("long"),0);case"y":return ro(u);case"yy":case"kk":return ro(h,e2);case"yyyy":case"kkkk":return ro(s);case"yyyyy":return ro(d);case"yyyyyy":return ro(a);case"M":case"L":case"d":case"H":case"h":case"m":case"q":case"s":case"W":return ro(o);case"MM":case"LL":case"dd":case"HH":case"hh":case"mm":case"qq":case"ss":case"WW":return ro(n);case"MMM":return rm(t.months("short",!0),1);case"MMMM":return rm(t.months("long",!0),1);case"LLL":return rm(t.months("short",!1),1);case"LLLL":return rm(t.months("long",!1),1);case"o":case"S":return ro(l);case"ooo":case"SSS":return ro(i);case"u":return ry(c);case"uu":return ry(o);case"uuu":case"E":case"c":return ro(r);case"a":return rm(t.meridiems(),0);case"EEE":return rm(t.weekdays("short",!1),1);case"EEEE":return rm(t.weekdays("long",!1),1);case"ccc":return rm(t.weekdays("short",!0),1);case"cccc":return rm(t.weekdays("long",!0),1);case"Z":case"ZZ":return rf(RegExp(`([+-]${o.source})(?::(${n.source}))?`),2);case"ZZZ":return rf(RegExp(`([+-]${o.source})(${n.source})?`),2);case"z":return ry(/[a-z_+-/]{1,256}?/i);case" ":return ry(/[^\S\n\r]/);default:return m(f)}})(e)||{invalidReason:"missing Intl.DateTimeFormat.formatToParts support"};return f.token=e,f})(t,e)),this.disqualifyingUnit=this.units.find(e=>e.invalidReason),!this.disqualifyingUnit){let[e,t]=function(e){let t=e.map(e=>e.regex).reduce((e,t)=>`${e}(${t.source})`,"");return[`^${t}$`,e]}(this.units);this.regex=RegExp(e,"i"),this.handlers=t}}explainFromTokens(e){if(!this.isValid)return{input:e,tokens:this.tokens,invalidReason:this.invalidReason};{let[t,r]=function(e,t,r){let n=e.match(t);if(!n)return[n,{}];{let e={},t=1;for(let i in r)if(e_(r,i)){let s=r[i],a=s.groups?s.groups+1:1;!s.literal&&s.token&&(e[s.token.val[0]]=s.deser(n.slice(t,t+a))),t+=a}return[n,e]}}(e,this.regex,this.handlers),[n,i,s]=r?function(e){let t;let r=e=>{switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},n=null;return eW(e.z)||(n=_.create(e.z)),eW(e.Z)||(n||(n=new es(e.Z)),t=e.Z),eW(e.q)||(e.M=(e.q-1)*3+1),eW(e.h)||(e.h<12&&1===e.a?e.h+=12:12!==e.h||0!==e.a||(e.h=0)),0===e.G&&e.y&&(e.y=-e.y),eW(e.u)||(e.S=eJ(e.u)),[Object.keys(e).reduce((t,n)=>{let i=r(n);return i&&(t[i]=e[n]),t},{}),n,t]}(r):[null,null,void 0];if(e_(r,"a")&&e_(r,"H"))throw new o("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:r,result:n,zone:i,specificOffset:s}}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function rv(e,t,r){return new rk(e,r).explainFromTokens(t)}function rT(e,t){if(!e)return null;let r=tf.create(t,e).dtFormatter((rw||(rw=rH.fromMillis(1555555555555)),rw)),n=r.formatToParts(),i=r.resolvedOptions();return n.map(t=>(function(e,t,r){let{type:n,value:i}=e;if("literal"===n){let e=/^\s+$/.test(i);return{literal:!e,val:e?" ":i}}let s=t[n],a=n;"hour"===n&&(a=null!=t.hour12?t.hour12?"hour12":"hour24":null!=t.hourCycle?"h11"===t.hourCycle||"h12"===t.hourCycle?"hour12":"hour24":r.hour12?"hour12":"hour24");let o=rg[a];if("object"==typeof o&&(o=o[s]),o)return{literal:!1,val:o}})(t,e,i))}let rS="Invalid DateTime";function rN(e){return new eS("unsupported zone",`the zone "${e.name}" is not supported`)}function rO(e){return null===e.weekData&&(e.weekData=eV(e.c)),e.weekData}function rb(e){return null===e.localWeekData&&(e.localWeekData=eV(e.c,e.loc.getMinDaysInFirstWeek(),e.loc.getStartOfWeek())),e.localWeekData}function rD(e,t){let r={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new rH({...r,...t,old:r})}function rM(e,t,r){let n=e-6e4*t,i=r.offset(n);if(t===i)return[n,t];n-=(i-t)*6e4;let s=r.offset(n);return i===s?[n,i]:[e-6e4*Math.min(i,s),Math.max(i,s)]}function rI(e,t){let r=new Date(e+=6e4*t);return{year:r.getUTCFullYear(),month:r.getUTCMonth()+1,day:r.getUTCDate(),hour:r.getUTCHours(),minute:r.getUTCMinutes(),second:r.getUTCSeconds(),millisecond:r.getUTCMilliseconds()}}function rV(e,t){let r=e.o,n=e.c.year+Math.trunc(t.years),i=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),s={...e.c,year:n,month:i,day:Math.min(e.c.day,eK(n,i))+Math.trunc(t.days)+7*Math.trunc(t.weeks)},a=rr.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),[o,l]=rM(eX(s),r,e.zone);return 0!==a&&(o+=a,l=e.zone.offset(o)),{ts:o,o:l}}function rE(e,t,r,n,i,s){let{setZone:a,zone:o}=r;if((!e||0===Object.keys(e).length)&&!t)return rH.invalid(new eS("unparsable",`the input "${i}" can't be parsed as ${n}`));{let n=rH.fromObject(e,{...r,zone:t||o,specificOffset:s});return a?n:n.setZone(o)}}function rx(e,t,r=!0){return e.isValid?tf.create(en.create("en-US"),{allowZ:r,forceSimple:!0}).formatDateTimeFromString(e,t):null}function rF(e,t){let r=e.c.year>9999||e.c.year<0,n="";return r&&e.c.year>=0&&(n+="+"),n+=eH(e.c.year,r?6:4),t?n+="-"+eH(e.c.month)+"-"+eH(e.c.day):n+=eH(e.c.month)+eH(e.c.day),n}function rC(e,t,r,n,i,s){let a=eH(e.c.hour);return t?(a+=":"+eH(e.c.minute),0===e.c.millisecond&&0===e.c.second&&r||(a+=":")):a+=eH(e.c.minute),0===e.c.millisecond&&0===e.c.second&&r||(a+=eH(e.c.second),0===e.c.millisecond&&n||(a+="."+eH(e.c.millisecond,3))),i&&(e.isOffsetFixed&&0===e.offset&&!s?a+="Z":e.o<0?a+="-"+eH(Math.trunc(-e.o/60))+":"+eH(Math.trunc(-e.o%60)):a+="+"+eH(Math.trunc(e.o/60))+":"+eH(Math.trunc(e.o%60))),s&&(a+="["+e.zone.ianaName+"]"),a}let r$={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},rZ={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},rW={ordinal:1,hour:0,minute:0,second:0,millisecond:0},rL=["year","month","day","hour","minute","second","millisecond"],rz=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],rq=["year","ordinal","hour","minute","second","millisecond"];function rA(e){switch(e.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return function(e){let t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new l(e);return t}(e)}}function rj(e,t){let n,i;let s=eo(t.zone,eT.defaultZone);if(!s.isValid)return rH.invalid(rN(s));let a=en.fromObject(t);if(eW(e.year))n=eT.now();else{for(let t of rL)eW(e[t])&&(e[t]=r$[t]);let t=e$(e)||eZ(e);if(t)return rH.invalid(t);let a=function(e){if(void 0===r&&(r=eT.now()),"iana"!==e.type)return e.offset(r);let t=e.name,n=rY.get(t);return void 0===n&&(n=e.offset(r),rY.set(t,n)),n}(s);[n,i]=rM(eX(e),a,s)}return new rH({ts:n,zone:s,loc:a,o:i})}function r_(e,t,r){let n=!!eW(r.round)||r.round,i=(e,i)=>(e=eG(e,n||r.calendary?0:2,!0),t.loc.clone(r).relFormatter(r).format(e,i)),s=n=>r.calendary?t.hasSame(e,n)?0:t.startOf(n).diff(e.startOf(n),n).get(n):t.diff(e,n).get(n);if(r.unit)return i(s(r.unit),r.unit);for(let e of r.units){let t=s(e);if(Math.abs(t)>=1)return i(t,e)}return i(e>t?-0:0,r.units[r.units.length-1])}function rU(e){let t={},r;return e.length>0&&"object"==typeof e[e.length-1]?(t=e[e.length-1],r=Array.from(e).slice(0,e.length-1)):r=Array.from(e),[t,r]}let rY=new Map;class rH{constructor(e){let t=e.zone||eT.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new eS("invalid input"):null)||(t.isValid?null:rN(t));this.ts=eW(e.ts)?eT.now():e.ts;let n=null,i=null;if(!r){if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[n,i]=[e.old.c,e.old.o];else{let s=eL(e.o)&&!e.old?e.o:t.offset(this.ts);n=(r=Number.isNaN((n=rI(this.ts,s)).year)?new eS("invalid input"):null)?null:n,i=r?null:s}}this._zone=t,this.loc=e.loc||en.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=n,this.o=i,this.isLuxonDateTime=!0}static now(){return new rH({})}static local(){let[e,t]=rU(arguments),[r,n,i,s,a,o,l]=t;return rj({year:r,month:n,day:i,hour:s,minute:a,second:o,millisecond:l},e)}static utc(){let[e,t]=rU(arguments),[r,n,i,s,a,o,l]=t;return e.zone=es.utcInstance,rj({year:r,month:n,day:i,hour:s,minute:a,second:o,millisecond:l},e)}static fromJSDate(e,t={}){let r="[object Date]"===Object.prototype.toString.call(e)?e.valueOf():NaN;if(Number.isNaN(r))return rH.invalid("invalid input");let n=eo(t.zone,eT.defaultZone);return n.isValid?new rH({ts:r,zone:n,loc:en.fromObject(t)}):rH.invalid(rN(n))}static fromMillis(e,t={}){if(eL(e))return e<-864e13||e>864e13?rH.invalid("Timestamp out of range"):new rH({ts:e,zone:eo(t.zone,eT.defaultZone),loc:en.fromObject(t)});throw new u(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(eL(e))return new rH({ts:1e3*e,zone:eo(t.zone,eT.defaultZone),loc:en.fromObject(t)});throw new u("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};let r=eo(t.zone,eT.defaultZone);if(!r.isValid)return rH.invalid(rN(r));let n=en.fromObject(t),i=e5(e,rA),{minDaysInFirstWeek:s,startOfWeek:a}=eC(i,n),l=eT.now(),u=eW(t.specificOffset)?r.offset(l):t.specificOffset,c=!eW(i.ordinal),h=!eW(i.year),d=!eW(i.month)||!eW(i.day),m=h||d,f=i.weekYear||i.weekNumber;if((m||c)&&f)throw new o("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(d&&c)throw new o("Can't mix ordinal dates with month/day");let y=f||i.weekday&&!m,g,w,p=rI(l,u);y?(g=rz,w=rZ,p=eV(p,s,a)):c?(g=rq,w=rW,p=ex(p)):(g=rL,w=r$);let k=!1;for(let e of g)eW(i[e])?k?i[e]=w[e]:i[e]=p[e]:k=!0;let v=(y?function(e,t=4,r=1){let n=ez(e.weekYear),i=eY(e.weekNumber,1,e1(e.weekYear,t,r)),s=eY(e.weekday,1,7);return n?i?!s&&eb("weekday",e.weekday):eb("week",e.weekNumber):eb("weekYear",e.weekYear)}(i,s,a):c?function(e){let t=ez(e.year),r=eY(e.ordinal,1,eQ(e.year));return t?!r&&eb("ordinal",e.ordinal):eb("year",e.year)}(i):e$(i))||eZ(i);if(v)return rH.invalid(v);let[T,S]=rM(eX(y?eE(i,s,a):c?eF(i):i),u,r),N=new rH({ts:T,zone:r,o:S,loc:n});return i.weekday&&m&&e.weekday!==N.weekday?rH.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${N.toISO()}`):N.isValid?N:rH.invalid(N.invalid)}static fromISO(e,t={}){let[r,n]=tp(e,[tH,tG],[tR,tB],[tP,tQ],[tJ,tK]);return rE(r,n,t,"ISO 8601",e)}static fromRFC2822(e,t={}){let[r,n]=tp(e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim(),[tz,tq]);return rE(r,n,t,"RFC 2822",e)}static fromHTTP(e,t={}){let[r,n]=tp(e,[tA,tU],[tj,tU],[t_,tY]);return rE(r,n,t,"HTTP",t)}static fromFormat(e,t,r={}){if(eW(e)||eW(t))throw new u("fromFormat requires an input string and a format");let{locale:n=null,numberingSystem:i=null}=r,[s,a,o,l]=function(e,t,r){let{result:n,zone:i,specificOffset:s,invalidReason:a}=rv(e,t,r);return[n,i,s,a]}(en.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0}),e,t);return l?rH.invalid(l):rE(s,a,r,`format ${t}`,e,o)}static fromString(e,t,r={}){return rH.fromFormat(e,t,r)}static fromSQL(e,t={}){let[r,n]=tp(e,[t0,tG],[t1,t2]);return rE(r,n,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the DateTime is invalid");let r=e instanceof eS?e:new eS(e,t);if(!eT.throwOnInvalid)return new rH({invalid:r});throw new i(r)}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){let r=rT(e,en.fromObject(t));return r?r.map(e=>e?e.val:null).join(""):null}static expandFormat(e,t={}){return rp(tf.parseFormat(e),en.fromObject(t)).map(e=>e.val).join("")}static resetCache(){r=void 0,rY.clear()}get(e){return this[e]}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?rO(this).weekYear:NaN}get weekNumber(){return this.isValid?rO(this).weekNumber:NaN}get weekday(){return this.isValid?rO(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?rb(this).weekday:NaN}get localWeekNumber(){return this.isValid?rb(this).weekNumber:NaN}get localWeekYear(){return this.isValid?rb(this).weekYear:NaN}get ordinal(){return this.isValid?ex(this.c).ordinal:NaN}get monthShort(){return this.isValid?rs.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?rs.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?rs.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?rs.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=eX(this.c),t=this.zone.offset(e-864e5),r=this.zone.offset(e+864e5),n=this.zone.offset(e-6e4*t),i=this.zone.offset(e-6e4*r);if(n===i)return[this];let s=e-6e4*n,a=e-6e4*i,o=rI(s,n),l=rI(a,i);return o.hour===l.hour&&o.minute===l.minute&&o.second===l.second&&o.millisecond===l.millisecond?[rD(this,{ts:s}),rD(this,{ts:a})]:[this]}get isInLeapYear(){return eB(this.year)}get daysInMonth(){return eK(this.year,this.month)}get daysInYear(){return this.isValid?eQ(this.year):NaN}get weeksInWeekYear(){return this.isValid?e1(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?e1(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:t,numberingSystem:r,calendar:n}=tf.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:r,outputCalendar:n}}toUTC(e=0,t={}){return this.setZone(es.instance(e),t)}toLocal(){return this.setZone(eT.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:r=!1}={}){if((e=eo(e,eT.defaultZone)).equals(this.zone))return this;if(!e.isValid)return rH.invalid(rN(e));{let i=this.ts;if(t||r){var n;let t=e.offset(this.ts),r=this.toObject();[i]=(n=e,rM(eX(r),t,n))}return rD(this,{ts:i,zone:e})}}reconfigure({locale:e,numberingSystem:t,outputCalendar:r}={}){return rD(this,{loc:this.loc.clone({locale:e,numberingSystem:t,outputCalendar:r})})}setLocale(e){return this.reconfigure({locale:e})}set(e){var t,r,n;let i;if(!this.isValid)return this;let s=e5(e,rA),{minDaysInFirstWeek:a,startOfWeek:l}=eC(s,this.loc),u=!eW(s.weekYear)||!eW(s.weekNumber)||!eW(s.weekday),c=!eW(s.ordinal),h=!eW(s.year),d=!eW(s.month)||!eW(s.day),m=s.weekYear||s.weekNumber;if((h||d||c)&&m)throw new o("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(d&&c)throw new o("Can't mix ordinal dates with month/day");u?i=eE({...eV(this.c,a,l),...s},a,l):eW(s.ordinal)?(i={...this.toObject(),...s},eW(s.day)&&(i.day=Math.min(eK(i.year,i.month),i.day))):i=eF({...ex(this.c),...s});let[f,y]=(t=i,r=this.o,n=this.zone,rM(eX(t),r,n));return rD(this,{ts:f,o:y})}plus(e){return this.isValid?rD(this,rV(this,rr.fromDurationLike(e))):this}minus(e){return this.isValid?rD(this,rV(this,rr.fromDurationLike(e).negate())):this}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;let r={},n=rr.normalizeUnit(e);switch(n){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0}if("weeks"===n){if(t){let e=this.loc.getStartOfWeek(),{weekday:t}=this;t<e&&(r.weekNumber=this.weekNumber-1),r.weekday=e}else r.weekday=1}if("quarters"===n){let e=Math.ceil(this.month/3);r.month=(e-1)*3+1}return this.set(r)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?tf.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):rS}toLocaleString(e=f,t={}){return this.isValid?tf.create(this.loc.clone(t),e).formatDateTime(this):rS}toLocaleParts(e={}){return this.isValid?tf.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:r=!1,includeOffset:n=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let s="extended"===e;return rF(this,s)+"T"+rC(this,s,t,r,n,i)}toISODate({format:e="extended"}={}){return this.isValid?rF(this,"extended"===e):null}toISOWeekDate(){return rx(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:r=!0,includePrefix:n=!1,extendedZone:i=!1,format:s="extended"}={}){return this.isValid?(n?"T":"")+rC(this,"extended"===s,t,e,r,i):null}toRFC2822(){return rx(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return rx(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?rF(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:r=!0}={}){let n="HH:mm:ss.SSS";return(t||e)&&(r&&(n+=" "),t?n+="z":e&&(n+="ZZ")),rx(this,n,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():rS}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",r={}){if(!this.isValid||!e.isValid)return rr.invalid("created by diffing an invalid DateTime");let n={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=(Array.isArray(t)?t:[t]).map(rr.normalizeUnit),s=e.valueOf()>this.valueOf(),a=function(e,t,r,n){let[i,s,a,o]=function(e,t,r){let n,i;let s={},a=e;for(let[o,l]of[["years",(e,t)=>t.year-e.year],["quarters",(e,t)=>t.quarter-e.quarter+(t.year-e.year)*4],["months",(e,t)=>t.month-e.month+(t.year-e.year)*12],["weeks",(e,t)=>{let r=ra(e,t);return(r-r%7)/7}],["days",ra]])r.indexOf(o)>=0&&(n=o,s[o]=l(e,t),(i=a.plus(s))>t?(s[o]--,(e=a.plus(s))>t&&(i=e,s[o]--,e=a.plus(s))):e=i);return[e,s,i,n]}(e,t,r),l=t-i,u=r.filter(e=>["hours","minutes","seconds","milliseconds"].indexOf(e)>=0);0===u.length&&(a<t&&(a=i.plus({[o]:1})),a!==i&&(s[o]=(s[o]||0)+l/(a-i)));let c=rr.fromObject(s,n);return u.length>0?rr.fromMillis(l,n).shiftTo(...u).plus(c):c}(s?this:e,s?e:this,i,n);return s?a.negate():a}diffNow(e="milliseconds",t={}){return this.diff(rH.now(),e,t)}until(e){return this.isValid?ri.fromDateTimes(this,e):this}hasSame(e,t,r){if(!this.isValid)return!1;let n=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(t,r)<=n&&n<=i.endOf(t,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let t=e.base||rH.fromObject({},{zone:this.zone}),r=e.padding?this<t?-e.padding:e.padding:0,n=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(n=e.unit,i=void 0),r_(t,this.plus(r),{...e,numeric:"always",units:n,unit:i})}toRelativeCalendar(e={}){return this.isValid?r_(e.base||rH.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(rH.isDateTime))throw new u("min requires all arguments be DateTimes");return ej(e,e=>e.valueOf(),Math.min)}static max(...e){if(!e.every(rH.isDateTime))throw new u("max requires all arguments be DateTimes");return ej(e,e=>e.valueOf(),Math.max)}static fromFormatExplain(e,t,r={}){let{locale:n=null,numberingSystem:i=null}=r;return rv(en.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0}),e,t)}static fromStringExplain(e,t,r={}){return rH.fromFormatExplain(e,t,r)}static buildFormatParser(e,t={}){let{locale:r=null,numberingSystem:n=null}=t;return new rk(en.fromOpts({locale:r,numberingSystem:n,defaultToEN:!0}),e)}static fromFormatParser(e,t,r={}){if(eW(e)||eW(t))throw new u("fromFormatParser requires an input string and a format parser");let{locale:n=null,numberingSystem:i=null}=r,s=en.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0});if(!s.equals(t.locale))throw new u(`fromFormatParser called with a locale of ${s}, but the format parser was created for ${t.locale}`);let{result:a,zone:o,specificOffset:l,invalidReason:c}=t.explainFromTokens(e);return c?rH.invalid(c):rE(a,o,r,`format ${t.format}`,e,l)}static get DATE_SHORT(){return f}static get DATE_MED(){return y}static get DATE_MED_WITH_WEEKDAY(){return g}static get DATE_FULL(){return w}static get DATE_HUGE(){return p}static get TIME_SIMPLE(){return k}static get TIME_WITH_SECONDS(){return v}static get TIME_WITH_SHORT_OFFSET(){return T}static get TIME_WITH_LONG_OFFSET(){return S}static get TIME_24_SIMPLE(){return N}static get TIME_24_WITH_SECONDS(){return O}static get TIME_24_WITH_SHORT_OFFSET(){return b}static get TIME_24_WITH_LONG_OFFSET(){return D}static get DATETIME_SHORT(){return M}static get DATETIME_SHORT_WITH_SECONDS(){return I}static get DATETIME_MED(){return V}static get DATETIME_MED_WITH_SECONDS(){return E}static get DATETIME_MED_WITH_WEEKDAY(){return x}static get DATETIME_FULL(){return F}static get DATETIME_FULL_WITH_SECONDS(){return C}static get DATETIME_HUGE(){return $}static get DATETIME_HUGE_WITH_SECONDS(){return Z}}function rR(e){if(rH.isDateTime(e))return e;if(e&&e.valueOf&&eL(e.valueOf()))return rH.fromJSDate(e);if(e&&"object"==typeof e)return rH.fromObject(e);throw new u(`Unknown datetime argument: ${e}, of type ${typeof e}`)}t.DateTime=rH,t.Duration=rr,t.FixedOffsetZone=es,t.IANAZone=_,t.Info=rs,t.Interval=ri,t.InvalidZone=ea,t.Settings=eT,t.SystemZone=z,t.VERSION="3.6.1",t.Zone=W}}]);