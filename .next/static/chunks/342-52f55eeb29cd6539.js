"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[342],{50342:function(e,t,n){var a=n(64836),r=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n(10434)),i=a(n(38416)),c=p(n(67294)),l=p(n(47642)),u=a(n(93967)),f=a(n(22029)),d=a(n(31682)),s=a(n(12155));a(n(76092));var v=n(31407),b=a(n(25742));function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(m=function(e){return e?n:t})(e)}function p(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=m(t);if(n&&n.has(e))return n.get(e);var a={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(a,i,c):a[i]=e[i]}return a.default=e,n&&n.set(e,a),a}var h=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};function y(e){var t,n=e.type,a=e.className,r=e.size,m=e.onEdit,p=e.hideAdd,y=e.centered,g=e.addIcon,E=h(e,["type","className","size","onEdit","hideAdd","centered","addIcon"]),Z=E.prefixCls,k=E.moreIcon,w=void 0===k?c.createElement(f.default,null):k,x=c.useContext(v.ConfigContext),C=x.getPrefixCls,P=x.direction,T=C("tabs",Z);"editable-card"===n&&(t={onEdit:function(e,t){var n=t.key,a=t.event;null==m||m("add"===e?a:n,e)},removeIcon:c.createElement(s.default,null),addIcon:g||c.createElement(d.default,null),showAdd:!0!==p});var I=C();return c.createElement(b.default.Consumer,null,function(e){var f,d=void 0!==r?r:e;return c.createElement(l.default,(0,o.default)({direction:P,moreTransitionName:"".concat(I,"-slide-up")},E,{className:(0,u.default)((f={},(0,i.default)(f,"".concat(T,"-").concat(d),d),(0,i.default)(f,"".concat(T,"-card"),["card","editable-card"].includes(n)),(0,i.default)(f,"".concat(T,"-editable-card"),"editable-card"===n),(0,i.default)(f,"".concat(T,"-centered"),y),f),a),editable:t,moreIcon:w,prefixCls:T}))})}y.TabPane=l.TabPane,t.default=y},47642:function(e,t,n){n.r(t),n.d(t,{TabPane:function(){return M},default:function(){return D}});var a=n(87462),r=n(4942),o=n(97685),i=n(71002),c=n(91),l=n(1413),u=n(67294),f=n(93967),d=n.n(f),s=n(50344),v=n(31131),b=n(21770),m=n(74902),p=n(75164),h=n(48555);function y(e){var t=(0,u.useRef)(),n=(0,u.useRef)(!1);return(0,u.useEffect)(function(){return function(){n.current=!0,p.Z.cancel(t.current)}},[]),function(){for(var a=arguments.length,r=Array(a),o=0;o<a;o++)r[o]=arguments[o];n.current||(p.Z.cancel(t.current),t.current=(0,p.Z)(function(){e.apply(void 0,r)}))}}var g=n(15105),E=u.forwardRef(function(e,t){var n,a=e.prefixCls,o=e.id,i=e.active,c=e.tab,l=c.key,f=c.tab,s=c.disabled,v=c.closeIcon,b=e.closable,m=e.renderWrapper,p=e.removeAriaLabel,h=e.editable,y=e.onClick,E=e.onRemove,Z=e.onFocus,k=e.style,w="".concat(a,"-tab");u.useEffect(function(){return E},[]);var x=h&&!1!==b&&!s;function C(e){s||y(e)}var P=u.createElement("div",{key:l,ref:t,className:d()(w,(n={},(0,r.Z)(n,"".concat(w,"-with-remove"),x),(0,r.Z)(n,"".concat(w,"-active"),i),(0,r.Z)(n,"".concat(w,"-disabled"),s),n)),style:k,onClick:C},u.createElement("div",{role:"tab","aria-selected":i,id:o&&"".concat(o,"-tab-").concat(l),className:"".concat(w,"-btn"),"aria-controls":o&&"".concat(o,"-panel-").concat(l),"aria-disabled":s,tabIndex:s?null:0,onClick:function(e){e.stopPropagation(),C(e)},onKeyDown:function(e){[g.Z.SPACE,g.Z.ENTER].includes(e.which)&&(e.preventDefault(),C(e))},onFocus:Z},f),x&&u.createElement("button",{type:"button","aria-label":p||"remove",tabIndex:0,className:"".concat(w,"-remove"),onClick:function(e){e.stopPropagation(),e.preventDefault(),e.stopPropagation(),h.onEdit("remove",{key:l,event:e})}},v||h.removeIcon||"\xd7"));return m?m(P):P}),Z={width:0,height:0,left:0,top:0},k={width:0,height:0,left:0,top:0,right:0},w=n(33203),x=n(60057),C=u.forwardRef(function(e,t){var n=e.prefixCls,a=e.editable,r=e.locale,o=e.style;return a&&!1!==a.showAdd?u.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:o,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){a.onEdit("add",{event:e})}},a.addIcon||"+"):null}),P=u.memo(u.forwardRef(function(e,t){var n=e.prefixCls,a=e.id,i=e.tabs,c=e.locale,l=e.mobile,f=e.moreIcon,s=e.moreTransitionName,v=e.style,b=e.className,m=e.editable,p=e.tabBarGutter,h=e.rtl,y=e.removeAriaLabel,E=e.onTabClick,Z=(0,u.useState)(!1),k=(0,o.Z)(Z,2),P=k[0],T=k[1],I=(0,u.useState)(null),N=(0,o.Z)(I,2),S=N[0],R=N[1],M="".concat(a,"-more-popup"),L="".concat(n,"-dropdown"),O=null!==S?"".concat(M,"-").concat(S):null,A=null==c?void 0:c.dropdownAriaLabel,D=u.createElement(w.default,{onClick:function(e){E(e.key,e.domEvent),T(!1)},id:M,tabIndex:-1,role:"listbox","aria-activedescendant":O,selectedKeys:[S],"aria-label":void 0!==A?A:"expanded dropdown"},i.map(function(e){var t=m&&!1!==e.closable&&!e.disabled;return u.createElement(w.MenuItem,{key:e.key,id:"".concat(M,"-").concat(e.key),role:"option","aria-controls":a&&"".concat(a,"-panel-").concat(e.key),disabled:e.disabled},u.createElement("span",null,e.tab),t&&u.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(L,"-menu-item-remove"),onClick:function(t){var n;t.stopPropagation(),n=e.key,t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:n,event:t})}},e.closeIcon||m.removeIcon||"\xd7"))}));function j(e){for(var t=i.filter(function(e){return!e.disabled}),n=t.findIndex(function(e){return e.key===S})||0,a=t.length,r=0;r<a;r+=1){var o=t[n=(n+e+a)%a];if(!o.disabled){R(o.key);return}}}(0,u.useEffect)(function(){var e=document.getElementById(O);e&&e.scrollIntoView&&e.scrollIntoView(!1)},[S]),(0,u.useEffect)(function(){P||R(null)},[P]);var B=(0,r.Z)({},h?"marginRight":"marginLeft",p);i.length||(B.visibility="hidden",B.order=1);var K=d()((0,r.Z)({},"".concat(L,"-rtl"),h)),W=l?null:u.createElement(x.default,{prefixCls:L,overlay:D,trigger:["hover"],visible:P,transitionName:s,onVisibleChange:T,overlayClassName:K,mouseEnterDelay:.1,mouseLeaveDelay:.1},u.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:B,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":M,id:"".concat(a,"-more"),"aria-expanded":P,onKeyDown:function(e){var t=e.which;if(!P){[g.Z.DOWN,g.Z.SPACE,g.Z.ENTER].includes(t)&&(T(!0),e.preventDefault());return}switch(t){case g.Z.UP:j(-1),e.preventDefault();break;case g.Z.DOWN:j(1),e.preventDefault();break;case g.Z.ESC:T(!1);break;case g.Z.SPACE:case g.Z.ENTER:null!==S&&E(S,e)}}},void 0===f?"More":f));return u.createElement("div",{className:d()("".concat(n,"-nav-operations"),b),style:v,ref:t},W,u.createElement(C,{prefixCls:n,locale:c,editable:m}))}),function(e,t){return t.tabMoving}),T=(0,u.createContext)(null);function I(e,t){var n=u.useRef(e),a=u.useState({}),r=(0,o.Z)(a,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var N=function(e){var t,n=e.position,a=e.prefixCls,r=e.extra;if(!r)return null;var o={};return r&&"object"===(0,i.Z)(r)&&!u.isValidElement(r)?o=r:o.right=r,"right"===n&&(t=o.right),"left"===n&&(t=o.left),t?u.createElement("div",{className:"".concat(a,"-extra-content")},t):null},S=u.forwardRef(function(e,t){var n,i,c,f,s,v,b,g,w,x,S,R,M,L,O,A,D,j,B,K,W,_,V,G,z,H,Y,F,X,U,q,J,Q,$,ee,et,en,ea,er,eo,ei,ec,el,eu,ef,ed,es=u.useContext(T),ev=es.prefixCls,eb=es.tabs,em=e.className,ep=e.style,eh=e.id,ey=e.animated,eg=e.activeKey,eE=e.rtl,eZ=e.extra,ek=e.editable,ew=e.locale,ex=e.tabPosition,eC=e.tabBarGutter,eP=e.children,eT=e.onTabClick,eI=e.onTabScroll,eN=(0,u.useRef)(),eS=(0,u.useRef)(),eR=(0,u.useRef)(),eM=(0,u.useRef)(),eL=(n=(0,u.useRef)(new Map),[function(e){return n.current.has(e)||n.current.set(e,u.createRef()),n.current.get(e)},function(e){n.current.delete(e)}]),eO=(0,o.Z)(eL,2),eA=eO[0],eD=eO[1],ej="top"===ex||"bottom"===ex,eB=I(0,function(e,t){ej&&eI&&eI({direction:e>t?"left":"right"})}),eK=(0,o.Z)(eB,2),eW=eK[0],e_=eK[1],eV=I(0,function(e,t){!ej&&eI&&eI({direction:e>t?"top":"bottom"})}),eG=(0,o.Z)(eV,2),ez=eG[0],eH=eG[1],eY=(0,u.useState)(0),eF=(0,o.Z)(eY,2),eX=eF[0],eU=eF[1],eq=(0,u.useState)(0),eJ=(0,o.Z)(eq,2),eQ=eJ[0],e$=eJ[1],e0=(0,u.useState)(null),e1=(0,o.Z)(e0,2),e2=e1[0],e4=e1[1],e7=(0,u.useState)(null),e6=(0,o.Z)(e7,2),e3=e6[0],e5=e6[1],e9=(0,u.useState)(0),e8=(0,o.Z)(e9,2),te=e8[0],tt=e8[1],tn=(0,u.useState)(0),ta=(0,o.Z)(tn,2),tr=ta[0],to=ta[1],ti=(i=new Map,c=(0,u.useRef)([]),f=(0,u.useState)({}),s=(0,o.Z)(f,2)[1],v=(0,u.useRef)("function"==typeof i?i():i),b=y(function(){var e=v.current;c.current.forEach(function(t){e=t(e)}),c.current=[],v.current=e,s({})}),[v.current,function(e){c.current.push(e),b()}]),tc=(0,o.Z)(ti,2),tl=tc[0],tu=tc[1],tf=(0,u.useMemo)(function(){for(var e=new Map,t=tl.get(null===(r=eb[0])||void 0===r?void 0:r.key)||Z,n=t.left+t.width,a=0;a<eb.length;a+=1){var r,o,i=eb[a].key,c=tl.get(i);c||(c=tl.get(null===(o=eb[a-1])||void 0===o?void 0:o.key)||Z);var u=e.get(i)||(0,l.Z)({},c);u.right=n-u.left-u.width,e.set(i,u)}return e},[eb.map(function(e){return e.key}).join("_"),tl,eX]),td="".concat(ev,"-nav-operations-hidden"),ts=0,tv=0;function tb(e){return e<ts?ts:e>tv?tv:e}ej?eE?(ts=0,tv=Math.max(0,eX-e2)):(ts=Math.min(0,e2-eX),tv=0):(ts=Math.min(0,e3-eQ),tv=0);var tm=(0,u.useRef)(),tp=(0,u.useState)(),th=(0,o.Z)(tp,2),ty=th[0],tg=th[1];function tE(){tg(Date.now())}function tZ(){window.clearTimeout(tm.current)}function tk(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eg,t=tf.get(e)||{width:0,height:0,left:0,right:0,top:0};if(ej){var n=eW;eE?t.right<eW?n=t.right:t.right+t.width>eW+e2&&(n=t.right+t.width-e2):t.left<-eW?n=-t.left:t.left+t.width>-eW+e2&&(n=-(t.left+t.width-e2)),eH(0),e_(tb(n))}else{var a=ez;t.top<-ez?a=-t.top:t.top+t.height>-ez+e3&&(a=-(t.top+t.height-e3)),e_(0),eH(tb(a))}}g=function(e,t){function n(e,t){e(function(e){return tb(e+t)})}if(ej){if(e2>=eX)return!1;n(e_,e)}else{if(e3>=eQ)return!1;n(eH,t)}return tZ(),tE(),!0},w=(0,u.useState)(),S=(x=(0,o.Z)(w,2))[0],R=x[1],M=(0,u.useState)(0),O=(L=(0,o.Z)(M,2))[0],A=L[1],D=(0,u.useState)(0),B=(j=(0,o.Z)(D,2))[0],K=j[1],W=(0,u.useState)(),V=(_=(0,o.Z)(W,2))[0],G=_[1],z=(0,u.useRef)(),H=(0,u.useRef)(),(Y=(0,u.useRef)(null)).current={onTouchStart:function(e){var t=e.touches[0];R({x:t.screenX,y:t.screenY}),window.clearInterval(z.current)},onTouchMove:function(e){if(S){e.preventDefault();var t=e.touches[0],n=t.screenX,a=t.screenY;R({x:n,y:a});var r=n-S.x,o=a-S.y;g(r,o);var i=Date.now();A(i),K(i-O),G({x:r,y:o})}},onTouchEnd:function(){if(S&&(R(null),G(null),V)){var e=V.x/B,t=V.y/B;if(!(.1>Math.max(Math.abs(e),Math.abs(t)))){var n=e,a=t;z.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(z.current);return}n*=.9046104802746175,a*=.9046104802746175,g(20*n,20*a)},20)}}},onWheel:function(e){var t=e.deltaX,n=e.deltaY,a=0,r=Math.abs(t),o=Math.abs(n);r===o?a="x"===H.current?t:n:r>o?(a=t,H.current="x"):(a=n,H.current="y"),g(-a,-a)&&e.preventDefault()}},u.useEffect(function(){function e(e){Y.current.onTouchMove(e)}function t(e){Y.current.onTouchEnd(e)}return document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",t,{passive:!1}),eN.current.addEventListener("touchstart",function(e){Y.current.onTouchStart(e)},{passive:!1}),eN.current.addEventListener("wheel",function(e){Y.current.onWheel(e)}),function(){document.removeEventListener("touchmove",e),document.removeEventListener("touchend",t)}},[]),(0,u.useEffect)(function(){return tZ(),ty&&(tm.current=window.setTimeout(function(){tg(0)},100)),tZ},[ty]);var tw=(F={width:e2,height:e3,left:eW,top:ez},X={width:eX,height:eQ},U={width:te,height:tr},ee=(q=(0,l.Z)((0,l.Z)({},e),{},{tabs:eb})).tabs,et=q.tabPosition,en=q.rtl,["top","bottom"].includes(et)?(J="width",Q=en?"right":"left",$=Math.abs(F.left)):(J="height",Q="top",$=-F.top),ea=F[J],er=X[J],eo=U[J],ei=ea,er+eo>ea&&er<ea&&(ei=ea-eo),(0,u.useMemo)(function(){if(!ee.length)return[0,0];for(var e=ee.length,t=e,n=0;n<e;n+=1){var a=tf.get(ee[n].key)||k;if(a[Q]+a[J]>$+ei){t=n-1;break}}for(var r=0,o=e-1;o>=0;o-=1)if((tf.get(ee[o].key)||k)[Q]<$){r=o+1;break}return[r,t]},[tf,$,ei,et,ee.map(function(e){return e.key}).join("_"),en])),tx=(0,o.Z)(tw,2),tC=tx[0],tP=tx[1],tT={};"top"===ex||"bottom"===ex?tT[eE?"marginRight":"marginLeft"]=eC:tT.marginTop=eC;var tI=eb.map(function(e,t){var n=e.key;return u.createElement(E,{id:eh,prefixCls:ev,key:n,tab:e,style:0===t?void 0:tT,closable:e.closable,editable:ek,active:n===eg,renderWrapper:eP,removeAriaLabel:null==ew?void 0:ew.removeAriaLabel,ref:eA(n),onClick:function(e){eT(n,e)},onRemove:function(){eD(n)},onFocus:function(){tk(n),tE(),eN.current&&(eE||(eN.current.scrollLeft=0),eN.current.scrollTop=0)}})}),tN=y(function(){var e,t,n,a,r,o,i=(null===(e=eN.current)||void 0===e?void 0:e.offsetWidth)||0,c=(null===(t=eN.current)||void 0===t?void 0:t.offsetHeight)||0,l=(null===(n=eM.current)||void 0===n?void 0:n.offsetWidth)||0,u=(null===(a=eM.current)||void 0===a?void 0:a.offsetHeight)||0;e4(i),e5(c),tt(l),to(u);var f=((null===(r=eS.current)||void 0===r?void 0:r.offsetWidth)||0)-l,d=((null===(o=eS.current)||void 0===o?void 0:o.offsetHeight)||0)-u;eU(f),e$(d),tu(function(){var e=new Map;return eb.forEach(function(t){var n=t.key,a=eA(n).current;a&&e.set(n,{width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop})}),e})}),tS=eb.slice(0,tC),tR=eb.slice(tP+1),tM=[].concat((0,m.Z)(tS),(0,m.Z)(tR)),tL=(0,u.useState)(),tO=(0,o.Z)(tL,2),tA=tO[0],tD=tO[1],tj=tf.get(eg),tB=(0,u.useRef)();function tK(){p.Z.cancel(tB.current)}(0,u.useEffect)(function(){var e={};return tj&&(ej?(eE?e.right=tj.right:e.left=tj.left,e.width=tj.width):(e.top=tj.top,e.height=tj.height)),tK(),tB.current=(0,p.Z)(function(){tD(e)}),tK},[tj,ej,eE]),(0,u.useEffect)(function(){tk()},[eg,tj,tf,ej]),(0,u.useEffect)(function(){tN()},[eE,eC,eg,eb.map(function(e){return e.key}).join("_")]);var tW=!!tM.length,t_="".concat(ev,"-nav-wrap");return ej?eE?(eu=eW>0,el=eW+e2<eX):(el=eW<0,eu=-eW+e2<eX):(ef=ez<0,ed=-ez+e3<eQ),u.createElement("div",{ref:t,role:"tablist",className:d()("".concat(ev,"-nav"),em),style:ep,onKeyDown:function(){tE()}},u.createElement(N,{position:"left",extra:eZ,prefixCls:ev}),u.createElement(h.default,{onResize:tN},u.createElement("div",{className:d()(t_,(ec={},(0,r.Z)(ec,"".concat(t_,"-ping-left"),el),(0,r.Z)(ec,"".concat(t_,"-ping-right"),eu),(0,r.Z)(ec,"".concat(t_,"-ping-top"),ef),(0,r.Z)(ec,"".concat(t_,"-ping-bottom"),ed),ec)),ref:eN},u.createElement(h.default,{onResize:tN},u.createElement("div",{ref:eS,className:"".concat(ev,"-nav-list"),style:{transform:"translate(".concat(eW,"px, ").concat(ez,"px)"),transition:ty?"none":void 0}},tI,u.createElement(C,{ref:eM,prefixCls:ev,locale:ew,editable:ek,style:(0,l.Z)((0,l.Z)({},0===tI.length?void 0:tT),{},{visibility:tW?"hidden":null})}),u.createElement("div",{className:d()("".concat(ev,"-ink-bar"),(0,r.Z)({},"".concat(ev,"-ink-bar-animated"),ey.inkBar)),style:tA}))))),u.createElement(P,(0,a.Z)({},e,{removeAriaLabel:null==ew?void 0:ew.removeAriaLabel,ref:eR,prefixCls:ev,tabs:tM,className:!tW&&td,tabMoving:!!ty})),u.createElement(N,{position:"right",extra:eZ,prefixCls:ev}))});function R(e){var t=e.id,n=e.activeKey,a=e.animated,o=e.tabPosition,i=e.rtl,c=e.destroyInactiveTabPane,l=u.useContext(T),f=l.prefixCls,s=l.tabs,v=a.tabPane,b=s.findIndex(function(e){return e.key===n});return u.createElement("div",{className:d()("".concat(f,"-content-holder"))},u.createElement("div",{className:d()("".concat(f,"-content"),"".concat(f,"-content-").concat(o),(0,r.Z)({},"".concat(f,"-content-animated"),v)),style:b&&v?(0,r.Z)({},i?"marginRight":"marginLeft","-".concat(b,"00%")):null},s.map(function(e){return u.cloneElement(e.node,{key:e.key,prefixCls:f,tabKey:e.key,id:t,animated:v,active:e.key===n,destroyInactiveTabPane:c})})))}function M(e){var t=e.prefixCls,n=e.forceRender,a=e.className,r=e.style,i=e.id,c=e.active,f=e.animated,s=e.destroyInactiveTabPane,v=e.tabKey,b=e.children,m=u.useState(n),p=(0,o.Z)(m,2),h=p[0],y=p[1];u.useEffect(function(){c?y(!0):s&&y(!1)},[c,s]);var g={};return c||(f?(g.visibility="hidden",g.height=0,g.overflowY="hidden"):g.display="none"),u.createElement("div",{id:i&&"".concat(i,"-panel-").concat(v),role:"tabpanel",tabIndex:c?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(v),"aria-hidden":!c,style:(0,l.Z)((0,l.Z)({},g),r),className:d()("".concat(t,"-tabpane"),c&&"".concat(t,"-tabpane-active"),a)},(c||h||n)&&b)}var L=["id","prefixCls","className","children","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","moreIcon","moreTransitionName","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll"],O=0,A=u.forwardRef(function(e,t){var n,f,m,p=e.id,h=e.prefixCls,y=void 0===h?"rc-tabs":h,g=e.className,E=e.children,Z=e.direction,k=e.activeKey,w=e.defaultActiveKey,x=e.editable,C=e.animated,P=void 0===C?{inkBar:!0,tabPane:!1}:C,I=e.tabPosition,N=void 0===I?"top":I,M=e.tabBarGutter,A=e.tabBarStyle,D=e.tabBarExtraContent,j=e.locale,B=e.moreIcon,K=e.moreTransitionName,W=e.destroyInactiveTabPane,_=e.renderTabBar,V=e.onChange,G=e.onTabClick,z=e.onTabScroll,H=(0,c.Z)(e,L),Y=(0,s.Z)(E).map(function(e){if(u.isValidElement(e)){var t=void 0!==e.key?String(e.key):void 0;return(0,l.Z)((0,l.Z)({key:t},e.props),{},{node:e})}return null}).filter(function(e){return e}),F="rtl"===Z;f=!1===P?{inkBar:!1,tabPane:!1}:!0===P?{inkBar:!0,tabPane:!0}:(0,l.Z)({inkBar:!0,tabPane:!1},"object"===(0,i.Z)(P)?P:{});var X=(0,u.useState)(!1),U=(0,o.Z)(X,2),q=U[0],J=U[1];(0,u.useEffect)(function(){J((0,v.Z)())},[]);var Q=(0,b.Z)(function(){var e;return null===(e=Y[0])||void 0===e?void 0:e.key},{value:k,defaultValue:w}),$=(0,o.Z)(Q,2),ee=$[0],et=$[1],en=(0,u.useState)(function(){return Y.findIndex(function(e){return e.key===ee})}),ea=(0,o.Z)(en,2),er=ea[0],eo=ea[1];(0,u.useEffect)(function(){var e,t=Y.findIndex(function(e){return e.key===ee});-1===t&&(t=Math.max(0,Math.min(er,Y.length-1)),et(null===(e=Y[t])||void 0===e?void 0:e.key)),eo(t)},[Y.map(function(e){return e.key}).join("_"),ee,er]);var ei=(0,b.Z)(null,{value:p}),ec=(0,o.Z)(ei,2),el=ec[0],eu=ec[1],ef=N;q&&!["left","right"].includes(N)&&(ef="top"),(0,u.useEffect)(function(){p||(eu("rc-tabs-".concat(O)),O+=1)},[]);var ed={id:el,activeKey:ee,animated:f,tabPosition:ef,rtl:F,mobile:q},es=(0,l.Z)((0,l.Z)({},ed),{},{editable:x,locale:j,moreIcon:B,moreTransitionName:K,tabBarGutter:M,onTabClick:function(e,t){null==G||G(e,t);var n=e!==ee;et(e),n&&(null==V||V(e))},onTabScroll:z,extra:D,style:A,panes:E});return m=_?_(es,S):u.createElement(S,es),u.createElement(T.Provider,{value:{tabs:Y,prefixCls:y}},u.createElement("div",(0,a.Z)({ref:t,id:p,className:d()(y,"".concat(y,"-").concat(ef),(n={},(0,r.Z)(n,"".concat(y,"-mobile"),q),(0,r.Z)(n,"".concat(y,"-editable"),x),(0,r.Z)(n,"".concat(y,"-rtl"),F),n),g)},H),m,u.createElement(R,(0,a.Z)({destroyInactiveTabPane:W},ed,{animated:f}))))});A.TabPane=M;var D=A}}]);