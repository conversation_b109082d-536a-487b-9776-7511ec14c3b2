(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[490],{20995:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/knowledge/question-sql-pairs",function(){return s(21516)}])},25707:function(e,t,s){"use strict";s.d(t,{H:function(){return d},z:function(){return c}});var n=s(85893),a=s(21367),i=s(4071),l=s.n(i),r=s(39616);let o=e=>t=>{let{onClick:s,onMouseEnter:i,onMouseLeave:l,className:r,marginLeft:o,marginRight:d,...c}=t;return(0,n.jsx)(a.default,{className:r,style:{marginLeft:o,marginRight:d},icon:e,onClick:e=>{s&&s(e),e.stopPropagation()},onMouseEnter:e=>{i&&i(e),e.stopPropagation()},onMouseLeave:e=>{l&&l(e),e.stopPropagation()},type:"text",size:"small",...c})},d=o((0,n.jsx)(l(),{})),c=o((0,n.jsx)(r.nX,{}))},87021:function(e,t,s){"use strict";s.d(t,{T:function(){return a}});var n=s(85893);let a=e=>{let{color:t="var(--gray-9)",size:s=30}=e;return(0,n.jsxs)("svg",{style:{width:s,height:"auto"},viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",shapeRendering:"geometricPrecision",children:[(0,n.jsx)("path",{d:"M15.8023 7.82981C16.2779 8.98701 16.0102 10.1784 15.2043 10.491C14.3983 10.8035 13.3594 10.1187 12.8838 8.96153C12.4082 7.80433 12.676 6.61289 13.4819 6.30038C14.2878 5.98786 15.3267 6.67261 15.8023 7.82981Z",fill:t}),(0,n.jsx)("path",{d:"M29.2498 21.952C29.6972 22.4662 30.06 23.0215 29.9917 23.685C29.9234 24.3486 29.3086 24.614 28.8987 24.6804C28.4888 24.7467 20.4276 25.8084 19.5396 25.8748C18.7537 25.9335 18.6097 25.8363 18.303 25.6293C18.1647 25.5398 15.0158 22.5446 13.4586 21.0582C12.69 20.502 11.9941 20.0605 11.5031 19.8762C11.0511 19.7065 10.8708 19.0927 11.2918 18.7366L12.3395 17.8503C12.6148 17.6175 13.0379 17.6319 13.2922 17.9027C13.5897 18.1624 14.5664 19.2307 15.0176 19.7324C16.4453 21.0988 18.1849 22.7674 19.1297 23.685C19.8811 23.9504 21.6801 23.6187 22.0672 23.486C22.0672 23.486 19.4312 20.2141 18.8919 19.8065C18.3525 19.3989 17.8676 19.0849 17.4905 18.9339C16.7022 18.5185 17.059 17.9764 17.336 17.7573L18.4758 16.9463C18.7522 16.7496 19.1457 16.7781 19.3852 17.0359C20.803 18.8099 23.7888 22.4906 24.3899 23.0215C25.1414 23.2869 26.8948 22.9994 27.3274 22.8224L21.8995 16.0762C21.0284 15.3386 20.2227 14.7346 19.6677 14.5037C19.2263 14.3201 19.0551 13.6941 19.5023 13.3497L20.6563 12.4606C20.8517 12.3422 21.3174 12.1979 21.6163 12.5686L22.4088 13.5326L29.2498 21.952Z",fill:t}),(0,n.jsx)("path",{d:"M11.8478 1.99067C10.5707 1.99067 9.16387 2.29138 7.65774 3.03904C4.50493 4.60413 2.85603 7.82981 2.33475 10.202C1.60286 13.5326 2.02008 17.0359 3.22442 19.2504C4.07463 20.8137 4.76646 21.6232 6.14326 22.773C8.22894 24.5149 9.81294 25.2866 12.5342 25.6205C13.3997 25.7267 14.7668 25.6858 14.7668 25.6858C15.0555 25.6943 15.3305 25.807 15.5383 26.0018L16.946 27.322L18.5323 28.7087C18.8115 28.9528 18.6338 29.4028 18.2581 29.4028H16.4788C16.1959 29.4028 15.9415 29.1595 15.8496 29.0378C15.2849 28.4856 14.3617 27.6654 14.3617 27.6654L12.5949 27.6134L12.0702 29.601C12.0082 29.8358 11.7904 30 11.5408 30H10.8359C10.4826 30 10.2222 29.6793 10.3044 29.3456L10.7754 27.4341C10.4111 27.3614 10.0218 27.2603 9.62389 27.1204L8.89011 29.615C8.82307 29.8429 8.60863 30 8.36462 30H7.57716C7.21754 30 6.95595 29.6684 7.04944 29.3312L7.88561 26.3144C6.21075 25.5469 4.73704 24.2212 4.73704 24.2212C3.61206 23.3518 2.40208 21.6625 1.93772 20.9265C0.758197 18.9403 0.394862 17.7438 0.0906286 15.4688C-0.12877 13.8281 0.0906362 11.06 0.319969 9.83744C1.19531 6.30038 2.88436 3.2418 6.58906 1.34044C8.46832 0.375947 10.2771 0 11.8478 0C13.447 0 15.5446 0.508003 17.1081 1.30947C18.6578 2.10386 19.4247 2.82829 20.4677 3.84627C20.795 4.1666 21.6271 4.79496 22.3369 4.74577C22.7866 4.80486 24.0471 4.75887 25.4919 4.10215C26.2394 3.83912 26.7594 4.35638 26.9213 4.63196C27.103 4.91761 27.3006 5.6137 26.6372 6.11287L21.7843 9.76476C21.5609 10.1265 21.3962 10.4625 21.3325 10.7478C21.1055 11.6585 20.3487 11.25 20.057 11.06C19.7653 10.87 19.0996 9.90218 19.0996 9.90218C18.8562 9.60785 18.9128 9.17176 19.2335 8.94832C19.5878 8.70146 20.7613 7.88917 21.6518 7.34629L22.4617 6.73683C21.868 6.78175 20.3598 6.55944 19.0768 5.3108C19.0768 5.3108 17.441 3.72084 16.1516 3.07001C14.6097 2.29178 13.0964 1.99067 11.8478 1.99067Z",fill:t})]})}},89764:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var n=s(85893),a=s(80002);function i(e){let{title:t,titleExtra:s,description:i,children:l}=e;return(0,n.jsxs)("div",{className:"px-6 py-4",children:[(0,n.jsxs)("div",{className:"d-flex align-center justify-space-between mb-3",children:[(0,n.jsx)(a.default.Title,{level:4,className:"text-medium gray-8 mb-0",children:t}),s]}),i&&(0,n.jsx)(a.default.Text,{className:"gray-7",children:i}),(0,n.jsx)("div",{className:"mt-3",children:l})]})}},48196:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var n=s(67294),a=s(84908);function i(){let[e,t]=(0,n.useState)(!1),[s,i]=(0,n.useState)(a.SD.CREATE),[l,r]=(0,n.useState)(null);return{state:{visible:e,formMode:s,defaultValue:l},openDrawer:e=>{e&&r(e),e&&i(a.SD.EDIT),t(!0)},closeDrawer:()=>{t(!1),r(null),i(a.SD.CREATE)},updateState:e=>{r(e)}}}},21516:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return k}});var n=s(85893),a=s(5152),i=s.n(a),l=s(41664),r=s.n(l),o=s(80002),d=s(37031),c=s(21367),u=s(17579),C=s(61957),h=s(30848),x=s(89764),m=s(29078),p=s.n(m),f=s(84908),g=s(36238),w=s(48196),j=s(90883),v=s(25707),y=s(4791),L=s(49430),q=s(18337),N=s(63284);function b(e){let{visible:t,defaultValue:s,onClose:a}=e;return(0,n.jsxs)(q.Z,{closable:!0,destroyOnClose:!0,onClose:a,title:"View question-SQL pair",visible:t,width:760,children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"gray-7 mb-2",children:"Question"}),(0,n.jsx)("div",{children:(null==s?void 0:s.question)||"-"})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"gray-7 mb-2",children:"SQL statement"}),(0,n.jsx)(N.default,{code:(null==s?void 0:s.sql)||"",showLineNumbers:!0,maxHeight:"500"})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"gray-7 mb-2",children:"Created time"}),(0,n.jsx)("div",{children:(null==s?void 0:s.createdAt)?(0,g.Eq)(s.createdAt):"-"})]})]})}var S=s(33454);let E=i()(()=>Promise.resolve().then(s.bind(s,63284)),{loadableGenerated:{webpack:()=>[63284]},ssr:!1}),{Paragraph:_,Text:T}=o.default;function k(){let e=(0,j.Z)(),t=(0,w.Z)(),{data:s,loading:a}=(0,S.Gd)({fetchPolicy:"cache-and-network"}),i=(null==s?void 0:s.sqlPairs)||[],l=e=>({onError:e=>console.error(e),refetchQueries:["SqlPairs"],awaitRefetchQueries:!0,...e}),[o,{loading:m}]=(0,S.Nz)(l({onCompleted:()=>{d.default.success("Successfully created question-sql pair.")}})),[q]=(0,S.jn)(l({onCompleted:()=>{d.default.success("Successfully deleted question-sql pair.")}})),[N,{loading:k}]=(0,S.$i)(l({onCompleted:()=>{d.default.success("Successfully updated question-sql pair.")}})),I=async s=>{let{type:n,data:a}=s;n===f.dI.DELETE?await q({variables:{where:{id:a.id}}}):n===f.dI.EDIT?e.openModal(a):n===f.dI.VIEW_SQL_PAIR&&t.openDrawer({...a,sql:(0,C.WU)(a.sql)})},P=[{title:"Question",dataIndex:"question",width:300,render:e=>(0,n.jsx)(_,{title:e,ellipsis:{rows:2},children:e})},{title:"SQL statement",dataIndex:"sql",width:"60%",render:e=>(0,n.jsx)("div",{style:{width:"100%"},children:(0,n.jsx)(E,{code:e,maxHeight:"130"})})},{title:"Created time",dataIndex:"createdAt",width:130,render:e=>(0,n.jsx)(T,{className:"gray-7",children:(0,g.Eq)(e)})},{key:"action",width:64,align:"center",fixed:"right",render:(e,t)=>(0,n.jsx)(y.iI,{onMoreClick:I,data:t,children:(0,n.jsx)(v.z,{className:"gray-8"})})}];return(0,n.jsx)(h.Z,{loading:!1,children:(0,n.jsxs)(x.Z,{title:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p(),{className:"mr-2 gray-8"}),"Manage question-SQL pairs"]}),titleExtra:(0,n.jsx)(c.default,{type:"primary",className:"",onClick:()=>e.openModal(),children:"Add question-SQL pair"}),description:(0,n.jsxs)(n.Fragment,{children:["On this page, you can manage your saved question-SQL pairs. These pairs help Wren AI learn how your organization writes SQL, allowing it to generate queries that better align with your expectations."," ",(0,n.jsx)(r(),{className:"gray-8 underline",href:"https://docs.getwren.ai/oss/guide/knowledge/question-sql-pairs",rel:"noopener noreferrer",target:"_blank",children:"Learn more."})]}),children:[(0,n.jsx)(u.Z,{className:"ant-table-has-header",dataSource:i,loading:a,columns:P,rowKey:"id",pagination:{hideOnSinglePage:!0,pageSize:10,size:"small"},scroll:{x:1080}}),(0,n.jsx)(b,{...t.state,onClose:t.closeDrawer}),(0,n.jsx)(L.Z,{...e.state,onClose:e.closeModal,loading:m||k,onSubmit:async e=>{let{id:t,data:s}=e;t?await N({variables:{where:{id:t},data:s}}):await o({variables:{data:s}})}})]})})}}},function(e){e.O(0,[774,281,530,90,543,337,510,830,858,848,548,888,179],function(){return e(e.s=20995)}),_N_E=e.O()}]);