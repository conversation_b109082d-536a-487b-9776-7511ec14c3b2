(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[895],{35208:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2a7.63 7.63 0 01-2.9-6.1V351c0-6.8 7.9-10.5 13.1-6.1l192 160.9c3.9 3.2 3.9 9.1 0 12.3zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"}}]},name:"code",theme:"filled"}},5430:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"}},83487:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M764 280.9c-14-30.6-33.9-58.1-59.3-81.6C653.1 151.4 584.6 125 512 125s-141.1 26.4-192.7 74.2c-25.4 23.6-45.3 51-59.3 81.7-14.6 32-22 65.9-22 100.9v27c0 6.2 5 11.2 11.2 11.2h54c6.2 0 11.2-5 11.2-11.2v-27c0-99.5 88.6-180.4 197.6-180.4s197.6 80.9 197.6 180.4c0 40.8-14.5 79.2-42 111.2-27.2 31.7-65.6 54.4-108.1 64-24.3 5.5-46.2 19.2-61.7 38.8a110.85 110.85 0 00-23.9 68.6v31.4c0 6.2 5 11.2 11.2 11.2h54c6.2 0 11.2-5 11.2-11.2v-31.4c0-15.7 10.9-29.5 26-32.9 58.4-13.2 111.4-44.7 149.3-88.7 19.1-22.3 34-47.1 44.3-74 10.7-27.9 16.1-57.2 16.1-87 0-35-7.4-69-22-100.9zM512 787c-30.9 0-56 25.1-56 56s25.1 56 56 56 56-25.1 56-56-25.1-56-56-56z"}}]},name:"question",theme:"outlined"}},62434:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/knowledge/instructions",function(){return n(51223)}])},31499:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,s=(r=n(20685))&&r.__esModule?r:{default:r};t.default=s,e.exports=s},93445:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,s=(r=n(67267))&&r.__esModule?r:{default:r};t.default=s,e.exports=s},66222:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,s=(r=n(8093))&&r.__esModule?r:{default:r};t.default=s,e.exports=s},20685:function(e,t,n){"use strict";var r=n(64836),s=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(42122)),l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var n=o(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&({}).hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}return r.default=e,n&&n.set(e,r),r}(n(67294)),i=r(n(35208)),c=r(n(3247));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}var u=l.forwardRef(function(e,t){return l.createElement(c.default,(0,a.default)((0,a.default)({},e),{},{ref:t,icon:i.default}))});t.default=u},67267:function(e,t,n){"use strict";var r=n(64836),s=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(42122)),l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var n=o(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&({}).hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}return r.default=e,n&&n.set(e,r),r}(n(67294)),i=r(n(5430)),c=r(n(3247));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}var u=l.forwardRef(function(e,t){return l.createElement(c.default,(0,a.default)((0,a.default)({},e),{},{ref:t,icon:i.default}))});t.default=u},8093:function(e,t,n){"use strict";var r=n(64836),s=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(42122)),l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var n=o(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&({}).hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}return r.default=e,n&&n.set(e,r),r}(n(67294)),i=r(n(83487)),c=r(n(3247));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}var u=l.forwardRef(function(e,t){return l.createElement(c.default,(0,a.default)((0,a.default)({},e),{},{ref:t,icon:i.default}))});t.default=u},25707:function(e,t,n){"use strict";n.d(t,{H:function(){return o},z:function(){return u}});var r=n(85893),s=n(21367),a=n(4071),l=n.n(a),i=n(39616);let c=e=>t=>{let{onClick:n,onMouseEnter:a,onMouseLeave:l,className:i,marginLeft:c,marginRight:o,...u}=t;return(0,r.jsx)(s.default,{className:i,style:{marginLeft:c,marginRight:o},icon:e,onClick:e=>{n&&n(e),e.stopPropagation()},onMouseEnter:e=>{a&&a(e),e.stopPropagation()},onMouseLeave:e=>{l&&l(e),e.stopPropagation()},type:"text",size:"small",...u})},o=c((0,r.jsx)(l(),{})),u=c((0,r.jsx)(i.nX,{}))},4791:function(e,t,n){"use strict";n.d(t,{At:function(){return _},Lb:function(){return w},aV:function(){return P},iI:function(){return T},px:function(){return N},rt:function(){return k},s_:function(){return C},uu:function(){return D}});var r=n(85893);n(67294);var s=n(19521),a=n(69371),l=n(59046),i=n(84908),c=n(93181),o=n.n(c),u=n(7337),d=n.n(u),f=n(80112),m=n.n(f),p=n(92870),h=n.n(p),x=n(31499),g=n.n(x),j=n(40492),y=n.n(j),I=n(32815),v=n(26123);let E=(0,s.ZP)(a.default).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),b=e=>t=>{let{children:n,onMenuEnter:s,onDropdownVisibleChange:a}=t,i=e(t);return(0,r.jsx)(l.default,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:(0,r.jsx)(E,{onClick:e=>e.domEvent.stopPropagation(),items:i,onMouseEnter:s}),onVisibleChange:a,children:n})},_=b(e=>{let{onMoreClick:t}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{className:"mr-2"}),"Update Columns"]}),key:i.dI.UPDATE_COLUMNS,onClick:()=>t(i.dI.UPDATE_COLUMNS)},{label:(0,r.jsx)(v.ZQ,{onConfirm:()=>t(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),w=b(e=>{let{onMoreClick:t}=e;return[{label:(0,r.jsx)(v.e0,{onConfirm:()=>t(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),C=b(e=>{let{onMoreClick:t,data:n}=e,{nodeType:s}=n,a={[i.QZ.CALCULATED_FIELD]:v.pn,[i.QZ.RELATION]:v.ck}[s]||v.pn;return[{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>t(i.dI.EDIT)},{label:(0,r.jsx)(a,{onConfirm:()=>t(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),N=b(e=>{let{onMoreClick:t,isSupportCached:n}=e;return[n&&{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y(),{className:"mr-2"}),"Cache settings"]}),key:i.dI.CACHE_SETTINGS,onClick:()=>t(i.dI.CACHE_SETTINGS)},{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d(),{className:"mr-2"}),n?"Refresh all caches":"Refresh all"]}),key:i.dI.REFRESH,onClick:()=>t(i.dI.REFRESH)}].filter(Boolean)}),k=b(e=>{let{onMoreClick:t,isHideLegend:n,isSupportCached:s}=e;return[{label:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h(),{className:"mr-2"}),"Show categories"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m(),{className:"mr-2"}),"Hide categories"]}),key:i.dI.HIDE_CATEGORY,onClick:()=>t(i.dI.HIDE_CATEGORY)},{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d(),{className:"mr-2"}),s?"Refresh cache":"Refresh"]}),key:i.dI.REFRESH,onClick:()=>t(i.dI.REFRESH)},{label:(0,r.jsx)(v.mU,{onConfirm:()=>t(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),T=b(e=>{let{onMoreClick:t,data:n}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h(),{className:"mr-2"}),"View"]}),key:i.dI.VIEW_SQL_PAIR,onClick:()=>t({type:i.dI.VIEW_SQL_PAIR,data:n})},{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>t({type:i.dI.EDIT,data:n})},{label:(0,r.jsx)(v.pv,{onConfirm:()=>t({type:i.dI.DELETE,data:n}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),D=b(e=>{let{onMoreClick:t,data:n}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h(),{className:"mr-2"}),"View"]}),key:i.dI.VIEW_INSTRUCTION,onClick:()=>t({type:i.dI.VIEW_INSTRUCTION,data:n})},{label:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>t({type:i.dI.EDIT,data:n})},{label:(0,r.jsx)(v.py,{onConfirm:()=>t({type:i.dI.DELETE,data:n}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),P=b(e=>{let{onMoreClick:t,data:n}=e;return[{label:"Adjust steps",icon:(0,r.jsx)(I.Wi,{}),disabled:!n.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>t({type:i.dI.ADJUST_STEPS,data:n})},{label:"Adjust SQL",icon:(0,r.jsx)(g(),{className:"text-base"}),disabled:!n.sql,key:"adjust-sql",onClick:()=>t({type:i.dI.ADJUST_SQL,data:n})}]})},89764:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(85893),s=n(80002);function a(e){let{title:t,titleExtra:n,description:a,children:l}=e;return(0,r.jsxs)("div",{className:"px-6 py-4",children:[(0,r.jsxs)("div",{className:"d-flex align-center justify-space-between mb-3",children:[(0,r.jsx)(s.default.Title,{level:4,className:"text-medium gray-8 mb-0",children:t}),n]}),a&&(0,r.jsx)(s.default.Text,{className:"gray-7",children:a}),(0,r.jsx)("div",{className:"mt-3",children:l})]})}},48196:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(67294),s=n(84908);function a(){let[e,t]=(0,r.useState)(!1),[n,a]=(0,r.useState)(s.SD.CREATE),[l,i]=(0,r.useState)(null);return{state:{visible:e,formMode:n,defaultValue:l},openDrawer:e=>{e&&i(e),e&&a(s.SD.EDIT),t(!0)},closeDrawer:()=>{t(!1),i(null),a(s.SD.CREATE)},updateState:e=>{i(e)}}}},51223:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return ed}});var r=n(85893),s=n(41664),a=n.n(s),l=n(80002),i=n(51618),c=n(37031),o=n(21367),u=n(17579),d=n(19521),f=n(30848),m=n(89764),p=n(32815),h=n(66222),x=n.n(h),g=n(84908),j=n(36238),y=n(25707),I=n(4791),v=n(48196),E=n(90883),b=n(93445),_=n.n(b);let{Text:w}=l.default;function C(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(_(),{className:"mr-2"}),(0,r.jsx)(w,{className:"gray-9",children:"Global"})]})}var N=n(67294),k=n(13518),T=n(69828),D=n(98885),P=n(26515),S=n(40582),M=n(55041),O=n(81506),A=n.n(O),q=n(31682),L=n.n(q),R=n(41609),F=n.n(R),z=n(59530);function Z(e){let{defaultValue:t,formMode:n,loading:s,onClose:a,onSubmit:l,visible:i}=e,c=n===g.SD.CREATE,[u]=k.Z.useForm(),d=k.Z.useWatch("isDefault",u);return(0,N.useEffect)(()=>{i&&u.setFieldsValue({isDefault:!!F()(t)||t.isDefault,instruction:null==t?void 0:t.instruction,questions:null==t?void 0:t.questions})},[i,t]),(0,r.jsx)(T.Z,{title:"".concat(c?"Add":"Update"," an instruction"),centered:!0,closable:!0,confirmLoading:s,destroyOnClose:!0,maskClosable:!1,onCancel:a,visible:i,width:720,cancelButtonProps:{disabled:s},okText:"Submit",onOk:()=>{u.validateFields().then(async e=>{let n={isDefault:e.isDefault,instruction:e.instruction,questions:(null==e?void 0:e.questions)||[]};await l({data:n,id:null==t?void 0:t.id}),a()}).catch(console.error)},afterClose:()=>u.resetFields(),children:(0,r.jsxs)(k.Z,{form:u,preserve:!1,layout:"vertical",children:[(0,r.jsx)(k.Z.Item,{label:"Instruction details",name:"instruction",rules:[{required:!0,message:z.q.INSTRUCTION.DETAILS.REQUIRED}],children:(0,r.jsx)(D.default.TextArea,{autoFocus:!0,placeholder:"Enter a rule that Wren AI should follow when generating SQL queries.",maxLength:1e3,rows:3,showCount:!0})}),(0,r.jsx)(k.Z.Item,{label:"Apply instruction to",name:"isDefault",required:!1,rules:[{required:!0,message:z.q.INSTRUCTION.IS_DEFAULT_GLOBAL.REQUIRED}],extra:(0,r.jsxs)(r.Fragment,{children:["Choose whether this instruction applies to"," ",(0,r.jsx)("span",{className:"gray-7",children:"all queries"})," or"," ",(0,r.jsx)("span",{className:"gray-7",children:"only when similar user questions are detected"}),"."]}),children:(0,r.jsxs)(P.default.Group,{children:[(0,r.jsx)(P.default.Button,{value:!0,children:"Global (applies to all questions)"}),(0,r.jsx)(P.default.Button,{value:!1,children:"Matched to specific questions"})]})}),!d&&(0,r.jsx)(k.Z.Item,{label:"Matching questions",required:!0,extra:"Wren AI will match user queries based on similarity and apply this instruction when relevant.",children:(0,r.jsx)(k.Z.List,{name:"questions",initialValue:[""],children:(e,t)=>{let{add:n,remove:s}=t;return(0,r.jsxs)(r.Fragment,{children:[e.map(t=>{let{key:n,name:a,...l}=t;return(0,r.jsxs)(S.default,{wrap:!1,gutter:8,className:"my-2",children:[(0,r.jsx)(M.default,{flex:"1 0",children:(0,r.jsx)(k.Z.Item,{...l,name:a,required:!0,className:"mb-2",style:{width:"100%"},rules:[{required:!0,whitespace:!0,message:z.q.INSTRUCTION.QUESTIONS.REQUIRED}],children:(0,r.jsx)(D.default,{placeholder:"Enter an example question that should trigger this instruction.",maxLength:100,showCount:!0})})}),(0,r.jsx)(M.default,{flex:"none",className:"p-1",children:(0,r.jsx)(o.default,{onClick:()=>s(a),disabled:e.length<=1,icon:(0,r.jsx)(A(),{}),size:"small",style:{border:"none"},className:"bg-gray-1"})})]},n)}),(0,r.jsx)(k.Z.Item,{noStyle:!0,children:(0,r.jsx)(o.default,{type:"dashed",onClick:()=>n(),block:!0,icon:(0,r.jsx)(L(),{}),disabled:e.length>=100,className:"mb-1",children:"Add a question"})})]})}})})]})})}var U=n(18337);let{Text:W}=l.default;function V(e){let{visible:t,defaultValue:n,onClose:s}=e;return(0,r.jsxs)(U.Z,{closable:!0,destroyOnClose:!0,onClose:s,title:"View instruction",visible:t,width:760,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(l.default.Text,{className:"gray-7 mb-2",children:"Instruction details"}),(0,r.jsx)("div",{children:(null==n?void 0:n.instruction)||"-"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(l.default.Text,{className:"gray-7 mb-2",children:"Matching questions"}),(0,r.jsx)("div",{children:(null==n?void 0:n.isDefault)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(C,{}),(0,r.jsx)(W,{className:"gray-7 ml-2",type:"secondary",children:"(applies to all questions)"})]}):null==n?void 0:n.questions.map((e,t)=>(0,r.jsx)("div",{className:"my-2",children:(0,r.jsxs)(i.default,{className:"bg-gray-1 border-gray-5",children:[(0,r.jsx)(x(),{className:"geekblue-6"}),(0,r.jsx)(W,{className:"gray-9",children:e})]})},"".concat(e,"-").concat(t)))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(l.default.Text,{className:"gray-7 mb-2",children:"Created time"}),(0,r.jsx)("div",{children:(null==n?void 0:n.createdAt)?(0,j.Eq)(n.createdAt):"-"})]})]})}var H=n(82729),Q=n(68806),B=n(6812),G=n(50319);function $(){let e=(0,H._)(["\n    fragment Instruction on Instruction {\n  id\n  projectId\n  instruction\n  questions\n  isDefault\n  createdAt\n  updatedAt\n}\n    "]);return $=function(){return e},e}function X(){let e=(0,H._)(["\n    query Instructions {\n  instructions {\n    ...Instruction\n  }\n}\n    ",""]);return X=function(){return e},e}function J(){let e=(0,H._)(["\n    mutation CreateInstruction($data: CreateInstructionInput!) {\n  createInstruction(data: $data) {\n    ...Instruction\n  }\n}\n    ",""]);return J=function(){return e},e}function Y(){let e=(0,H._)(["\n    mutation UpdateInstruction($where: InstructionWhereInput!, $data: UpdateInstructionInput!) {\n  updateInstruction(where: $where, data: $data) {\n    ...Instruction\n  }\n}\n    ",""]);return Y=function(){return e},e}function K(){let e=(0,H._)(["\n    mutation DeleteInstruction($where: InstructionWhereInput!) {\n  deleteInstruction(where: $where)\n}\n    "]);return K=function(){return e},e}let ee={},et=(0,Q.Ps)($()),en=(0,Q.Ps)(X(),et),er=(0,Q.Ps)(J(),et),es=(0,Q.Ps)(Y(),et),ea=(0,Q.Ps)(K()),{Paragraph:el,Text:ei}=l.default,ec=d.ZP.div.withConfig({displayName:"instructions__StyledQuestionsBlock",componentId:"sc-86b6427d-0"})(["margin:-2px -4px;"]),eo=(0,d.ZP)(i.default).withConfig({displayName:"instructions__StyledTag",componentId:"sc-86b6427d-1"})(["&.ant-tag.ant-tag{display:inline-block;margin:2px 4px;max-width:100%;}"]),eu=(0,d.ZP)(p.pB).withConfig({displayName:"instructions__StyledInstructionsIcon",componentId:"sc-86b6427d-2"})(["width:20px;height:20px;"]);function ed(){let e=(0,E.Z)(),t=(0,v.Z)(),{data:n,loading:s}=function(e){let t={...ee,...e};return B.aM(en,t)}({fetchPolicy:"cache-and-network"}),l=(null==n?void 0:n.instructions)||[],i=e=>({onError:e=>console.error(e),refetchQueries:["Instructions"],awaitRefetchQueries:!0,...e}),[d,{loading:p}]=function(e){let t={...ee,...e};return G.D(er,t)}(i({onCompleted:()=>{c.default.success("Successfully created instruction.")}})),[h,{loading:b}]=function(e){let t={...ee,...e};return G.D(es,t)}(i({onCompleted:()=>{c.default.success("Successfully updated instruction.")}})),[_]=function(e){let t={...ee,...e};return G.D(ea,t)}(i({onCompleted:()=>{c.default.success("Successfully deleted instruction.")}})),w=async n=>{let{type:r,data:s}=n;r===g.dI.DELETE?await _({variables:{where:{id:s.id}}}):r===g.dI.EDIT?e.openModal(s):r===g.dI.VIEW_INSTRUCTION&&t.openDrawer(s)},N=[{title:"Instruction details",dataIndex:"instruction",render:e=>(0,r.jsx)(el,{title:e,ellipsis:{rows:3},children:e})},{title:"Matching questions",dataIndex:"questions",width:"50%",render:(e,t)=>{if(t.isDefault)return(0,r.jsx)(C,{});let n=e.slice(0,2),s=e.length-2;return(0,r.jsxs)(ec,{children:[n.map(e=>(0,r.jsx)("div",{className:"mb-1",children:(0,r.jsxs)(eo,{className:"bg-gray-1 border-gray-5 text-truncate",children:[(0,r.jsx)(x(),{className:"geekblue-6"}),(0,r.jsx)(ei,{className:"gray-9",title:e,children:e})]})},e)),s>0&&(0,r.jsxs)("div",{className:"text-sm gray-7 pl-1",children:["+",s," more question",s>1?"s":""]})]})}},{title:"Created time",dataIndex:"createdAt",width:130,render:e=>(0,r.jsx)(ei,{className:"gray-7",children:(0,j.Eq)(e)})},{key:"action",width:64,align:"center",fixed:"right",render:(e,t)=>(0,r.jsx)(I.uu,{onMoreClick:w,data:t,children:(0,r.jsx)(y.z,{className:"gray-8"})})}];return(0,r.jsx)(f.Z,{loading:!1,children:(0,r.jsxs)(m.Z,{title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eu,{className:"mr-2 gray-8"}),"Manage instruction"]}),titleExtra:(0,r.jsx)(o.default,{type:"primary",onClick:()=>e.openModal(),children:"Add an instruction"}),description:(0,r.jsxs)(r.Fragment,{children:["On this page, you can manage saved instructions that guide Wren AI in generating SQL queries. These instructions help Wren AI understand your data model and business rules, improving query accuracy and reducing the need for manual refinements."," ",(0,r.jsx)(a(),{className:"gray-8 underline",href:"https://docs.getwren.ai/oss/guide/knowledge/instructions",rel:"noopener noreferrer",target:"_blank",children:"Learn more."})]}),children:[(0,r.jsx)(u.Z,{className:"ant-table-has-header",dataSource:l,loading:s,columns:N,rowKey:"id",pagination:{hideOnSinglePage:!0,pageSize:10,size:"small"},scroll:{x:1080}}),(0,r.jsx)(V,{...t.state,onClose:t.closeDrawer}),(0,r.jsx)(Z,{...e.state,onClose:e.closeModal,loading:p||b,onSubmit:async e=>{let{id:t,data:n}=e;t?await h({variables:{where:{id:t},data:n}}):await d({variables:{data:n}})}})]})})}}},function(e){e.O(0,[774,530,90,543,337,858,848,888,179],function(){return e(e.s=62434)}),_N_E=e.O()}]);