(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[272],{23076:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/home/<USER>",function(){return a(84997)}])},3482:function(e,t,a){"use strict";a.d(t,{SG:function(){return b},Sj:function(){return j},_R:function(){return I},bI:function(){return E},mC:function(){return C},w5:function(){return S},yH:function(){return P}});var n=a(82729),s=a(68806),l=a(50319),r=a(6812);function i(){let e=(0,n._)(["\n    fragment CommonDashboardItem on DashboardItem {\n  id\n  dashboardId\n  type\n  layout {\n    x\n    y\n    w\n    h\n  }\n  detail {\n    sql\n    chartSchema\n  }\n  displayName\n}\n    "]);return i=function(){return e},e}function o(){let e=(0,n._)(["\n    query DashboardItems {\n  dashboardItems {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return o=function(){return e},e}function d(){let e=(0,n._)(["\n    mutation CreateDashboardItem($data: CreateDashboardItemInput!) {\n  createDashboardItem(data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return d=function(){return e},e}function c(){let e=(0,n._)(["\n    mutation UpdateDashboardItem($where: DashboardItemWhereInput!, $data: UpdateDashboardItemInput!) {\n  updateDashboardItem(where: $where, data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return c=function(){return e},e}function u(){let e=(0,n._)(["\n    mutation UpdateDashboardItemLayouts($data: UpdateDashboardItemLayoutsInput!) {\n  updateDashboardItemLayouts(data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return u=function(){return e},e}function m(){let e=(0,n._)(["\n    mutation DeleteDashboardItem($where: DashboardItemWhereInput!) {\n  deleteDashboardItem(where: $where)\n}\n    "]);return m=function(){return e},e}function h(){let e=(0,n._)(["\n    mutation PreviewItemSQL($data: PreviewItemSQLInput!) {\n  previewItemSQL(data: $data) {\n    data\n    cacheHit\n    cacheCreatedAt\n    cacheOverrodeAt\n    override\n  }\n}\n    "]);return h=function(){return e},e}function p(){let e=(0,n._)(["\n    mutation SetDashboardSchedule($data: SetDashboardScheduleInput!) {\n  setDashboardSchedule(data: $data) {\n    id\n    projectId\n    name\n    cacheEnabled\n    scheduleFrequency\n    scheduleTimezone\n    scheduleCron\n    nextScheduledAt\n  }\n}\n    "]);return p=function(){return e},e}function x(){let e=(0,n._)(["\n    query Dashboard {\n  dashboard {\n    id\n    name\n    description\n    cacheEnabled\n    nextScheduledAt\n    schedule {\n      frequency\n      hour\n      minute\n      day\n      timezone\n      cron\n    }\n    items {\n      ...CommonDashboardItem\n    }\n  }\n}\n    ",""]);return x=function(){return e},e}let v={},g=(0,s.Ps)(i());(0,s.Ps)(o(),g);let f=(0,s.Ps)(d(),g);function j(e){let t={...v,...e};return l.D(f,t)}let y=(0,s.Ps)(c(),g);function b(e){let t={...v,...e};return l.D(y,t)}let w=(0,s.Ps)(u(),g);function S(e){let t={...v,...e};return l.D(w,t)}let N=(0,s.Ps)(m());function I(e){let t={...v,...e};return l.D(N,t)}let k=(0,s.Ps)(h());function C(e){let t={...v,...e};return l.D(k,t)}let T=(0,s.Ps)(p());function E(e){let t={...v,...e};return l.D(T,t)}let R=(0,s.Ps)(x(),g);function P(e){let t={...v,...e};return r.aM(R,t)}},94601:function(e,t,a){"use strict";a.d(t,{Ox:function(){return y},S1:function(){return p},SJ:function(){return v},fB:function(){return f}});var n=a(82729),s=a(68806),l=a(50319);function r(){let e=(0,n._)(["\n    mutation CreateView($data: CreateViewInput!) {\n  createView(data: $data) {\n    id\n    name\n    statement\n  }\n}\n    "]);return r=function(){return e},e}function i(){let e=(0,n._)(["\n    mutation DeleteView($where: ViewWhereUniqueInput!) {\n  deleteView(where: $where)\n}\n    "]);return i=function(){return e},e}function o(){let e=(0,n._)(["\n    query GetView($where: ViewWhereUniqueInput!) {\n  view(where: $ViewWhereUniqueInput) {\n    id\n    name\n    statement\n  }\n}\n    "]);return o=function(){return e},e}function d(){let e=(0,n._)(["\n    query ListViews {\n  listViews {\n    id\n    name\n    displayName\n    statement\n  }\n}\n    "]);return d=function(){return e},e}function c(){let e=(0,n._)(["\n    mutation PreviewViewData($where: PreviewViewDataInput!) {\n  previewViewData(where: $where)\n}\n    "]);return c=function(){return e},e}function u(){let e=(0,n._)(["\n    mutation ValidateView($data: ValidateViewInput!) {\n  validateView(data: $data) {\n    valid\n    message\n  }\n}\n    "]);return u=function(){return e},e}let m={},h=(0,s.Ps)(r());function p(e){let t={...m,...e};return l.D(h,t)}let x=(0,s.Ps)(i());function v(e){let t={...m,...e};return l.D(x,t)}(0,s.Ps)(o()),(0,s.Ps)(d());let g=(0,s.Ps)(c());function f(e){let t={...m,...e};return l.D(g,t)}let j=(0,s.Ps)(u());function y(e){let t={...m,...e};return l.D(j,t)}},79421:function(e,t,a){"use strict";a.d(t,{C9:function(){return C},Cf:function(){return k},ZP:function(){return N}});var n,s,l=a(46140),r=a(14293),i=a.n(r),o=a(50361),d=a.n(o),c=a(44908),u=a.n(c),m=a(89734),h=a.n(m),p=a(57557),x=a.n(p),v=a(81763),g=a.n(v);(n=s||(s={})).ARC="arc",n.AREA="area",n.BAR="bar",n.BOXPLOT="boxplot",n.CIRCLE="circle",n.ERRORBAND="errorband",n.ERRORBAR="errorbar",n.IMAGE="image",n.LINE="line",n.POINT="point",n.RECT="rect",n.RULE="rule",n.SQUARE="square",n.TEXT="text",n.TICK="tick",n.TRAIL="trail";let f="#434343",j="#65676c",y=["#7763CF","#444CE7","#1570EF","#0086C9","#3E4784","#E31B54","#EC4A0A","#EF8D0C","#EBC405","#5381AD"],b=[y[4],y[5],y[8],y[3],y[0]],w=y[2],S={mark:{tooltip:!0},font:"Roboto, Arial, Noto Sans, sans-serif",padding:{top:30,bottom:20,left:0,right:0},title:{color:"#262626",fontSize:14},axis:{labelPadding:0,labelOffset:0,labelFontSize:10,gridColor:"#d9d9d9",titleColor:f,labelColor:j,labelFont:" Roboto, Arial, Noto Sans, sans-serif"},axisX:{labelAngle:-45},line:{color:w},bar:{color:w},legend:{symbolLimit:15,columns:1,labelFontSize:10,labelColor:j,titleColor:f,titleFontSize:14},range:{category:y,ordinal:y,diverging:y,symbol:y,heatmap:y,ramp:y},point:{size:60,color:w}};class N{getChartSpec(){let e=this.getAllCategories(this.encoding);return e.length>this.options.categoriesLimit?null:(e.length<=5&&(this.encoding.color={...this.encoding.color,scale:{range:b}}),this.options.isHideLegend&&(this.encoding.color={...this.encoding.color,legend:null}),this.options.isHideTitle&&(this.title=null),this.data=this.transformDataValues(this.data,this.encoding),{$schema:this.$schema,title:this.title,data:this.data,mark:this.mark,width:this.options.width,height:this.options.height,autosize:this.autosize,encoding:this.encoding,params:this.params,transform:this.transform})}parseSpec(e){if(this.$schema=e.$schema,this.title=e.title,this.transform=e.transform,"mark"in e){let t="string"==typeof e.mark?{type:e.mark}:e.mark;this.addMark(t)}if("encoding"in e){var t;if(null===(t=this.options)||void 0===t?void 0:t.isShowTopCategories){let t=this.filterTopCategories(e.encoding);t&&(this.data=t)}this.addEncoding(e.encoding)}}addMark(e){let t={};"line"===e.type?t={point:this.options.point,tooltip:!0}:"arc"===e.type&&(t={innerRadius:this.options.donutInner}),this.mark={type:e.type,...t}}addEncoding(e){if(this.encoding=e,i()(this.encoding.color)){let t=["x","y"].find(t=>{var a;return(null===(a=e[t])||void 0===a?void 0:a.type)==="nominal"});if(t){let a=e[t];this.encoding.color={field:a.field,type:a.type}}}if("bar"===this.mark.type&&("stack"in this.encoding.y&&(this.encoding.y.stack=this.options.stack),"xOffset"in this.encoding)){let e=this.encoding.xOffset,t=null==e?void 0:e.title;t||(t=this.findFieldTitleInEncoding(this.encoding,null==e?void 0:e.field)),this.encoding.xOffset={...e,title:t}}this.addHoverHighlight(this.encoding)}addHoverHighlight(e){var t;let a=(null===(t=e.color)||void 0===t?void 0:t.condition)?e.color.condition:e.color;if(!(null==a?void 0:a.field)||!(null==a?void 0:a.type))return;this.params&&(null==a?void 0:a.field)&&(this.params[0].select.fields=[a.field]),this.encoding.opacity={condition:{param:"hover",value:1},value:.3};let n=null==a?void 0:a.title;n||(n=this.findFieldTitleInEncoding(this.encoding,null==a?void 0:a.field));let s={title:n,field:null==a?void 0:a.field,type:null==a?void 0:a.type,scale:{range:y}};this.encoding.color={...s,condition:{param:"hover",...x()(s,"scale")}}}filterTopCategories(e){let t=["xOffset","color","x","y"].filter(t=>{var a;return(null===(a=e[t])||void 0===a?void 0:a.type)==="nominal"}),a=["theta","x","y"].filter(t=>{var a;return(null===(a=e[t])||void 0===a?void 0:a.type)==="quantitative"});if(!t.length||!a.length)return;let n=d()(this.data.values),s=e[a[0]],l=h()(n,e=>{let t=e[s.field];return g()(t)?-t:0}),r=[];for(let a of t){let t=e[a];if(r.some(e=>e.field===t.field))continue;let n=l.map(e=>e[t.field]),s=u()(n).slice(0,this.options.categoriesLimit);r.push({field:t.field,values:s})}return{values:n.filter(e=>r.every(t=>t.values.includes(e[t.field])))}}getAllCategories(e){let t=["xOffset","color","x","y"].find(t=>{var a;return(null===(a=e[t])||void 0===a?void 0:a.type)==="nominal"});if(!t)return[];let a=e[t],n=this.data.values.map(e=>e[a.field]);return u()(n)}findFieldTitleInEncoding(e,t){var a;return(null===(a=e[["x","y","xOffset","color"].find(a=>{var n,s;return(null===(n=e[a])||void 0===n?void 0:n.field)===t&&(null===(s=e[a])||void 0===s?void 0:s.title)})])||void 0===a?void 0:a.title)||void 0}transformDataValues(e,t){var a,n;if((null==t?void 0:null===(a=t.x)||void 0===a?void 0:a.type)==="temporal"){let a=e.values.map(e=>({...e,[t.x.field]:this.transformTemporalValue(e[t.x.field])}));return{...e,values:a}}if((null==t?void 0:null===(n=t.y)||void 0===n?void 0:n.type)==="temporal"){let a=e.values.map(e=>({...e,[t.y.field]:this.transformTemporalValue(e[t.y.field])}));return{...e,values:a}}return e}transformTemporalValue(e){if(null==e)return e;let t="string"==typeof e?e:String(e);return t.includes("UTC")?t.replace(/\s+UTC([+-][0-9]+)?(:[0-9]+)?/,""):t}constructor(e,t){this.config=S,this.data=e.data,this.autosize={type:"fit",contains:"padding"},this.params=[{name:"hover",select:{type:"point",on:"mouseover",clear:"mouseout"}}],this.options={width:i()(null==t?void 0:t.width)?"container":t.width,height:i()(null==t?void 0:t.height)?"container":t.height,stack:i()(null==t?void 0:t.stack)?"zero":t.stack,point:!!i()(null==t?void 0:t.point)||t.point,donutInner:i()(null==t?void 0:t.donutInner)?60:t.donutInner,categoriesLimit:i()(null==t?void 0:t.categoriesLimit)?25:t.categoriesLimit,isShowTopCategories:!i()(null==t?void 0:t.isShowTopCategories)&&(null==t?void 0:t.isShowTopCategories),isHideLegend:!i()(null==t?void 0:t.isHideLegend)&&t.isHideLegend,isHideTitle:!i()(null==t?void 0:t.isHideTitle)&&t.isHideTitle};let a=d()(e);this.parseSpec(a)}}let I=(e,t)=>{if("bar"===e){var a,n;if(null==t?void 0:t.xOffset)return l.oX.GROUPED_BAR;if(!i()(null==t?void 0:null===(a=t.y)||void 0===a?void 0:a.stack)||!i()(null==t?void 0:null===(n=t.x)||void 0===n?void 0:n.stack))return l.oX.STACKED_BAR}else if("arc"===e)return l.oX.PIE;return e?e.toUpperCase():null},k=e=>{let t=null==e?void 0:e.chartSchema,a=(null==e?void 0:e.chartType)||null,n=null,s=null,l=null,r=null,i=null;if(t&&"encoding"in t){var o,d,c,u,m;let e=t.encoding;n=(null==e?void 0:null===(o=e.x)||void 0===o?void 0:o.field)||null,s=(null==e?void 0:null===(d=e.y)||void 0===d?void 0:d.field)||null,l=(null==e?void 0:null===(c=e.color)||void 0===c?void 0:c.field)||null,r=(null==e?void 0:null===(u=e.xOffset)||void 0===u?void 0:u.field)||null,i=(null==e?void 0:null===(m=e.theta)||void 0===m?void 0:m.field)||null,null===a&&(a=I("string"==typeof t.mark?t.mark:t.mark.type,e))}return{chartType:a,xAxis:n,yAxis:s,color:l,xOffset:r,theta:i}},C=e=>e?["x","y","xOffset","color"].reduce((t,a)=>{let n=e[a];return(null==n?void 0:n.field)&&(null==n?void 0:n.title)&&(t[null==n?void 0:n.field]=null==n?void 0:n.title),t},{}):{}},84997:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ak}});var n=a(85893),s=a(11163),l=a(39332),r=a(67294),i=a(41609),o=a.n(i),d=a(37031),c=a(84908),u=a(83777),m=a(30848),h=a(17778),p=a(55023),x=a(50361),v=a.n(x),g=a(98683),f=a(36238),j=a(77491),y=a(46140);let b=e=>[y.j.FINISHED,y.j.FAILED,y.j.STOPPED].includes(e),w=(e,t,a)=>{let n=a.cache.readQuery({query:g.i,variables:{threadId:e}});(null==n?void 0:n.thread)&&a.cache.updateQuery({query:g.i,variables:{threadId:e}},e=>{let a=!e.thread.responses.map(e=>e.id).includes(t.id);return{thread:{...e.thread,responses:a?[...e.thread.responses,t]:e.thread.responses.map(e=>e.id===t.id?v()(t):e)}}})};var S=a(90883),N=a(72786),I=a(19521);let k=new Map,C="PromptThread";function T(){return{createStore:e=>{if(k.has(e))return k.get(e);let t=(0,r.createContext)(null);return k.set(e,t),t},clearStore:e=>{k.delete(e)},useStore:e=>{let t=k.get(e);if(!t)throw Error("Context not found for id: ".concat(e));return(0,r.useContext)(t)}}}let E=e=>{let t=T(),a=t.createStore(C);return(0,r.useEffect)(()=>()=>t.clearStore(C),[]),(0,n.jsx)(a.Provider,{value:e.value,children:e.children})};function R(){return T().useStore(C)}var P=a(23279),q=a.n(P),A=a(90512),Z=a(80002),L=a(50342),D=a(51618),_=a(29060),F=a(21367),M=a(91031),Q=a.n(M),V=a(31499),O=a.n(V),G=a(90461),z=a.n(G),H=a(50604),$=a.n(H),U=a(10677),B=a.n(U),W=a(32815),X=a(22546),Y=a(41664),J=a.n(Y),K=a(96873),ee=a.n(K),et=a(41789),ea=a.n(et);function en(e){let{view:t,onClick:a}=e;return t?(0,n.jsxs)("div",{className:"gray-6 text-medium",children:[(0,n.jsx)(ee(),{className:"mr-2"}),"Generated from saved view"," ",(0,n.jsx)(J(),{className:"gray-7",href:"".concat(c.y$.Modeling,"?viewId=").concat(t.id,"&openMetadata=true"),target:"_blank",rel:"noreferrer noopener",children:t.displayName})]}):(0,n.jsx)(F.default,{className:"gray-6",type:"text",size:"small",icon:(0,n.jsx)(ea(),{}),onClick:a,children:"Save as View"})}var es=a(5152),el=a.n(es),er=a(25675),ei=a.n(er),eo=a(62819),ed=a(4922),ec=a(19662),eu=a(34145),em=a(51903),eh=a.n(em),ep=a(12155),ex=a.n(ep),ev=a(39616),eg=a(10102),ef=a(25063),ej=a(87021),ey=a(91397);let eb=el()(()=>Promise.resolve().then(a.bind(a,63284)),{loadableGenerated:{webpack:()=>[63284]},ssr:!1}),{Text:ew}=Z.default,eS=I.ZP.pre.withConfig({displayName:"ViewSQLTabContent__StyledPre",componentId:"sc-979f72fe-0"})([".adm_code-block{border-top:none;border-radius:0px 0px 4px 4px;}"]),eN=I.ZP.div.withConfig({displayName:"ViewSQLTabContent__StyledToolBar",componentId:"sc-979f72fe-1"})(["background-color:var(--gray-2);height:32px;padding:4px 8px;border:1px solid var(--gray-3);border-radius:4px 4px 0px 0px;"]);function eI(e){var t,a;let{isLastThreadResponse:s,onInitPreviewDone:l,threadResponse:i}=e,{onOpenAdjustSQLModal:o}=R(),{fetchNativeSQL:c,nativeSQLResult:u}=function(){let e=function(){let{data:e}=(0,eg.ks)(),t=null==e?void 0:e.settings,a=null==t?void 0:t.dataSource.type;return{hasNativeSQL:!(null==t?void 0:t.dataSource.sampleDataset),dataSourceType:a}}(),[t,a]=(0,r.useState)(!1),[n,{data:s,loading:l}]=(0,j.xF)({fetchPolicy:"cache-and-network"}),i=(null==s?void 0:s.nativeSql)||"";return{fetchNativeSQL:n,nativeSQLResult:{...e,data:i,loading:l,nativeSQLMode:t,setNativeSQLMode:a}}}(),[m,h]=(0,j.HT)({onError:e=>console.error(e)}),p=async()=>{await m({variables:{where:{responseId:v}}})},x=async()=>{await (0,f.Y3)(),await p(),await (0,f.Y3)(),l()};(0,r.useEffect)(()=>{s&&x()},[s]);let{id:v,sql:g}=i,{hasNativeSQL:y,dataSourceType:b}=u,w=u.nativeSQLMode&&!1===u.loading?u.data:g,S=async e=>{u.setNativeSQLMode(e),e&&c({variables:{responseId:v}})};return(0,n.jsxs)("div",{className:"text-md gray-10 p-6 pb-4",children:[(0,n.jsx)(eo.default,{banner:!0,className:"mb-3 adm-alert-info",message:(0,n.jsxs)(n.Fragment,{children:["You’re viewing Wren SQL by default. If you want to run this query on your own database, click “Show original SQL” to get the exact syntax.",(0,n.jsx)(Z.default.Link,{className:"underline ml-1",href:"https://docs.getwren.ai/oss/guide/home/<USER>",target:"_blank",rel:"noopener noreferrer",children:"Learn more about Wren SQL"})]}),type:"info"}),(0,n.jsxs)(eS,{className:"p-0 mb-3",children:[(0,n.jsxs)(eN,{className:"d-flex align-center justify-space-between text-family-base",children:[(0,n.jsx)("div",{children:u.nativeSQLMode?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(ei(),{className:"mr-2",src:ef.cS[b].logo,alt:ef.cS[b].label,width:"22",height:"22"}),(0,n.jsx)(ew,{className:"gray-8 text-medium text-sm",children:ef.cS[b].label})]}):(0,n.jsxs)("span",{className:"d-flex align-center gx-2",children:[(0,n.jsx)(ej.T,{size:18}),(0,n.jsx)(ew,{className:"gray-8 text-medium text-sm",children:"Wren SQL"})]})}),(0,n.jsxs)(ed.default,{split:(0,n.jsx)(N.Z,{type:"vertical",className:"m-0"}),children:[y&&(0,n.jsxs)("div",{className:"d-flex align-center cursor-pointer",onClick:()=>S(!u.nativeSQLMode),children:[(0,n.jsx)(ec.Z,{checkedChildren:(0,n.jsx)(eh(),{}),unCheckedChildren:(0,n.jsx)(ex(),{}),className:"mr-2",size:"small",checked:u.nativeSQLMode,loading:u.loading}),(0,n.jsx)(ew,{className:"gray-8 text-medium text-base",children:"Show original SQL"})]}),(0,n.jsx)(F.default,{type:"link","data-ph-capture":"true","data-ph-capture-attribute-name":"view_sql_copy_sql",icon:(0,n.jsx)(O(),{}),size:"small",onClick:()=>o({sql:g,responseId:v}),children:"Adjust SQL"})]})]}),(0,n.jsx)(eb,{code:w,showLineNumbers:!0,maxHeight:"300",loading:u.loading,copyable:!0,onCopy:()=>{u.nativeSQLMode||d.default.success((0,n.jsxs)(n.Fragment,{children:["You copied Wren SQL. This dialect is for the Wren Engine and may not run directly on your database.",y&&(0,n.jsxs)(n.Fragment,{children:[" ","Click “",(0,n.jsx)("b",{children:"Show original SQL"}),"” to get the executable version."]})]}))}})]}),(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)(F.default,{size:"small",icon:(0,n.jsx)(ev.F$,{style:{paddingBottom:2,marginRight:8}}),loading:h.loading,onClick:p,"data-ph-capture":"true","data-ph-capture-attribute-name":"view_sql_preview_data",children:"View results"}),(null==h?void 0:null===(t=h.data)||void 0===t?void 0:t.previewData)&&(0,n.jsxs)("div",{className:"mt-2 mb-3",children:[(0,n.jsx)(ey.Z,{error:h.error,loading:h.loading,previewData:null==h?void 0:null===(a=h.data)||void 0===a?void 0:a.previewData,locale:{emptyText:(0,n.jsx)(eu.default,{image:eu.default.PRESENTED_IMAGE_SIMPLE,description:"Sorry, we couldn't find any records that match your search criteria."})}}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsx)(ew,{className:"text-base gray-6",children:"Showing up to 500 rows"})})]})]})]})}var ek=a(81116),eC=a(7337),eT=a.n(eC),eE=a(63626),eR=a.n(eE),eP=a(9036),eq=a.n(eP),eA=a(93181),eZ=a.n(eA),eL=a(90536),eD=a(4791);let{Text:e_}=Z.default,eF=(0,I.ZP)(ek.Z).withConfig({displayName:"TextBasedAnswer__StyledSkeleton",componentId:"sc-a697e332-0"})(["padding:16px;.ant-skeleton-paragraph{margin-bottom:0;}"]),eM=e=>[y.Vr.FINISHED,y.Vr.FAILED,y.Vr.INTERRUPTED].includes(e),eQ=e=>eM(e)||e===y.Vr.STREAMING;function eV(e){var t,a,s,l,i;let{onGenerateTextBasedAnswer:o,onOpenAdjustReasoningStepsModal:d,onOpenAdjustSQLModal:u}=R(),{isLastThreadResponse:m,onInitPreviewDone:h,threadResponse:p}=e,{id:x}=p,{content:v,error:g,numRowsUsedInLLM:b,status:w}=(null==p?void 0:p.answerDetail)||{},[S,N]=(0,r.useState)(""),I=function(){let[e,t]=(0,r.useState)(!1);return{visible:e,onVisibleChange:e=>t(e),onCloseDropdownMenu:()=>t(!1)}}(),[k,C]=function(){let e=(0,r.useRef)(null),[t,a]=(0,r.useState)(!1),[n,s]=(0,r.useState)(""),l=()=>{if(e.current){var t;null===(t=e.current)||void 0===t||t.close(),e.current=null}s("")};return[t=>{a(!0),l();let n=new EventSource("/api/ask_task/streaming_answer?responseId=".concat(t));n.onmessage=e=>{let t=JSON.parse(e.data);t.done?(n.close(),a(!1)):s(e=>e+((null==t?void 0:t.message)||""))},n.onerror=e=>{console.error(e),n.close(),a(!1)},e.current=n},{data:n,loading:t,onReset:l}]}(),T=C.data,E=(0,r.useMemo)(()=>w===y.Vr.STREAMING,[w]),P=(0,r.useMemo)(()=>{var e,t;let{payload:a}=p.adjustment||{};return{responseId:p.id,sql:p.sql,retrievedTables:(null===(e=p.askingTask)||void 0===e?void 0:e.retrievedTables)||(null==a?void 0:a.retrievedTables)||[],sqlGenerationReasoning:(null===(t=p.askingTask)||void 0===t?void 0:t.sqlGenerationReasoning)||(null==a?void 0:a.sqlGenerationReasoning)||""}},[p.id,p.sql,null===(t=p.adjustment)||void 0===t?void 0:t.payload,null===(a=p.askingTask)||void 0===a?void 0:a.retrievedTables,null===(s=p.askingTask)||void 0===s?void 0:s.sqlGenerationReasoning]);(0,r.useEffect)(()=>{E?N(T):N(v)},[T,E,v]),(0,r.useEffect)(()=>{E&&k(x)},[E,x]),(0,r.useEffect)(()=>()=>{C.onReset()},[]);let q=(0,r.useMemo)(()=>w===y.Vr.FINISHED?b:0,[b,w]),A=(0,r.useMemo)(()=>q>0,[q]),[Z,L]=(0,j.HT)({onError:e=>console.error(e)}),D=async()=>{await Z({variables:{where:{responseId:x}}})},_=async()=>{await (0,f.Y3)(),await D()};(0,r.useEffect)(()=>{m&&(A&&_(),h())},[m,A]);let M=!eQ(w),Q=async e=>{let{type:t,data:a}=e;t===c.dI.ADJUST_STEPS?d({responseId:a.responseId,retrievedTables:a.retrievedTables,sqlGenerationReasoning:a.sqlGenerationReasoning}):t===c.dI.ADJUST_SQL&&u({responseId:x,sql:a.sql})},V=(0,n.jsx)(eD.aV,{onMoreClick:Q,data:P,onDropdownVisibleChange:I.onVisibleChange,children:(0,n.jsxs)(F.default,{className:"px-0",type:"link",size:"small",icon:(0,n.jsx)(eZ(),{}),onClick:e=>e.stopPropagation(),children:["Adjust the answer",(0,n.jsx)(eq(),{className:"ml-1",rotate:I.visible?180:0})]})});return g?(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"py-4 px-6",children:[(0,n.jsx)("div",{className:"text-right",children:V}),(0,n.jsx)(eo.default,{className:"mt-4 mb-2",message:g.shortMessage,description:g.message,type:"error",showIcon:!0})]})}):(0,n.jsx)(eF,{active:!0,loading:M,paragraph:{rows:4},title:!1,children:(0,n.jsxs)("div",{className:"text-md gray-10 py-4 px-6",children:[(0,n.jsx)("div",{className:"text-right mb-4",children:V}),(0,n.jsx)(eL.Z,{content:S}),E&&(0,n.jsx)(eR(),{className:"geekblue-6",spin:!0}),w===y.Vr.INTERRUPTED&&(0,n.jsx)("div",{className:"mt-2 text-right",children:(0,n.jsx)(F.default,{icon:(0,n.jsx)(eT(),{}),size:"small",type:"link",title:"Regenerate answer",onClick:()=>{N(""),o(x)},children:"Regenerate"})}),A?(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)(F.default,{size:"small",icon:(0,n.jsx)(ev.F$,{style:{paddingBottom:2,marginRight:8}}),loading:L.loading,onClick:D,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_text-answer_preview_data",children:"View results"}),(null==L?void 0:null===(l=L.data)||void 0===l?void 0:l.previewData)&&(0,n.jsxs)("div",{className:"mt-2 mb-3","data-guideid":"text-answer-preview-data",children:[(0,n.jsx)(e_,{type:"secondary",className:"text-sm",children:"Considering the limit of the context window, we retrieve up to 500 rows of results to generate the answer."}),(0,n.jsx)(ey.Z,{error:L.error,loading:L.loading,previewData:null==L?void 0:null===(i=L.data)||void 0===i?void 0:i.previewData})]})]}):(0,n.jsx)(n.Fragment,{children:!E&&(0,n.jsx)(eo.default,{message:(0,n.jsxs)(n.Fragment,{children:["Click ",(0,n.jsx)("b",{children:"View SQL"})," to review the step-by-step query logic and verify why the data is unavailable."]}),type:"info"})})]})})}var eO=a(13518),eG=a(69828),ez=a(83846),eH=a(48403),e$=a.n(eH),eU=a(66590),eB=a(40582),eW=a(55041);let eX=()=>Object.entries(y.oX).map(e=>{let[t,a]=e;return{label:e$()(a.replace("_"," ")),value:t}}),eY=(e,t)=>(e||[]).map(e=>({label:(null==t?void 0:t[e.name])||e.name,value:e.name}));function eJ(e){let{options:t}=e;return(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Chart type",name:"chartType",children:(0,n.jsx)(eU.default,{size:"small",options:t,placeholder:"Select chart type"})})}function eK(e){let{options:t}=e;return(0,n.jsxs)(eB.default,{gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"X-axis",name:"xAxis",children:(0,n.jsx)(eU.default,{size:"small",options:t,placeholder:"Select x-axis"})})}),(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Y-axis",name:"yAxis",children:(0,n.jsx)(eU.default,{size:"small",options:t,placeholder:"Select y-axis"})})})]})}function e0(e){let{columns:t,titleMap:a}=e,s=eX(),l=eY(t,a);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(eB.default,{className:"mb-2",gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eJ,{options:s})}),(0,n.jsx)(eW.default,{span:12})]}),(0,n.jsx)(eK,{options:l})]})}function e1(e){let{columns:t,titleMap:a}=e,s=eX(),l=eY(t,a);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(eB.default,{gutter:16,className:"mb-2",children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eJ,{options:s})}),(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Category",name:"color",children:(0,n.jsx)(eU.default,{size:"small",options:l,placeholder:"Select category"})})})]}),(0,n.jsxs)(eB.default,{gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Value",name:"theta",children:(0,n.jsx)(eU.default,{size:"small",options:l,placeholder:"Select value"})})}),(0,n.jsx)(eW.default,{span:12})]})]})}function e2(e){let{columns:t,titleMap:a}=e,s=eX(),l=eY(t,a);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(eB.default,{className:"mb-2",gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eJ,{options:s})}),(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Line groups",name:"color",children:(0,n.jsx)(eU.default,{size:"small",options:l,placeholder:"Select line groups"})})})]}),(0,n.jsx)(eK,{options:l})]})}function e3(e){let{columns:t,titleMap:a}=e,s=eX(),l=eY(t,a);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(eB.default,{className:"mb-2",gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eJ,{options:s})}),(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Stack groups",name:"color",children:(0,n.jsx)(eU.default,{size:"small",options:l,placeholder:"Select stack groups"})})})]}),(0,n.jsx)(eK,{options:l})]})}function e4(e){let{columns:t,titleMap:a}=e,s=eX(),l=eY(t,a);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(eB.default,{className:"mb-2",gutter:16,children:[(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eJ,{options:s})}),(0,n.jsx)(eW.default,{span:12,children:(0,n.jsx)(eO.Z.Item,{className:"mb-0",label:"Sub-category",name:"xOffset",children:(0,n.jsx)(eU.default,{size:"small",options:l,placeholder:"Select sub-category"})})})]}),(0,n.jsx)(eK,{options:l})]})}var e6=a(18446),e8=a.n(e6),e7=a(79421),e5=a(3482);let e9=el()(()=>Promise.all([a.e(26),a.e(768),a.e(775)]).then(a.bind(a,37711)),{loadableGenerated:{webpack:()=>[37711]},ssr:!1}),te=(0,I.ZP)(ek.Z).withConfig({displayName:"ChartAnswer__StyledSkeleton",componentId:"sc-69cfb09c-0"})(["padding:16px;.ant-skeleton-paragraph{margin-bottom:0;}"]),tt=I.ZP.div.withConfig({displayName:"ChartAnswer__ChartWrapper",componentId:"sc-69cfb09c-1"})(["position:relative;padding-top:0;transition:padding-top 0.2s ease-out;&.isEditMode{padding-top:72px;}"]),ta=I.ZP.div.withConfig({displayName:"ChartAnswer__Toolbar",componentId:"sc-69cfb09c-2"})(["display:flex;justify-content:space-between;align-items:center;background-color:var(--gray-3);padding:8px 16px;position:absolute;top:-72px;left:0;right:0;transition:top 0.2s ease-out;&.isEditMode{top:0;}"]),tn=e=>[y.DN.FINISHED,y.DN.FAILED,y.DN.STOPPED].includes(e),ts=e=>({[y.oX.GROUPED_BAR]:e4,[y.oX.STACKED_BAR]:e3,[y.oX.LINE]:e2,[y.oX.MULTI_LINE]:e2,[y.oX.PIE]:e1})[e]||e0;function tl(e){let{onGenerateChartAnswer:t,onAdjustChartAnswer:a}=R(),{threadResponse:s}=e,[l,i]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),[p]=eO.Z.useForm(),x=eO.Z.useWatch("chartType",p),{chartDetail:v}=s,{error:g,status:f,adjustment:y}=v||{},[b,w]=(0,j.HT)({onError:e=>console.error(e)}),[S]=(0,e5.Sj)({onError:e=>console.error(e),onCompleted:()=>{d.default.success("Successfully pinned chart to dashboard.")}});(0,r.useEffect)(()=>{b({variables:{where:{responseId:s.id}}})},[]);let N=(0,r.useMemo)(()=>!(null==v?void 0:v.chartSchema)||tn(f)&&o()(null==v?void 0:v.chartSchema)?null:v.chartSchema,[v]),I=(0,r.useMemo)(()=>(0,e7.Cf)(v),[v]),k=(0,r.useMemo)(()=>(0,e7.C9)(null==N?void 0:N.encoding),[N]);(0,r.useEffect)(()=>{p.setFieldsValue(I)},[I]);let C=(0,r.useMemo)(()=>null!==m&&!e8()(I,m),[I,m]),T=(0,r.useMemo)(()=>{var e;let{data:t,columns:a}=(null===(e=w.data)||void 0===e?void 0:e.previewData)||{};return(t||[]).map(e=>(a||[]).reduce((t,a,n)=>(t[a.name]=e[n],t),{}))},[w.data]),E=(0,r.useMemo)(()=>{var e;let{columns:t}=(null===(e=w.data)||void 0===e?void 0:e.previewData)||{};return t||[]},[w.data]),P=w.loading||!tn(f)||l,q=ts(x),Z=()=>{(0,ez.iZ)(t,i)(s.id),L()},L=()=>{u(!1),h(null),p.resetFields()},D=()=>{eG.Z.confirm({title:"Are you sure you want to regenerate the chart?",onOk:Z})},_=async()=>{(0,ez.iZ)(a,i)(s.id,p.getFieldsValue()),L()},M=(0,n.jsx)("div",{className:"text-center mt-4",children:(0,n.jsx)(F.default,{icon:(0,n.jsx)(eT(),{}),onClick:D,children:"Regenerate"})});if(g)return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)(eo.default,{message:g.shortMessage,description:g.message,type:"error",showIcon:!0}),M]});let Q=y?M:null;return(0,n.jsx)(te,{active:!0,loading:P,paragraph:{rows:4},title:!1,children:(0,n.jsxs)("div",{className:"text-md gray-10 p-6",children:[null==v?void 0:v.description,N?(0,n.jsxs)(tt,{className:(0,A.Z)("border border-gray-4 rounded mt-4 pb-3 overflow-hidden",{isEditMode:c}),children:[(0,n.jsx)(ta,{className:(0,A.Z)({isEditMode:c}),children:(0,n.jsx)(eO.Z,{size:"small",style:{width:"100%"},form:p,initialValues:I,onFieldsChange:()=>{h(p.getFieldsValue())},children:(0,n.jsxs)("div",{className:"d-flex justify-content-between align-center",children:[(0,n.jsx)("div",{className:"flex-grow-1",children:(0,n.jsx)(q,{columns:E,titleMap:k})}),C&&(0,n.jsxs)("div",{className:"d-flex flex-column",children:[(0,n.jsx)(F.default,{className:"ml-4 mb-2",onClick:()=>{h(null),p.resetFields()},children:"Reset"}),(0,n.jsx)(F.default,{className:"ml-4",type:"primary",onClick:_,children:"Adjust"})]})]})})}),(0,n.jsx)(e9,{width:700,spec:N,values:T,onEdit:()=>{u(!c)},onReload:D,onPin:()=>{eG.Z.confirm({title:"Are you sure you want to pin this chart to the dashboard?",okText:"Save",onOk:async()=>await S({variables:{data:{itemType:x,responseId:s.id}}})})}})]}):Q]})})}var tr=a(42187),ti=a(75746),to=a.n(ti),td=a(47284),tc=a(63879),tu=a.n(tc),tm=a(64909),th=a.n(tm),tp=a(59976),tx=a(59530),tv=a(94638),tg=a(48076),tf=a(22635);function tj(e){var t;let{visible:a,defaultValue:s,loading:l,onSubmit:i,onClose:o}=e,[d,c]=(0,r.useState)(!1),[u]=eO.Z.useForm(),[m,h]=(0,tf.Zg)(),p=(0,r.useMemo)(()=>h.error?{...(0,tv.Bx)(h.error),shortMessage:"Invalid SQL syntax"}:null,[h.error]);(0,r.useEffect)(()=>{a&&u.setFieldsValue(s||{})},[u,s,a]);let x=async()=>{let e=u.getFieldValue("sql");await m({variables:{data:{sql:e,limit:1,dryRun:!0}}})},v=async()=>{u.validateFields().then(async e=>{await (0,ez.iZ)(m,c)({variables:{data:{sql:e.sql,limit:50}}})}).catch(console.error)},g=async()=>{u.validateFields().then(async e=>{await x(),await i(e.sql),o()}).catch(console.error)},f=h.data||h.loading;return(0,n.jsxs)(eG.Z,{title:"Fix SQL",width:640,visible:a,okText:"Submit",onOk:g,onCancel:o,confirmLoading:l,maskClosable:!1,destroyOnClose:!0,centered:!0,afterClose:()=>{u.resetFields(),h.reset()},children:[(0,n.jsx)(Z.default.Text,{className:"d-block gray-7 mb-3",children:"The following SQL statement needs to be fixed:"}),(0,n.jsx)(eO.Z,{form:u,preserve:!1,layout:"vertical",children:(0,n.jsx)(eO.Z.Item,{label:"SQL statement",name:"sql",required:!0,rules:[{required:!0,message:tx.q.FIX_SQL.SQL.REQUIRED}],children:(0,n.jsx)(tg.Z,{autoComplete:!0,autoFocus:!0})})}),(0,n.jsxs)("div",{className:"my-3",children:[(0,n.jsx)(Z.default.Text,{className:"d-block gray-7 mb-2",children:"Data preview (50 rows)"}),(0,n.jsx)(F.default,{onClick:v,loading:d,disabled:d,children:"Preview data"}),f&&(0,n.jsx)("div",{className:"my-3",children:(0,n.jsx)(ey.Z,{loading:d,previewData:null==h?void 0:null===(t=h.data)||void 0===t?void 0:t.previewSql,copyable:!1})})]}),!!p&&(0,n.jsx)(eo.default,{showIcon:!0,type:"error",message:p.shortMessage,description:(0,n.jsx)(tp.Z,{message:p.message})})]})}function ty(e){let{children:t,error:a}=e,s=(0,S.Z)();if(!a)return(0,n.jsx)(n.Fragment,{children:t});let l=!!a.invalidSql;return(0,n.jsx)(td.Z,{className:"px-1 -mb-4",children:(0,n.jsxs)(td.Z.Item,{dot:(0,n.jsx)(tu(),{className:"red-5"}),children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:l?"Failed to generate SQL statement":a.shortMessage}),(0,n.jsxs)("div",{className:"gray-7 text-sm mt-1",children:[(0,n.jsx)("div",{children:l?"We tried to generate SQL based on your question but encountered a small issue. Help us fix it!":a.message}),l&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-gray-2 p-2 my-4",children:(0,n.jsx)(tp.Z,{message:a.message,defaultActive:!0})}),(0,n.jsx)(F.default,{className:"mt-2 adm-fix-it-btn",icon:(0,n.jsx)(th(),{}),size:"small",onClick:()=>s.openModal({sql:a.invalidSql}),children:"Fix it"}),(0,n.jsx)(tj,{...s.state,onClose:s.closeModal,onSubmit:async e=>{await a.fixStatement(e)}})]})]})]})})}var tb=a(68730),tw=a.n(tb);function tS(e){let{data:t,preparedTask:a,onStopAskingTask:s,onReRunAskingTask:l,onStopAdjustTask:i,onReRunAdjustTask:o}=e,[d,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)(!1);if(!(0,p.eX)(a.status))return(0,n.jsx)(F.default,{icon:(0,n.jsx)(tw(),{}),danger:!0,size:"small",onClick:e=>{e.stopPropagation();let t=a.isAdjustment?i:s;(0,ez.iZ)(t,c)(a.queryId)},loading:d,children:"Cancel"});if(a.status===y.j.STOPPED)return(0,n.jsxs)(ed.default,{className:"-mr-4",children:[(0,n.jsx)(D.default,{color:"red",children:"Cancelled by user"}),(0,n.jsx)(F.default,{icon:(0,n.jsx)(eT(),{}),className:"gray-7",size:"small",type:"text",onClick:e=>{e.stopPropagation();let n=a.isAdjustment?o:l;(0,ez.iZ)(n,m)(t)},loading:u,children:"Re-run"})]});if(a.status===y.j.FINISHED){var h;let e=null!==t.view,s=!!(null==a?void 0:null===(h=a.candidates[0])||void 0===h?void 0:h.sqlPair);return(0,n.jsx)("div",{className:"gray-6",children:e||s?"1 step":"3 steps"})}return null}var tN=a(34262),tI=a(77840),tk=a(33190);let tC=(0,tI.x)(e=>{let{name:t}=e;return(0,n.jsx)(D.default,{className:"gray-7 mb-2",children:t})});function tT(e){let{tables:t,loading:a,isAdjustment:s}=e,l=t.map(e=>({name:e})),r=s?(0,n.jsxs)(n.Fragment,{children:[t.length," models applied"]}):(0,n.jsxs)(n.Fragment,{children:["Top ",t.length," model candidates identified"]});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:s?"User-selected models applied":"Retrieving top 10 model candidates"}),(0,n.jsx)("div",{className:"gray-7 text-sm mt-1",children:a?(0,n.jsxs)("div",{className:"d-flex align-center gx-2",children:["Searching",(0,n.jsx)(tk.$j,{className:"gray-6",size:12})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"mb-1",children:r}),(0,n.jsx)(tC,{data:l})]})})]})}function tE(e){let t=(0,r.useRef)(null),{stream:a,loading:s,isAdjustment:l}=e,i=a&&!s,o=()=>{t.current&&t.current.scrollTo({top:t.current.scrollHeight})};return(0,r.useEffect)(()=>{o()},[a]),(0,r.useEffect)(()=>{i&&o()},[i]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:l?"User-provided reasoning steps applied":"Organizing thoughts"}),(0,n.jsx)("div",{ref:t,className:"gray-7 text-sm mt-2",style:{maxHeight:"calc(100vh - 550px)",overflowY:"auto"},children:s&&!a?(0,n.jsxs)("div",{className:"d-flex align-center gx-2",children:["Thinking",(0,n.jsx)(tk.$j,{className:"gray-6",size:12})]}):(0,n.jsx)(eL.Z,{content:a})})]})}function tR(e){let{loading:t,generating:a,correcting:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:"Generating SQL statement"}),(0,n.jsx)("div",{className:"gray-7 text-sm mt-1",children:a||s?(0,n.jsxs)("div",{className:"d-flex align-center gx-2",children:[s?"Correcting SQL statement":"Generating",(0,n.jsx)(tk.$j,{className:"gray-6",size:12})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{children:"Successfully generated SQL statement"}),t&&(0,n.jsxs)("div",{className:"d-flex align-center gx-2 mt-1",children:["Wrapping up ",(0,n.jsx)(tk.$j,{className:"gray-6",size:16})]})]})})]})}function tP(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:"User-Provided SQL applied"}),(0,n.jsx)("div",{className:"gray-7 text-sm mt-1",children:"System encountered an issue generating SQL. The manually submitted query is now being processed."})]})}function tq(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:"Using pre-saved view"}),(0,n.jsx)("div",{className:"gray-7 text-sm mt-1",children:(0,n.jsx)("div",{children:"Matching saved view found. Returning results instantly."})})]})}function tA(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.default.Text,{className:"gray-8",children:"Using question-SQL pair"}),(0,n.jsx)("div",{className:"gray-7 text-sm mt-1",children:(0,n.jsx)("div",{children:"Matching question-SQL pair found. Returning results instantly."})})]})}var tZ=a(33520);let tL=(0,I.ZP)(tN.Z).withConfig({displayName:"PreparationSteps__StyledBadge",componentId:"sc-49ce2ca0-0"})(["position:absolute;top:-5px;left:-3px;.ant-badge-status-dot{width:7px;height:7px;}.ant-badge-status-text{display:none;}"]),tD=tZ.ZI.getAllNextStates(c.F4.SEARCHING,!0),t_=tZ.ZI.getAllNextStates(c.F4.PLANNING,!0),tF=tZ.ZI.getAllNextStates(c.F4.GENERATING,!0),tM=e=>e?(0,n.jsx)(tL,{color:"geekblue",status:"processing"}):null;function tQ(e){var t;let{className:a,data:s,askingStreamTask:l,minimized:i,preparedTask:o}=e,{view:d,sql:u}=s,m=(0,r.useMemo)(()=>(0,tZ.Xq)(o),[o]),h=(0,r.useMemo)(()=>u&&(null==o?void 0:o.invalidSql),[u,null==o?void 0:o.invalidSql]),p=!!(null==o?void 0:null===(t=o.candidates[0])||void 0===t?void 0:t.sqlPair),x=tD.includes(m),v=t_.includes(m),g=tF.includes(m),f=(null==o?void 0:o.retrievedTables)||[],j=(null==o?void 0:o.sqlGenerationReasoning)||l||"",y=m===c.F4.SEARCHING,b=m===c.F4.PLANNING,w=m===c.F4.GENERATING,S=m===c.F4.CORRECTING;return d?(0,n.jsx)(tG,{...e}):p?(0,n.jsx)(tz,{...e}):h?(0,n.jsx)(tO,{...e}):(0,n.jsxs)(td.Z,{className:a,children:[x&&(0,n.jsx)(td.Z.Item,{dot:tM(y),children:(0,n.jsx)(tT,{loading:y,tables:f,isAdjustment:o.isAdjustment})}),v&&(0,n.jsx)(td.Z.Item,{dot:tM(b),children:(0,n.jsx)(tE,{loading:b,stream:j,isAdjustment:o.isAdjustment})}),g&&(0,n.jsx)(td.Z.Item,{dot:tM(w||S),children:(0,n.jsx)(tR,{generating:w,correcting:S,loading:!i})})]})}let tV=(0,n.jsx)(ee(),{className:"gray-6",style:{position:"relative",top:-2,left:2}});function tO(e){let{className:t}=e;return(0,n.jsx)(td.Z,{className:t,children:(0,n.jsx)(td.Z.Item,{dot:tV,children:(0,n.jsx)(tP,{})})})}function tG(e){let{className:t}=e;return(0,n.jsx)(td.Z,{className:t,children:(0,n.jsx)(td.Z.Item,{dot:tV,children:(0,n.jsx)(tq,{})})})}function tz(e){let{className:t}=e;return(0,n.jsx)(td.Z,{className:t,children:(0,n.jsx)(td.Z.Item,{dot:tV,children:(0,n.jsx)(tA,{})})})}function tH(e){let{className:t,data:a,minimized:s,onFixSQLStatement:l}=e,{askingTask:i,adjustmentTask:o,adjustment:d,id:c,sql:u}=a,[m,h]=(0,r.useState)(!u),p=(0,r.useMemo)(()=>{if(null===i&&null===o)return null;let{payload:e}=d||{};return{candidates:[],invalidSql:"",retrievedTables:(null==e?void 0:e.retrievedTables)||[],sqlGenerationReasoning:(null==e?void 0:e.sqlGenerationReasoning)||"",isAdjustment:!!o,...i||{},...o||{}}},[null==i?void 0:i.status,null==o?void 0:o.status,null==d?void 0:d.payload]);(0,r.useEffect)(()=>{h(!s)},[s]);let x=(0,r.useMemo)(()=>(null==p?void 0:p.error)&&!u?{...p.error,invalidSql:null==p?void 0:p.invalidSql,fixStatement:e=>l(c,e)}:null,[p,c,u]);if(null===p)return null;let v=p.status===y.j.STOPPED;return(0,n.jsx)("div",{className:(0,A.Z)("border border-gray-4 rounded",t),children:(0,n.jsx)(tr.default,{className:"bg-gray-1",bordered:!1,expandIconPosition:"right",expandIcon:e=>{let{isActive:t}=e;return!v&&(0,n.jsx)(to(),{className:"gray-6 text-sm",rotate:t?180:0})},activeKey:m&&!v?"preparation":void 0,onChange:e=>{let[t]=e;return h("preparation"===t)},children:(0,n.jsx)(tr.default.Panel,{header:(0,n.jsxs)("div",{className:"flex-grow-1 d-flex align-center justify-space-between gx-2 select-none",children:[(0,n.jsxs)(Z.default.Title,{level:5,className:"gray-8 text-medium mb-0",children:[(0,n.jsx)(ei(),{src:"/images/icon/message-ai.svg",alt:"Answer Preparation Steps",width:24,height:24,className:"mr-1"}),"Answer preparation steps"]}),(0,n.jsx)(tS,{...e,preparedTask:p})]}),children:(0,n.jsx)(ty,{error:x,children:(0,n.jsx)(tQ,{...e,preparedTask:p,className:"px-1 -mb-4"})})},"preparation")})})}let{Title:t$,Text:tU}=Z.default,tB={[y.fz.APPLY_SQL]:"User-provided SQL applied",[y.fz.REASONING]:"Reasoning steps adjusted"},tW=(0,n.jsxs)(n.Fragment,{children:["Store this answer as a Question-SQL pair to help Wren AI improve SQL generation.",(0,n.jsx)("br",{}),(0,n.jsx)(Z.default.Link,{className:"gray-1 underline",href:"https://docs.getwren.ai/oss/guide/knowledge/question-sql-pairs#save-to-knowledge",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})]}),tX=(0,I.ZP)(L.default).withConfig({displayName:"AnswerResult__StyledTabs",componentId:"sc-5b484f8c-0"})([".ant-tabs-nav{margin-bottom:0;}.ant-tabs-content-holder{border-left:1px var(--gray-4) solid;border-right:1px var(--gray-4) solid;border-bottom:1px var(--gray-4) solid;}.ant-tabs-tab{.ant-typography{color:var(--gray-6);}[aria-label='check-circle']{color:var(--gray-5);}[aria-label='code']{color:var(--gray-5);}[aria-label='pie-chart']{color:var(--gray-5);}&.ant-tabs-tab-active{.ant-typography{color:var(--gray-8);}[aria-label='check-circle']{color:var(--green-5);}[aria-label='code']{color:var(--geekblue-5);}[aria-label='pie-chart']{color:var(--gold-6);}.adm-beta-tag{background-color:var(--geekblue-2);color:var(--geekblue-5);}}.adm-beta-tag{padding:0 4px;line-height:18px;margin:0 0 0 6px;border-radius:2px;background-color:var(--gray-5);color:white;border:none;}}"]),tY=e=>{let{question:t,className:a}=e;return(0,n.jsxs)(t$,{className:(0,A.Z)("d-flex bg-gray-1 rounded mt-0",a),level:4,children:[(0,n.jsx)($(),{className:"geekblue-5 mt-1 mr-3"}),(0,n.jsx)(tU,{className:"text-medium gray-8",children:t})]})},tJ=(e,t,a)=>e&&t.show?(0,n.jsx)(X.Z,{className:"mt-5 mb-4",...t.state,onSelect:a}):null,tK=e=>{let{adjustment:t}=e;return(0,n.jsx)("div",{className:"rounded bg-gray-3 gray-6 py-2 px-3 mb-2",children:(0,n.jsxs)("div",{className:"d-flex align-center gx-2",children:[(0,n.jsx)(B(),{className:"gray-7"}),(0,n.jsxs)("div",{className:"flex-grow-1 gray-7",children:["Adjusted answer",(0,n.jsx)(D.default,{className:"gray-6 border border-gray-5 bg-gray-3 ml-3 text-medium",children:tB[t.type]})]})]})})},t0=e=>{let t=eM(null==e?void 0:e.status),a=[y.Vr.NOT_STARTED,y.Vr.PREPROCESSING,y.Vr.FETCHING_DATA].includes(null==e?void 0:e.status);return(null==e?void 0:e.queryId)===null&&!t&&!a};function t1(e){let{threadResponse:t,isLastThreadResponse:a}=e,{onOpenSaveAsViewModal:s,onGenerateThreadRecommendedQuestions:l,onGenerateTextBasedAnswer:i,onGenerateChartAnswer:d,onOpenSaveToKnowledgeModal:u,recommendedQuestions:m,showRecommendedQuestions:h,onSelectRecommendedQuestion:x,preparation:v}=R(),{askingTask:g,adjustmentTask:f,answerDetail:j,breakdownDetail:b,id:w,question:S,sql:N,view:I,adjustment:k}=t,C=(0,X.j)(m,h),T=!!(null==j?void 0:j.queryId)||!!(null==j?void 0:j.status),E=(0,r.useMemo)(()=>null===j&&!o()(b),[j,b]);(0,r.useEffect)(()=>{if(!E&&(0,p.rP)(g,f)&&t0(j)){let e=q()(()=>{i(w),l()},250,{leading:!1,trailing:!0});return e(),()=>{e.cancel()}}},[E,null==g?void 0:g.status,null==f?void 0:f.status,null==j?void 0:j.status]);let P=(null==g?void 0:g.status)===y.j.FINISHED||T||E;return(0,n.jsxs)("div",{style:a?{minHeight:"calc(100vh - (194px))"}:null,"data-jsid":"answerResult",children:[!!k&&(0,n.jsx)(tK,{adjustment:k}),(0,n.jsx)(tY,{className:"mb-4",question:S}),(0,n.jsx)(tH,{className:"mb-3",...v,data:t,minimized:T}),P&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(tX,{type:"card",size:"small",onTabClick:e=>{e!==c.HE.CHART||t.chartDetail||d(w)},children:[!E&&(0,n.jsx)(L.default.TabPane,{tab:(0,n.jsxs)("div",{className:"select-none",children:[(0,n.jsx)(Q(),{className:"mr-2"}),(0,n.jsx)(tU,{children:"Answer"})]}),children:(0,n.jsx)(eV,{...e})},c.HE.ANSWER),(0,n.jsx)(L.default.TabPane,{tab:(0,n.jsxs)("div",{className:"select-none",children:[(0,n.jsx)(O(),{className:"mr-2"}),(0,n.jsx)(tU,{children:"View SQL"})]}),children:(0,n.jsx)(eI,{...e})},c.HE.VIEW_SQL),(0,n.jsx)(L.default.TabPane,{tab:(0,n.jsxs)("div",{className:"select-none",children:[(0,n.jsx)(z(),{className:"mr-2"}),(0,n.jsxs)(tU,{children:["Chart",(0,n.jsx)(D.default,{className:"adm-beta-tag",children:"Beta"})]})]}),children:(0,n.jsx)(tl,{...e})},"chart")]}),(0,n.jsxs)("div",{className:"mt-2 d-flex align-center",children:[(0,n.jsx)(_.default,{overlayInnerStyle:{width:"max-content"},placement:"topLeft",title:tW,children:(0,n.jsx)(F.default,{type:"link",size:"small",className:"mr-2",onClick:()=>{var e;return u({question:(null==t?void 0:null===(e=t.askingTask)||void 0===e?void 0:e.rephrasedQuestion)||S,sql:N},{isCreateMode:!0})},"data-guideid":"save-to-knowledge",children:(0,n.jsxs)("div",{className:"d-flex align-center",children:[(0,n.jsx)(W.Hx,{className:"mr-2"}),"Save to knowledge"]})})}),(0,n.jsx)(en,{view:I,onClick:()=>s({sql:N,responseId:w})})]}),tJ(a,C,x)]})]})}let t2=I.ZP.div.withConfig({displayName:"promptThread__StyledPromptThread",componentId:"sc-dc4d76f8-0"})(["width:768px;margin-left:auto;margin-right:auto;h4.ant-typography{margin-top:10px;}.ant-typography pre{border:none;border-radius:4px;}button{vertical-align:middle;}"]),t3=(0,tI.x)(e=>{let{data:t,index:a,motion:s,onInitPreviewDone:l,...r}=e,{id:i}=r,o=i===t[t.length-1].id;return(0,n.jsxs)("div",{"data-guideid":o?"last-answer-result":void 0,children:[a>0&&(0,n.jsx)(N.Z,{}),(0,n.jsx)(t1,{motion:s,isLastThreadResponse:o,onInitPreviewDone:l,threadResponse:r})]},"".concat(i,"-").concat(a))});function t4(){let e=(0,s.useRouter)(),t=(0,r.useRef)(null),{data:a}=R(),l=(0,r.useMemo)(()=>(null==a?void 0:a.responses)||[],[null==a?void 0:a.responses]),i=e=>{var a,n;if(l.length<=1)return;let s=null===(a=t.current)||void 0===a?void 0:a.parentElement,r=(null===(n=t.current)||void 0===n?void 0:n.querySelectorAll('[data-jsid="answerResult"]'))||[],i=r[r.length-1];s&&i&&s.scrollTo({top:i.offsetTop-48,behavior:e})};return(0,r.useEffect)(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.parentElement;a&&a.scrollTo({top:0})},[e.query]),(0,r.useEffect)(()=>{var e,t;let a=l[l.length-1],n=(0,p.eX)(null==a?void 0:null===(e=a.askingTask)||void 0===e?void 0:e.status)||eM(null==a?void 0:null===(t=a.answerDetail)||void 0===t?void 0:t.status);(0,f.Y3)().then(()=>{i(n?"auto":"smooth")})},[l.length]),(0,n.jsx)(t2,{className:"mt-12",ref:t,children:(0,n.jsx)(t3,{data:l,onInitPreviewDone:()=>{i()}})})}var t6=a(98885),t8=a(18310),t7=a.n(t8),t5=a(32235),t9=a(63284),ae=a(94601);let{Text:at}=Z.default;function aa(e){let{visible:t,loading:a,onSubmit:s,onClose:l,defaultValue:r}=e,[i]=eO.Z.useForm(),[o]=(0,ae.Ox)({fetchPolicy:"no-cache"}),d=r?r.sql:"";return(0,n.jsx)(eG.Z,{title:"Save as View",centered:!0,closable:!0,destroyOnClose:!0,onCancel:l,maskClosable:!1,visible:t,width:600,afterClose:()=>i.resetFields(),footer:(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center",children:[(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center ml-2",style:{width:300},children:[(0,n.jsx)(t7(),{className:"mr-2 text-sm gray-6"}),(0,n.jsx)(at,{type:"secondary",className:"text-sm gray-6 text-left",children:'After saving, make sure you go to "Modeling Page" to deploy all saved views.'})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(F.default,{onClick:l,children:"Cancel"}),(0,n.jsx)(F.default,{type:"primary",onClick:()=>{i.validateFields().then(async e=>{await s({responseId:r.responseId,...e}),l()}).catch(console.error)},loading:a,children:"Save"})]})]}),children:(0,n.jsxs)(eO.Z,{form:i,preserve:!1,layout:"vertical",children:[(0,n.jsx)(eO.Z.Item,{label:"Name",name:"name",required:!0,rules:[{required:!0,validator:(0,t5.qd)(o)}],children:(0,n.jsx)(t6.default,{})}),(0,n.jsx)(eO.Z.Item,{label:"SQL Statement",children:(0,n.jsx)(t9.default,{code:d,showLineNumbers:!0,maxHeight:"300"})})]})})}var an=a(49430),as=a(24350),al=a.n(as),ar=a(12850),ai=a.n(ar),ao=a(88872),ad=a(97395),ac=a(82637),au=a.n(ac),am=a(56144);let ah=I.ZP.div.withConfig({displayName:"MarkdownEditor__Wrapper",componentId:"sc-7ad08917-0"})(["transition:all 0.3s cubic-bezier(0.645,0.045,0.355,1);&:hover{border-color:var(--geekblue-5) !important;}&.adm-markdown-editor-error{border-color:var(--red-5) !important;.adm-markdown-editor-length{color:var(--red-5) !important;}}&:not(.adm-markdown-editor-error).adm-markdown-editor-focused{border-color:var(--geekblue-5) !important;box-shadow:0 0 0 2px rgba(47,84,235,0.2);}&.adm-markdown-editor-focused.adm-markdown-editor-error{borer-color:var(--red-4) !important;box-shadow:0 0 0 2px rgba(255,77,79,0.2);}"]),ap=I.ZP.div.withConfig({displayName:"MarkdownEditor__OverflowContainer",componentId:"sc-7ad08917-1"})(["overflow-y:auto;max-height:318px;"]),ax=(0,I.ZP)(F.default).withConfig({displayName:"MarkdownEditor__LinkButton",componentId:"sc-7ad08917-2"})(["color:var(--gray-7);"]),av=(0,I.ZP)(ad.ZP).withConfig({displayName:"MarkdownEditor__StyledTextArea",componentId:"sc-7ad08917-3"})(["border:none;border-radius:0;textarea{padding:16px 16px 16px 20px;}"]),ag=e=>(0,n.jsx)(ad.ZP.Option,{value:e.value,children:(0,n.jsxs)("div",{className:"d-flex align-center justify-space-between",children:[(0,n.jsxs)("div",{className:"d-flex align-center gray-8",children:[e.icon,(0,n.jsx)(Z.default.Text,{className:"gray-8 mr-2",style:{maxWidth:240},ellipsis:!0,children:e.label})]}),e.meta&&(0,n.jsxs)("div",{className:"gray-6",children:[(0,n.jsxs)(Z.default.Text,{className:"gray-6 text-sm mr-1",style:{maxWidth:240},ellipsis:!0,children:["(",e.meta,")"]}),e.nodeType]})]})},e.id);function af(e){let{value:t,onChange:a,maxLength:s,autoFocus:l,mentions:i}=e,o=(0,r.useRef)(null),d=(0,r.useRef)(null),[c,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!1),{status:p}=(0,r.useContext)(am.FormItemInputContext);return(0,n.jsxs)(ah,{ref:o,className:(0,A.Z)("border border-gray-5 rounded overflow-hidden",p?"adm-markdown-editor-".concat(p):"",c?"adm-markdown-editor-focused":""),tabIndex:-1,children:[(0,n.jsxs)("div",{className:"bg-gray-3 px-2 py-1 d-flex align-center justify-space-between",children:[(0,n.jsx)("div",{className:"adm-markdown-editor-length gray-6 text-sm mr-2",children:s?(0,n.jsxs)(n.Fragment,{children:[null==t?void 0:t.length," / ",s," characters"]}):(0,n.jsxs)(n.Fragment,{children:[null==t?void 0:t.length," characters"]})}),(0,n.jsx)(ax,{icon:m?(0,n.jsx)(eZ(),{}):(0,n.jsx)(au(),{}),type:"link",size:"small",onClick:()=>h(!m),children:m?"Edit mode":"Read mode"})]}),(0,n.jsx)(ap,{className:(0,A.Z)({"p-4":m}),children:m?(0,n.jsx)(eL.Z,{content:t}):(0,n.jsx)(av,{ref:d,rows:13,autoFocus:l,getPopupContainer:()=>null==o?void 0:o.current,onChange:e=>{null==a||a(e)},onSelect:e=>{var n;let s=null===(n=d.current)||void 0===n?void 0:n.textarea;if(!s)return;let l=((null==t?void 0:t.slice(0,s.selectionStart))||"").lastIndexOf("@"),r=l>=0?l:s.selectionStart,i=s.selectionEnd,o=(null==t?void 0:t.slice(0,r))+e.value+(null==t?void 0:t.slice(i));null==a||a(o||""),(0,f.Y3)().then(()=>{s.selectionStart=s.selectionEnd=r+e.value.length})},onKeyDown:e=>{if("Tab"===e.key){e.preventDefault();let n=e.currentTarget,s=n.selectionStart,l=n.selectionEnd,r=(null==t?void 0:t.slice(0,s))+"  "+(null==t?void 0:t.slice(l));null==a||a(r||""),(0,f.Y3)().then(()=>{n.selectionStart=n.selectionEnd=s+2})}if("`"===e.key){let n=e.currentTarget,s=n.selectionStart,l=n.selectionEnd;if(s!==l){e.preventDefault();let r="`".concat(null==t?void 0:t.slice(s,l),"`"),i=(null==t?void 0:t.slice(0,s))+r+(null==t?void 0:t.slice(l));null==a||a(i||""),(0,f.Y3)().then(()=>{n.selectionStart=n.selectionEnd=s+r.length})}}if("ArrowDown"===e.key||"ArrowUp"===e.key){var n;let e=null===(n=o.current)||void 0===n?void 0:n.querySelector(".ant-mentions-dropdown-menu");e&&(0,f.Y3)().then(()=>{let t=e.querySelector(".ant-mentions-dropdown-menu-item-active");if(t){let a=e.getBoundingClientRect(),n=t.getBoundingClientRect();n.bottom>a.bottom?e.scrollTo({top:e.scrollTop+(n.bottom-a.bottom),behavior:"smooth"}):n.top<a.top&&e.scrollTo({top:e.scrollTop-(a.top-n.top),behavior:"smooth"})}})}},onFocus:()=>u(!0),onBlur:()=>u(!1),value:t,prefix:"@",maxLength:s,children:(i||[]).map(ag)})})]})}var aj=a(3591);let ay=(0,I.ZP)(eU.default).withConfig({displayName:"AdjustReasoningStepsModal__MultiSelect",componentId:"sc-b074c384-0"})([".ant-select-selector{padding-top:3px;}.ant-tag{padding:3px 5px;margin-right:3px;margin-bottom:3px;}"]),ab=I.ZP.div.withConfig({displayName:"AdjustReasoningStepsModal__TagText",componentId:"sc-b074c384-1"})(["line-height:16px;"]);function aw(e){var t,a,s;let{visible:l,defaultValue:i,loading:o,onSubmit:d,onClose:c}=e,[u]=eO.Z.useForm(),m=(0,ao.ZP)({convertor:ao.fV,includeColumns:!0,skip:!l}),h=(0,aj.it)({skip:!l}),p=al()(null===(t=h.data)||void 0===t?void 0:t.listModels,"referenceName"),x=(0,r.useMemo)(()=>{var e;return null===(e=h.data)||void 0===e?void 0:e.listModels.map(e=>({label:e.displayName,value:e.referenceName}))},[null===(a=h.data)||void 0===a?void 0:a.listModels]);(0,r.useEffect)(()=>{var e;if(!l)return;let t=((null===(e=h.data)||void 0===e?void 0:e.listModels)||[]).reduce((e,t)=>((null==i?void 0:i.retrievedTables.includes(t.referenceName))&&e.push(t.referenceName),e),[]);u.setFieldsValue({tables:t,sqlGenerationReasoning:null==i?void 0:i.sqlGenerationReasoning})},[u,i,l,null===(s=h.data)||void 0===s?void 0:s.listModels]);let v=async()=>{u.validateFields().then(async e=>{await d({responseId:i.responseId,data:e}),c()}).catch(console.error)};return(0,n.jsx)(eG.Z,{title:"Adjust steps",width:640,visible:l,okText:"Regenerate answer",onOk:v,onCancel:c,confirmLoading:o,maskClosable:!1,destroyOnClose:!0,centered:!0,afterClose:()=>{u.resetFields()},children:(0,n.jsxs)(eO.Z,{form:u,preserve:!1,layout:"vertical",children:[(0,n.jsx)(eO.Z.Item,{label:"Selected models",name:"tables",required:!1,rules:[{required:!0,message:tx.q.ADJUST_REASONING.SELECTED_MODELS.REQUIRED}],extra:(0,n.jsxs)("div",{className:"text-sm gray-6 mt-1",children:["Select the tables needed to answer your question."," ",(0,n.jsx)("span",{className:"gray-7",children:"Tables not selected won't be used in SQL generation."})]}),children:(0,n.jsx)(ay,{mode:"multiple",placeholder:"Select models",options:x,tagRender:e=>{let{value:t,closable:a,onClose:s}=e,l=p[t];return(0,n.jsx)(D.default,{onMouseDown:e=>e.stopPropagation(),closable:a,onClose:s,className:"d-flex align-center bg-gray-3 border-gray-3",style:{maxWidth:140},children:(0,n.jsxs)("div",{className:"pr-1",style:{minWidth:0},children:[(0,n.jsx)(ab,{className:"gray-8 text-truncate",title:l.displayName,children:l.displayName}),(0,n.jsx)(ab,{className:"gray-7 text-xs text-truncate",title:l.referenceName,children:l.referenceName})]})})}})}),(0,n.jsxs)(eO.Z.Item,{label:"Reasoning steps",className:"pb-0",extra:(0,n.jsxs)("div",{className:"text-sm gray-6 mt-1",children:[(0,n.jsx)(ai(),{className:"mr-1"}),"Protip: Use @ to choose model in the textarea."]}),children:[(0,n.jsx)("div",{className:"text-sm gray-6 mb-1",children:"Edit the reasoning logic below. Each step should build toward answering the question accurately."}),(0,n.jsx)(eO.Z.Item,{noStyle:!0,name:"sqlGenerationReasoning",required:!1,rules:[{required:!0,message:tx.q.ADJUST_REASONING.STEPS.REQUIRED},{max:3e3,message:tx.q.ADJUST_REASONING.STEPS.MAX_LENGTH}],children:(0,n.jsx)(af,{maxLength:3e3,mentions:m})})]})]})})}function aS(e){var t;let{defaultValue:a,loading:s,onClose:l,onSubmit:i,visible:o}=e,[d]=eO.Z.useForm(),[c,u]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1),[p,x]=(0,r.useState)(!1),[v,g]=(0,r.useState)(!1),[f,j]=(0,tf.Zg)(),y=eO.Z.useWatch("sql",d);(0,r.useEffect)(()=>{o&&d.setFieldsValue({sql:null==a?void 0:a.sql})},[o,a]);let b=()=>{j.reset(),g(!1),u(null),d.resetFields()},w=async()=>{await f({variables:{data:{sql:y,limit:1,dryRun:!0}}})},S=e=>{let t=(0,tv.Bx)(e);u({...t,shortMessage:"Invalid SQL syntax"}),console.error(t)},N=async()=>{u(null),h(!0);try{await w(),g(!0),await f({variables:{data:{sql:y,limit:50}}})}catch(e){g(!1),S(e)}finally{h(!1)}},I=s||p,k=!y;return(0,n.jsxs)(eG.Z,{title:"Adjust SQL",centered:!0,closable:!0,confirmLoading:I,destroyOnClose:!0,maskClosable:!1,onCancel:l,visible:o,width:640,cancelButtonProps:{disabled:I},okButtonProps:{disabled:j.loading},afterClose:()=>b(),footer:(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center",children:[(0,n.jsxs)("div",{className:"text-sm ml-2 d-flex justify-space-between align-center",style:{width:300},children:[(0,n.jsx)(t7(),{className:"mr-2 text-sm gray-7"}),(0,n.jsxs)(Z.default.Text,{type:"secondary",className:"text-sm gray-7 text-left",children:["The SQL statement used here follows ",(0,n.jsx)("b",{children:"Wren SQL"}),", which is based on ANSI SQL and optimized for Wren AI."," ",(0,n.jsx)(Z.default.Link,{type:"secondary",href:"https://docs.getwren.ai/oss/guide/home/<USER>",target:"_blank",rel:"noopener noreferrer",children:"Learn more about the syntax."})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(F.default,{onClick:l,children:"Cancel"}),(0,n.jsx)(F.default,{type:"primary",onClick:()=>{u(null),x(!0),g(!1),d.validateFields().then(async e=>{try{await w(),await i({responseId:null==a?void 0:a.responseId,sql:e.sql}),l()}catch(e){S(e)}finally{x(!1)}}).catch(e=>{x(!1),console.error(e)})},loading:I,children:"Submit"})]})]}),children:[(0,n.jsx)(eO.Z,{form:d,preserve:!1,layout:"vertical",children:(0,n.jsx)(eO.Z.Item,{label:"SQL statement",name:"sql",required:!0,rules:[{required:!0,message:tx.q.SQL_PAIR.SQL.REQUIRED}],children:(0,n.jsx)(tg.Z,{autoComplete:!0,autoFocus:!0})})}),(0,n.jsxs)("div",{className:"my-3",children:[(0,n.jsx)(Z.default.Text,{className:"d-block gray-7 mb-2",children:"Data preview (50 rows)"}),(0,n.jsx)(F.default,{onClick:N,loading:m,disabled:k,children:"Preview data"}),v&&(0,n.jsx)("div",{className:"my-3",children:(0,n.jsx)(ey.Z,{loading:m,previewData:null==j?void 0:null===(t=j.data)||void 0===t?void 0:t.previewSql,copyable:!1})})]}),!!c&&(0,n.jsx)(eo.default,{showIcon:!0,type:"error",message:c.shortMessage,description:(0,n.jsx)(tp.Z,{message:c.message})})]})}var aN=a(33454);let aI=e=>{let{answerDetail:t,breakdownDetail:a,chartDetail:n}=e||{},s=!!(null===t&&!o()(a))&&null,l=null;return((null==t?void 0:t.queryId)||(null==t?void 0:t.status))&&(s=eM(null==t?void 0:t.status)),(null==n?void 0:n.queryId)&&(l=tn(null==n?void 0:n.status)),!1!==s&&!1!==l};function ak(){var e;let t=(0,r.useRef)(null),a=(0,s.useRouter)(),i=(0,l.useParams)(),o=(0,u.Z)(),x=(0,r.useMemo)(()=>Number(null==i?void 0:i.id)||null,[i]),v=(0,p.ZP)(x),g=function(e){let[t]=(0,j.dP)(),[a]=(0,j.T_)(),[n,s]=(0,j.wK)(),[l,i]=(0,j.kY)({pollInterval:1e3}),o=s.loading,d=(0,r.useMemo)(()=>{var e;return(null===(e=i.data)||void 0===e?void 0:e.threadResponse.adjustmentTask)||null},[i.data]),c=(0,r.useMemo)(()=>({adjustmentTask:d}),[d]);return(0,r.useEffect)(()=>{b(null==d?void 0:d.status)&&i.stopPolling()},[null==d?void 0:d.status]),{data:c,loading:o,onAdjustReasoningSteps:async(t,a)=>{var s;let r=null===(s=(await n({variables:{responseId:t,data:{tables:a.tables,sqlGenerationReasoning:a.sqlGenerationReasoning}}})).data)||void 0===s?void 0:s.adjustThreadResponse;await l({variables:{responseId:r.id}}),w(e,r,i.client)},onAdjustSQL:async(t,a)=>{var s;w(e,null===(s=(await n({variables:{responseId:t,data:{sql:a}}})).data)||void 0===s?void 0:s.adjustThreadResponse,i.client)},onStop:async e=>{var a,n,l;let r=e||(null===(l=s.data)||void 0===l?void 0:null===(n=l.adjustThreadResponse)||void 0===n?void 0:null===(a=n.adjustmentTask)||void 0===a?void 0:a.queryId);r&&(await t({variables:{taskId:r}}),await (0,f.Y3)(1e3))},onReRun:async e=>{let t=e.id;await a({variables:{responseId:t}}),await l({variables:{responseId:t}})}}}(x),y=(0,S.Z)(),N=(0,S.Z)(),I=(0,S.Z)(),k=(0,S.Z)(),[C,T]=(0,r.useState)(!1),[R,{loading:P}]=(0,ae.S1)({onError:e=>console.error(e),onCompleted:()=>d.default.success("Successfully created view.")}),{data:q,updateQuery:A}=(0,j.e8)({variables:{threadId:x},fetchPolicy:"cache-and-network",skip:null===x,onError:()=>a.push(c.y$.Home)}),[Z]=(0,j.Ok)({onCompleted(e){let t=e.createThreadResponse;A(e=>({...e,thread:{...e.thread,responses:[...e.thread.responses,t]}}))}}),[L]=(0,j.jv)({onCompleted:e=>{d.default.success("Successfully updated the SQL statement"),Y(e.updateThreadResponse.id)}}),[D,_]=(0,j.kY)({pollInterval:1e3,onCompleted(e){let t=e.threadResponse;A(e=>({...e,thread:{...e.thread,responses:e.thread.responses.map(e=>e.id===t.id?t:e)}}))}}),[F]=(0,j.Zy)(),[M,Q]=(0,j.Rf)({pollInterval:1e3}),[V]=(0,j.WL)(),[O]=(0,j.j8)(),[G]=(0,j.DC)(),[z,{loading:H}]=(0,aN.Nz)({refetchQueries:["SqlPairs"],awaitRefetchQueries:!0,onError:e=>console.error(e),onCompleted:()=>{d.default.success("Successfully created question-sql pair.")}}),$=(0,r.useMemo)(()=>(null==q?void 0:q.thread)||null,[q]),U=(0,r.useMemo)(()=>(null==$?void 0:$.responses)||[],[$]),B=(0,r.useMemo)(()=>{var e;return(null===(e=_.data)||void 0===e?void 0:e.threadResponse)||null},[_.data]),W=(0,r.useMemo)(()=>aI(B),[B]),X=async(e,t)=>{await L({variables:{where:{id:e},data:{sql:t}}})},Y=async e=>{await V({variables:{responseId:e}}),D({variables:{responseId:e}})},J=async e=>{await O({variables:{responseId:e}}),D({variables:{responseId:e}})},K=async(e,t)=>{await G({variables:{responseId:e,data:t}}),D({variables:{responseId:e}})},ee=async()=>{await F({variables:{threadId:x}}),M({variables:{threadId:x}})},et=(0,r.useCallback)(e=>{let t=(e||[]).find(e=>{var t;return(null==e?void 0:e.askingTask)&&!(0,p.eX)(null==e?void 0:null===(t=e.askingTask)||void 0===t?void 0:t.status)});if(t){var a;v.onFetching(null==t?void 0:null===(a=t.askingTask)||void 0===a?void 0:a.queryId);return}let n=(e||[]).find(e=>!aI(e));(0,p.di)(null==n?void 0:n.askingTask)&&n&&D({variables:{responseId:n.id}})},[v,D]),ea=(0,r.useCallback)(e=>{let t=e.flatMap(e=>e.question||[]);t&&v.onStoreThreadQuestions(t)},[v]);(0,r.useEffect)(()=>(null!==x&&(M({variables:{threadId:x}}),T(!0)),()=>{var e;v.onStopPolling(),_.stopPolling(),Q.stopPolling(),null===(e=t.current)||void 0===e||e.close()}),[x]),(0,r.useEffect)(()=>{U&&(et(U),ea(U))},[U]),(0,r.useEffect)(()=>{W&&(_.stopPolling(),T(!0))},[W]);let en=(0,r.useMemo)(()=>{var e;return(null===(e=Q.data)||void 0===e?void 0:e.getThreadRecommendationQuestions)||null},[Q.data]);(0,r.useEffect)(()=>{(0,p.pd)(null==en?void 0:en.status)&&Q.stopPolling()},[en]);let es=async e=>{try{v.onStopPolling();let t=$.id;await Z({variables:{threadId:t,data:e}}),T(!1)}catch(e){console.error(e)}},el={data:$,recommendedQuestions:en,showRecommendedQuestions:C,preparation:{askingStreamTask:null===(e=v.data)||void 0===e?void 0:e.askingStreamTask,onStopAskingTask:v.onStop,onReRunAskingTask:v.onReRun,onStopAdjustTask:g.onStop,onReRunAdjustTask:g.onReRun,onFixSQLStatement:X},onOpenSaveAsViewModal:y.openModal,onSelectRecommendedQuestion:es,onGenerateThreadRecommendedQuestions:ee,onGenerateTextBasedAnswer:Y,onGenerateChartAnswer:J,onAdjustChartAnswer:K,onOpenSaveToKnowledgeModal:N.openModal,onOpenAdjustReasoningStepsModal:I.openModal,onOpenAdjustSQLModal:k.openModal};return(0,n.jsxs)(m.Z,{loading:!1,sidebar:o,children:[(0,n.jsx)(E,{value:el,children:(0,n.jsx)(t4,{})}),(0,n.jsx)("div",{className:"py-12"}),(0,n.jsx)(h.Z,{ref:t,...v,onCreateResponse:es}),(0,n.jsx)(aa,{...y.state,loading:P,onClose:y.closeModal,onSubmit:async e=>{await R({variables:{data:e}})}}),(0,n.jsx)(an.Z,{...N.state,onClose:N.closeModal,loading:H,onSubmit:async e=>{let{data:t}=e;await z({variables:{data:t}})}}),(0,n.jsx)(aw,{...I.state,onClose:I.closeModal,loading:g.loading,onSubmit:async e=>{await g.onAdjustReasoningSteps(e.responseId,e.data)}}),(0,n.jsx)(aS,{...k.state,onClose:k.closeModal,loading:g.loading,onSubmit:async e=>await g.onAdjustSQL(e.responseId,e.sql)})]})}}},function(e){e.O(0,[774,281,530,90,543,510,342,143,993,858,848,623,548,840,888,179],function(){return e(e.s=23076)}),_N_E=e.O()}]);