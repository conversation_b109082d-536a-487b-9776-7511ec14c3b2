(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[942],{51706:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/home/<USER>",function(){return t(4535)}])},3482:function(e,n,t){"use strict";t.d(n,{SG:function(){return v},Sj:function(){return E},_R:function(){return S},bI:function(){return T},mC:function(){return N},w5:function(){return I},yH:function(){return L}});var a=t(82729),r=t(68806),l=t(50319),d=t(6812);function s(){let e=(0,a._)(["\n    fragment CommonDashboardItem on DashboardItem {\n  id\n  dashboardId\n  type\n  layout {\n    x\n    y\n    w\n    h\n  }\n  detail {\n    sql\n    chartSchema\n  }\n  displayName\n}\n    "]);return s=function(){return e},e}function i(){let e=(0,a._)(["\n    query DashboardItems {\n  dashboardItems {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return i=function(){return e},e}function o(){let e=(0,a._)(["\n    mutation CreateDashboardItem($data: CreateDashboardItemInput!) {\n  createDashboardItem(data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return o=function(){return e},e}function c(){let e=(0,a._)(["\n    mutation UpdateDashboardItem($where: DashboardItemWhereInput!, $data: UpdateDashboardItemInput!) {\n  updateDashboardItem(where: $where, data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return c=function(){return e},e}function u(){let e=(0,a._)(["\n    mutation UpdateDashboardItemLayouts($data: UpdateDashboardItemLayoutsInput!) {\n  updateDashboardItemLayouts(data: $data) {\n    ...CommonDashboardItem\n  }\n}\n    ",""]);return u=function(){return e},e}function h(){let e=(0,a._)(["\n    mutation DeleteDashboardItem($where: DashboardItemWhereInput!) {\n  deleteDashboardItem(where: $where)\n}\n    "]);return h=function(){return e},e}function m(){let e=(0,a._)(["\n    mutation PreviewItemSQL($data: PreviewItemSQLInput!) {\n  previewItemSQL(data: $data) {\n    data\n    cacheHit\n    cacheCreatedAt\n    cacheOverrodeAt\n    override\n  }\n}\n    "]);return m=function(){return e},e}function f(){let e=(0,a._)(["\n    mutation SetDashboardSchedule($data: SetDashboardScheduleInput!) {\n  setDashboardSchedule(data: $data) {\n    id\n    projectId\n    name\n    cacheEnabled\n    scheduleFrequency\n    scheduleTimezone\n    scheduleCron\n    nextScheduledAt\n  }\n}\n    "]);return f=function(){return e},e}function p(){let e=(0,a._)(["\n    query Dashboard {\n  dashboard {\n    id\n    name\n    description\n    cacheEnabled\n    nextScheduledAt\n    schedule {\n      frequency\n      hour\n      minute\n      day\n      timezone\n      cron\n    }\n    items {\n      ...CommonDashboardItem\n    }\n  }\n}\n    ",""]);return p=function(){return e},e}let x={},b=(0,r.Ps)(s());(0,r.Ps)(i(),b);let y=(0,r.Ps)(o(),b);function E(e){let n={...x,...e};return l.D(y,n)}let g=(0,r.Ps)(c(),b);function v(e){let n={...x,...e};return l.D(g,n)}let j=(0,r.Ps)(u(),b);function I(e){let n={...x,...e};return l.D(j,n)}let C=(0,r.Ps)(h());function S(e){let n={...x,...e};return l.D(C,n)}let D=(0,r.Ps)(m());function N(e){let n={...x,...e};return l.D(D,n)}let w=(0,r.Ps)(f());function T(e){let n={...x,...e};return l.D(w,n)}let k=(0,r.Ps)(p(),b);function L(e){let n={...x,...e};return d.aM(k,n)}},53692:function(e,n,t){"use strict";t.d(n,{P:function(){return h},Z:function(){return m}});var a=t(85893),r=t(67294),l=t(13518),d=t(98885),s=t(19521),i=t(27361),o=t.n(i),c=t(5019);let u=s.ZP.div.withConfig({displayName:"EditableWrapper__EditableStyle",componentId:"sc-12f513e9-0"})(["line-height:24px;min-height:25px;.editable-cell-value-wrap{padding:0 7px;border:1px var(--gray-4) solid;border-radius:4px;cursor:pointer;&:hover{border-color:var(--gray-5);}}.ant-form-item-control-input{min-height:24px;.ant-input{line-height:24px;}}"]),h=(0,r.createContext)(null);function m(e){let{children:n,dataIndex:t,record:s,rules:i,handleSave:m}=e,[f,p]=(0,r.useState)(!1),x=(0,r.useRef)(null),b=(0,r.useRef)(0),y=(0,r.useRef)(null),E=(0,r.useContext)(h),g=Array.isArray(t)?t.join("."):t;(0,r.useEffect)(()=>{f&&y.current.focus()},[f]);let v=()=>{x.current&&(b.current=x.current.clientWidth),p(!f);let e=o()(s,g);E.setFieldsValue({[g]:e})},j=async()=>{try{let e=await E.validateFields();v(),m(s.id,e)}catch(e){console.log("Save failed:",e)}},I=f?(0,a.jsx)(l.Z.Item,{style:{margin:0},name:g,rules:i,children:(0,a.jsx)(d.default,{size:"small",ref:y,onPressEnter:j,onBlur:j,style:{width:b.current}})}):(0,a.jsx)("div",{ref:x,className:"editable-cell-value-wrap",style:{paddingRight:24},onClick:v,children:(0,a.jsx)(c.Z,{text:n})});return(0,a.jsx)(u,{children:I})}},4791:function(e,n,t){"use strict";t.d(n,{At:function(){return C},Lb:function(){return S},aV:function(){return L},iI:function(){return T},px:function(){return N},rt:function(){return w},s_:function(){return D},uu:function(){return k}});var a=t(85893);t(67294);var r=t(19521),l=t(69371),d=t(59046),s=t(84908),i=t(93181),o=t.n(i),c=t(7337),u=t.n(c),h=t(80112),m=t.n(h),f=t(92870),p=t.n(f),x=t(31499),b=t.n(x),y=t(40492),E=t.n(y),g=t(32815),v=t(26123);let j=(0,r.ZP)(l.default).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),I=e=>n=>{let{children:t,onMenuEnter:r,onDropdownVisibleChange:l}=n,s=e(n);return(0,a.jsx)(d.default,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:(0,a.jsx)(j,{onClick:e=>e.domEvent.stopPropagation(),items:s,onMouseEnter:r}),onVisibleChange:l,children:t})},C=I(e=>{let{onMoreClick:n}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{className:"mr-2"}),"Update Columns"]}),key:s.dI.UPDATE_COLUMNS,onClick:()=>n(s.dI.UPDATE_COLUMNS)},{label:(0,a.jsx)(v.ZQ,{onConfirm:()=>n(s.dI.DELETE)}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),S=I(e=>{let{onMoreClick:n}=e;return[{label:(0,a.jsx)(v.e0,{onConfirm:()=>n(s.dI.DELETE)}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),D=I(e=>{let{onMoreClick:n,data:t}=e,{nodeType:r}=t,l={[s.QZ.CALCULATED_FIELD]:v.pn,[s.QZ.RELATION]:v.ck}[r]||v.pn;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:s.dI.EDIT,onClick:()=>n(s.dI.EDIT)},{label:(0,a.jsx)(l,{onConfirm:()=>n(s.dI.DELETE)}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),N=I(e=>{let{onMoreClick:n,isSupportCached:t}=e;return[t&&{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E(),{className:"mr-2"}),"Cache settings"]}),key:s.dI.CACHE_SETTINGS,onClick:()=>n(s.dI.CACHE_SETTINGS)},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u(),{className:"mr-2"}),t?"Refresh all caches":"Refresh all"]}),key:s.dI.REFRESH,onClick:()=>n(s.dI.REFRESH)}].filter(Boolean)}),w=I(e=>{let{onMoreClick:n,isHideLegend:t,isSupportCached:r}=e;return[{label:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p(),{className:"mr-2"}),"Show categories"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m(),{className:"mr-2"}),"Hide categories"]}),key:s.dI.HIDE_CATEGORY,onClick:()=>n(s.dI.HIDE_CATEGORY)},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u(),{className:"mr-2"}),r?"Refresh cache":"Refresh"]}),key:s.dI.REFRESH,onClick:()=>n(s.dI.REFRESH)},{label:(0,a.jsx)(v.mU,{onConfirm:()=>n(s.dI.DELETE)}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),T=I(e=>{let{onMoreClick:n,data:t}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p(),{className:"mr-2"}),"View"]}),key:s.dI.VIEW_SQL_PAIR,onClick:()=>n({type:s.dI.VIEW_SQL_PAIR,data:t})},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:s.dI.EDIT,onClick:()=>n({type:s.dI.EDIT,data:t})},{label:(0,a.jsx)(v.pv,{onConfirm:()=>n({type:s.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),k=I(e=>{let{onMoreClick:n,data:t}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p(),{className:"mr-2"}),"View"]}),key:s.dI.VIEW_INSTRUCTION,onClick:()=>n({type:s.dI.VIEW_INSTRUCTION,data:t})},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{className:"mr-2"}),"Edit"]}),key:s.dI.EDIT,onClick:()=>n({type:s.dI.EDIT,data:t})},{label:(0,a.jsx)(v.py,{onConfirm:()=>n({type:s.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:s.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),L=I(e=>{let{onMoreClick:n,data:t}=e;return[{label:"Adjust steps",icon:(0,a.jsx)(g.Wi,{}),disabled:!t.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>n({type:s.dI.ADJUST_STEPS,data:t})},{label:"Adjust SQL",icon:(0,a.jsx)(b(),{className:"text-base"}),disabled:!t.sql,key:"adjust-sql",onClick:()=>n({type:s.dI.ADJUST_SQL,data:t})}]})},48196:function(e,n,t){"use strict";t.d(n,{Z:function(){return l}});var a=t(67294),r=t(84908);function l(){let[e,n]=(0,a.useState)(!1),[t,l]=(0,a.useState)(r.SD.CREATE),[d,s]=(0,a.useState)(null);return{state:{visible:e,formMode:t,defaultValue:d},openDrawer:e=>{e&&s(e),e&&l(r.SD.EDIT),n(!0)},closeDrawer:()=>{n(!1),s(null),l(r.SD.CREATE)},updateState:e=>{s(e)}}}},4535:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return ev}});var a=t(85893),r=t(67294),l=t(37031),d=t(84908),s=t(11163),i=t(30848),o=t(83777),c=t(48196),u=t(33190),h=t(5152),m=t.n(h),f=t(13518),p=t(21367),x=t(19521),b=t(69968),y=t.n(b),E=t(39616),g=t(36238),v=t(4791),j=t(53692),I=t(3482);t(98511),t(68044);let C=m()(()=>Promise.all([t.e(26),t.e(768),t.e(711)]).then(t.bind(t,37711)),{loadableGenerated:{webpack:()=>[37711]},ssr:!1}),S=x.ZP.div.withConfig({displayName:"dashboardGrid__StyledDashboardGrid",componentId:"sc-cbcdafbf-0"})(["flex:1;padding:16px;.react-grid-layout{width:100%;height:100%;}.adm-pinned-item{cursor:grab;background-color:white;height:100%;border-radius:4px;border:2px solid transparent;box-shadow:rgba(45,62,80,0.12) 0px 1px 5px 0px;transition:border-color 0.2s ease;&:hover{border-color:var(--geekblue-6);}}.adm-pinned-item-header{display:flex;justify-content:space-between;align-items:center;padding:16px 8px 0 16px;*{min-width:0;}}.adm-pinned-item-title{font-size:14px;font-weight:700;flex-grow:1;}.adm-pinned-actions{display:flex;gap:4px;align-items:center;flex-shrink:0;}.adm-pinned-content{height:calc(100% - 40px);padding:16px 12px 16px;&-overflow{overflow:auto;height:calc(100% - 18px);padding:8px 12px;}&-info{font-size:12px;color:var(--gray-6);text-align:right;user-select:none;}}.adm-pinned-item-chart{height:100%;}.react-grid-placeholder{background-color:var(--blue-6);}"]),D=e=>6*e+48,N=e=>(e-48)/6,w=e=>({i:e.id.toString(),x:e.layout.x,y:e.layout.y,w:e.layout.w,h:e.layout.h}),T=e=>({itemId:Number(e.i),x:e.x,y:e.y,w:e.w,h:e.h}),k=(0,r.forwardRef)((e,n)=>{let{items:t,isSupportCached:l,onUpdateChange:d,onDelete:s}=e,i=(0,r.useRef)({}),o=(0,r.useRef)(null),[c,u]=(0,r.useState)(250);(0,r.useEffect)(()=>{t.forEach(e=>{i.current[e.id]=(0,r.createRef)()})},[t]),(0,r.useImperativeHandle)(n,()=>({onRefreshAll:()=>{Object.values(i.current).forEach(e=>{var n;null===(n=e.current)||void 0===n||n.onRefresh()})}}),[t]);let h=(0,r.useMemo)(()=>t.map(e=>w(e)),[t]);return(0,r.useEffect)(()=>{let e=()=>{if(!o.current)return;let e=window.innerWidth-280-32,n=e;e<=1024?(n=1024,o.current.style.minWidth="".concat(1056,"px")):o.current.style.minWidth="100%",u(N(n))};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[o]),(0,a.jsx)(S,{ref:o,children:(0,a.jsx)(y(),{layout:h,cols:6,margin:[8,8],containerPadding:[0,0],rowHeight:c,width:D(c),onLayoutChange:e=>{d(e.map(e=>T(e)))},children:t.map(e=>(0,a.jsx)("div",{children:(0,a.jsx)(R,{ref:i.current[e.id],isSupportCached:l,item:e,onDelete:s})},e.id))})})}),L=e=>{let{title:n}=e,[t]=f.Z.useForm(),[r]=(0,I.SG)({onError:e=>console.error(e)});return(0,a.jsx)(j.P.Provider,{value:t,children:(0,a.jsx)(f.Z,{className:"d-flex",form:t,children:(0,a.jsx)(j.Z,{record:e,dataIndex:"title",handleSave:(e,t)=>{t.title!==n&&r({variables:{where:{id:e},data:{displayName:t.title.trim()}}})},children:n})})})},R=(0,r.forwardRef)((e,n)=>{var t,l;let{item:s,isSupportCached:i,onDelete:o}=e,{detail:c}=s,[h,m]=(0,r.useState)(!0),[f,x]=(0,r.useState)(!1),[b,y]=(0,r.useState)(0);(0,r.useImperativeHandle)(n,()=>({onRefresh:()=>{j({variables:{data:{itemId:s.id,refresh:i}}})}}),[s.id]);let[j,S]=(0,I.mC)(),D=null===(t=S.data)||void 0===t?void 0:t.previewItemSQL,N=(null==D?void 0:D.cacheOverrodeAt)||(null==D?void 0:D.cacheCreatedAt);(0,r.useEffect)(()=>{j({variables:{data:{itemId:s.id}}})},[s.id]),(0,r.useEffect)(()=>{x(!0),(0,g.Y3)(200).then(()=>{y(e=>e+1),x(!1)})},[s.layout]);let w=(0,r.useMemo)(()=>{var e;return s.displayName||(null===(e=s.detail.chartSchema)||void 0===e?void 0:e.title)||""},[s.displayName,null===(l=s.detail.chartSchema)||void 0===l?void 0:l.title]),T=()=>{m(!h),y(e=>e+1)},k=async e=>{e===d.dI.DELETE?await o(s.id):e===d.dI.REFRESH?j({variables:{data:{itemId:s.id,refresh:i}}}):e===d.dI.HIDE_CATEGORY&&T()},R=f||S.loading;return(0,a.jsxs)("div",{className:"adm-pinned-item",children:[(0,a.jsxs)("div",{className:"adm-pinned-item-header",children:[(0,a.jsx)("div",{className:"adm-pinned-item-title",title:w,onMouseDown:e=>e.stopPropagation(),children:(0,a.jsx)(L,{id:s.id,title:w})}),(0,a.jsx)("div",{className:"adm-pinned-actions",children:(0,a.jsx)(v.rt,{onMoreClick:k,isHideLegend:h,isSupportCached:i,children:(0,a.jsx)(p.default,{className:"adm-pinned-more gray-8",type:"text",size:"small",icon:(0,a.jsx)(E.nX,{}),onMouseDown:e=>e.stopPropagation()})})})]}),(0,a.jsxs)("div",{className:"adm-pinned-content",children:[(0,a.jsx)("div",{className:"adm-pinned-content-overflow adm-scrollbar-track",children:(0,a.jsx)(u.u,{loading:R,tip:"Loading...",children:(0,a.jsx)(C,{className:"adm-pinned-item-chart",width:"100%",height:"100%",spec:c.chartSchema,values:null==D?void 0:D.data,forceUpdate:b,autoFilter:!0,hideActions:!0,hideTitle:!0,hideLegend:h})})}),N&&(0,a.jsxs)("div",{className:"adm-pinned-content-info",children:["Last refreshed: ",(0,g.Eq)(N)]})]})]})});var _=t(41664),A=t.n(_),F=t(25675),P=t.n(F),U=t(55041),Z=t(40582),H=t(87021);let q=(0,t(77840).x)(e=>(0,a.jsx)(U.default,{children:(0,a.jsxs)("div",{className:"p-3 rounded bg-gray-1 border border-gray-5",style:{boxShadow:"2px 2px 2px 0px #00000006"},children:[(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsx)("span",{className:"d-inline-block bg-geekblue-1 geekblue-6 rounded-pill text-sm px-2",style:{lineHeight:"22px"},children:e.title})}),(0,a.jsx)(P(),{className:"rounded border border-gray-4",src:e.image,width:160,height:80,alt:e.title})]})}));var M=e=>{let{show:n,children:t}=e;return n?(0,a.jsxs)("div",{className:"d-flex align-center justify-center flex-column -mt-8",style:{height:"100%"},children:[(0,a.jsx)(H.T,{size:48,color:"var(--gray-8)"}),(0,a.jsx)("div",{className:"text-lg text-medium text-center gray-8 mt-3",children:"No charts have been added yet"}),(0,a.jsxs)("div",{className:"gray-7",children:["Follow these steps to pin charts to your dashboard."," ",(0,a.jsx)(A(),{className:"gray-8 underline",href:"https://docs.getwren.ai/oss/guide/home/<USER>",rel:"noopener noreferrer",target:"_blank",children:"Learn more"})]}),(0,a.jsx)(Z.default,{className:"mt-4",gutter:[16,16],children:(0,a.jsx)(q,{data:[{title:"1. Create a thread",image:"/images/dashboard/s1.jpg"},{title:"2. Generate a chart",image:"/images/dashboard/s2.jpg"},{title:"3. Pin to dashboard",image:"/images/dashboard/s3.jpg"}]})})]}):(0,a.jsx)(a.Fragment,{children:t})},Y=t(29060),W=t(48403),O=t.n(W),V=t(60652),G=t(30381),Q=t.n(G),$=t(18337),z=t(4922),K=t(19662),B=t(72786),X=t(66590),J=t(98885),ee=t(84859),en=t(59530),et=t(32235),ea=t(46140);let er="HH:mm",el={DAILY:"DAILY",WEEKLY:"WEEKLY",CUSTOM:"CUSTOM",NEVER:"NEVER"},ed=[ea.L6.SUN,ea.L6.MON,ea.L6.TUE,ea.L6.WED,ea.L6.THU,ea.L6.FRI,ea.L6.SAT],es=e=>({[ea.L6.MON]:"Monday",[ea.L6.TUE]:"Tuesday",[ea.L6.WED]:"Wednesday",[ea.L6.THU]:"Thursday",[ea.L6.FRI]:"Friday",[ea.L6.SAT]:"Saturday",[ea.L6.SUN]:"Sunday"})[e]||"",ei=e=>e===el.NEVER?"Manual refresh only":O()(e),eo=e=>{let n={};switch(e){case el.DAILY:n={day:null,time:Q()("00:00",er),cron:null};break;case el.WEEKLY:n={day:ed[0],time:Q()("00:00",er),cron:null};break;case el.CUSTOM:n={day:null,time:null,cron:"0 0 * * *"};break;case el.NEVER:n={day:null,time:null,cron:null}}return n},ec=e=>{if(!e)return"";let{frequency:n}=e,t=e=>Q()("".concat(e.hour,":").concat(e.minute),er).format(er);switch(n){case el.DAILY:{let n=t(e);return"Cache refreshes daily at ".concat(n)}case el.WEEKLY:{let n=t(e);return"Cache refreshes every ".concat(es(e.day)," at ").concat(n)}case el.CUSTOM:return"Cache refreshes on custom schedule";case el.NEVER:return"Cache auto-refresh disabled";default:return""}},eu=e=>{let{frequency:n,day:t,time:a,cron:r}=e;if(n===el.CUSTOM)return eh(r);if(n===el.NEVER||!a)return null;let l=Q()(),d=Q()("".concat(l.format("YYYY-MM-DD")," ").concat(a.format(er)));if(t){let e=ed.findIndex(e=>e===t);d.set({day:e})}return l.isAfter(d)&&(n===el.DAILY?d.add(1,"d"):n===el.WEEKLY&&d.add(7,"d")),d.isValid()?d.format("YYYY-MM-DD HH:mm"):null},eh=e=>{if(!e||!(0,et.gY)(e))return null;try{let n=V.CronExpressionParser.parse(e,{tz:"UTC"}),t=Q().utc(n.next().toDate()).local();return t.isValid()?t.format("YYYY-MM-DD HH:mm"):null}catch(e){return console.warn(e),null}};function em(e){let{visible:n,defaultValue:t,loading:l,onClose:d,onSubmit:s}=e,[i]=f.Z.useForm(),o=f.Z.useWatch("cacheEnabled",i);return(0,r.useEffect)(()=>{if(n){let{schedule:e,...n}=t||{};i.setFieldsValue({...n,schedule:{day:null==e?void 0:e.day,frequency:null==e?void 0:e.frequency,time:(null==e?void 0:e.hour.toString())&&(null==e?void 0:e.minute.toString())?Q()("".concat(null==e?void 0:e.hour,":").concat(null==e?void 0:e.minute),er):null,cron:null==e?void 0:e.cron}})}},[n,t]),(0,a.jsx)($.Z,{visible:n,title:"Cache settings",width:410,closable:!0,destroyOnClose:!0,maskClosable:!1,afterVisibleChange:e=>{e||i.resetFields()},onClose:d,footer:(0,a.jsxs)(z.default,{className:"d-flex justify-end",children:[(0,a.jsx)(p.default,{onClick:d,disabled:l,children:"Cancel"}),(0,a.jsx)(p.default,{type:"primary",onClick:()=>{i.validateFields().then(async e=>{var n,t;let{schedule:a}=e;await s({...e,schedule:e.cacheEnabled?{frequency:null==a?void 0:a.frequency,day:null==a?void 0:a.day,hour:null==a?void 0:null===(n=a.time)||void 0===n?void 0:n.hour(),minute:null==a?void 0:null===(t=a.time)||void 0===t?void 0:t.minute(),cron:null==a?void 0:a.cron,timezone:g.tq}:null}),d()}).catch(console.error)},loading:l,disabled:l,children:"Submit"})]}),children:(0,a.jsxs)(f.Z,{form:i,layout:"vertical",children:[(0,a.jsx)(f.Z.Item,{label:"Enable caching",name:"cacheEnabled",valuePropName:"checked",extra:"Enable caching to speed up dashboard loading by reusing recent results. Choose a refresh schedule that fits your needs below.",children:(0,a.jsx)(K.Z,{})}),o&&(0,a.jsx)(ef,{})]})})}function ef(){let e=f.Z.useFormInstance(),n=f.Z.useWatch(["schedule","frequency"],e),t=f.Z.useWatch(["schedule","day"],e),r=eu({frequency:n,day:t,time:f.Z.useWatch(["schedule","time"],e),cron:f.Z.useWatch(["schedule","cron"],e)});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(B.Z,{className:"gray-6 text-sm",children:"Refresh settings"}),(0,a.jsx)(f.Z.Item,{label:"Frequency",name:["schedule","frequency"],children:(0,a.jsx)(X.default,{placeholder:"Select frequency",options:Object.keys(el).map(e=>({label:ei(e),value:el[e]})),onChange:n=>{e.setFieldsValue({schedule:{frequency:n,...eo(n)}})}})}),n===el.DAILY&&(0,a.jsx)(ep,{}),n===el.WEEKLY&&(0,a.jsx)(ex,{}),n===el.CUSTOM&&(0,a.jsx)(f.Z.Item,{label:"Cron expression",name:["schedule","cron"],required:!1,rules:[{validator:et.x7}],extra:"Cron expression will be executed in UTC timezone (e.g. '0 0 * * *' for daily at midnight UTC)",children:(0,a.jsx)(J.default,{style:{maxWidth:200},placeholder:"* * * * *"})}),r&&(0,a.jsxs)("div",{className:"gray-7",children:["Next scheduled refresh:",(0,a.jsxs)("div",{className:"gray-8",children:[r," ",g.tq&&(0,a.jsxs)("span",{children:["(",g.tq,")"]})]})]})]})}function ep(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(f.Z.Item,{label:"Time",name:["schedule","time"],required:!1,rules:[{required:!0,message:en.q.CACHE_SETTINGS.TIME.REQUIRED}],children:(0,a.jsx)(ee.Z,{minuteStep:10,format:er})})})}function ex(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(Z.default,{gutter:16,children:[(0,a.jsx)(U.default,{children:(0,a.jsx)(f.Z.Item,{label:"Day",name:["schedule","day"],required:!1,rules:[{required:!0,message:en.q.CACHE_SETTINGS.DAY.REQUIRED}],children:(0,a.jsx)(X.default,{style:{minWidth:123},options:ed.map(e=>({label:es(e),value:e})),placeholder:"Select day"})})}),(0,a.jsx)(U.default,{children:(0,a.jsx)(f.Z.Item,{label:"Time",name:["schedule","time"],required:!1,rules:[{required:!0,message:en.q.CACHE_SETTINGS.TIME.REQUIRED}],children:(0,a.jsx)(ee.Z,{minuteStep:10,format:er})})})]})})}let eb=x.ZP.div.withConfig({displayName:"DashboardHeader__StyledHeader",componentId:"sc-25f54310-0"})(["display:flex;align-items:center;justify-content:space-between;min-height:49px;padding:8px 16px;background-color:white;border-bottom:1px solid var(--gray-4);"]);function ey(e){let{isSupportCached:n,nextScheduleTime:t,schedule:r,onCacheSettings:l,onRefreshAll:s}=e,i=ec(r),o=async e=>{e===d.dI.CACHE_SETTINGS?null==l||l():e===d.dI.REFRESH&&(null==s||s())};return(0,a.jsxs)(eb,{children:[(0,a.jsx)("div",{}),(0,a.jsx)("div",{children:r&&(0,a.jsxs)("div",{className:"d-flex align-center gray-6 gx-2",children:[n&&(0,a.jsx)(a.Fragment,{children:t?(0,a.jsx)(Y.default,{placement:"bottom",title:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"gray-6",children:"Next schedule:"})," ",(0,g.Eq)(t)]}),r.cron&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"gray-6",children:"Cron expression:"})," ",r.cron]})]}),children:(0,a.jsx)("span",{className:"cursor-pointer",children:i})}):i}),(0,a.jsx)(v.px,{onMoreClick:o,isSupportCached:n,children:(0,a.jsx)(p.default,{type:"text",icon:(0,a.jsx)(E.nX,{className:"gray-8"})})})]})})]})}var eE=t(10102);let eg=e=>!(null==e?void 0:e.sampleDataset)&&(null==e?void 0:e.type)!==ea.ri.DUCKDB;function ev(){var e,n,t;let h=(0,s.useRouter)(),m=(0,r.useRef)(null),f=(0,o.Z)(),p=(0,c.Z)(),{data:x}=(0,eE.ks)(),b=null==x?void 0:x.settings,y=(0,r.useMemo)(()=>eg(null==b?void 0:b.dataSource),[null==b?void 0:b.dataSource]),{data:E,loading:g,updateQuery:v}=(0,I.yH)({fetchPolicy:"cache-and-network",onError:()=>{l.default.error("Failed to fetch dashboard items."),h.push(d.y$.Home)}}),j=(0,r.useMemo)(()=>{var e;return(null==E?void 0:null===(e=E.dashboard)||void 0===e?void 0:e.items)||[]},[null==E?void 0:null===(e=E.dashboard)||void 0===e?void 0:e.items]),[C]=(0,I.bI)({refetchQueries:["Dashboard"],onCompleted:()=>{l.default.success("Successfully updated dashboard schedule.")},onError:e=>console.error(e)}),[S]=(0,I.w5)({onError:()=>{l.default.error("Failed to update dashboard item layouts.")}}),[D]=(0,I._R)({onCompleted:(e,n)=>{l.default.success("Successfully deleted dashboard item."),N(n.variables.where.id)}}),N=e=>{v(n=>{var t,a;return{...n,dashboard:{...n.dashboard,items:(null==n?void 0:null===(a=n.dashboard)||void 0===a?void 0:null===(t=a.items)||void 0===t?void 0:t.filter(n=>n.id!==e))||[]}}})},w=async e=>{e&&e.length>0&&await S({variables:{data:{layouts:e}}})},T=async e=>{await D({variables:{where:{id:e}}})};return(0,a.jsx)(i.Z,{loading:!1,color:"gray-3",sidebar:f,children:(0,a.jsx)(u.u,{loading:g,children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(M,{show:0===j.length,children:[(0,a.jsx)(ey,{isSupportCached:y,schedule:null==E?void 0:null===(n=E.dashboard)||void 0===n?void 0:n.schedule,nextScheduleTime:null==E?void 0:null===(t=E.dashboard)||void 0===t?void 0:t.nextScheduledAt,onCacheSettings:()=>{var e,n;p.openDrawer({cacheEnabled:null==E?void 0:null===(e=E.dashboard)||void 0===e?void 0:e.cacheEnabled,schedule:null==E?void 0:null===(n=E.dashboard)||void 0===n?void 0:n.schedule})},onRefreshAll:()=>{var e;null==m||null===(e=m.current)||void 0===e||e.onRefreshAll()}}),(0,a.jsx)(k,{ref:m,items:j,isSupportCached:y,onUpdateChange:w,onDelete:T})]}),y&&(0,a.jsx)(em,{...p.state,onClose:p.closeDrawer,onSubmit:async e=>{await C({variables:{data:e}})}})]})})})}},40348:function(){},91154:function(){}},function(e){e.O(0,[774,885,225,530,90,543,337,74,858,848,623,888,179],function(){return e(e.s=51706)}),_N_E=e.O()}]);