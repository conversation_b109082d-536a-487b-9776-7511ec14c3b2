(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[135],{77213:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/setup/models",function(){return t(77287)}])},29500:function(e,n,t){"use strict";t.d(n,{Z:function(){return c}});var i=t(85893),s=t(19521),a=t(24329),o=t(37177);let r=s.ZP.div.withConfig({displayName:"ContainerCard__Container",componentId:"sc-bea19298-0"})(["max-width:","px;margin:68px auto;"],e=>e.maxWidth||1200);function c(e){let{step:n,maxWidth:t}=e;return(0,i.jsx)(r,{maxWidth:t,children:(0,i.jsxs)(a.<PERSON>,{children:[(0,i.jsxs)(o.Z,{current:n,className:"mb-12",children:[(0,i.jsx)(o.Z.Step,{title:"Connect"}),(0,i.jsx)(o.Z.Step,{title:"Select Tables"}),(0,i.jsx)(o.Z.Step,{title:"Define Relationships"})]}),(0,i.jsx)("div",{className:"px-12 pb-6",children:e.children})]})})}},77287:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return d}});var i=t(85893),s=t(67294),a=t(59709),o=t(29500),r=t(84908),c=t(11163),l=t(34217),u=t(25063);function d(){let{fetching:e,stepKey:n,tables:t,onNext:d,onBack:h,submitting:p}=function(){let[e]=(0,s.useState)(r.ye.SELECT_MODELS),n=(0,c.useRouter)(),{data:t,loading:i}=(0,l.Cu)({fetchPolicy:"no-cache",onError:e=>console.error(e)}),[a,{loading:o}]=(0,l.kI)(),u=async e=>{await a({variables:{data:{tables:e}}}),n.push(r.y$.OnboardingRelationships)};return{submitting:o,fetching:i,stepKey:e,onBack:()=>{n.push(r.y$.OnboardingConnection)},onNext:e=>{u(e.selectedTables)},tables:(null==t?void 0:t.listDataSourceTables)||[]}}(),x=(0,s.useMemo)(()=>u.B5[n],[n]);return(0,i.jsx)(a.Z,{children:(0,i.jsx)(o.Z,{step:x.step,maxWidth:x.maxWidth,children:(0,i.jsx)(x.component,{fetching:e,onBack:h,onNext:d,submitting:p,tables:t})})})}}},function(e){e.O(0,[530,90,342,117,858,888,774,179],function(){return e(e.s=77213)}),_N_E=e.O()}]);