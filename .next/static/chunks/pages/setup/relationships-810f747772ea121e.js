(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[692],{7022:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/setup/relationships",function(){return t(12460)}])},29500:function(e,n,t){"use strict";t.d(n,{Z:function(){return m}});var o=t(85893),i=t(19521),r=t(24329),d=t(37177);let l=i.ZP.div.withConfig({displayName:"ContainerCard__Container",componentId:"sc-bea19298-0"})(["max-width:","px;margin:68px auto;"],e=>e.maxWidth||1200);function m(e){let{step:n,maxWidth:t}=e;return(0,o.jsx)(l,{maxWidth:t,children:(0,o.jsxs)(r.Z,{children:[(0,o.jsxs)(d.Z,{current:n,className:"mb-12",children:[(0,o.jsx)(d.<PERSON>.Step,{title:"Connect"}),(0,o.jsx)(d.Z.Step,{title:"Select Tables"}),(0,o.jsx)(d.Z.Step,{title:"Define Relationships"})]}),(0,o.jsx)("div",{className:"px-12 pb-6",children:e.children})]})})}},12460:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return c}});var o=t(85893),i=t(67294),r=t(59709),d=t(29500),l=t(84908),m=t(11163),a=t(34217),u=t(67783),s=t(25063);function c(){let{fetching:e,stepKey:n,recommendRelationsResult:t,onNext:c,onBack:f,onSkip:p,submitting:h}=function(){let[e]=(0,i.useState)(l.ye.DEFINE_RELATIONS),n=(0,m.useRouter)(),{refetch:t}=(0,u.Z)(),{data:o,loading:r}=(0,a.vD)({fetchPolicy:"no-cache"}),d=null==o?void 0:o.autoGenerateRelation,s=()=>{n.push(l.y$.Modeling),t()},[c,{loading:f}]=(0,a.Jg)({onError:e=>console.error(e),onCompleted:s}),p=async e=>{let n=Object.entries(e).reduce((e,n)=>{let[t,o]=n;return e=[...e,...o.map(e=>({fromModelId:Number(e.fromField.modelId),fromColumnId:Number(e.fromField.fieldId),toModelId:Number(e.toField.modelId),toColumnId:Number(e.toField.fieldId),type:e.type}))]},[]);if(0===n.length){s();return}await c({variables:{data:{relations:n}}})};return{fetching:r,submitting:f,stepKey:e,recommendRelationsResult:(0,i.useMemo)(()=>(d||[]).reduce((e,n)=>{let{displayName:t,referenceName:o,relations:i}=n,r=i.map(e=>({name:e.name,fromField:{modelId:String(e.fromModelId),modelName:e.fromModelReferenceName,fieldId:String(e.fromColumnId),fieldName:e.fromColumnReferenceName},toField:{modelId:String(e.toModelId),modelName:e.toModelReferenceName,fieldId:String(e.toColumnId),fieldName:e.toColumnReferenceName},type:e.type,isAutoGenerated:!0}));return e.recommendRelations[o]=r,e.recommendNameMapping[o]=t,e},{recommendRelations:{},recommendNameMapping:{}}),[d]),onBack:()=>{n.push("/setup/models")},onNext:e=>{p(e.relations)},onSkip:s}}(),N=(0,i.useMemo)(()=>s.B5[n],[n]);return(0,o.jsx)(r.Z,{children:(0,o.jsx)(d.Z,{step:N.step,maxWidth:N.maxWidth,children:(0,o.jsx)(N.component,{fetching:e,...t,onNext:c,onBack:f,onSkip:p,submitting:h})})})}}},function(e){e.O(0,[530,90,342,117,858,888,774,179],function(){return e(e.s=7022)}),_N_E=e.O()}]);