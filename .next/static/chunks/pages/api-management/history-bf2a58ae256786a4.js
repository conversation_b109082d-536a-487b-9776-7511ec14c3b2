(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{33708:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/api-management/history",function(){return a(64368)}])},72786:function(e,t,a){"use strict";var n=a(64836),r=a(18698);t.Z=void 0;var l=n(a(10434)),i=n(a(38416)),s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var a=c(void 0);if(a&&a.has(e))return a.get(e);var n={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=l?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,a&&a.set(e,n),n}(a(67294)),o=n(a(93967)),d=a(31407);function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(c=function(e){return e?a:t})(e)}var u=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a};t.Z=function(e){var t,a=s.useContext(d.ConfigContext),n=a.getPrefixCls,r=a.direction,c=e.prefixCls,p=e.type,f=e.orientation,h=void 0===f?"center":f,x=e.orientationMargin,m=e.className,y=e.children,g=e.dashed,j=e.plain,v=u(e,["prefixCls","type","orientation","orientationMargin","className","children","dashed","plain"]),b=n("divider",c),w=h.length>0?"-".concat(h):h,N=!!y,k="left"===h&&null!=x,C="right"===h&&null!=x,I=(0,o.default)(b,"".concat(b,"-").concat(void 0===p?"horizontal":p),(t={},(0,i.default)(t,"".concat(b,"-with-text"),N),(0,i.default)(t,"".concat(b,"-with-text").concat(w),N),(0,i.default)(t,"".concat(b,"-dashed"),!!g),(0,i.default)(t,"".concat(b,"-plain"),!!j),(0,i.default)(t,"".concat(b,"-rtl"),"rtl"===r),(0,i.default)(t,"".concat(b,"-no-default-orientation-margin-left"),k),(0,i.default)(t,"".concat(b,"-no-default-orientation-margin-right"),C),t),m),T=(0,l.default)((0,l.default)({},k&&{marginLeft:x}),C&&{marginRight:x});return s.createElement("div",(0,l.default)({className:I},v,{role:"separator"}),y&&s.createElement("span",{className:"".concat(b,"-inner-text"),style:T},y))}},20411:function(e,t,a){"use strict";a.d(t,{iD:function(){return m}});var n=a(85893),r=a(67294),l=a(19521),i=a(80002),s=a(21367),o=a(51903),d=a.n(o),c=a(72869),u=a.n(c),p=a(33190);a(84882);let f=l.ZP.div.withConfig({displayName:"BaseCodeBlock__Block",componentId:"sc-b28653c8-0"})(["position:relative;white-space:pre;font-size:13px;border:1px var(--gray-4) solid;border-radius:4px;font-family:'Source Code Pro',monospace;user-select:text;cursor:text;&:focus{outline:none;}"," .adm-code-wrap{"," "," user-select:text;}.adm-code-line{display:block;user-select:text;&-number{user-select:none;display:inline-block;min-width:14px;text-align:right;margin-right:1em;color:var(--gray-6);font-weight:700;font-size:12px;}}"],e=>e.inline?"\n      display: inline;\n      border: none;\n      background: transparent !important;\n      padding: 0;\n      * { display: inline !important; }\n    ":"\n    background: ".concat(e.backgroundColor||"var(--gray-1)"," !important;\n    padding: 8px;\n  "),e=>e.inline?"":"overflow: auto;",e=>e.maxHeight?"max-height: ".concat(e.maxHeight,"px;"):""),h=(0,l.ZP)(i.default.Text).withConfig({displayName:"BaseCodeBlock__CopyText",componentId:"sc-b28653c8-1"})(["position:absolute;top:0;right:",";font-size:0;button{background:var(--gray-1) !important;}.ant-typography-copy{font-size:12px;}.ant-btn:not(:hover){color:var(--gray-8);}"],e=>e.$hasVScrollbar?"20px":"0"),x=e=>{let t="ace-tomorrow";if(!document.getElementById(t)){let a=document.createElement("style");a.id=t,document.head.appendChild(a),a.appendChild(document.createTextNode(e))}},m=e=>function(t){let{code:a,copyable:l,maxHeight:i,inline:o,loading:c,showLineNumbers:m,backgroundColor:y,onCopy:g}=t,{ace:j}=window,{Tokenizer:v}=j.require("ace/tokenizer"),b=new v(new e().getRules()),w=(0,r.useRef)(null),[N,k]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let{cssText:e}=j.require("ace/theme/tomorrow");x(e)},[]),(0,r.useEffect)(()=>{let e=w.current;e&&k(e.scrollHeight>e.clientHeight)},[a]);let C=(a||"").split("\n").map((e,t)=>{let a=b.getLineTokens(e).tokens.map((e,t)=>{let a=e.type.split(".").map(e=>"ace_".concat(e));return(0,n.jsx)("span",{className:a.join(" "),children:e.value},t)});return(0,n.jsxs)("span",{className:"adm-code-line ace_line",children:[m&&(0,n.jsx)("span",{className:"adm-code-line-number",children:t+1}),a]},"".concat(e,"-").concat(t))});return(0,n.jsx)(f,{className:"ace_editor ace-tomorrow adm_code-block",maxHeight:i,inline:o,backgroundColor:y,tabIndex:0,onKeyDown:e=>{if((e.metaKey||e.ctrlKey)&&"a"===e.key){e.preventDefault();let t=window.getSelection(),a=document.createRange();a.selectNodeContents(e.currentTarget.querySelector(".adm-code-wrap")||e.currentTarget),null==t||t.removeAllRanges(),null==t||t.addRange(a)}},children:(0,n.jsx)(p.gb,{spinning:c,children:(0,n.jsxs)("div",{className:"adm-code-wrap",ref:w,children:[C,l&&(0,n.jsx)(h,{$hasVScrollbar:N,copyable:{onCopy:g,icon:[(0,n.jsx)(s.default,{icon:(0,n.jsx)(u(),{}),size:"small",style:{backgroundColor:"transparent"}},"copy-icon"),(0,n.jsx)(s.default,{icon:(0,n.jsx)(d(),{className:"green-6"}),size:"small"},"copied-icon")],text:a},children:a})]})})})}},63284:function(e,t,a){"use strict";a.r(t);var n=a(85893),r=a(20411);t.default=e=>{let{ace:t}=window,{SqlHighlightRules:a}=t.require("ace/mode/sql_highlight_rules"),l=(0,r.iD)(a);return(0,n.jsx)(l,{...e})}},84882:function(e,t,a){"use strict";var n=a(74981);a(89899),a(90252),a(42557),a(82679),t.Z=n.ZP},89764:function(e,t,a){"use strict";a.d(t,{Z:function(){return l}});var n=a(85893),r=a(80002);function l(e){let{title:t,titleExtra:a,description:l,children:i}=e;return(0,n.jsxs)("div",{className:"px-6 py-4",children:[(0,n.jsxs)("div",{className:"d-flex align-center justify-space-between mb-3",children:[(0,n.jsx)(r.default.Title,{level:4,className:"text-medium gray-8 mb-0",children:t}),a]}),l&&(0,n.jsx)(r.default.Text,{className:"gray-7",children:l}),(0,n.jsx)("div",{className:"mt-3",children:i})]})}},48196:function(e,t,a){"use strict";a.d(t,{Z:function(){return l}});var n=a(67294),r=a(84908);function l(){let[e,t]=(0,n.useState)(!1),[a,l]=(0,n.useState)(r.SD.CREATE),[i,s]=(0,n.useState)(null);return{state:{visible:e,formMode:a,defaultValue:i},openDrawer:e=>{e&&s(e),e&&l(r.SD.EDIT),t(!0)},closeDrawer:()=>{t(!1),s(null),l(r.SD.CREATE)},updateState:e=>{s(e)}}}},64368:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return $}});var n=a(85893),r=a(41664),l=a.n(r),i=a(67294),s=a(51618),o=a(80002),d=a(21367),c=a(17579),u=a(36238),p=a(48196);a(30381);var f=a(4922),h=a(98885),x=a(72786),m=a(78661),y=a.n(m);let g=e=>({filterDropdown:t=>(0,n.jsx)(j,{...t,...e}),filterIcon:e=>(0,n.jsx)(y(),{style:{color:e?"var(--geekblue-6)":void 0}}),filteredValue:e.filteredValue}),j=e=>{let{setSelectedKeys:t,selectedKeys:a,confirm:r,clearFilters:l,visible:s,dataIndex:o,placeholder:c,filteredValue:u}=e;return(0,i.useEffect)(()=>{s||0!==a.length||r()},[s]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.default,{className:"p-2",children:(0,n.jsx)(h.default,{size:"small",placeholder:"Search ".concat(c||o),value:a[0],onChange:e=>t(e.target.value?[e.target.value]:[]),onPressEnter:()=>r(),style:{width:188}})}),(0,n.jsx)(x.Z,{style:{margin:0}}),(0,n.jsxs)(f.default,{className:"d-flex justify-end p-2",children:[(0,n.jsx)(d.default,{type:"link",onClick:()=>l(),size:"small",disabled:!u,children:"Reset"}),(0,n.jsx)(d.default,{type:"primary",onClick:()=>r(),size:"small",children:"Search"})]})]})};var v=a(30848),b=a(89764),w=a(85571),N=a.n(w),k=a(92870),C=a.n(k),I=a(17625),T=a.n(I),S=a(18142),P=a.n(S),_=a(63284),O=a(18337),D=a(40582),E=a(55041),H=a(20411),q=e=>{let t;let{code:a,...r}=e,{ace:l}=window;try{t="string"==typeof a?a:JSON.stringify(a,null,2)}catch(e){console.warn("Failed to format JSON",a),t=a}let{JsonHighlightRules:i}=l.require("ace/mode/json_highlight_rules"),s=(0,H.iD)(i);return(0,n.jsx)(s,{code:t,...r})};function A(e){let{visible:t,onClose:a,defaultValue:r}=e,{threadId:l,apiType:i,createdAt:d,durationMs:c,statusCode:p,headers:f,requestPayload:h,responsePayload:x}=r||{};return(0,n.jsxs)(O.Z,{visible:t,className:"gray-8",title:"API details",width:760,closable:!0,destroyOnClose:!0,onClose:a,footer:null,children:[(0,n.jsxs)(D.default,{className:"mb-6",children:[(0,n.jsxs)(E.default,{span:12,children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"API type"}),(0,n.jsx)("div",{children:(0,n.jsx)(s.default,{className:"gray-8",children:null==i?void 0:i.toLowerCase()})})]}),(0,n.jsxs)(E.default,{span:12,children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Thread ID"}),(0,n.jsx)("div",{children:l||"-"})]})]}),(0,n.jsxs)(D.default,{className:"mb-6",children:[(0,n.jsxs)(E.default,{span:12,children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Created at"}),(0,n.jsx)("div",{children:(0,u.uy)(d)})]}),(0,n.jsxs)(E.default,{span:12,children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Duration"}),(0,n.jsxs)("div",{children:[c," ms"]})]})]}),(0,n.jsx)(D.default,{className:"mb-6",children:(0,n.jsxs)(E.default,{span:12,children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Status code"}),(0,n.jsx)("div",{children:(e=>{let t=e>=200&&e<300;return(0,n.jsx)(s.default,{icon:t?(0,n.jsx)(T(),{}):(0,n.jsx)(P(),{}),color:t?"success":"error",children:e})})(p)})]})}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Headers"}),(0,n.jsx)(q,{code:f,backgroundColor:"var(--gray-2)",maxHeight:"400",copyable:!0})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Request payload"}),(0,n.jsx)(q,{code:h,backgroundColor:"var(--gray-2)",maxHeight:"400",copyable:!0})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(o.default.Text,{className:"d-block gray-7 mb-2",children:"Response payload"}),(0,n.jsx)(q,{code:x,backgroundColor:"var(--gray-2)",maxHeight:"400",copyable:!0})]})]})}var z=a(82729),Z=a(68806),M=a(6812);function R(){let e=(0,z._)(["\n    query ApiHistory($filter: ApiHistoryFilterInput, $pagination: ApiHistoryPaginationInput!) {\n  apiHistory(filter: $filter, pagination: $pagination) {\n    items {\n      id\n      projectId\n      apiType\n      threadId\n      headers\n      requestPayload\n      responsePayload\n      statusCode\n      durationMs\n      createdAt\n      updatedAt\n    }\n    total\n    hasMore\n  }\n}\n    "]);return R=function(){return e},e}let L={},V=(0,Z.Ps)(R());var B=a(46140);function $(){var e,t,a;let r=(0,p.Z)(),[f,h]=(0,i.useState)(1),[x,m]=(0,i.useState)({}),{data:y,loading:j}=function(e){let t={...L,...e};return M.aM(V,t)}({fetchPolicy:"cache-and-network",variables:{pagination:{offset:(f-1)*10,limit:10},filter:{apiType:null===(e=x.apiType)||void 0===e?void 0:e[0],statusCode:null===(t=x.statusCode)||void 0===t?void 0:t[0],threadId:null===(a=x.threadId)||void 0===a?void 0:a[0]}},onError:e=>console.error(e)}),w=[{title:"Timestamp",dataIndex:"createdAt",key:"createdAt",width:180,render:e=>(0,n.jsx)("div",{className:"gray-7",children:(0,u.uy)(e)})},{title:"API type",dataIndex:"apiType",key:"apiType",width:180,render:e=>(0,n.jsx)(s.default,{className:"gray-8",children:e.toLowerCase()}),filters:Object.keys(B.Ii).map(e=>({text:e.toLowerCase(),value:e})),filteredValue:x.apiType,filterMultiple:!1},{title:"Status",dataIndex:"statusCode",key:"statusCode",width:100,render:e=>{let t=200===e?(0,n.jsx)(T(),{}):(0,n.jsx)(P(),{});return(0,n.jsx)(s.default,{icon:t,color:200===e?"success":"error",children:e})},filters:[{text:"Successful (code: 2xx)",value:200},{text:"Client error (code: 4xx)",value:400},{text:"Server error (code: 5xx)",value:500}],filteredValue:x.statusCode,filterMultiple:!1},{title:"Question / SQL",dataIndex:"requestPayload",key:"requestPayload",render:(e,t)=>t.apiType===B.Ii.RUN_SQL&&e.sql?(0,n.jsx)("div",{style:{width:"100%"},children:(0,n.jsx)(_.default,{code:e.sql,maxHeight:"130"})}):(0,n.jsx)("div",{className:"gray-8",children:e.question||e.sql||"-"})},{title:"Thread ID",dataIndex:"threadId",key:"threadId",width:200,render:e=>e?(0,n.jsx)(o.default.Text,{ellipsis:!0,className:"gray-7",copyable:{text:e},children:e}):(0,n.jsx)("div",{className:"gray-7",children:"-"}),...g({dataIndex:"threadId",placeholder:"thread ID",filteredValue:x.threadId})},{title:"Duration (ms)",dataIndex:"durationMs",key:"durationMs",width:124,render:e=>(0,n.jsx)("div",{className:"gray-7 text-right",children:e||"-"})},{title:"Actions",key:"actions",width:110,align:"center",fixed:"right",render:e=>(0,n.jsxs)(d.default,{className:"gray-8",type:"text",size:"small",onClick:()=>r.openDrawer(e),children:[(0,n.jsx)(C(),{})," Details"]})}];return(0,n.jsx)(v.Z,{loading:!1,sidebar:null,children:(0,n.jsxs)(b.Z,{title:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N(),{className:"mr-2 gray-8"}),"API history"]}),description:(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{children:["Here you can view the full history of API calls, including request inputs, responses, and execution details."," ",(0,n.jsx)(l(),{className:"gray-8 underline mr-2",href:"https://docs.getwren.ai/oss/guide/api-access/history",target:"_blank",rel:"noopener noreferrer",children:"Learn more."})]})}),children:[(0,n.jsx)(c.Z,{className:"ant-table-has-header",dataSource:(null==y?void 0:y.apiHistory.items)||[],loading:j,columns:w,rowKey:"id",pagination:{hideOnSinglePage:!0,pageSize:10,size:"small",total:null==y?void 0:y.apiHistory.total},scroll:{x:1200},onChange:(e,t,a)=>{h(e.current),m(t)}}),(0,n.jsx)(A,{...r.state,onClose:r.closeDrawer})]})})}}},function(e){e.O(0,[774,281,885,530,90,543,337,510,858,848,888,179],function(){return e(e.s=33708)}),_N_E=e.O()}]);