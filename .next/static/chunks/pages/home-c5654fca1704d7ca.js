(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[229],{50720:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M840 836H184c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h656c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm0-724H184c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h656c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zM610.8 378c6 0 9.4-7 5.7-11.7L515.7 238.7a7.14 7.14 0 00-11.3 0L403.6 366.3a7.23 7.23 0 005.7 11.7H476v268h-62.8c-6 0-9.4 7-5.7 11.7l100.8 127.5c2.9 3.7 8.5 3.7 11.3 0l100.8-127.5c3.7-4.7.4-11.7-5.7-11.7H548V378h62.8z"}}]},name:"column-height",theme:"outlined"}},9992:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"}},94654:function(e,t,r){var n=r(21078),a=r(35161);e.exports=function(e,t){return n(a(e,t),1)}},7739:function(e,t,r){var n=r(89465),a=r(55189),o=Object.prototype.hasOwnProperty,s=a(function(e,t,r){o.call(e,r)?e[r].push(t):n(e,r,[t])});e.exports=s},35161:function(e,t,r){var n=r(29932),a=r(67206),o=r(69199),s=r(1469);e.exports=function(e,t){return(s(e)?n:o)(e,a(t,3))}},75472:function(e,t,r){var n=r(82689),a=r(1469);e.exports=function(e,t,r,o){return null==e?[]:(a(t)||(t=null==t?[]:[t]),a(r=o?void 0:r)||(r=null==r?[]:[r]),n(e,t,r))}},77183:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/home",function(){return r(59274)}])},7958:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=(n=r(25666))&&n.__esModule?n:{default:n};t.default=a,e.exports=a},34227:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=(n=r(84275))&&n.__esModule?n:{default:n};t.default=a,e.exports=a},25666:function(e,t,r){"use strict";var n=r(64836),a=r(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(42122)),s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=c(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var l=o?Object.getOwnPropertyDescriptor(e,s):null;l&&(l.get||l.set)?Object.defineProperty(n,s,l):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(r(67294)),l=n(r(50720)),i=n(r(3247));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}var u=s.forwardRef(function(e,t){return s.createElement(i.default,(0,o.default)((0,o.default)({},e),{},{ref:t,icon:l.default}))});t.default=u},84275:function(e,t,r){"use strict";var n=r(64836),a=r(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(42122)),s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=c(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var l=o?Object.getOwnPropertyDescriptor(e,s):null;l&&(l.get||l.set)?Object.defineProperty(n,s,l):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(r(67294)),l=n(r(9992)),i=n(r(3247));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}var u=s.forwardRef(function(e,t){return s.createElement(i.default,(0,o.default)((0,o.default)({},e),{},{ref:t,icon:l.default}))});t.default=u},59976:function(e,t,r){"use strict";r.d(t,{Z:function(){return u}});var n=r(85893),a=r(67294),o=r(42187),s=r(19521),l=r(51228),i=r.n(l);let c=(0,s.ZP)(o.default).withConfig({displayName:"ErrorCollapse__StyledCollapse",componentId:"sc-96b0dd67-0"})([".ant-collapse-item{> .ant-collapse-header{user-select:none;color:var(--gray-7);padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;.ant-collapse-arrow{margin-right:8px;}}> .ant-collapse-content .ant-collapse-content-box{color:var(--gray-7);padding:4px 0 0 0;}}"]);function u(e){let{message:t,className:r,defaultActive:s}=e,[l,u]=(0,a.useState)(s?["1"]:[]);return(0,n.jsx)(c,{className:r,ghost:!0,activeKey:l,onChange:e=>u(e),expandIcon:e=>{let{isActive:t}=e;return(0,n.jsx)(i(),{rotate:t?90:0})},children:(0,n.jsx)(o.default.Panel,{header:"Show error messages",children:(0,n.jsx)("pre",{className:"text-sm mb-0 pl-5",style:{whiteSpace:"pre-wrap"},children:t})},"1")})}},59274:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Y}});var n=r(85893),a=r(67294),o=r(11163),s=r(80002),l=r(21367),i=r(87021),c=r(84908),u=r(30848),d=r(17778),f=r(55041),p=r(40582),m=r(19521),g=r(77840),v=r(5019);let x=m.ZP.div.withConfig({displayName:"DemoPrompt__DemoBlock",componentId:"sc-cb7244f4-0"})(["user-select:none;height:150px;&:hover{border-color:var(--geekblue-6) !important;transition:border-color ease 0.2s;}"]),h=(0,g.x)(e=>{let{label:t,question:r,onSelect:a}=e;return(0,n.jsx)(f.default,{span:8,children:(0,n.jsxs)(x,{className:"border border-gray-5 rounded px-3 pt-3 pb-4 cursor-pointer",onClick:()=>a({label:t,question:r}),children:[(0,n.jsx)("div",{className:"d-flex justify-space-between align-center text-sm mb-3",children:(0,n.jsx)("div",{className:"border border-gray-5 px-2 rounded-pill",children:t})}),(0,n.jsx)(v.Z,{multipleLine:4,text:r})]})})});function y(e){let{demo:t,onSelect:r}=e;return(0,n.jsxs)("div",{className:"gray-8",style:{width:580},children:[(0,n.jsx)("div",{className:"text-center mt-3 mb-2",children:"Try asking..."}),(0,n.jsx)(p.default,{gutter:16,children:(0,n.jsx)(h,{data:t,onSelect:r})})]})}var j=r(83777),b=r(55023),_=r(7739),w=r.n(_),N=r(75472),P=r.n(N),k=r(94654),M=r.n(k),O=r(37031),S=r(49397),C=r(7337),Z=r.n(C),q=r(32815),E=r(46140),R=r(77491);let T=e=>{let t=w()(e,"category");return P()(M()(t),e=>t[e.category].length,"desc")};var Q=r(90512),W=r(4922),z=r(7958),D=r.n(z),I=r(34227),L=r.n(I),H=r(63626),B=r.n(H);let K=m.ZP.div.withConfig({displayName:"RecommendedQuestionsPrompt__CategorySectionBlock",componentId:"sc-67d6c27-0"})(["background:var(--gray-1);border:1px solid var(--gray-4);border-radius:4px;padding:16px;"]),X=m.ZP.div.withConfig({displayName:"RecommendedQuestionsPrompt__QuestionBlock",componentId:"sc-67d6c27-1"})(["background:var(--gray-1);user-select:none;height:150px;transition:border-color ease 0.2s;&:hover:not(.is-disabled){border-color:var(--geekblue-6) !important;}&.is-active{border-color:var(--geekblue-6) !important;}&.is-disabled{opacity:0.8;}"]),G=(0,g.x)(e=>{let{category:t,sql:r,question:a,onSelect:o,loading:s,selectedQuestion:l}=e,i=l===a,c=s&&!i;return(0,n.jsx)(f.default,{span:8,children:(0,n.jsxs)(X,{className:(0,Q.Z)("border border-gray-5 rounded px-3 pt-3 pb-4",s?"cursor-wait":"cursor-pointer",{"is-active":i,"is-disabled cursor-not-allowed":c}),onClick:()=>{s||o({sql:r,question:a})},children:[(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center text-sm mb-3",children:[(0,n.jsx)("div",{className:"border border-gray-5 px-2 rounded-pill text-truncate",title:t,children:t}),i&&s&&(0,n.jsx)(B(),{className:"ml-1 gray-7"})]}),(0,n.jsx)(v.Z,{multipleLine:4,text:a})]})})});function $(e){let{onSelect:t,recommendedQuestions:r,loading:o}=e,[s,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)(""),f=(0,a.useMemo)(()=>r.slice(0,s?void 0:9),[r,s]),m=()=>c(e=>!e),g=r.length>9;return(0,n.jsxs)("div",{className:"bg-gray-2 px-10 py-6",children:[(0,n.jsxs)("div",{className:"d-flex align-center mb-3",children:[(0,n.jsx)(i.T,{size:24,color:"var(--gray-8)"}),(0,n.jsx)("div",{className:"text-md text-medium gray-8 mx-3",children:"Know more about your data."}),(0,n.jsx)("div",{className:"text-medium gray-7",children:"Try asking some of the following questions"})]}),(0,n.jsx)(W.default,{style:{width:680},className:"gray-8",direction:"vertical",size:[0,16],children:(0,n.jsxs)(K,{children:[(0,n.jsx)(p.default,{gutter:[16,16],className:"mt-3",children:(0,n.jsx)(G,{data:f,onSelect:e=>{t(e),d(e.question)},loading:o,selectedQuestion:u})}),g&&(0,n.jsx)("div",{className:"text-right",children:(0,n.jsx)(l.default,{onClick:()=>m(),className:"gray-6 mt-3",type:"text",size:"small",icon:s?(0,n.jsx)(L(),{}):(0,n.jsx)(D(),{}),children:s?"Collapse":"Expand all"})})]})})]})}var A=r(10102);let{Text:F}=s.default,V=e=>{let{children:t}=e;return(0,n.jsxs)("div",{className:"d-flex align-center justify-center flex-column",style:{height:"100%"},children:[(0,n.jsx)(i.T,{size:48,color:"var(--gray-8)"}),(0,n.jsx)("div",{className:"text-md text-medium gray-8 mt-3",children:"Know more about your data"}),t]})},J=e=>{let{sampleQuestions:t,onSelect:r}=e;return(0,n.jsx)(V,{children:(0,n.jsx)(y,{demo:t,onSelect:r})})};function U(e){let{onSelect:t,loading:r}=e,{buttonProps:o,generating:s,recommendedQuestions:i,showRetry:c,showRecommendedQuestionsPromptMode:u}=function(){let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)(!1),[s,l]=(0,a.useState)(!1),[i,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)([]),[f,p]=(0,R.MG)({pollInterval:2e3}),[m]=(0,R.vi)(),g=(0,a.useMemo)(()=>{var e;return(null===(e=p.data)||void 0===e?void 0:e.getProjectRecommendationQuestions)||null},[p.data]);(0,a.useEffect)(()=>{(async()=>{var e;let t=null===(e=(await f()).data)||void 0===e?void 0:e.getProjectRecommendationQuestions;(0,b.pd)(t.status)&&t.questions.length>0&&(d(T(t.questions)),c(!0))})()},[]),(0,a.useEffect)(()=>{(0,b.pd)(null==g?void 0:g.status)&&(p.stopPolling(),0===g.questions.length?(s&&t(!0),i&&g.status===E.Tj.FAILED&&O.default.error("We couldn't regenerate questions right now. Let's try again later.")):(l(!0),d(T(g.questions)),c(!0)),o(!1))},[g]);let v=async()=>{o(!0),l(!0);try{await m(),f()}catch(e){console.error(e)}},x=(0,a.useMemo)(()=>{let t={loading:r,onClick:v};return i&&s?{...t,icon:(0,n.jsx)(Z(),{}),children:"Regenerate"}:{...t,icon:e?(0,n.jsx)(Z(),{}):(0,n.jsx)(S.Z,{component:q.gq,className:"geekblue-6"}),children:r?"Generating questions":e?"Retry":"What could I ask?"}},[r,s,e,i]);return{recommendedQuestions:u,generating:r,showRetry:e,showRecommendedQuestionsPromptMode:i,buttonProps:x}}();return u?(0,n.jsxs)("div",{className:"d-flex align-center flex-column pt-10",style:{margin:"auto"},children:[(0,n.jsx)($,{recommendedQuestions:i,onSelect:t,loading:r}),(0,n.jsx)("div",{className:"py-12"})]}):(0,n.jsxs)(V,{children:[(0,n.jsx)(l.default,{className:"mt-6",...o}),s&&(0,n.jsx)(F,{className:"mt-3 text-sm gray-6",children:"Thinking of good questions for you... (about 1 minute)"}),!s&&c&&(0,n.jsxs)(F,{className:"mt-3 text-sm gray-6 text-center",children:["We couldn't think of questions right now.",(0,n.jsx)("br",{}),"Let's try again later."]})]})}function Y(){let e=(0,a.useRef)(null),t=(0,o.useRouter)(),r=(0,j.Z)(),s=(0,b.ZP)(),{data:l}=(0,R.$4)({fetchPolicy:"cache-and-network"}),[i,{loading:f}]=(0,R.tT)({onCompleted:()=>r.refetch()}),[p]=(0,R.Xy)({fetchPolicy:"cache-and-network"}),{data:m}=(0,A.ks)(),g=null==m?void 0:m.settings,v=(0,a.useMemo)(()=>{var e;return!!(null==g?void 0:null===(e=g.dataSource)||void 0===e?void 0:e.sampleDataset)},[g]),x=(0,a.useMemo)(()=>(null==l?void 0:l.suggestedQuestions.questions)||[],[l]),h=async t=>{let{question:r}=t;e.current.submit(r)},y=async e=>{try{s.onStopPolling();let r=(await i({variables:{data:e}})).data.createThread.id;await p({variables:{threadId:r}}),t.push(c.y$.Home+"/".concat(r))}catch(e){console.error(e)}};return(0,n.jsxs)(u.Z,{loading:!1,sidebar:r,children:[v&&(0,n.jsx)(J,{sampleQuestions:x,onSelect:h}),!v&&(0,n.jsx)(U,{onSelect:y,loading:f}),(0,n.jsx)(d.Z,{ref:e,...s,onCreateResponse:y})]})}}},function(e){e.O(0,[774,530,90,543,143,858,848,623,840,888,179],function(){return e(e.s=77183)}),_N_E=e.O()}]);