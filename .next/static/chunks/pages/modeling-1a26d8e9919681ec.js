(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{89825:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/modeling",function(){return a(78586)}])},93373:function(e,t,a){"use strict";a.d(t,{st:function(){return y}});var n=a(82729),l=a(68806),i=a(6812);function d(){let e=(0,n._)(["\n    fragment ViewField on DiagramViewField {\n  id\n  displayName\n  referenceName\n  type\n  nodeType\n  description\n}\n    "]);return d=function(){return e},e}function r(){let e=(0,n._)(["\n    fragment RelationField on DiagramModelRelationField {\n  id\n  relationId\n  type\n  nodeType\n  displayName\n  referenceName\n  fromModelId\n  fromModelName\n  fromModelDisplayName\n  fromColumnId\n  fromColumnName\n  fromColumnDisplayName\n  toModelId\n  toModelName\n  toModelDisplayName\n  toColumnId\n  toColumnName\n  toColumnDisplayName\n  description\n}\n    "]);return r=function(){return e},e}function s(){let e=(0,n._)(["\n    fragment NestedField on DiagramModelNestedField {\n  id\n  nestedColumnId\n  columnPath\n  type\n  displayName\n  referenceName\n  description\n}\n    "]);return s=function(){return e},e}function o(){let e=(0,n._)(["\n    fragment Field on DiagramModelField {\n  id\n  columnId\n  type\n  nodeType\n  displayName\n  referenceName\n  description\n  isPrimaryKey\n  expression\n  aggregation\n  lineage\n  nestedFields {\n    ...NestedField\n  }\n}\n    ",""]);return o=function(){return e},e}function c(){let e=(0,n._)(["\n    query Diagram {\n  diagram {\n    models {\n      id\n      modelId\n      nodeType\n      displayName\n      referenceName\n      sourceTableName\n      refSql\n      cached\n      refreshTime\n      description\n      fields {\n        ...Field\n      }\n      calculatedFields {\n        ...Field\n      }\n      relationFields {\n        ...RelationField\n      }\n    }\n    views {\n      id\n      viewId\n      nodeType\n      displayName\n      description\n      referenceName\n      statement\n      fields {\n        ...ViewField\n      }\n    }\n  }\n}\n    ","\n","\n",""]);return c=function(){return e},e}let u={},m=(0,l.Ps)(d()),p=(0,l.Ps)(r()),f=(0,l.Ps)(s()),h=(0,l.Ps)(o(),f),x=(0,l.Ps)(c(),h,p,m);function y(e){let t={...u,...e};return i.aM(x,t)}},94601:function(e,t,a){"use strict";a.d(t,{Ox:function(){return v},S1:function(){return f},SJ:function(){return x},fB:function(){return g}});var n=a(82729),l=a(68806),i=a(50319);function d(){let e=(0,n._)(["\n    mutation CreateView($data: CreateViewInput!) {\n  createView(data: $data) {\n    id\n    name\n    statement\n  }\n}\n    "]);return d=function(){return e},e}function r(){let e=(0,n._)(["\n    mutation DeleteView($where: ViewWhereUniqueInput!) {\n  deleteView(where: $where)\n}\n    "]);return r=function(){return e},e}function s(){let e=(0,n._)(["\n    query GetView($where: ViewWhereUniqueInput!) {\n  view(where: $ViewWhereUniqueInput) {\n    id\n    name\n    statement\n  }\n}\n    "]);return s=function(){return e},e}function o(){let e=(0,n._)(["\n    query ListViews {\n  listViews {\n    id\n    name\n    displayName\n    statement\n  }\n}\n    "]);return o=function(){return e},e}function c(){let e=(0,n._)(["\n    mutation PreviewViewData($where: PreviewViewDataInput!) {\n  previewViewData(where: $where)\n}\n    "]);return c=function(){return e},e}function u(){let e=(0,n._)(["\n    mutation ValidateView($data: ValidateViewInput!) {\n  validateView(data: $data) {\n    valid\n    message\n  }\n}\n    "]);return u=function(){return e},e}let m={},p=(0,l.Ps)(d());function f(e){let t={...m,...e};return i.D(p,t)}let h=(0,l.Ps)(r());function x(e){let t={...m,...e};return i.D(h,t)}(0,l.Ps)(s()),(0,l.Ps)(o());let y=(0,l.Ps)(c());function g(e){let t={...m,...e};return i.D(y,t)}let N=(0,l.Ps)(u());function v(e){let t={...m,...e};return i.D(N,t)}},53692:function(e,t,a){"use strict";a.d(t,{P:function(){return m},Z:function(){return p}});var n=a(85893),l=a(67294),i=a(13518),d=a(98885),r=a(19521),s=a(27361),o=a.n(s),c=a(5019);let u=r.ZP.div.withConfig({displayName:"EditableWrapper__EditableStyle",componentId:"sc-12f513e9-0"})(["line-height:24px;min-height:25px;.editable-cell-value-wrap{padding:0 7px;border:1px var(--gray-4) solid;border-radius:4px;cursor:pointer;&:hover{border-color:var(--gray-5);}}.ant-form-item-control-input{min-height:24px;.ant-input{line-height:24px;}}"]),m=(0,l.createContext)(null);function p(e){let{children:t,dataIndex:a,record:r,rules:s,handleSave:p}=e,[f,h]=(0,l.useState)(!1),x=(0,l.useRef)(null),y=(0,l.useRef)(0),g=(0,l.useRef)(null),N=(0,l.useContext)(m),v=Array.isArray(a)?a.join("."):a;(0,l.useEffect)(()=>{f&&g.current.focus()},[f]);let b=()=>{x.current&&(y.current=x.current.clientWidth),h(!f);let e=o()(r,v);N.setFieldsValue({[v]:e})},w=async()=>{try{let e=await N.validateFields();b(),p(r.id,e)}catch(e){console.log("Save failed:",e)}},I=f?(0,n.jsx)(i.Z.Item,{style:{margin:0},name:v,rules:s,children:(0,n.jsx)(d.default,{size:"small",ref:g,onPressEnter:w,onBlur:w,style:{width:y.current}})}):(0,n.jsx)("div",{ref:x,className:"editable-cell-value-wrap",style:{paddingRight:24},onClick:b,children:(0,n.jsx)(c.Z,{text:t})});return(0,n.jsx)(u,{children:I})}},59976:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var n=a(85893),l=a(67294),i=a(42187),d=a(19521),r=a(51228),s=a.n(r);let o=(0,d.ZP)(i.default).withConfig({displayName:"ErrorCollapse__StyledCollapse",componentId:"sc-96b0dd67-0"})([".ant-collapse-item{> .ant-collapse-header{user-select:none;color:var(--gray-7);padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;.ant-collapse-arrow{margin-right:8px;}}> .ant-collapse-content .ant-collapse-content-box{color:var(--gray-7);padding:4px 0 0 0;}}"]);function c(e){let{message:t,className:a,defaultActive:d}=e,[r,c]=(0,l.useState)(d?["1"]:[]);return(0,n.jsx)(o,{className:a,ghost:!0,activeKey:r,onChange:e=>c(e),expandIcon:e=>{let{isActive:t}=e;return(0,n.jsx)(s(),{rotate:t?90:0})},children:(0,n.jsx)(i.default.Panel,{header:"Show error messages",children:(0,n.jsx)("pre",{className:"text-sm mb-0 pl-5",style:{whiteSpace:"pre-wrap"},children:t})},"1")})}},20411:function(e,t,a){"use strict";a.d(t,{iD:function(){return x}});var n=a(85893),l=a(67294),i=a(19521),d=a(80002),r=a(21367),s=a(51903),o=a.n(s),c=a(72869),u=a.n(c),m=a(33190);a(84882);let p=i.ZP.div.withConfig({displayName:"BaseCodeBlock__Block",componentId:"sc-b28653c8-0"})(["position:relative;white-space:pre;font-size:13px;border:1px var(--gray-4) solid;border-radius:4px;font-family:'Source Code Pro',monospace;user-select:text;cursor:text;&:focus{outline:none;}"," .adm-code-wrap{"," "," user-select:text;}.adm-code-line{display:block;user-select:text;&-number{user-select:none;display:inline-block;min-width:14px;text-align:right;margin-right:1em;color:var(--gray-6);font-weight:700;font-size:12px;}}"],e=>e.inline?"\n      display: inline;\n      border: none;\n      background: transparent !important;\n      padding: 0;\n      * { display: inline !important; }\n    ":"\n    background: ".concat(e.backgroundColor||"var(--gray-1)"," !important;\n    padding: 8px;\n  "),e=>e.inline?"":"overflow: auto;",e=>e.maxHeight?"max-height: ".concat(e.maxHeight,"px;"):""),f=(0,i.ZP)(d.default.Text).withConfig({displayName:"BaseCodeBlock__CopyText",componentId:"sc-b28653c8-1"})(["position:absolute;top:0;right:",";font-size:0;button{background:var(--gray-1) !important;}.ant-typography-copy{font-size:12px;}.ant-btn:not(:hover){color:var(--gray-8);}"],e=>e.$hasVScrollbar?"20px":"0"),h=e=>{let t="ace-tomorrow";if(!document.getElementById(t)){let a=document.createElement("style");a.id=t,document.head.appendChild(a),a.appendChild(document.createTextNode(e))}},x=e=>function(t){let{code:a,copyable:i,maxHeight:d,inline:s,loading:c,showLineNumbers:x,backgroundColor:y,onCopy:g}=t,{ace:N}=window,{Tokenizer:v}=N.require("ace/tokenizer"),b=new v(new e().getRules()),w=(0,l.useRef)(null),[I,j]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let{cssText:e}=N.require("ace/theme/tomorrow");h(e)},[]),(0,l.useEffect)(()=>{let e=w.current;e&&j(e.scrollHeight>e.clientHeight)},[a]);let C=(a||"").split("\n").map((e,t)=>{let a=b.getLineTokens(e).tokens.map((e,t)=>{let a=e.type.split(".").map(e=>"ace_".concat(e));return(0,n.jsx)("span",{className:a.join(" "),children:e.value},t)});return(0,n.jsxs)("span",{className:"adm-code-line ace_line",children:[x&&(0,n.jsx)("span",{className:"adm-code-line-number",children:t+1}),a]},"".concat(e,"-").concat(t))});return(0,n.jsx)(p,{className:"ace_editor ace-tomorrow adm_code-block",maxHeight:d,inline:s,backgroundColor:y,tabIndex:0,onKeyDown:e=>{if((e.metaKey||e.ctrlKey)&&"a"===e.key){e.preventDefault();let t=window.getSelection(),a=document.createRange();a.selectNodeContents(e.currentTarget.querySelector(".adm-code-wrap")||e.currentTarget),null==t||t.removeAllRanges(),null==t||t.addRange(a)}},children:(0,n.jsx)(m.gb,{spinning:c,children:(0,n.jsxs)("div",{className:"adm-code-wrap",ref:w,children:[C,i&&(0,n.jsx)(f,{$hasVScrollbar:I,copyable:{onCopy:g,icon:[(0,n.jsx)(r.default,{icon:(0,n.jsx)(u(),{}),size:"small",style:{backgroundColor:"transparent"}},"copy-icon"),(0,n.jsx)(r.default,{icon:(0,n.jsx)(o(),{className:"green-6"}),size:"small"},"copied-icon")],text:a},children:a})]})})})}},63284:function(e,t,a){"use strict";a.r(t);var n=a(85893),l=a(20411);t.default=e=>{let{ace:t}=window,{SqlHighlightRules:a}=t.require("ace/mode/sql_highlight_rules"),i=(0,l.iD)(a);return(0,n.jsx)(i,{...e})}},91397:function(e,t,a){"use strict";a.d(t,{Z:function(){return w}});var n=a(85893),l=a(67294),i=a(80002),d=a(21367),r=a(62819),s=a(19521),o=a(70474),c=a(17579),u=a(47037),m=a.n(u);let p=e=>["boolean","object"].includes(typeof e)?JSON.stringify(e):e,f=(e,t)=>e.map((e,a)=>{let n={};return n.key=a,e.forEach((e,a)=>{n[t[a].dataIndex]=p(e)}),n});function h(e){let{columns:t=[],data:a=[],loading:i,locale:d}=e,r=!!t.length,s=(0,l.useMemo)(()=>t.reduce((e,t)=>e+(m()(t.titleText||t.title)?16*(t.titleText||t.title).length:100),0),[t]),o=(0,l.useMemo)(()=>t.map(e=>({...e,ellipsis:!0})),[t]),u=(0,l.useMemo)(()=>f(a,t),[a]);return(0,n.jsx)(c.Z,{className:"ph-no-capture ".concat(r?"ant-table-has-header":""),showHeader:r,dataSource:u,columns:o,pagination:!1,size:"small",scroll:{y:280,x:s},loading:i,locale:d})}var x=a(94638);let{Text:y}=i.default,g=s.ZP.div.withConfig({displayName:"PreviewData__StyledCell",componentId:"sc-81799b7d-0"})(["position:relative;.copy-icon{position:absolute;top:50%;right:0;transform:translateY(-50%);opacity:0;transition:opacity 0.3s;}.ant-typography-copy{margin:-4px;}&:hover .copy-icon{opacity:1;}"]),N=(0,l.memo)(e=>{let{name:t,type:a}=e,l=(0,o.u)({type:a},{title:a});return(0,n.jsxs)(n.Fragment,{children:[l,(0,n.jsx)(y,{title:t,className:"ml-1",children:t})]})}),v=(0,l.memo)(e=>{let{text:t,copyable:a}=e;return(0,n.jsxs)(g,{className:"text-truncate",children:[(0,n.jsx)("span",{title:t,className:"text text-container",children:t}),a&&(0,n.jsx)(d.default,{size:"small",className:"copy-icon",children:(0,n.jsx)(y,{copyable:{text:t,tooltips:!1},className:"gray-8"})})]})}),b=(e,t)=>{let{copyable:a}=t;return e.map(e=>{let{name:t,type:l}=e;return{dataIndex:t,titleText:t,key:t,ellipsis:!0,title:(0,n.jsx)(N,{name:t,type:l}),render:e=>(0,n.jsx)(v,{text:e,copyable:a}),onCell:()=>({style:{lineHeight:"24px"}})}})};function w(e){let{previewData:t,loading:a,error:i,locale:d,copyable:s=!0}=e,o=(0,l.useMemo)(()=>(null==t?void 0:t.columns)&&b(t.columns,{copyable:s}),[null==t?void 0:t.columns,s]),c=i&&i.message;if(!a&&c){let{message:e,shortMessage:t}=(0,x.Bx)(i);return(0,n.jsx)(r.default,{message:t,description:e,type:"error",showIcon:!0})}return(0,n.jsx)(h,{columns:o,data:(null==t?void 0:t.data)||[],loading:a,locale:d})}},84882:function(e,t,a){"use strict";var n=a(74981);a(89899),a(90252),a(42557),a(82679),t.Z=n.ZP},48196:function(e,t,a){"use strict";a.d(t,{Z:function(){return i}});var n=a(67294),l=a(84908);function i(){let[e,t]=(0,n.useState)(!1),[a,i]=(0,n.useState)(l.SD.CREATE),[d,r]=(0,n.useState)(null);return{state:{visible:e,formMode:a,defaultValue:d},openDrawer:e=>{e&&r(e),e&&i(l.SD.EDIT),t(!0)},closeDrawer:()=>{t(!1),r(null),i(l.SD.CREATE)},updateState:e=>{r(e)}}}},78586:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return tR}});var n=a(85893),l=a(5152),i=a.n(l),d=a(11163),r=a(39332),s=a(67294),o=a(37031),c=a(19521),u=a(84908),m=a(24350),p=a.n(m),f=a(39616),h=a(66590),x=a(83846);let y=e=>{let t="object"==typeof e.value?JSON.stringify(e.value):e.value;return{...e,value:t,"data-testid":"common__fields__select-option"}},g=c.ZP.div.withConfig({displayName:"FieldSelect__FieldBox",componentId:"sc-a2f468d6-0"})(["user-select:none;border-radius:4px;background-color:white;width:170px;box-shadow:0px 9px 28px 8px rgba(0,0,0,0.05),0px 6px 16px 0px rgba(0,0,0,0.08),0px 3px 6px -4px rgba(0,0,0,0.12);+ .adm-fieldBox{position:relative;margin-left:40px;&:before{content:'';position:absolute;top:50%;left:-40px;width:40px;height:1px;background-color:var(--gray-8);}}.ant-select-selection-placeholder{color:var(--geekblue-6);}&:last-child{border:1px var(--geekblue-6) solid;}"]),N=c.ZP.div.withConfig({displayName:"FieldSelect__FieldHeader",componentId:"sc-a2f468d6-1"})(["display:flex;align-items:center;border-bottom:1px var(--gray-4) solid;"]),v=(0,c.ZP)(function(e){let{value:t,onChange:a,options:l,...i}=e,d=(0,s.useCallback)((e,t)=>{let n=Array.isArray(e)?e.map(e=>(0,x.Mo)(e)):(0,x.Mo)(e);a&&a(n,t)},[]),r=(0,s.useMemo)(()=>l.map(e=>{var t;return(0,x.yg)({...y(e),options:null===(t=e.options)||void 0===t?void 0:t.map(y)})}),[l]),o=(0,s.useMemo)(()=>Array.isArray(t)?t.map(e=>JSON.stringify(e)):JSON.stringify(t),[t]);return(0,n.jsx)(h.default,{value:o,options:r,onChange:d,...i})}).withConfig({displayName:"FieldSelect__StyledSelector",componentId:"sc-a2f468d6-2"})(["&.ant-select-status-error.ant-select:not(.ant-select-disabled):not( .ant-select-customize-input ) .ant-select-selector{border-color:transparent !important;}"]),b=e=>({nodeType:e.nodeType,referenceName:e.referenceName,displayName:e.displayName,type:e.type,relationId:null==e?void 0:e.relationId,columnId:null==e?void 0:e.columnId}),w=(e,t)=>{let{diagramData:a,data:n}=e,l=a.models.find(e=>e.modelId===n.modelId),i=(e,t)=>[...(null==e?void 0:e.fields)||[],...(null==e?void 0:e.calculatedFields)||[]].find(e=>e.columnId===t);if(1===n.lineage.length){let e=i(l,n.lineage[0]);t&&t({columnId:n.columnId,name:n.displayName,expression:n.aggregation,lineage:[b(e)]},{models:a.models,sourceModel:l});return}let d=p()(a.models,"referenceName"),r=[...n.lineage],s=r.pop(),o=null,c=r.reduce((e,t)=>{let a=(o||l).relationFields.find(e=>e.relationId===t);return o=d[a.referenceName],[...e,a]},[]),u=i(d[c[c.length-1].referenceName],s);t&&t({columnId:n.columnId,name:n.displayName,expression:n.aggregation,lineage:[...c,u].map(b)},{models:a.models,sourceModel:l})};var I=a(30848),j=a(18337),C=a(21367),E=a(93181),S=a.n(E),_=a(40582),D=a(55041),T=a(80002),M=a(17579),k=a(5019),R=a(63284),F=a(70474),P=a(44280),L=a(77840);let A={ALIAS:{title:"Alias",dataIndex:"displayName",key:"alias",ellipsis:!0,render:e=>e||"-"},NAME:{title:"Name",dataIndex:"referenceName",key:"referenceName",ellipsis:!0,render:e=>e||"-"},TYPE:{title:"Type",dataIndex:"type",render:e=>(0,n.jsxs)("div",{className:"d-flex align-center",children:[(0,F.u)({type:e},{className:"mr-2"}),e]})},EXPRESSION:{title:"Expression",dataIndex:"expression",key:"expression",render:e=>(0,n.jsx)(k.Z,{text:e,children:(0,n.jsx)(R.default,{code:e,inline:!0})})},RELATION_FROM:{title:"From",key:"fromField",ellipsis:!0,render:e=>"".concat(e.fromModelDisplayName,".").concat(e.fromColumnDisplayName)},RELATION_TO:{title:"To",key:"toField",ellipsis:!0,render:e=>"".concat(e.toModelDisplayName,".").concat(e.toColumnDisplayName)},RELATION:{title:"Type",dataIndex:"type",key:"joinType",render:e=>(0,P.I)(e)},DESCRIPTION:{title:"Description",dataIndex:"description",key:"description",ellipsis:!0,render:e=>e||"-"}};function O(e){let{dataSource:t=[],columns:a=[],actionColumns:l,...i}=e,d=(0,s.useMemo)(()=>a.concat(l||[]),[t]),r=(0,s.useMemo)(()=>(t||[]).map((e,t)=>({...e,key:"".concat(e.id,"-").concat(t)})),[t]);return(0,n.jsx)(M.Z,{...i,dataSource:r,showHeader:r.length>0,columns:d,pagination:{hideOnSinglePage:!0,pageSize:10,size:"small"}})}let Z=(0,L.x)(e=>{let{title:t,value:a,index:l}=e;return(0,n.jsxs)(n.Fragment,{children:[l>0&&(0,n.jsx)("div",{className:"border-b border-gray-5"}),(0,n.jsxs)(_.default,{wrap:!1,className:"py-1 px-2",children:[(0,n.jsx)(D.default,{span:6,className:"gray-6",children:t}),(0,n.jsx)(D.default,{style:{wordBreak:"break-word"},children:a})]})]})});function V(e){let{data:t,extra:a}=e;return(0,n.jsxs)("div",{className:"pl-12 text-sm gray-8 -my-1",children:[(0,n.jsx)(Z,{data:t}),a]})}function $(e){let{columns:t}=e;return(0,n.jsx)(M.Z,{...e,columns:t||[{...A.NAME,width:70},{...A.ALIAS,width:70},{...A.TYPE,width:45},{...A.DESCRIPTION,width:80}],className:"ant-table--text-sm ml-2",scroll:{x:600},size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10}})}function Q(e){let{columns:t,showExpandable:a,expandable:l}=e;return(0,n.jsx)(O,{...e,columns:t||[A.NAME,A.ALIAS,A.TYPE,A.DESCRIPTION],expandable:a?l||{expandedRowRender:e=>(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(V,{data:[{title:"Description",value:e.description||"-"}],extra:e.nestedFields&&(0,n.jsxs)("div",{children:[(0,n.jsxs)(_.default,{wrap:!1,className:"py-1 px-2",children:[(0,n.jsx)(D.default,{span:6,className:"gray-6",children:"Nested columns"}),(0,n.jsxs)(D.default,{style:{wordBreak:"break-word"},children:[e.nestedFields.length," column(s)"]})]}),(0,n.jsx)($,{dataSource:e.nestedFields})]})})})}:null})}function U(e){let{columns:t,showExpandable:a}=e;return(0,n.jsx)(O,{...e,columns:t||[{...A.NAME,dataIndex:"displayName",width:160},A.EXPRESSION,{...A.DESCRIPTION,width:160}],expandable:a?{expandedRowRender:e=>(0,n.jsx)(V,{data:[{title:"Description",value:e.description||"-"}]})}:null})}function q(e){let{columns:t,showExpandable:a}=e;return(0,n.jsx)(O,{...e,columns:t||[{...A.NAME,dataIndex:"displayName"},A.RELATION_FROM,A.RELATION_TO,A.RELATION,{...A.DESCRIPTION,width:160}],expandable:a?{expandedRowRender:e=>(0,n.jsx)(V,{data:[{title:"From",value:"".concat(e.fromModelDisplayName,".").concat(e.fromColumnDisplayName)},{title:"To",value:"".concat(e.toModelDisplayName,".").concat(e.toColumnDisplayName)},{title:"Description",value:e.description||"-"}]})}:null})}var B=a(91397),W=a(3591);function z(e){let{modelId:t,displayName:a,referenceName:l,fields:i=[],calculatedFields:d=[],relationFields:r=[],description:o}=e||{},[c,u]=(0,W.R5)({onError:e=>console.error(e)}),m=(0,s.useMemo)(()=>p()(i,"referenceName"),[i]),f=(0,s.useMemo)(()=>{var e;let t=null===(e=u.data)||void 0===e?void 0:e.previewModelData,a=((null==t?void 0:t.columns)||[]).map(e=>{var t;let a=null===(t=m[e.name])||void 0===t?void 0:t.displayName;return{...e,name:a||e.name}});return{...t,columns:a}},[m,u.data]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(_.default,{className:"mb-6",children:[(0,n.jsxs)(D.default,{span:12,"data-testid":"metadata__name",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Name"}),(0,n.jsx)("div",{children:l||"-"})]}),(0,n.jsxs)(D.default,{span:12,"data-testid":"metadata__alias",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Alias"}),(0,n.jsx)("div",{children:a||"-"})]})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__description",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Description"}),(0,n.jsx)("div",{children:o||"-"})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__columns",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Columns (",i.length,")"]}),(0,n.jsx)(Q,{dataSource:i,showExpandable:!0})]}),!!d.length&&(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__calculated-fields",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Calculated fields (",d.length,")"]}),(0,n.jsx)(U,{dataSource:d,showExpandable:!0})]}),!!r.length&&(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__relationships",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Relationships (",r.length,")"]}),(0,n.jsx)(q,{dataSource:r,showExpandable:!0})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__preview-data",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Data preview (100 rows)"}),(0,n.jsx)(C.default,{onClick:()=>{c({variables:{where:{id:t}}})},loading:u.loading,children:"Preview data"}),(0,n.jsx)("div",{className:"my-3",children:(0,n.jsx)(B.Z,{error:u.error,loading:u.loading,previewData:f})})]})]})}var Y=a(94601);function K(e){var t;let{displayName:a,description:l,fields:i=[],statement:d,viewId:r}=e||{},[s,o]=(0,Y.fB)({onError:e=>console.error(e)});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__name",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Name"}),(0,n.jsx)("div",{children:a||"-"})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__description",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Description"}),(0,n.jsx)("div",{children:l||"-"})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__columns",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Columns (",i.length,")"]}),(0,n.jsx)(Q,{columns:[A.NAME,A.TYPE,A.DESCRIPTION],dataSource:i,showExpandable:!0})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__sql-statement",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"SQL statement"}),(0,n.jsx)(R.default,{code:d,showLineNumbers:!0,maxHeight:"300"})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__preview-data",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Data preview (100 rows)"}),(0,n.jsx)(C.default,{onClick:()=>{s({variables:{where:{id:r}}})},loading:o.loading,children:"Preview data"}),(0,n.jsx)("div",{className:"my-3",children:(0,n.jsx)(B.Z,{error:o.error,loading:o.loading,previewData:null==o?void 0:null===(t=o.data)||void 0===t?void 0:t.previewViewData})})]})]})}function H(e){let{visible:t,defaultValue:a,onClose:l,onEditClick:i}=e,{displayName:d,nodeType:r=u.QZ.MODEL}=a||{},s=r===u.QZ.MODEL,o=r===u.QZ.VIEW;return(0,n.jsxs)(j.Z,{visible:t,title:d,width:760,closable:!0,destroyOnClose:!0,onClose:l,extra:(0,n.jsx)(C.default,{icon:(0,n.jsx)(S(),{}),onClick:()=>i(a),children:"Edit"}),children:[s&&(0,n.jsx)(z,{...a}),o&&(0,n.jsx)(K,{...a})]})}var G=a(13518),J=a(69828),X=a(53692),ee=a(36968),et=a.n(ee),ea=a(50361),en=a.n(ea),el=a(41609),ei=a.n(el);let ed=e=>{let{className:t,colSpan:a,title:l,editable:i,record:d,handleSave:r,dataIndex:s,children:o}=e,c=i?(0,n.jsx)(X.Z,{record:d,dataIndex:s,handleSave:r,children:o}):o;return(0,n.jsx)("td",{className:t,title:l,colSpan:a,children:c})},er=e=>t=>{let{columns:a,dataSource:l,onChange:i,...d}=t,[r,o]=(0,s.useState)(l),c={body:{cell:ei()(l)?void 0:ed}};(0,s.useEffect)(()=>{i&&i(r)},[r]);let u=(e,t)=>{let[a]=Object.keys(t),n=en()(r);n.forEach(n=>{e===n.id&&et()(n,a,t[a])}),o(n)},m=a.map(e=>({...e,onCell:t=>({editable:[A.ALIAS.title,A.DESCRIPTION.title].includes(e.title),dataIndex:e.dataIndex,record:t,handleSave:u})}));return(0,n.jsx)(e,{...d,size:"small",dataSource:r,columns:m,components:c})};function es(e){let{dataSource:t,onChange:a,nodeType:l,rules:i}=e,[d,r]=(0,s.useState)(t),o=l===u.QZ.MODEL,c=l===u.QZ.VIEW;(0,s.useEffect)(()=>{a&&a({displayName:d.displayName,description:d.description})},[d]);let m=(e,t)=>{let[a]=Object.keys(t),n=en()(d);et()(n,a,t[a]),r(n)};return(0,n.jsxs)(n.Fragment,{children:[o&&(0,n.jsxs)(_.default,{children:[(0,n.jsx)(D.default,{span:12,children:(0,n.jsxs)("div",{className:"mb-6","data-testid":"edit-metadata__name",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Name"}),(0,n.jsx)("div",{children:d.referenceName})]})}),(0,n.jsx)(D.default,{span:12,"data-testid":"edit-metadata__alias",children:(0,n.jsxs)("div",{className:"mb-6","data-testid":"metadata__name",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Alias"}),(0,n.jsx)(X.Z,{record:d,dataIndex:"displayName",handleSave:m,children:d.displayName||"-"})]})})]}),c&&(0,n.jsxs)("div",{className:"mb-6","data-testid":"edit-metadata__name",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Name"}),(0,n.jsx)(X.Z,{record:d,dataIndex:"displayName",handleSave:m,rules:null==i?void 0:i.displayName,children:d.displayName||"-"})]}),(0,n.jsxs)("div",{className:"mb-6","data-testid":"edit-metadata__description",children:[(0,n.jsx)(T.default.Text,{className:"d-block gray-7 mb-2",children:"Description"}),(0,n.jsx)(X.Z,{record:d,dataIndex:"description",handleSave:m,children:d.description||"-"})]})]})}let eo={FIELDS:"columns",NESTED_FIELDS:"nestedColumns",CALCULATED_FIELDS:"calculatedFields",RELATIONSHIPS:"relationships"},ec=er(Q),eu=er($),em=er(U),ep=er(q);function ef(e){let{formNamespace:t,displayName:a,referenceName:l,fields:i=[],calculatedFields:d=[],relationFields:r=[],description:o,nodeType:c,modelId:u}=e||{},m=(0,s.useContext)(X.P),p=e=>{m.setFieldsValue({[t]:{...m.getFieldValue(t)||{},...e,modelId:u}})},f=e=>t=>{p({[e]:t.map(t=>({id:t.relationId||t.columnId||t.nestedColumnId,description:t.description,...[eo.FIELDS,eo.NESTED_FIELDS].includes(e)?{displayName:t.displayName}:{}}))})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(es,{dataSource:{displayName:a,referenceName:l,description:o},onChange:p,nodeType:c}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Columns (",i.length,")"]}),(0,n.jsx)(ec,{dataSource:i,columns:[A.NAME,A.ALIAS,{...A.TYPE,width:150},{...A.DESCRIPTION,width:280}],onChange:f(eo.FIELDS),showExpandable:!0,expandable:{expandedRowRender:e=>(0,n.jsx)("div",{className:"px-3 py-2",children:(0,n.jsx)(eu,{dataSource:e.nestedFields,columns:[A.NAME,A.ALIAS,A.TYPE,A.DESCRIPTION],onChange:f(eo.NESTED_FIELDS)})}),rowExpandable:e=>!!e.nestedFields}})]}),!!d.length&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Calculated fields (",d.length,")"]}),(0,n.jsx)(em,{dataSource:d,columns:[{...A.NAME,dataIndex:"displayName",width:160},A.EXPRESSION,{...A.DESCRIPTION,width:280}],onChange:f(eo.CALCULATED_FIELDS)})]}),!!r.length&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Relationships (",r.length,")"]}),(0,n.jsx)(ep,{dataSource:r,columns:[{...A.NAME,dataIndex:"displayName"},A.RELATION_FROM,A.RELATION_TO,{...A.RELATION,width:130},{...A.DESCRIPTION,width:200}],onChange:f(eo.RELATIONSHIPS)})]})]})}var eh=a(32235);let ex=er(Q);function ey(e){let t;let{formNamespace:a,displayName:l,fields:i=[],description:d,nodeType:r,viewId:o}=e||{},c=(0,s.useContext)(X.P),[u]=(0,Y.Ox)({fetchPolicy:"no-cache"}),m=e=>{c.setFieldsValue({[a]:{...c.getFieldValue(a)||{},...e,viewId:o}})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(es,{dataSource:{displayName:l,description:d},onChange:m,nodeType:r,rules:{displayName:[{required:!0,validator:(0,eh.qd)(u)}]}}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)(T.default.Text,{className:"d-block gray-7 mb-2",children:["Columns (",i.length,")"]}),(0,n.jsx)(ex,{dataSource:i,columns:[A.NAME,{...A.TYPE},{...A.DESCRIPTION,width:280}],onChange:(t="columns",e=>{m({[t]:e.map(e=>({referenceName:e.referenceName,description:e.description}))})})})]})]})}let eg="metadata";function eN(e){let{visible:t,defaultValue:a,loading:l,onSubmit:i,onClose:d}=e,{nodeType:r}=a||{},[s]=G.Z.useForm(),o=async()=>{s.validateFields().then(async()=>{let e=s.getFieldValue(eg);await i({data:e,nodeType:r}),d()}).catch(console.error)};return(0,n.jsx)(J.Z,{title:"Edit metadata",width:800,visible:t,okText:"Submit",onOk:o,onCancel:d,confirmLoading:l,maskClosable:!1,destroyOnClose:!0,centered:!0,afterClose:()=>s.resetFields(),children:(0,n.jsx)(X.P.Provider,{value:s,children:(0,n.jsxs)(G.Z,{form:s,component:!1,children:[r===u.QZ.MODEL&&(0,n.jsx)(ef,{formNamespace:eg,...a}),r===u.QZ.VIEW&&(0,n.jsx)(ey,{formNamespace:eg,...a})]})})})}var ev=a(98885),eb=a(62819),ew=a(54793),eI=a.n(ew),ej=a(59530),eC=a(94638),eE=a(95462),eS=a(39693),e_=a.n(eS),eD=a(36238),eT=a(56144),eM=a(50608);let ek=c.ZP.div.withConfig({displayName:"lineageSelector__Wrapper",componentId:"sc-37963c50-0"})(["border:1px var(--gray-5) solid;border-radius:4px;overflow-x:auto;&.adm-error{border-color:var(--red-5);}"]),eR=(0,L.x)(function(e){let{nodeType:t,referenceName:a,displayName:l,data:i,onFetchOptions:d,onChange:r,index:o}=e,c=i[o+1],m=[u.QZ.MODEL,u.QZ.RELATION].includes(t),[p,h]=(0,s.useState)([]),x=async()=>{h(d&&await d(e,o)||[])};(0,s.useEffect)(()=>{c&&x()},[]);let y=async e=>{e&&x()};return m?(0,n.jsxs)(g,{className:"adm-fieldBox flex-shrink-0","data-testid":"common__lineage-field-block",children:[(0,n.jsxs)(N,{className:"py-1 px-3",children:[(0,n.jsx)(f.ZA,{className:"mr-1 flex-shrink-0"}),(0,n.jsx)("div",{className:"text-truncate flex-grow-1",title:l||a,children:l||a})]}),(null==c?void 0:c.nodeType)===u.QZ.RELATION&&(0,n.jsx)("div",{className:"gray-7 text-sm px-3 pt-1",children:"Relationships"}),(0,n.jsx)(v,{bordered:!1,options:p,optionLabelProp:"label",placeholder:"Select field",suffixIcon:null,value:c,dropdownClassName:"adm-model-field-select-dropdown",onDropdownVisibleChange:y,onSelect:e=>{r&&r(e,o)},"data-testid":"common__lineage-fields-select"})]}):null});function eF(e){let t=(0,s.useRef)(null),{sourceModel:a,value:l=[],onChange:i,onFetchOptions:d}=e,{status:r}=(0,s.useContext)(eT.FormItemInputContext),o=(0,s.useMemo)(()=>[{referenceName:a.referenceName,displayName:a.displayName,nodeType:u.QZ.MODEL},...l],[a,l]),c=async(e,a)=>{var n,d;let r=(0,x.Mo)(e),s=[...l.slice(0,a),r];i&&i(s),await (0,eD.Y3)(),null===(d=t.current)||void 0===d||d.scrollTo({left:null===(n=t.current)||void 0===n?void 0:n.scrollWidth})};return(0,n.jsx)(ek,{ref:t,className:"d-flex align-center bg-gray-3 px-8 py-12".concat(r?" adm-".concat(r):""),"data-testid":"common__lineage",children:(0,n.jsx)(eR,{data:o,onChange:c,onFetchOptions:d})})}let eP=e=>{let{model:t,sourceModel:a,expression:l,values:i=[]}=e,d=t.fields.some(e=>e.isPrimaryKey),r=t.modelId===a.modelId,s=e=>{let t;let a=(0,x.yg)(b(e)),s=e.nodeType===u.QZ.RELATION,o=r&&e.nodeType===u.QZ.CALCULATED_FIELD,c=eE.yA.includes(l)&&r&&!s,m=s&&i.some(e=>e.relationId&&e.relationId===a.relationId),p=s&&!d,f=!1,h="";(0,eh.r7)(l,a)?(0,eh.gZ)(l,a)||(f=!0,h="Please select a number type field."):(f=!0,h="Please select a string type field.");let y=c||p||o||m||f;return c?t="Aggregation functions don't allow selecting from source model fields to prevent unexpected outcomes.":p?t="Please set a primary key within this model to use it in a calculated field.":o?t="Calculated field from the source model is not supported.":m?t="This relationship is in use.":f&&(t=h),{label:(0,n.jsxs)("div",{className:"d-flex align-center",children:[(0,eM.i)({nodeType:e.nodeType,type:e.type},{className:"mr-1 flex-shrink-0",title:e.type}),(0,n.jsx)("div",{title:y?null:e.displayName,className:"text-truncate",children:e.displayName})]}),value:a,title:t,disabled:y}},o=[...(null==t?void 0:t.fields)||[]].map(s),c=((null==t?void 0:t.calculatedFields)||[]).map(s),m=((null==t?void 0:t.relationFields)||[]).map(s);return e_()([...o,c.length?{label:"Calculated fields",options:c}:void 0,m.length?{label:"Relationships",options:m}:void 0])};var eL=a(4922),eA=a(57557),eO=a.n(eA);let{Title:eZ}=T.default,eV=c.ZP.div.withConfig({displayName:"DescriptiveSelector__DescribeBox",componentId:"sc-79bd1fae-0"})(["display:flex;.rc-virtual-list{min-width:230px;}.describeBox{&-codeBlock{background:var(--gray-3);border-radius:4px;padding:6px 8px;}}"]),e$=e=>(0,n.jsxs)(eL.default,{style:{width:"100%"},size:[0,16],direction:"vertical",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{marginBottom:4},children:(0,n.jsx)("b",{children:"Description"})}),(null==e?void 0:e.description)||"-"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{marginBottom:4},children:(0,n.jsx)("b",{children:"Example"})}),(null==e?void 0:e.example)?(0,n.jsx)("div",{className:"describeBox-codeBlock",children:null==e?void 0:e.example}):"-"]})]});function eQ(e){let{mode:t,value:a,options:l,onChange:i,descriptiveContentRender:d,listHeight:r,placeholder:o,dropdownMatchSelectWidth:c}=e,[u]=l,[m,p]=(0,s.useState)(u.options?u.options[0]:u),f=e=>{p(e)},x=e=>({...eO()(e,["content"]),"data-value":e.value,onMouseEnter:t=>{f(e),e.onMouseEnter&&e.onMouseEnter(t)}}),y=l.map(e=>e.options?{...e,options:e.options.map(x)}:x(e));return(0,n.jsx)(h.default,{style:{width:"100%"},mode:t,options:y,value:a,onChange:i,dropdownRender:e=>(0,n.jsxs)(eV,{children:[e,(0,n.jsxs)("div",{style:{width:"100%",borderLeft:"1px solid var(--gray-3)",margin:"-4px 0",minWidth:0},children:[(0,n.jsx)(eZ,{level:5,ellipsis:!0,style:{padding:"8px 16px",borderBottom:"1px solid var(--gray-3)"},children:(null==m?void 0:m.label)||(null==m?void 0:m.value)}),(0,n.jsx)("div",{style:{padding:"4px 16px 12px"},children:(d||e$)(null==m?void 0:m.content)})]})]}),listHeight:d?r:193,placeholder:o,dropdownMatchSelectWidth:c,"data-testid":"common__descriptive-select"})}var eU=a(59976),eq=a(82729),eB=a(68806),eW=a(50319);function ez(){let e=(0,eq._)(["\n    mutation ValidateCalculatedField($data: ValidateCalculatedFieldInput!) {\n  validateCalculatedField(data: $data) {\n    message\n    valid\n  }\n}\n    "]);return ez=function(){return e},e}function eY(){let e=(0,eq._)(["\n    mutation CreateCalculatedField($data: CreateCalculatedFieldInput!) {\n  createCalculatedField(data: $data)\n}\n    "]);return eY=function(){return e},e}function eK(){let e=(0,eq._)(["\n    mutation UpdateCalculatedField($where: UpdateCalculatedFieldWhere!, $data: UpdateCalculatedFieldInput!) {\n  updateCalculatedField(where: $where, data: $data)\n}\n    "]);return eK=function(){return e},e}function eH(){let e=(0,eq._)(["\n    mutation DeleteCalculatedField($where: UpdateCalculatedFieldWhere!) {\n  deleteCalculatedField(where: $where)\n}\n    "]);return eH=function(){return e},e}let eG={},eJ=(0,eB.Ps)(ez()),eX=(0,eB.Ps)(eY()),e0=(0,eB.Ps)(eK()),e1=(0,eB.Ps)(eH());function e2(e){let{visible:t,loading:a,onSubmit:l,onClose:i,defaultValue:d,payload:r,formMode:o}=e,c=o===u.SD.EDIT,[m,p]=(0,s.useState)(null),[f]=G.Z.useForm(),h=G.Z.useWatch("expression",f),x=G.Z.useWatch("lineage",f),y=(0,s.useMemo)(()=>{let e=e=>{let t=(0,P.X)(e);return{label:t.name,value:e,content:{title:t.syntax,description:t.description,expression:t.syntax}}};return[{label:"Aggregation",options:eE.yA.map(e)},{label:"Math functions",options:eE.pQ.map(e)},{label:"String functions",options:eE.lV.map(e)}]},[]),g=(0,s.useMemo)(()=>null==r?void 0:r.models,[r]),N=(0,s.useMemo)(()=>null==r?void 0:r.sourceModel,[r]),[v]=function(e){let t={...eG};return eW.D(eJ,t)}(),b=(0,s.useCallback)(async e=>await v({variables:{data:{name:e,modelId:N.modelId,columnId:null==d?void 0:d.columnId}}}),[N,d]);(0,s.useEffect)(()=>{t&&f.setFieldsValue(d||{})},[f,d,t]);let w=(0,s.useCallback)(async e=>eP({model:g.find(t=>t.referenceName===e.referenceName),sourceModel:N,expression:h,values:x}),[g,x,h]),I=()=>{p(null),f.resetFields()};return(0,n.jsxs)(J.Z,{title:"".concat(c?"Update":"Add"," calculated field"),width:750,visible:t,onCancel:i,confirmLoading:a,maskClosable:!1,destroyOnClose:!0,afterClose:()=>I(),footer:(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center",children:[(0,n.jsxs)("div",{className:"text-sm ml-2",children:[(0,n.jsx)(eI(),{className:"gray-6 mr-2"}),(0,n.jsx)(T.default.Link,{type:"secondary",href:"https://docs.getwren.ai/oss/guide/modeling/models#update-primary-key",target:"_blank",rel:"noopener noreferrer",children:"How to set primary key in a model."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(C.default,{onClick:i,children:"Cancel"}),(0,n.jsx)(C.default,{type:"primary",onClick:()=>{p(null),f.validateFields().then(async e=>{let t=null==d?void 0:d.columnId,a=t?void 0:N.modelId;await l({id:t,data:{modelId:a,expression:e.expression,name:e.name,lineage:e.lineage.map(e=>e.relationId||e.columnId)}}),i()}).catch(e=>{let t=(0,eC.Bx)(e);t.code===eC.O1.INVALID_CALCULATED_FIELD&&p(t),console.error(e)})},loading:a,children:"Save"})]})]}),children:[(0,n.jsxs)(G.Z,{form:f,preserve:!1,layout:"vertical",children:[(0,n.jsx)(G.Z.Item,{label:"Name",name:"name",required:!0,rules:[{validator:(0,eh.rv)(b)}],children:(0,n.jsx)(ev.default,{})}),(0,n.jsx)(G.Z.Item,{label:"Select an expression",name:"expression",required:!0,rules:[{required:!0,message:ej.q.CALCULATED_FIELD.EXPRESSION.REQUIRED}],children:(0,n.jsx)(eQ,{placeholder:"Select an expression",options:y,descriptiveContentRender:e=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"mb-1",children:(null==e?void 0:e.description)||"-"}),(null==e?void 0:e.expression)&&(0,n.jsx)(T.default.Text,{className:"mb-1",code:!0,children:e.expression})]})})}),(0,n.jsx)("div",{className:"py-1"}),!!h&&(0,n.jsx)(G.Z.Item,{name:"lineage",rules:[{validator:(0,eh.kN)(h)}],children:(0,n.jsx)(eF,{sourceModel:N,onFetchOptions:w})})]}),!!m&&(0,n.jsx)(eb.default,{showIcon:!0,type:"error",message:m.shortMessage,description:(0,n.jsx)(eU.Z,{message:m.message})})]})}var e8=a(33190),e6=a(51618),e9=a(44249),e3=a(91966),e7=a.n(e3);let e5=(0,c.ZP)(M.Z).withConfig({displayName:"TableTransfer__StyledTable",componentId:"sc-519e4d1b-0"})([".ant-table-row{cursor:pointer;}.ant-table-row-disabled{cursor:not-allowed;color:var(--gray-5);.ant-tag{color:var(--gray-5);}}"]),e4=[{dataIndex:"name",title:"Column Name"},{dataIndex:"type",title:"Column Type",render:e=>(0,n.jsx)(e6.default,{children:e.toUpperCase()})}];var te=s.forwardRef((e,t)=>{let{leftColumns:a=e4,rightColumns:l=e4,...i}=e;return(0,n.jsx)(e9.Z,{...i,showSelectAll:!1,listStyle:{height:332},children:e=>{let{direction:i,filteredItems:d,onItemSelectAll:r,onItemSelect:s,selectedKeys:o,disabled:c}=e;return(0,n.jsx)("div",{ref:t,children:(0,n.jsx)(e5,{rowSelection:{getCheckboxProps:e=>({disabled:c||e.disabled}),onSelectAll(e,t){let a=t.filter(e=>!e.disabled).map(e=>{let{key:t}=e;return t});r(e?e7()(a,o):e7()(o,a),e)},onSelect(e,t){let{key:a}=e;s(a,t)},selectedRowKeys:o},columns:"left"===i?a:l,dataSource:d,size:"small",style:{pointerEvents:c?"none":null},onRow:e=>{let{key:t,disabled:a,title:n}=e;return{title:n,onClick:()=>{a||c||s(t,!o.includes(t))}}},rowClassName:e=>{let{disabled:t}=e;return t?"ant-table-row-disabled":""},scroll:{y:200},pagination:!1})})}})}),tt=a(34217);let{Option:ta}=h.default,tn={SOURCE_TABLE:"sourceTableName",COLUMNS:"fields",PRIMARY_KEY:"primaryKey"},tl=e=>async(t,a)=>a&&!e.includes(a)?Promise.reject(ej.q.MODELING_CREATE_MODEL.PRIMARY_KEY.INVALID):Promise.resolve();function ti(e){let{defaultValue:t,form:a,formMode:l}=e,[i,d]=(0,s.useState)([]),[r,o]=(0,s.useState)(void 0),c=G.Z.useWatch(tn.SOURCE_TABLE,a),m=l===u.SD.EDIT,{data:p,loading:f}=(0,W.it)({fetchPolicy:"cache-and-network",skip:m}),{data:x,loading:y}=(0,tt.Cu)({fetchPolicy:"cache-and-network",onError:e=>console.error(e)}),g=(null==x?void 0:x.listDataSourceTables)||[],N=null==p?void 0:p.listModels,v=(0,s.useMemo)(()=>(N||[]).map(e=>e.sourceTableName),[N]);(0,s.useEffect)(()=>{m||(d([]),a.resetFields([tn.PRIMARY_KEY]))},[l,r]),(0,s.useEffect)(()=>{c&&o(c)},[c]);let b=(0,s.useMemo)(()=>{if(ei()(r))return[];let e=g.find(e=>e.name===r);return e?e.columns.map(e=>({...e,key:e.name})):[]},[g,r]);(0,s.useEffect)(()=>{if(t){let e=t.fields.map(e=>e.referenceName).filter(e=>b.find(t=>t.name===e)),n=t.fields.find(e=>e.isPrimaryKey);a.setFieldsValue({[tn.COLUMNS]:e,[tn.PRIMARY_KEY]:null==n?void 0:n.referenceName}),o(t.sourceTableName),d(e)}},[t,a,b]);let w=g.map(e=>{let t={disabled:v.includes(e.name),children:e.name,value:e.name};return(0,s.createElement)(ta,{...t,key:t.value})}),I=y||f;return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(G.Z,{form:a,layout:"vertical",children:[!m&&(0,n.jsx)("div",{children:(0,n.jsx)(G.Z.Item,{label:"Select a table",name:tn.SOURCE_TABLE,required:!0,rules:[{required:!0,message:ej.q.MODELING_CREATE_MODEL.TABLE.REQUIRED}],children:(0,n.jsx)(h.default,{getPopupContainer:e=>e.parentElement,placeholder:"Select a table",showSearch:!0,loading:I,disabled:m,children:w})})}),(0,n.jsx)(e8.gb,{spinning:!!m&&I,children:(0,n.jsx)(G.Z.Item,{label:"Select columns",name:tn.COLUMNS,rules:[{required:!0,message:ej.q.MODELING_CREATE_MODEL.COLUMNS.REQUIRED}],children:(0,n.jsx)(te,{dataSource:b,targetKeys:i,onChange:e=>d(e),filterOption:(e,t)=>-1!==t.name.toLowerCase().indexOf(e.toLowerCase())||-1!==t.type.toLowerCase().indexOf(e.toLowerCase()),leftColumns:e4,rightColumns:e4,titles:["Available Columns","Target Columns"],showSearch:!0})})}),(0,n.jsx)(G.Z.Item,{label:"Select primary key",name:tn.PRIMARY_KEY,rules:[{validator:tl(i)}],children:(0,n.jsx)(h.default,{getPopupContainer:e=>e.parentElement,placeholder:"Select a column",showSearch:!0,allowClear:!0,children:i.map(e=>(0,n.jsx)(ta,{value:e,children:e},e))})})]})})}let td=(e,t)=>({[u.SD.CREATE]:"Create a data model",[u.SD.EDIT]:t})[e];function tr(e){let{visible:t,formMode:a,defaultValue:l,submitting:i,onClose:d,onSubmit:r}=e,[s]=G.Z.useForm();return(0,n.jsx)(j.Z,{visible:t,title:td(a,null==l?void 0:l.displayName),width:750,closable:!0,destroyOnClose:!0,afterVisibleChange:e=>{e||s.resetFields()},onClose:d,footer:(0,n.jsxs)(eL.default,{className:"d-flex justify-end",children:[(0,n.jsx)(C.default,{onClick:d,disabled:i,children:"Cancel"}),(0,n.jsx)(C.default,{type:"primary",onClick:()=>{s.validateFields().then(async e=>{await r({data:e,id:null==l?void 0:l.modelId}),d()}).catch(console.error)},loading:i,disabled:i,children:"Submit"})]}),children:(0,n.jsx)(ti,{formMode:a,form:s,defaultValue:l})})}var ts=a(43053),to=a(48196),tc=a(90883),tu=a(45403),tm=a(68753),tp=a(25739),tf=a(58923),th=a(93373),tx=a(61030);function ty(){let e=(0,eq._)(["\n    mutation UpdateModelMetadata($where: ModelWhereInput!, $data: UpdateModelMetadataInput!) {\n  updateModelMetadata(where: $where, data: $data)\n}\n    "]);return ty=function(){return e},e}function tg(){let e=(0,eq._)(["\n    mutation UpdateViewMetadata($where: ViewWhereUniqueInput!, $data: UpdateViewMetadataInput!) {\n  updateViewMetadata(where: $where, data: $data)\n}\n    "]);return tg=function(){return e},e}let tN={},tv=(0,eB.Ps)(ty()),tb=(0,eB.Ps)(tg());function tw(){let e=(0,eq._)(["\n    mutation CreateRelationship($data: RelationInput!) {\n  createRelation(data: $data)\n}\n    "]);return tw=function(){return e},e}function tI(){let e=(0,eq._)(["\n    mutation UpdateRelationship($where: WhereIdInput!, $data: UpdateRelationInput!) {\n  updateRelation(where: $where, data: $data)\n}\n    "]);return tI=function(){return e},e}function tj(){let e=(0,eq._)(["\n    mutation DeleteRelationship($where: WhereIdInput!) {\n  deleteRelation(where: $where)\n}\n    "]);return tj=function(){return e},e}let tC={},tE=(0,eB.Ps)(tw()),tS=(0,eB.Ps)(tI()),t_=(0,eB.Ps)(tj());var tD=a(89997);let tT=i()(()=>Promise.all([a.e(639),a.e(241),a.e(26),a.e(518),a.e(916)]).then(a.bind(a,22916)),{loadableGenerated:{webpack:()=>[22916]},ssr:!1}),tM=(0,s.forwardRef)(function(e,t){return(0,n.jsx)(tT,{...e,forwardRef:t})}),tk=c.ZP.div.withConfig({displayName:"modeling__DiagramWrapper",componentId:"sc-724491d9-0"})(["position:relative;height:100%;"]);function tR(){let e=(0,d.useRouter)(),t=(0,r.useSearchParams)(),a=(0,s.useRef)(null),{data:l}=(0,th.st)({fetchPolicy:"cache-and-network",onCompleted:()=>{var e;null===(e=a.current)||void 0===e||e.fitView()}}),i=(0,tx.wC)({pollInterval:1e3,fetchPolicy:"no-cache"}),c=[{query:tp.W}],m=[...c,{query:tf.uG}],p=e=>({onError:e=>console.error(e),refetchQueries:c,awaitRefetchQueries:!0,...e,onCompleted:()=>{i.refetch(),e.onCompleted&&e.onCompleted()}}),[f,{loading:h}]=function(e){let t={...eG,...e};return eW.D(eX,t)}(p({onError:null,onCompleted:()=>{o.default.success("Successfully created calculated field.")}})),[x,{loading:y}]=function(e){let t={...eG,...e};return eW.D(e0,t)}(p({onError:null,onCompleted:()=>{o.default.success("Successfully updated calculated field.")}})),[g]=function(e){let t={...eG,...e};return eW.D(e1,t)}(p({onCompleted:()=>{o.default.success("Successfully deleted calculated field.")}})),[N,{loading:v}]=(0,W.Pw)(p({onCompleted:()=>{o.default.success("Successfully created model.")},refetchQueries:m})),[b]=(0,W.Af)(p({onCompleted:()=>{o.default.success("Successfully deleted model.")},refetchQueries:m})),[j,{loading:C}]=(0,W.ko)(p({onCompleted:()=>{o.default.success("Successfully updated model.")},refetchQueries:m})),[E]=(0,Y.SJ)(p({onCompleted:()=>{o.default.success("Successfully deleted view.")}})),[S,{loading:_}]=function(e){let t={...tN,...e};return eW.D(tv,t)}(p({onCompleted:()=>{o.default.success("Successfully updated model metadata.")}})),[D,{loading:T}]=function(e){let t={...tC,...e};return eW.D(tE,t)}(p({onCompleted:()=>{o.default.success("Successfully created relationship.")}})),[M]=function(e){let t={...tC,...e};return eW.D(t_,t)}(p({onCompleted:()=>{o.default.success("Successfully deleted relationship.")}})),[k,{loading:R}]=function(e){let t={...tC,...e};return eW.D(tS,t)}(p({onCompleted:()=>{o.default.success("Successfully updated relationship.")}})),[F,{loading:P}]=function(e){let t={...tN,...e};return eW.D(tb,t)}(p({onCompleted:()=>{o.default.success("Successfully updated view metadata.")}})),L=(0,s.useMemo)(()=>l?null==l?void 0:l.diagram:null,[l]),A=(0,to.Z)(),O=(0,to.Z)(),Z=(0,tc.Z)(),V=(0,tc.Z)(),$=function(e){let t=(0,tc.Z)(),[a,n]=(0,s.useState)(null),l=(0,s.useMemo)(()=>((null==e?void 0:e.models)||[]).reduce((e,t)=>{let{referenceName:a,relationFields:n}=t,l=n.map(e=>({id:e.relationId,fromField:{modelId:String(e.fromModelId),modelName:e.fromModelName,fieldId:String(e.fromColumnId),fieldName:e.fromColumnName},toField:{modelId:String(e.toModelId),modelName:e.toModelName,fieldId:String(e.toColumnId),fieldName:e.toColumnName},type:e.type}));return e[a]=l,e},{}),[e]);return{onClose:()=>{n(null),t.closeModal()},openModal:e=>{if(e.nodeType===u.QZ.RELATION){n(e.fromModelName),t.openModal({relationId:e.relationId,fromField:{modelId:String(e.fromModelId),modelName:e.fromModelName,fieldId:String(e.fromColumnId),fieldName:e.fromColumnName},toField:{modelId:String(e.toModelId),modelName:e.toModelName,fieldId:String(e.toColumnId),fieldName:e.toColumnName},type:e.type});return}n(e.referenceName),t.openModal()},state:{...t.state,model:a,relations:l}}}(L),Q={viewId:t.get("viewId"),openMetadata:t.get("openMetadata")};(0,s.useEffect)(()=>{if(L&&Q.viewId&&Q.openMetadata){let t=L.views.find(e=>e.viewId===Number(Q.viewId));t&&A.openDrawer(t),e.replace(e.pathname)}},[Q,L]),(0,s.useEffect)(()=>{if(A.state.visible){let e=A.state.defaultValue,t=null;switch(e.nodeType){case u.QZ.MODEL:t=L.models.find(t=>t.modelId===e.modelId);break;case u.QZ.VIEW:t=L.views.find(t=>t.viewId===e.viewId)}A.updateState(t)}},[L]),(0,s.useEffect)(()=>(tD.Ld(tD.W1.GO_TO_FIRST_MODEL,U),()=>{tD.r1(tD.W1.GO_TO_FIRST_MODEL,U)}),[]);let U=()=>{if(a.current){let{getNodes:e}=a.current,t=e()[0];(null==t?void 0:t.id)&&q([t.id])}},q=e=>{if(a.current){let{getNodes:t,fitBounds:n}=a.current,l=t().find(t=>t.id===e[0]);n({...l.position,width:l.width,height:l.height})}},B=async e=>{A.openDrawer(e.data)};return(0,n.jsx)(tm.N.Provider,{value:{...i},children:(0,n.jsxs)(I.Z,{loading:null===L,sidebar:{data:L,onOpenModelDrawer:O.openDrawer,onSelect:q},children:[(0,n.jsx)(tk,{children:(0,n.jsx)(tM,{ref:a,data:L,onMoreClick:e=>{let{type:t,data:a}=e,{nodeType:n}=a,l={[u.dI.UPDATE_COLUMNS]:()=>{n===u.QZ.MODEL?O.openDrawer(a):console.log(a)},[u.dI.EDIT]:()=>{switch(n){case u.QZ.CALCULATED_FIELD:w({...e,diagramData:L},V.openModal);break;case u.QZ.RELATION:$.openModal(a);break;default:console.log(a)}},[u.dI.DELETE]:async()=>{switch(n){case u.QZ.MODEL:await b({variables:{where:{id:a.modelId}}});break;case u.QZ.CALCULATED_FIELD:await g({variables:{where:{id:a.columnId}}});break;case u.QZ.RELATION:await M({variables:{where:{id:a.relationId}}});break;case u.QZ.VIEW:await E({variables:{where:{id:a.viewId}}});break;default:console.log(a)}}};l[t]&&l[t]()},onNodeClick:B,onAddClick:e=>{let{targetNodeType:t,data:a}=e;switch(t){case u.QZ.CALCULATED_FIELD:V.openModal(null,{models:L.models,sourceModel:a});break;case u.QZ.RELATION:$.openModal(a);break;default:console.log("add",t)}}})}),(0,n.jsx)(H,{...A.state,onClose:A.closeDrawer,onEditClick:Z.openModal}),(0,n.jsx)(eN,{...Z.state,onClose:Z.closeModal,loading:_||P,onSubmit:async e=>{let{nodeType:t,data:a}=e,{modelId:n,viewId:l,...i}=a;switch(t){case u.QZ.MODEL:await S({variables:{where:{id:n},data:i}});break;case u.QZ.VIEW:await F({variables:{where:{id:l},data:i}});break;default:console.log("onSubmit",t,a)}}}),(0,n.jsx)(tr,{...O.state,onClose:O.closeDrawer,submitting:v||C,onSubmit:async e=>{let{id:t,data:a}=e;t?await j({variables:{where:{id:t},data:a}}):await N({variables:{data:a}})}}),(0,n.jsx)(e2,{...V.state,onClose:V.closeModal,loading:h||y,onSubmit:async e=>{let{id:t,data:a}=e;t?await x({variables:{where:{id:t},data:a}}):await f({variables:{data:a}})}}),(0,n.jsx)(ts.Z,{...$.state,onClose:$.onClose,loading:R||T,onSubmit:async e=>{let t=(0,tu.T9)(e);e.relationId?await k({variables:{where:{id:e.relationId},data:{type:t.type}}}):await D({variables:{data:{fromModelId:Number(t.fromField.modelId),fromColumnId:Number(t.fromField.fieldId),toModelId:Number(t.toField.modelId),toColumnId:Number(t.toField.fieldId),type:t.type}}})}})]})})}},83846:function(e,t,a){"use strict";a.d(t,{Mo:function(){return s},iZ:function(){return o},yg:function(){return r}});var n=a(14176),l=a.n(n),i=a(52353),d=a.n(i);let r=e=>l()(e,d()),s=e=>{try{return JSON.parse(e)}catch(t){return e}},o=(e,t)=>async function(){for(var a=arguments.length,n=Array(a),l=0;l<a;l++)n[l]=arguments[l];t(!0);try{await e(...n)}finally{t(!1)}}}},function(e){e.O(0,[774,281,530,90,543,337,510,428,858,848,888,179],function(){return e(e.s=89825)}),_N_E=e.O()}]);