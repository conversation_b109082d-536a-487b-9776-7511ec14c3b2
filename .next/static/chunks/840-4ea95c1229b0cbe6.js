"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[840],{98683:function(n,e,t){t.d(e,{i:function(){return X}});var r=t(82729),s=t(68806);function a(){let n=(0,r._)(["\n  fragment CommonError on Error {\n    code\n    shortMessage\n    message\n    stacktrace\n  }\n"]);return a=function(){return n},n}function o(){let n=(0,r._)(["\n  fragment CommonBreakdownDetail on ThreadResponseBreakdownDetail {\n    queryId\n    status\n    description\n    steps {\n      summary\n      sql\n      cteName\n    }\n    error {\n      ...CommonError\n    }\n  }\n\n  ","\n"]);return o=function(){return n},n}function i(){let n=(0,r._)(["\n  fragment CommonAnswerDetail on ThreadResponseAnswerDetail {\n    queryId\n    status\n    content\n    numRowsUsedInLLM\n    error {\n      ...CommonError\n    }\n  }\n\n  ","\n"]);return i=function(){return n},n}function u(){let n=(0,r._)(["\n  fragment CommonChartDetail on ThreadResponseChartDetail {\n    queryId\n    status\n    description\n    chartType\n    chartSchema\n    error {\n      ...CommonError\n    }\n    adjustment\n  }\n"]);return u=function(){return n},n}function d(){let n=(0,r._)(["\n  fragment CommonAskingTask on AskingTask {\n    status\n    type\n    candidates {\n      sql\n      type\n      view {\n        id\n        name\n        statement\n        displayName\n      }\n      sqlPair {\n        id\n        question\n        sql\n        projectId\n      }\n    }\n    error {\n      ...CommonError\n    }\n    rephrasedQuestion\n    intentReasoning\n    sqlGenerationReasoning\n    retrievedTables\n    invalidSql\n    traceId\n    queryId\n  }\n  ","\n"]);return d=function(){return n},n}function l(){let n=(0,r._)(["\n  fragment CommonResponse on ThreadResponse {\n    id\n    threadId\n    question\n    sql\n    view {\n      id\n      name\n      statement\n      displayName\n    }\n    breakdownDetail {\n      ...CommonBreakdownDetail\n    }\n    answerDetail {\n      ...CommonAnswerDetail\n    }\n    chartDetail {\n      ...CommonChartDetail\n    }\n    askingTask {\n      ...CommonAskingTask\n    }\n    adjustment {\n      type\n      payload\n    }\n    adjustmentTask {\n      queryId\n      status\n      error {\n        ...CommonError\n      }\n      sql\n      traceId\n      invalidSql\n    }\n  }\n\n  ","\n  ","\n  ","\n  ","\n  ","\n"]);return l=function(){return n},n}function c(){let n=(0,r._)(["\n  fragment CommonRecommendedQuestionsTask on RecommendedQuestionsTask {\n    status\n    questions {\n      question\n      category\n      sql\n    }\n    error {\n      ...CommonError\n    }\n  }\n\n  ","\n"]);return c=function(){return n},n}function m(){let n=(0,r._)(["\n  query SuggestedQuestions {\n    suggestedQuestions {\n      questions {\n        label\n        question\n      }\n    }\n  }\n"]);return m=function(){return n},n}function p(){let n=(0,r._)(["\n  query AskingTask($taskId: String!) {\n    askingTask(taskId: $taskId) {\n      ...CommonAskingTask\n    }\n  }\n  ","\n"]);return p=function(){return n},n}function I(){let n=(0,r._)(["\n  query Threads {\n    threads {\n      id\n      summary\n    }\n  }\n"]);return I=function(){return n},n}function f(){let n=(0,r._)(["\n  query Thread($threadId: Int!) {\n    thread(threadId: $threadId) {\n      id\n      responses {\n        ...CommonResponse\n      }\n    }\n  }\n  ","\n"]);return f=function(){return n},n}function h(){let n=(0,r._)(["\n  query ThreadResponse($responseId: Int!) {\n    threadResponse(responseId: $responseId) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return h=function(){return n},n}function N(){let n=(0,r._)(["\n  mutation CreateAskingTask($data: AskingTaskInput!) {\n    createAskingTask(data: $data) {\n      id\n    }\n  }\n"]);return N=function(){return n},n}function g(){let n=(0,r._)(["\n  mutation CancelAskingTask($taskId: String!) {\n    cancelAskingTask(taskId: $taskId)\n  }\n"]);return g=function(){return n},n}function v(){let n=(0,r._)(["\n  mutation RerunAskingTask($responseId: Int!) {\n    rerunAskingTask(responseId: $responseId) {\n      id\n    }\n  }\n"]);return v=function(){return n},n}function x(){let n=(0,r._)(["\n  mutation CreateThread($data: CreateThreadInput!) {\n    createThread(data: $data) {\n      id\n    }\n  }\n"]);return x=function(){return n},n}function T(){let n=(0,r._)(["\n  mutation CreateThreadResponse(\n    $threadId: Int!\n    $data: CreateThreadResponseInput!\n  ) {\n    createThreadResponse(threadId: $threadId, data: $data) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return T=function(){return n},n}function E(){let n=(0,r._)(["\n  mutation UpdateThread(\n    $where: ThreadUniqueWhereInput!\n    $data: UpdateThreadInput!\n  ) {\n    updateThread(where: $where, data: $data) {\n      id\n      summary\n    }\n  }\n"]);return E=function(){return n},n}function R(){let n=(0,r._)(["\n  mutation UpdateThreadResponse(\n    $where: ThreadResponseUniqueWhereInput!\n    $data: UpdateThreadResponseInput!\n  ) {\n    updateThreadResponse(where: $where, data: $data) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return R=function(){return n},n}function y(){let n=(0,r._)(["\n  mutation AdjustThreadResponse(\n    $responseId: Int!\n    $data: AdjustThreadResponseInput!\n  ) {\n    adjustThreadResponse(responseId: $responseId, data: $data) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return y=function(){return n},n}function k(){let n=(0,r._)(["\n  mutation DeleteThread($where: ThreadUniqueWhereInput!) {\n    deleteThread(where: $where)\n  }\n"]);return k=function(){return n},n}function S(){let n=(0,r._)(["\n  mutation PreviewData($where: PreviewDataInput!) {\n    previewData(where: $where)\n  }\n"]);return S=function(){return n},n}function j(){let n=(0,r._)(["\n  mutation PreviewBreakdownData($where: PreviewDataInput!) {\n    previewBreakdownData(where: $where)\n  }\n"]);return j=function(){return n},n}function A(){let n=(0,r._)(["\n  query GetNativeSQL($responseId: Int!) {\n    nativeSql(responseId: $responseId)\n  }\n"]);return A=function(){return n},n}function F(){let n=(0,r._)(["\n  mutation CreateInstantRecommendedQuestions(\n    $data: InstantRecommendedQuestionsInput!\n  ) {\n    createInstantRecommendedQuestions(data: $data) {\n      id\n    }\n  }\n"]);return F=function(){return n},n}function P(){let n=(0,r._)(["\n  query InstantRecommendedQuestions($taskId: String!) {\n    instantRecommendedQuestions(taskId: $taskId) {\n      ...CommonRecommendedQuestionsTask\n    }\n  }\n  ","\n"]);return P=function(){return n},n}function C(){let n=(0,r._)(["\n  query GetThreadRecommendationQuestions($threadId: Int!) {\n    getThreadRecommendationQuestions(threadId: $threadId) {\n      ...CommonRecommendedQuestionsTask\n    }\n  }\n\n  ","\n"]);return C=function(){return n},n}function b(){let n=(0,r._)(["\n  query GetProjectRecommendationQuestions {\n    getProjectRecommendationQuestions {\n      ...CommonRecommendedQuestionsTask\n    }\n  }\n\n  ","\n"]);return b=function(){return n},n}function w(){let n=(0,r._)(["\n  mutation GenerateProjectRecommendationQuestions {\n    generateProjectRecommendationQuestions\n  }\n"]);return w=function(){return n},n}function D(){let n=(0,r._)(["\n  mutation GenerateThreadRecommendationQuestions($threadId: Int!) {\n    generateThreadRecommendationQuestions(threadId: $threadId)\n  }\n"]);return D=function(){return n},n}function G(){let n=(0,r._)(["\n  mutation GenerateThreadResponseAnswer($responseId: Int!) {\n    generateThreadResponseAnswer(responseId: $responseId) {\n      ...CommonResponse\n    }\n  }\n\n  ","\n"]);return G=function(){return n},n}function _(){let n=(0,r._)(["\n  mutation GenerateThreadResponseChart($responseId: Int!) {\n    generateThreadResponseChart(responseId: $responseId) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return _=function(){return n},n}function $(){let n=(0,r._)(["\n  mutation AdjustThreadResponseChart(\n    $responseId: Int!\n    $data: AdjustThreadResponseChartInput!\n  ) {\n    adjustThreadResponseChart(responseId: $responseId, data: $data) {\n      ...CommonResponse\n    }\n  }\n  ","\n"]);return $=function(){return n},n}function q(){let n=(0,r._)(["\n  query AdjustmentTask($taskId: String!) {\n    adjustmentTask(taskId: $taskId) {\n      queryId\n      status\n      error {\n        code\n        shortMessage\n        message\n        stacktrace\n      }\n      sql\n      traceId\n      invalidSql\n    }\n  }\n"]);return q=function(){return n},n}function L(){let n=(0,r._)(["\n  mutation CancelAdjustmentTask($taskId: String!) {\n    cancelAdjustmentTask(taskId: $taskId)\n  }\n"]);return L=function(){return n},n}function Q(){let n=(0,r._)(["\n  mutation RerunAdjustmentTask($responseId: Int!) {\n    rerunAdjustmentTask(responseId: $responseId)\n  }\n"]);return Q=function(){return n},n}let H=(0,s.Ps)(a()),Z=(0,s.Ps)(o(),H),U=(0,s.Ps)(i(),H),O=(0,s.Ps)(u()),B=(0,s.Ps)(d(),H),M=(0,s.Ps)(l(),Z,U,O,B,H),z=(0,s.Ps)(c(),H);(0,s.Ps)(m()),(0,s.Ps)(p(),B),(0,s.Ps)(I());let X=(0,s.Ps)(f(),M);(0,s.Ps)(h(),M),(0,s.Ps)(N()),(0,s.Ps)(g()),(0,s.Ps)(v()),(0,s.Ps)(x()),(0,s.Ps)(T(),M),(0,s.Ps)(E()),(0,s.Ps)(R(),M),(0,s.Ps)(y(),M),(0,s.Ps)(k()),(0,s.Ps)(S()),(0,s.Ps)(j()),(0,s.Ps)(A()),(0,s.Ps)(F()),(0,s.Ps)(P(),z),(0,s.Ps)(C(),z),(0,s.Ps)(b(),z),(0,s.Ps)(w()),(0,s.Ps)(D()),(0,s.Ps)(G(),M),(0,s.Ps)(_(),M),(0,s.Ps)($(),M),(0,s.Ps)(q()),(0,s.Ps)(L()),(0,s.Ps)(Q())},90536:function(n,e,t){t.d(e,{Z:function(){return u}});var r=t(85893),s=t(19521),a=t(25332),o=t(4958);let i=(0,s.ZP)(a.UG).withConfig({displayName:"MarkdownBlock__ReactMarkdownBlock",componentId:"sc-99fa7ae9-0"})(["h1,h2,h3,h4,h5,h6{color:var(--gray-10);margin-bottom:8px;}h1{font-size:20px;}h2{font-size:18px;}h3{font-size:16px;}h4{font-size:14px;}hr{border-top:1px solid var(--gray-5);border-bottom:none;border-left:none;border-right:none;margin:18px 0;}pre{background-color:var(--gray-2);border:1px var(--gray-4) solid;padding:16px;border-radius:4px;}table td,table th{border:1px solid var(--gray-4);padding:4px 8px;}table th{background-color:var(--gray-2);font-weight:600;}table{border:1px solid var(--gray-4);border-collapse:collapse;margin-bottom:16px;}ol,ul,dl{padding-inline-start:20px;}h1 code,h2 code,h3 code,h4 code,li code,p code{font-size:12px;background:var(--gray-4);color:var(--gray-8);padding:2px 4px;border-radius:4px;}"]);function u(n){return(0,r.jsx)(i,{remarkPlugins:[o.Z],children:n.content})}},22546:function(n,e,t){t.d(e,{Z:function(){return f},j:function(){return p}});var r=t(85893),s=t(90512),a=t(19521),o=t(67294),i=t(81116),u=t(76774),d=t.n(u),l=t(77840),c=t(46140);let m=(0,a.ZP)(i.Z).withConfig({displayName:"RecommendedQuestions__StyledSkeleton",componentId:"sc-2ba47ac7-0"})(["padding:4px 0;.ant-skeleton-paragraph{margin-bottom:0;li{height:14px;+ li{margin-top:12px;}}}"]),p=function(n){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!n||!e)return{show:!1};let t=((null==n?void 0:n.questions)||[]).slice(0,3).map(n=>({question:n.question,sql:n.sql})),r=(null==n?void 0:n.status)===c.Tj.GENERATING;return{show:r||t.length>0,state:{items:t,loading:r,error:null==n?void 0:n.error}}},I=(0,l.x)(n=>{let{index:e,question:t,sql:a,onSelect:o}=n;return(0,r.jsx)("div",{className:(0,s.Z)(e>0&&"mt-1"),children:(0,r.jsx)("span",{className:"cursor-pointer hover:text",onClick:()=>o({question:t,sql:a}),children:t})})});function f(n){let{items:e,loading:t,className:a,onSelect:i}=n,u=(0,o.useMemo)(()=>e.map(n=>{let{question:e,sql:t}=n;return{question:e,sql:t}}),[e]);return(0,r.jsxs)("div",{className:(0,s.Z)("bg-gray-2 rounded p-3",a),children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)(d(),{className:"mr-1 gray-6"}),(0,r.jsx)("b",{className:"text-semi-bold text-sm gray-7",children:"Recommended questions"})]}),(0,r.jsx)("div",{className:"pl-1 gray-8",children:(0,r.jsx)(m,{active:!0,loading:t,paragraph:{rows:3},title:!1,children:(0,r.jsx)(I,{data:u,onSelect:i})})})]})}},17778:function(n,e,t){let r;t.d(e,{Z:function(){return z}});var s=t(85893),a=t(67294),o=t(19521),i=t(84908),u=t(21367),d=t(98885),l=t(83846);let c=(0,o.ZP)(u.default).withConfig({displayName:"Input__PromptButton",componentId:"sc-4c88e34d-0"})(["min-width:72px;"]);function m(n){let{onAsk:e,isProcessing:t,question:r,inputProps:o}=n,i=(0,a.useRef)(null),[u,m]=(0,a.useState)(""),[p,I]=(0,a.useState)(!1);(0,a.useEffect)(()=>{r&&m(r)},[r]),(0,a.useEffect)(()=>{if(!t){var n;null===(n=i.current)||void 0===n||n.focus(),m("")}},[t]);let f=()=>{let n=u.trim();n&&(0,l.iZ)(e,I)(n)},h=p||t;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.default.TextArea,{ref:i,"data-gramm":"false",size:"large",autoSize:!0,value:u,onInput:n=>{m(n.target.value)},onPressEnter:n=>{n.shiftKey||(n.preventDefault(),f())},disabled:h,...o}),(0,s.jsx)(c,{type:"primary",size:"large",className:"ml-3",onClick:f,disabled:h,children:"Ask"})]})}var p=t(90512),I=t(12155),f=t.n(I),h=t(49374),N=t.n(h),g=t(63626),v=t.n(g),x=t(63879),T=t.n(x),E=t(30046),R=t.n(E),y=t(50604),k=t.n(y),S=t(59976),j=t(18310),A=t.n(j),F=t(22546),P=t(90536),C=t(46140);let b=o.ZP.div.withConfig({displayName:"Result__StyledResult",componentId:"sc-a8a5bd3f-0"})(["position:absolute;bottom:calc(100% + 12px);left:0;width:100%;background:white;box-shadow:rgba(0,0,0,0.1) 0px 10px 15px -3px,rgba(0,0,0,0.05) 0px 4px 6px -2px;"]),w=n=>{let{children:e}=n;return(0,s.jsx)(b,{className:"border border-gray-3 rounded p-4","data-testid":"prompt__result",children:e})},D=n=>e=>{let{onClose:t,onSelectRecommendedQuestion:r,data:a,error:o}=e,{message:i,shortMessage:d,stacktrace:l}=o||{},c=(0,F.j)(null==a?void 0:a.recommendedQuestions);return(0,s.jsxs)(w,{children:[(0,s.jsxs)("div",{className:"d-flex justify-space-between text-medium mb-2",children:[(0,s.jsxs)("div",{className:"d-flex align-center",children:[n.icon,n.title||d]}),(0,s.jsxs)(u.default,{className:"adm-btn-no-style gray-7 bg-gray-3 text-sm px-2",type:"text",size:"small",onClick:t,children:[(0,s.jsx)(f(),{className:"-mr-1"}),"Close"]})]}),(0,s.jsx)("div",{className:"gray-7",children:n.description||a.intentReasoning||i}),!!l&&(0,s.jsx)(S.Z,{className:"mt-2",message:l.join("\n")}),c.show&&(0,s.jsx)(F.Z,{className:"mt-2",...c.state,onSelect:r})]})},G=D({icon:(0,s.jsx)(()=>(0,s.jsx)(T(),{className:"mr-2 red-5 text-lg"}),{})}),_=(r="Understanding question",n=>{let{onStop:e}=n,[t,o]=(0,a.useState)(!1);return(0,s.jsx)(w,{children:(0,s.jsxs)("div",{className:"d-flex justify-space-between",children:[(0,s.jsxs)("span",{children:[(0,s.jsx)(v(),{className:"mr-2 geekblue-6 text-lg",spin:!0}),r]}),(0,s.jsxs)(u.default,{className:(0,p.Z)("adm-btn-no-style bg-gray-3 text-sm px-2",t?"gray-6":"gray-7"),type:"text",size:"small",onClick:(0,l.iZ)(e,o),disabled:t,children:[(0,s.jsx)(N(),{className:"-mr-1"}),"Stop"]})]})})}),$=n=>{let{data:e,onIntentSQLAnswer:t}=n,{type:r}=e;return(0,a.useEffect)(()=>{r===C.oB.TEXT_TO_SQL&&t&&t()},[r]),(0,s.jsx)(_,{...n})},q=n=>{let{onClose:e,onSelectRecommendedQuestion:t,data:r,loading:o}=n,i=(0,a.useRef)(null),{originalQuestion:d,askingStreamTask:l,recommendedQuestions:c}=r,m=l&&!o,p=()=>{i.current&&i.current.scrollTo({top:i.current.scrollHeight})};(0,a.useEffect)(()=>{p()},[l]),(0,a.useEffect)(()=>{m&&p()},[m]);let I=(0,F.j)(c);return(0,s.jsxs)(w,{children:[(0,s.jsxs)("div",{className:"d-flex justify-space-between",children:[(0,s.jsxs)("div",{className:"d-flex align-start",children:[(0,s.jsx)(k(),{className:"mr-2 mt-1 geekblue-6"}),(0,s.jsx)("b",{className:"text-semi-bold",children:d})]}),(0,s.jsxs)(u.default,{className:"adm-btn-no-style gray-7 bg-gray-3 text-sm px-2",type:"text",size:"small",onClick:e,children:[(0,s.jsx)(f(),{className:"-mr-1"}),"Close"]})]}),(0,s.jsx)("div",{className:"py-3",children:(0,s.jsxs)("div",{ref:i,className:"py-2 px-3",style:{maxHeight:"calc(100vh - 420px)",overflowY:"auto"},children:[(0,s.jsx)(P.Z,{content:l}),m&&(0,s.jsxs)("div",{className:"gray-6",children:[(0,s.jsx)(A(),{className:"mr-2"}),"For the most accurate semantics, please visit the modeling page."]})]})}),I.show&&(0,s.jsx)(F.Z,{...I.state,onSelect:t})]})},L=D({icon:(0,s.jsx)(R(),{className:"mr-2 text-lg gold-6"}),title:"Clarification needed"}),Q=n=>({[i.F4.FINISHED]:q})[n]||null,H=n=>({[i.F4.FINISHED]:L})[n]||null,Z=n=>({[i.F4.UNDERSTANDING]:_,[i.F4.SEARCHING]:$,[i.F4.PLANNING]:$,[i.F4.GENERATING]:$,[i.F4.FINISHED]:$,[i.F4.FAILED]:G})[n]||null,U=n=>n===C.oB.GENERAL?Q:n===C.oB.MISLEADING_QUERY?H:Z;var O=(0,a.memo)(function(n){let{processState:e,data:t}=n,r=U(null==t?void 0:t.type)(e);return null===r?null:(0,s.jsx)(r,{...n})}),B=t(33520);let M=o.ZP.div.withConfig({displayName:"prompt__PromptStyle",componentId:"sc-5b784cfe-0"})(["position:fixed;width:680px;left:50%;margin-left:calc(-340px + 133px);bottom:18px;z-index:999;box-shadow:rgba(0,0,0,0.1) 0px 10px 15px -3px,rgba(0,0,0,0.05) 0px 4px 6px -2px;"]);var z=(0,a.forwardRef)(function(n,e){let{data:t,loading:r,onSubmit:o,onStop:u,onCreateResponse:d,onStopStreaming:l,onStopRecommend:c,inputProps:p}=n,I=(0,B.ZP)(),{originalQuestion:f,askingTask:h,askingStreamTask:N,recommendedQuestions:g}=t,v=(0,a.useMemo)(()=>({type:null==h?void 0:h.type,originalQuestion:f,askingStreamTask:N,recommendedQuestions:g,intentReasoning:(null==h?void 0:h.intentReasoning)||""}),[t]),x=(0,a.useMemo)(()=>(null==h?void 0:h.error)||null,[null==h?void 0:h.error]),[T,E]=(0,a.useState)(!1),[R,y]=(0,a.useState)(""),k=(0,a.useMemo)(()=>I.currentState,[I.currentState]),S=(0,a.useMemo)(()=>(0,B.c1)(k),[k]);(0,a.useEffect)(()=>{if(h){let n=I.matchedState(h);I.transitionTo(n)}},[h]),(0,a.useEffect)(()=>{x&&(I.isFailed()||I.transitionTo(i.F4.FAILED))},[x]);let j=async n=>{d&&await d(n),F()},A=async()=>{d&&await d({question:R,taskId:null==h?void 0:h.queryId}),E(!1)},F=()=>{I.resetState(),y(""),l&&l(),c&&c()},P=async()=>{u&&await u(),E(!1),I.resetState()},C=async n=>{y(n),!S&&n&&(I.transitionTo(i.F4.UNDERSTANDING),E(!0),o&&await o(n))};return(0,a.useImperativeHandle)(e,()=>({submit:C,close:F}),[R,S,y]),(0,s.jsxs)(M,{className:"d-flex align-end bg-gray-2 p-3 border border-gray-3 rounded",children:[(0,s.jsx)(m,{question:R,isProcessing:S,onAsk:C,inputProps:p}),T&&(0,s.jsx)(O,{data:v,error:x,loading:r,processState:k,onSelectRecommendedQuestion:j,onIntentSQLAnswer:A,onClose:F,onStop:P})]})})},33520:function(n,e,t){t.d(e,{Xq:function(){return i},ZI:function(){return d},ZP:function(){return u},c1:function(){return o}});var r=t(67294),s=t(84908),a=t(46140);let o=n=>[s.F4.UNDERSTANDING,s.F4.SEARCHING,s.F4.PLANNING,s.F4.GENERATING,s.F4.CORRECTING].includes(n),i=n=>{if(!n)return null;let e={[a.j.UNDERSTANDING]:s.F4.UNDERSTANDING,[a.j.SEARCHING]:s.F4.SEARCHING,[a.j.PLANNING]:s.F4.PLANNING,[a.j.GENERATING]:s.F4.GENERATING,[a.j.CORRECTING]:s.F4.CORRECTING,[a.j.FINISHED]:s.F4.FINISHED,[a.j.STOPPED]:s.F4.STOPPED,[a.j.FAILED]:s.F4.FAILED}[n.status];return(null==n?void 0:n.type)===a.oB.TEXT_TO_SQL&&e===s.F4.FINISHED&&0===n.candidates.length?s.F4.NO_RESULT:e};function u(){let[n,e]=(0,r.useState)(s.F4.IDLE);return{currentState:n,resetState:()=>{e(s.F4.IDLE)},matchedState:e=>{let t=i(e);return t&&t!==n?d.canTransition(n,t)?t:(console.warn("Invalid transition from ".concat(n," to ").concat(t,".")),n):n},transitionTo:n=>{e(n)},isFinished:()=>n===s.F4.FINISHED,isFailed:()=>n===s.F4.FAILED}}class d{static canTransition(n,e){var t;return n===s.F4.IDLE||e===s.F4.FINISHED||e===s.F4.FAILED||e===s.F4.STOPPED||(null===(t=this.transitions[n])||void 0===t?void 0:t.next.includes(e))}static getAllNextStates(n){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=new Set(e?[n]:[]),r=n=>{var e;((null===(e=this.transitions[n])||void 0===e?void 0:e.next)||[]).forEach(n=>{t.has(n)||(t.add(n),r(n))})};return r(n),Array.from(t)}}d.transitions={[s.F4.IDLE]:{next:[s.F4.UNDERSTANDING],prev:[]},[s.F4.UNDERSTANDING]:{next:[s.F4.SEARCHING,s.F4.PLANNING,s.F4.GENERATING],prev:[s.F4.IDLE]},[s.F4.SEARCHING]:{next:[s.F4.PLANNING],prev:[s.F4.UNDERSTANDING]},[s.F4.PLANNING]:{next:[s.F4.GENERATING],prev:[s.F4.SEARCHING]},[s.F4.GENERATING]:{next:[s.F4.CORRECTING,s.F4.FINISHED,s.F4.FAILED],prev:[s.F4.PLANNING]},[s.F4.CORRECTING]:{next:[s.F4.FINISHED,s.F4.FAILED],prev:[s.F4.GENERATING]},[s.F4.FINISHED]:{next:[],prev:[s.F4.GENERATING,s.F4.CORRECTING]}}},55023:function(n,e,t){t.d(e,{di:function(){return I},rP:function(){return p},ZP:function(){return x},eX:function(){return m},pd:function(){return f}});var r=t(67294),s=t(50361),a=t.n(s),o=t(44908),i=t.n(o),u=t(46140),d=t(77491),l=t(98683),c=t(36238);let m=n=>[u.j.FINISHED,u.j.FAILED,u.j.STOPPED].includes(n),p=(n,e)=>null===n&&null===e||(null==n?void 0:n.status)===u.j.FINISHED||(null==e?void 0:e.status)===u.j.FINISHED,I=n=>null!==n&&(null==n?void 0:n.status)!==u.j.FAILED&&(null==n?void 0:n.status)!==u.j.STOPPED,f=n=>[u.Tj.FINISHED,u.Tj.FAILED,u.Tj.NOT_STARTED].includes(n),h=n=>{let e=[u.oB.GENERAL,u.oB.MISLEADING_QUERY].includes(null==n?void 0:n.type),t=(null==n?void 0:n.type)!==u.oB.TEXT_TO_SQL&&(null==n?void 0:n.status)===u.j.FAILED;return e||t},N=n=>(null==n?void 0:n.type)===u.oB.TEXT_TO_SQL,g=(n,e,t)=>{if(!e)return;let r=t.cache.readQuery({query:l.i,variables:{threadId:n}});(null==r?void 0:r.thread)&&t.cache.updateQuery({query:l.i,variables:{threadId:n}},n=>({thread:{...n.thread,responses:n.thread.responses.map(n=>{var t;return(null===(t=n.askingTask)||void 0===t?void 0:t.queryId)===(null==e?void 0:e.queryId)?{...n,askingTask:a()(e)}:n})}}))},v=(n,e,t,r)=>{if(!t)return;let s=r.cache.readQuery({query:l.i,variables:{threadId:n}});if(null==s?void 0:s.thread){let s=a()(t);s.status===u.j.UNDERSTANDING&&(s.status=u.j.SEARCHING,s.type=u.oB.TEXT_TO_SQL),r.cache.updateQuery({query:l.i,variables:{threadId:n}},n=>({thread:{...n.thread,responses:n.thread.responses.map(n=>n.id===e?{...n,askingTask:s}:n)}}))}};function x(n){let[e,t]=(0,r.useState)(""),[s,a]=(0,r.useState)([]),[o,l]=(0,d.Gf)(),[p]=(0,d.wU)(),[I]=(0,d.vt)(),[x,T]=(0,d.Rh)({pollInterval:1e3}),[E,R]=function(){let n=(0,r.useRef)(null),[e,t]=(0,r.useState)(!1),[s,a]=(0,r.useState)(""),o=()=>{if(n.current){var e;null===(e=n.current)||void 0===e||e.close(),n.current=null}a("")};return[e=>{t(!0),o();let r=new EventSource("/api/ask_task/streaming?queryId=".concat(e));r.onmessage=n=>{let e=JSON.parse(n.data);e.done?(r.close(),t(!1)):a(n=>n+((null==e?void 0:e.message)||""))},r.onerror=n=>{console.error(n),r.close(),t(!1)},n.current=r},{data:s,loading:e,reset:o}]}(),[y]=(0,d.eO)(),[k,S]=(0,d.hC)({pollInterval:1e3}),j=(0,r.useMemo)(()=>{var n;return(null===(n=T.data)||void 0===n?void 0:n.askingTask)||null},[T.data]),A=(0,r.useMemo)(()=>null==j?void 0:j.type,[null==j?void 0:j.type]),F=R.data,P=(0,r.useMemo)(()=>{var n;return(null===(n=S.data)||void 0===n?void 0:n.instantRecommendedQuestions)||null},[S.data]),C=R.loading,b=(0,r.useMemo)(()=>({originalQuestion:e,askingTask:j,askingStreamTask:F,recommendedQuestions:P}),[e,j,F,P]),w=(0,r.useCallback)(async()=>{let n=[...i()(s).slice(-5),e];k({variables:{taskId:(await y({variables:{data:{previousQuestions:n}}})).data.createInstantRecommendedQuestions.id}})},[e]),D=(0,r.useCallback)(n=>{F||n.status!==u.j.PLANNING||E(n.queryId)},[F]);return(0,r.useEffect)(()=>{m(null==j?void 0:j.status)&&T.stopPolling(),N(j)&&n&&(g(n,j,T.client),D(j))},[null==j?void 0:j.status,n,D]),(0,r.useEffect)(()=>{h(j)&&w()},[null==j?void 0:j.type]),(0,r.useEffect)(()=>{f(null==P?void 0:P.status)&&S.stopPolling()},[P]),(0,r.useEffect)(()=>{var n;let e=null===(n=l.data)||void 0===n?void 0:n.createAskingTask.id;e&&A===u.oB.GENERAL&&E(e)},[A,l.data]),{data:b,loading:C,onStop:async n=>{var e;let t=n||(null===(e=l.data)||void 0===e?void 0:e.createAskingTask.id);t&&(await p({variables:{taskId:t}}).catch(n=>console.error(n)),await (0,c.Y3)(1e3))},onReRun:async e=>{R.reset(),t(e.question);try{let t=await I({variables:{responseId:e.id}}),{data:r}=await x({variables:{taskId:t.data.rerunAskingTask.id}});v(n,e.id,r.askingTask,T.client)}catch(n){console.error(n)}},onSubmit:async e=>{R.reset(),t(e);try{let t=await o({variables:{data:{question:e,threadId:n}}});await x({variables:{taskId:t.data.createAskingTask.id}})}catch(n){console.error(n)}},onFetching:async n=>{await x({variables:{taskId:n}})},onStopPolling:()=>T.stopPolling(),onStopStreaming:()=>R.reset(),onStopRecommend:()=>S.stopPolling(),onStoreThreadQuestions:n=>a(n),inputProps:{placeholder:n?"Ask follow-up questions to explore your data":"Ask to explore your data"}}}},83846:function(n,e,t){t.d(e,{Mo:function(){return u},iZ:function(){return d},yg:function(){return i}});var r=t(14176),s=t.n(r),a=t(52353),o=t.n(a);let i=n=>s()(n,o()),u=n=>{try{return JSON.parse(n)}catch(e){return n}},d=(n,e)=>async function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];e(!0);try{await n(...r)}finally{e(!1)}}}}]);