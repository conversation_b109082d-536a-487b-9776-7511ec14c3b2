"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[916],{25707:function(e,t,i){i.d(t,{H:function(){return d},z:function(){return c}});var n=i(85893),l=i(21367),a=i(4071),r=i.n(a),o=i(39616);let s=e=>t=>{let{onClick:i,onMouseEnter:a,onMouseLeave:r,className:o,marginLeft:s,marginRight:d,...c}=t;return(0,n.jsx)(l.default,{className:o,style:{marginLeft:s,marginRight:d},icon:e,onClick:e=>{i&&i(e),e.stopPropagation()},onMouseEnter:e=>{a&&a(e),e.stopPropagation()},onMouseLeave:e=>{r&&r(e),e.stopPropagation()},type:"text",size:"small",...c})},d=s((0,n.jsx)(r(),{})),c=s((0,n.jsx)(o.nX,{}))},4791:function(e,t,i){i.d(t,{At:function(){return N},Lb:function(){return C},aV:function(){return D},iI:function(){return w},px:function(){return I},rt:function(){return _},s_:function(){return b},uu:function(){return H}});var n=i(85893);i(67294);var l=i(19521),a=i(69371),r=i(59046),o=i(84908),s=i(93181),d=i.n(s),c=i(7337),h=i.n(c),m=i(80112),u=i.n(m),g=i(92870),x=i.n(g),p=i(31499),f=i.n(p),j=i(40492),L=i.n(j),y=i(32815),E=i(26123);let v=(0,l.ZP)(a.default).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),k=e=>t=>{let{children:i,onMenuEnter:l,onDropdownVisibleChange:a}=t,o=e(t);return(0,n.jsx)(r.default,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:(0,n.jsx)(v,{onClick:e=>e.domEvent.stopPropagation(),items:o,onMouseEnter:l}),onVisibleChange:a,children:i})},N=k(e=>{let{onMoreClick:t}=e;return[{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d(),{className:"mr-2"}),"Update Columns"]}),key:o.dI.UPDATE_COLUMNS,onClick:()=>t(o.dI.UPDATE_COLUMNS)},{label:(0,n.jsx)(E.ZQ,{onConfirm:()=>t(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),C=k(e=>{let{onMoreClick:t}=e;return[{label:(0,n.jsx)(E.e0,{onConfirm:()=>t(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),b=k(e=>{let{onMoreClick:t,data:i}=e,{nodeType:l}=i,a={[o.QZ.CALCULATED_FIELD]:E.pn,[o.QZ.RELATION]:E.ck}[l]||E.pn;return[{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>t(o.dI.EDIT)},{label:(0,n.jsx)(a,{onConfirm:()=>t(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),I=k(e=>{let{onMoreClick:t,isSupportCached:i}=e;return[i&&{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(L(),{className:"mr-2"}),"Cache settings"]}),key:o.dI.CACHE_SETTINGS,onClick:()=>t(o.dI.CACHE_SETTINGS)},{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h(),{className:"mr-2"}),i?"Refresh all caches":"Refresh all"]}),key:o.dI.REFRESH,onClick:()=>t(o.dI.REFRESH)}].filter(Boolean)}),_=k(e=>{let{onMoreClick:t,isHideLegend:i,isSupportCached:l}=e;return[{label:i?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x(),{className:"mr-2"}),"Show categories"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u(),{className:"mr-2"}),"Hide categories"]}),key:o.dI.HIDE_CATEGORY,onClick:()=>t(o.dI.HIDE_CATEGORY)},{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h(),{className:"mr-2"}),l?"Refresh cache":"Refresh"]}),key:o.dI.REFRESH,onClick:()=>t(o.dI.REFRESH)},{label:(0,n.jsx)(E.mU,{onConfirm:()=>t(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),w=k(e=>{let{onMoreClick:t,data:i}=e;return[{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x(),{className:"mr-2"}),"View"]}),key:o.dI.VIEW_SQL_PAIR,onClick:()=>t({type:o.dI.VIEW_SQL_PAIR,data:i})},{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>t({type:o.dI.EDIT,data:i})},{label:(0,n.jsx)(E.pv,{onConfirm:()=>t({type:o.dI.DELETE,data:i}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),H=k(e=>{let{onMoreClick:t,data:i}=e;return[{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x(),{className:"mr-2"}),"View"]}),key:o.dI.VIEW_INSTRUCTION,onClick:()=>t({type:o.dI.VIEW_INSTRUCTION,data:i})},{label:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>t({type:o.dI.EDIT,data:i})},{label:(0,n.jsx)(E.py,{onConfirm:()=>t({type:o.dI.DELETE,data:i}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:o.dI.DELETE,onClick:e=>{let{domEvent:t}=e;return t.stopPropagation()}}]}),D=k(e=>{let{onMoreClick:t,data:i}=e;return[{label:"Adjust steps",icon:(0,n.jsx)(y.Wi,{}),disabled:!i.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>t({type:o.dI.ADJUST_STEPS,data:i})},{label:"Adjust SQL",icon:(0,n.jsx)(f(),{className:"text-base"}),disabled:!i.sql,key:"adjust-sql",onClick:()=>t({type:o.dI.ADJUST_SQL,data:i})}]})},22916:function(e,t,i){i.r(t),i.d(t,{default:function(){return ei}});var n=i(85893),l=i(67294),a=i(36851),r=i(70866),o=i(24885),s=i(59819),d=i(80002);let c=e=>e.split("_")[0],h=(e,t)=>i=>i.map(i=>{let n="_selected",l=i.markerStart.replace(n,""),a=i.markerEnd.replace(n,"");return e.includes(i.id)?{...i,data:{...i.data,highlight:t},markerStart:l+n,markerEnd:a+n}:{...i,data:{...i.data,highlight:!1},markerStart:l,markerEnd:a}}),m=(e,t)=>i=>i.map(i=>e.includes(i.id)?{...i,data:{...i.data,highlight:t}}:{...i,data:{...i.data,highlight:[]}});var u=i(39616),g=i(29060),x=i(19521);let p=x.ZP.div.withConfig({displayName:"utils__StyledNode",componentId:"sc-8b78834f-0"})(["position:relative;width:200px;border-radius:4px;overflow:hidden;box-shadow:0px 3px 6px -4px rgba(0,0,0,0.12),0px 6px 16px rgba(0,0,0,0.08),0px 9px 28px 8px rgba(0,0,0,0.05);cursor:pointer;&:before{content:'';pointer-events:none;position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;border:2px solid transparent;transition:border-color 0.15s ease-in-out;}&:hover,&:focus{&:before{border-color:var(--geekblue-6);}}.react-flow__handle{border:none;opacity:0;&-left{left:0;}&-right{right:0;}}"]),f=x.ZP.div.withConfig({displayName:"utils__NodeHeader",componentId:"sc-8b78834f-1"})(["position:relative;background-color:",";font-size:14px;color:white;padding:6px 8px;display:flex;align-items:center;justify-content:space-between;height:36px;&.dragHandle{cursor:move;}.adm-model-header{display:flex;align-items:center;svg{margin-right:6px;}+ svg{cursor:pointer;}.ant-typography{width:140px;color:white;}}"],e=>e.color||"var(--geekblue-6)"),j=x.ZP.div.withConfig({displayName:"utils__NodeBody",componentId:"sc-8b78834f-2"})(["background-color:white;padding-bottom:4px;"]),L=e=>{let{originalData:t}=e;return t.cached?(0,n.jsx)(g.default,{title:(0,n.jsxs)(n.Fragment,{children:["Cached",t.refreshTime?": refresh every ".concat(t.refreshTime):null]}),placement:"top",children:(0,n.jsx)(u.Bk,{className:"cursor-pointer"})}):null};function y(e){let{id:t}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.HH,{type:"source",position:a.Ly.Left,id:"".concat(t,"_").concat(a.Ly.Left)}),(0,n.jsx)(a.HH,{type:"source",position:a.Ly.Right,id:"".concat(t,"_").concat(a.Ly.Right)}),(0,n.jsx)(a.HH,{type:"target",position:a.Ly.Left,id:"".concat(t,"_").concat(a.Ly.Left)}),(0,n.jsx)(a.HH,{type:"target",position:a.Ly.Right,id:"".concat(t,"_").concat(a.Ly.Right)})]})}let E=(0,l.createContext)({onMoreClick:()=>{},onNodeClick:()=>{},onAddClick:()=>{}}),v=x.ZP.div.withConfig({displayName:"Column__NodeColumn",componentId:"sc-b3b66940-0"})(["position:relative;display:flex;align-items:center;justify-content:space-between;padding:4px 8px;color:var(--gray-9);line-height:24px;&:hover{background-color:var(--gray-3);}svg{flex-shrink:0;}.adm-column-title{display:flex;align-items:center;min-width:1px;svg{margin-right:6px;}> span{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}}"]),k=x.ZP.div.withConfig({displayName:"Column__Title",componentId:"sc-b3b66940-1"})(["display:flex;justify-content:space-between;align-items:center;color:var(--gray-8);padding:4px 12px;cursor:default;"]);function N(e){let{id:t,type:i,onMouseEnter:l,onMouseLeave:a,displayName:r,style:o={},icon:s,extra:d}=e;return(0,n.jsxs)(v,{style:o,onMouseEnter:l,onMouseLeave:a,children:[(0,n.jsxs)("div",{className:"adm-column-title",children:[(0,n.jsx)("span",{className:"d-inline-flex flex-shrink-0",title:i,children:s}),(0,n.jsx)("span",{title:r,children:r})]}),d,(0,n.jsx)(y,{id:t.toString()})]})}N.Title=e=>{let{show:t,extra:i,children:l}=e;return t?(0,n.jsxs)(k,{children:[l,(0,n.jsx)("span",{children:i})]}):null},N.MoreTip=e=>(0,n.jsxs)("div",{className:"text-sm gray-7 px-3 py-1",children:["and ",e.count," more"]});var C=i(70474),b=i(77840),I=i(84908);let _={nodesInRow:4,width:200,height:void 0,headerHeight:32,columnHeight:32,moreTipHeight:25,columnsLimit:10,bodyOverflow:"auto",marginX:100,marginY:50,modelNodePreservedHeight:96,viewNodePreservedHeight:32},w=e=>e?1:0,H=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=e.length>_.columnsLimit,i=t?_.columnsLimit:e.length;return{isOverLimit:t,limitedLength:i,originalLength:e.length}};class D{init(){for(let e of[...this.models,...this.views])this.addOne(e)}addOne(e){let{nodeType:t}=e,i=this.start.x,n=this.start.y,l=this.createNode({nodeType:t,data:e,x:i,y:n});this.nodes.push(l),this.updateNextStartedPoint()}updateNextStartedPoint(){let e=this.getNodeWidth(),t=0,{length:i}=this.nodes,{marginX:n,marginY:l,nodesInRow:a}=this.config,r=i%a==0;if(r){this.start.floor++;let e=a*(this.start.floor-1),i=[...this.models,...this.views].slice(e,e+4),n=i.reduce((e,t)=>[...e.fields,...(null==e?void 0:e.calculatedFields)||[],...(null==t?void 0:t.relationFields)||[]].length>[...t.fields,...(null==t?void 0:t.calculatedFields)||[],...(null==t?void 0:t.relationFields)||[]].length?e:t,i[0]);t=this.getNodeHeight(n)+l}this.start.x=this.start.x+e+n,r&&(this.start.x=0),this.start.y=this.start.y+t}createNode(e){let{nodeType:t,data:i,x:n,y:l}=e;return t===I.QZ.MODEL&&this.addModelEdge(i),{id:i.id,type:t,position:{x:n,y:l},dragHandle:".dragHandle",data:{originalData:i,index:this.nodes.length,highlight:[]}}}addModelEdge(e){let{relationFields:t}=e;for(let i of t){if(this.edges.some(e=>{var t,n;return(null===(n=e.data)||void 0===n?void 0:null===(t=n.relation)||void 0===t?void 0:t.relationId)===i.relationId}))continue;let t=this.models.find(t=>t.id!==e.id&&[i.fromModelName,i.toModelName].includes(t.referenceName));if(!t)continue;let n=t.relationFields.find(e=>["".concat(e.fromModelName,".").concat(e.fromColumnName),"".concat(e.toModelName,".").concat(e.toColumnName)].toString()===["".concat(i.fromModelName,".").concat(i.fromColumnName),"".concat(i.toModelName,".").concat(i.toColumnName)].toString()),l=[i.fromModelName,i.toModelName],a=l.findIndex(t=>t===e.referenceName),r=l.findIndex(e=>e===(null==t?void 0:t.referenceName));t&&this.edges.push(this.createEdge({type:I.Hk.MODEL,joinType:i.type,sourceModel:e,sourceField:i,sourceJoinIndex:a,targetModel:t,targetField:n,targetJoinIndex:r}))}}createEdge(e){let{type:t,sourceModel:i,sourceField:n,sourceJoinIndex:l,targetModel:a,targetField:r,targetJoinIndex:o,joinType:s,animated:d}=e,c=i.id,h=a.id,[m,u]=this.detectEdgePosition(c,h),g="".concat((null==n?void 0:n.id)||c,"_").concat(m),x="".concat((null==r?void 0:r.id)||h,"_").concat(u),p=this.getMarker(s,l,m),f=this.getMarker(s,o,u);return{id:"".concat(g,"_").concat(x),type:t,source:c,target:h,sourceHandle:g,targetHandle:x,markerStart:p,markerEnd:f,data:{relation:n,highlight:!1},animated:d}}getFloorIndex(e){let{nodesInRow:t}=this.config;return e%t}detectEdgePosition(e,t){let i=[],[n,l]=[...this.models].reduce((i,n,l)=>(n.id===e&&(i[0]=l),n.id===t&&(i[1]=l),i),[-1,-1]),r=this.getFloorIndex(n),o=this.getFloorIndex(l);return r===o?(i[0]=a.Ly.Left,i[1]=a.Ly.Left):r>o?(i[0]=a.Ly.Left,i[1]=a.Ly.Right):(i[0]=a.Ly.Right,i[1]=a.Ly.Left),i}getMarker(e,t,i){return(({[I.oD.ONE_TO_ONE]:[I.E6.ONE,I.E6.ONE],[I.oD.ONE_TO_MANY]:[I.E6.ONE,I.E6.MANY],[I.oD.MANY_TO_ONE]:[I.E6.MANY,I.E6.ONE]})[e]||[])[t]+(i?"_".concat(i):"")}getNodeWidth(){return this.config.width}getNodeHeight(e){let{height:t,headerHeight:i,columnHeight:n,moreTipHeight:l,modelNodePreservedHeight:a,viewNodePreservedHeight:r}=this.config,o={[I.QZ.MODEL]:a,[I.QZ.VIEW]:r}[e.nodeType],{limitedLength:s,isOverLimit:d}=H(e.fields),{limitedLength:c,isOverLimit:h}=H(null==e?void 0:e.calculatedFields),{limitedLength:m,isOverLimit:u}=H(null==e?void 0:e.relationFields);return i+(t||n*(s+c+m))+l*(w(d)+w(h)+w(u))+o+4}constructor(e){this.config=_,this.nodes=[],this.edges=[],this.start={x:0,y:0,floor:0},this.models=(null==e?void 0:e.models)||[],this.views=(null==e?void 0:e.views)||[],this.init()}}class M{toJsonObject(){return{nodes:this.nodes,edges:this.edges,viewport:this.viewport}}constructor(e){this.viewport={x:0,y:0,zoom:1};let t=new D(e);this.nodes=t.nodes,this.edges=t.edges}}var T=i(4791),R=i(25707);let{Text:F}=d.default;var S=(0,l.memo)(e=>{let{data:t}=e,i=(0,l.useContext)(E),a=e=>{null==i||i.onAddClick({targetNodeType:e,data:t.originalData})},r=(0,l.useCallback)(e=>A(e,t,{limit:_.columnsLimit}),[t.highlight]);return(0,n.jsxs)(p,{onClick:()=>{null==i||i.onNodeClick({data:t.originalData})},"data-testid":"diagram__model-node__".concat(t.originalData.displayName),"data-guideid":"model-".concat(t.index),children:[(0,n.jsxs)(f,{className:"dragHandle",children:[(0,n.jsxs)("span",{className:"adm-model-header",children:[(0,n.jsx)(u.ZA,{}),(0,n.jsx)(F,{ellipsis:!0,title:t.originalData.displayName,children:t.originalData.displayName})]}),(0,n.jsxs)("span",{children:[(0,n.jsx)(L,{originalData:t.originalData}),(0,n.jsx)(T.At,{data:t.originalData,onMoreClick:e=>{null==i||i.onMoreClick({type:e,data:t.originalData})},children:(0,n.jsx)(R.z,{className:"gray-1",marginRight:-4,"data-guideid":"edit-model-".concat(t.index)})})]}),(0,n.jsx)(y,{id:t.originalData.id.toString()})]}),(0,n.jsxs)(j,{draggable:!1,children:[(0,n.jsx)(N.Title,{show:!0,children:"Columns"}),r(t.originalData.fields),(0,n.jsx)(N.Title,{show:!0,extra:(0,n.jsx)(R.H,{className:"gray-8",marginRight:-8,onClick:()=>a(I.QZ.CALCULATED_FIELD)}),children:"Calculated Fields"}),r(t.originalData.calculatedFields),(0,n.jsx)(N.Title,{show:!0,extra:(0,n.jsx)(R.H,{className:"gray-8",marginRight:-8,onClick:()=>a(I.QZ.RELATION)}),children:"Relationships"}),r(t.originalData.relationFields)]})]})});let O=(0,b.x)(e=>{let{nodeType:t,id:i,type:r,isPrimaryKey:o,highlight:s}=e,d=t===I.QZ.RELATION,g=t===I.QZ.CALCULATED_FIELD,x=(0,a._K)(),p=(0,l.useContext)(E),f=(0,l.useCallback)(e=>{if(!d)return;let{getEdges:t,setEdges:n,setNodes:l}=x,a=t().find(e=>c(e.sourceHandle)===i||c(e.targetHandle)===i);a&&(n(h([null==a?void 0:a.id],!0)),l(m([a.source,a.target],[c(a.sourceHandle),c(a.targetHandle)])))},[x]),j=(0,l.useCallback)(e=>{if(!d)return;let{setEdges:t,setNodes:i}=x;t(h([],!1)),i(m([],[]))},[x]),L=(0,l.useCallback)(e=>{j(e)},[x]),y=(0,l.useCallback)(e=>{f(e)},[x]),v=(0,l.useCallback)(e=>{j(e)},[x]);return(0,l.createElement)(N,{...e,key:i,className:s.includes(i)?"bg-gray-3":void 0,icon:d?(0,n.jsx)(u.ZA,{}):(0,C.u)({type:r}),extra:(0,n.jsxs)(n.Fragment,{children:[o&&(0,n.jsx)(u.EU,{})," ",(g||d)&&(0,n.jsx)(T.s_,{data:e,onMoreClick:t=>{null==p||p.onMoreClick({type:t,data:e})},onMenuEnter:v,children:(0,n.jsx)(R.z,{className:"gray-8",marginRight:-4,onMouseEnter:L,onMouseLeave:y})})]}),onMouseLeave:j,onMouseEnter:f})}),A=(e,t,i)=>{let l=i?e.length-i.limit:0,a=i?e.slice(0,i.limit):e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(O,{data:a,highlight:t.highlight,modelId:t.originalData.modelId}),l>0&&(0,n.jsx)(N.MoreTip,{count:l})]})};var P=i(21367);let{Text:Z}=d.default;var V=(0,l.memo)(e=>{let{data:t}=e,i=(0,l.useContext)(E),a=(0,l.useCallback)(e=>(function(e,t,i){let l=i?e.length-i.limit:0,a=i?e.slice(0,i.limit):e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(W,{data:a,highlight:t.highlight}),l>0&&(0,n.jsx)(N.MoreTip,{count:l})]})})(e,t,{limit:_.columnsLimit}),[t.highlight]);return(0,n.jsxs)(p,{onClick:()=>{null==i||i.onNodeClick({data:t.originalData})},"data-testid":"diagram__view-node__".concat(t.originalData.displayName),children:[(0,n.jsxs)(f,{className:"dragHandle",color:"var(--green-6)",children:[(0,n.jsxs)("span",{className:"adm-model-header",children:[(0,n.jsx)(u.ON,{}),(0,n.jsx)(Z,{ellipsis:!0,title:t.originalData.displayName,children:t.originalData.displayName})]}),(0,n.jsx)("span",{children:(0,n.jsx)(T.Lb,{onMoreClick:e=>{null==i||i.onMoreClick({type:e,data:t.originalData})},children:(0,n.jsx)(P.default,{className:"gray-1",icon:(0,n.jsx)(u.nX,{}),onClick:e=>e.stopPropagation(),type:"text",size:"small"})})}),(0,n.jsx)(y,{id:t.originalData.id})]}),(0,n.jsx)(j,{draggable:!1,children:a(t.originalData.fields)})]})});let W=(0,b.x)(e=>{let{id:t,type:i}=e;return(0,l.createElement)(N,{...e,key:t,icon:(0,C.u)({type:i})})});var Y=i(16755),B=i(55041),Q=i(40582);function U(e){let{children:t}=e;return(0,n.jsx)(Y.Z,{...e,mouseLeaveDelay:0,overlayStyle:{maxWidth:520},children:t})}U.Row=Q.default,U.Col=e=>{let{title:t,children:i,code:l,span:a=24,marginBottom:r=8}=e;return(0,n.jsxs)(B.default,{span:a,children:[(0,n.jsx)("div",{className:"gray-7 mb-0",children:t}),(0,n.jsx)("div",{style:{marginBottom:r},children:(0,n.jsx)(d.default.Text,{code:l,children:i})})]})};var X=i(44280);let z=x.ZP.div.withConfig({displayName:"ModelEdge__Joint",componentId:"sc-4b47b209-0"})(["position:absolute;width:30px;height:30px;opacity:0;"]);var G=(0,l.memo)(e=>{let{sourceX:t,sourceY:i,targetX:r,targetY:o,sourcePosition:s,targetPosition:d,markerStart:c,markerEnd:h,data:m}=e,[u,g,x]=(0,a.OW)({sourceX:t,sourceY:i,sourcePosition:s,targetX:r,targetY:o,targetPosition:d}),p=m.highlight,f=(0,l.useMemo)(()=>{var e;let t="".concat(m.relation.fromModelName,".").concat(m.relation.fromColumnName),i="".concat(m.relation.toModelName,".").concat(m.relation.toColumnName);return{name:m.relation.name,joinType:(0,X.I)(m.relation.type),description:(null===(e=m.relation)||void 0===e?void 0:e.description)||"-",fromField:t,toField:i}},[m.relation]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.u5,{path:u,markerStart:c,markerEnd:h,style:p?{stroke:"var(--geekblue-6)",strokeWidth:1.5}:{stroke:"var(--gray-5)"}}),(0,n.jsx)(a.XQ,{children:(0,n.jsx)(U,{visible:p,title:"Relationship",content:(0,n.jsxs)(U.Row,{gutter:16,children:[(0,n.jsx)(U.Col,{title:"From",span:12,children:f.fromField}),(0,n.jsx)(U.Col,{title:"To",span:12,children:f.toField}),(0,n.jsx)(U.Col,{title:"Type",span:12,children:f.joinType}),(0,n.jsx)(U.Col,{title:"Description",children:f.description})]}),children:(0,n.jsx)(z,{style:{transform:"translate(-50%, -50%) translate(".concat(g,"px,").concat(x,"px)")}})})})]})});function J(){return(0,n.jsx)("svg",{style:{position:"absolute",top:0,left:0,width:0,height:0,zIndex:-1},children:(0,n.jsxs)("defs",{children:[(0,n.jsx)("marker",{id:"many_right",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:0,refY:11,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.28866 10L6.49577e-06 2.33206L1.4329e-05 -1.18499e-06L13.5547 11L2.869e-06 22L3.07287e-06 19.668L9.28864 12L5.65057e-06 12L5.82542e-06 10L9.28866 10Z",fill:"#b1b1b7"})}),(0,n.jsx)("marker",{id:"many_left",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:14,refY:11,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.26603 12L13.5547 19.6679L13.5547 22L0 11L13.5547 0V2.33204L4.26605 10L13.5547 10V12L4.26603 12Z",fill:"#b1b1b7"})}),(0,n.jsxs)("marker",{id:"one_right",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:-4,refY:11,children:[(0,n.jsx)("rect",{width:"1400",height:"993",transform:"translate(-407 -263)",fill:"none"}),(0,n.jsx)("rect",{x:"6",width:"2",height:"22",fill:"#b1b1b7"})]}),(0,n.jsxs)("marker",{id:"one_left",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:18,refY:11,children:[(0,n.jsx)("rect",{width:"1400",height:"993",transform:"translate(-407 -263)",fill:"none"}),(0,n.jsx)("rect",{x:"6",width:"2",height:"22",fill:"#b1b1b7"})]}),(0,n.jsx)("marker",{id:"many_right_selected",viewBox:"0 0 18 32",markerHeight:18,markerWidth:18,refX:0,refY:16,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.4161 4.94444L13.2993 8H14.7007L14.5839 4.94444L17.2993 6.58333L18 5.41667L15.1387 4L18 2.58333L17.2993 1.41667L14.5839 3.05556L14.7007 0H13.2993L13.4161 3.05556L10.7007 1.41667L10 2.58333L12.8613 4L10 5.41667L10.7007 6.58333L13.4161 4.94444ZM3.63475e-06 7.33206L9.28865 15L2.9644e-06 15L2.78955e-06 17L9.28863 17L0 24.668V27L13.5547 16L1.1468e-05 5L3.63475e-06 7.33206Z",fill:"#2F54EB"})}),(0,n.jsx)("marker",{id:"many_left_selected",viewBox:"0 0 18 32",markerHeight:18,markerWidth:18,refX:18,refY:16,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.41606 4.94444L3.29927 8H4.70073L4.58394 4.94444L7.29927 6.58333L8 5.41667L5.13869 4L8 2.58333L7.29927 1.41667L4.58394 3.05556L4.70073 0H3.29927L3.41606 3.05556L0.70073 1.41667L0 2.58333L2.86131 4L0 5.41667L0.70073 6.58333L3.41606 4.94444ZM17.8899 24.6679L8.60127 17H17.8899V15H8.60129L17.8899 7.33204V5L4.33524 16L17.8899 27L17.8899 24.6679Z",fill:"#2F54EB"})}),(0,n.jsx)("marker",{id:"one_right_selected",viewBox:"0 0 16 32",markerHeight:16,markerWidth:16,refX:0,refY:16,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 8V0H15.0211L13 1.32812V2.3125L14.9737 1.01563H15.0211V8H16ZM8.63351 5H6.63351V27H8.63351V5Z",fill:"#2F54EB"})}),(0,n.jsx)("marker",{id:"one_left_selected",viewBox:"0 0 16 32",markerHeight:16,markerWidth:16,refX:18,refY:16,children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 8V0H2.02105L0 1.32812V2.3125L1.97368 1.01563H2.02105V8H3ZM9 5H7V27H9V5Z",fill:"#2F54EB"})})]})})}var q=i(36238);i(73242);let K={[I.QZ.MODEL]:S,[I.QZ.VIEW]:V},$={[I.Hk.MODEL]:G},ee={height:120},et=(0,l.forwardRef)(function(e,t){let{data:i,onMoreClick:d,onNodeClick:g,onAddClick:x}=e,[p,f]=(0,l.useState)(!1),j=(0,a._K)();(0,l.useImperativeHandle)(t,()=>j,[j]);let L=(0,l.useMemo)(()=>new M(i).toJsonObject(),[i]);(0,l.useEffect)(()=>{v(L.nodes),C(L.edges),(0,q.Y3)(50).then(()=>j.fitView())},[L]);let[y,v,k]=(0,a.Rr)(L.nodes),[N,C,b]=(0,a.ll)(L.edges),I=(0,l.useCallback)((e,t)=>{C(h([t.id],!0)),v(m([t.source,t.target],[c(t.sourceHandle),c(t.targetHandle)]))},[]),_=(0,l.useCallback)((e,t)=>{C(h([],!1)),v(m([],[]))},[]),w=async()=>{v(L.nodes),C(L.edges)},H=async()=>{await (0,q.Y3)(),j.fitView(),await (0,q.Y3)(100),f(!p)},D=e=>{let t=new MouseEvent("mousedown",{bubbles:!0,cancelable:!0,clientX:e.clientX,clientY:e.clientY,button:0});document.dispatchEvent(t)},T=e=>Array.from(document.querySelectorAll(".ant-dropdown")).some(t=>t.contains(e)),R=e=>{!e.isTrusted||T(e.target)||D(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(E.Provider,{value:{onMoreClick:d,onNodeClick:g,onAddClick:x},children:(0,n.jsxs)(a.x$,{nodes:y,edges:N,onNodesChange:k,onEdgesChange:b,onEdgeMouseEnter:I,onEdgeMouseLeave:_,onInit:H,nodeTypes:K,edgeTypes:$,maxZoom:1,onPointerDown:e=>R(e),proOptions:{hideAttribution:!0},children:[(0,n.jsx)(r.a,{style:ee,zoomable:!0,pannable:!0}),(0,n.jsx)(o.Z,{showInteractive:!1,children:(0,n.jsx)(o.B,{onClick:w,children:(0,n.jsx)(u.Du,{style:{maxWidth:24,maxHeight:24}})})}),(0,n.jsx)(s.A,{gap:16})]})}),(0,n.jsx)(J,{})]})});var ei=e=>(0,n.jsx)(a.tV,{children:(0,n.jsx)(et,{ref:e.forwardRef,...e})})}}]);