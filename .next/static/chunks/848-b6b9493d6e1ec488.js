"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[848],{25739:function(e,n,t){t.d(n,{W:function(){return g}});var a=t(82729),r=t(68806);function o(){let e=(0,a._)(["\n  fragment ViewField on DiagramViewField {\n    id\n    displayName\n    referenceName\n    type\n    nodeType\n    description\n  }\n"]);return o=function(){return e},e}function l(){let e=(0,a._)(["\n  fragment RelationField on DiagramModelRelationField {\n    id\n    relationId\n    type\n    nodeType\n    displayName\n    referenceName\n    fromModelId\n    fromModelName\n    fromModelDisplayName\n    fromColumnId\n    fromColumnName\n    fromColumnDisplayName\n    toModelId\n    toModelName\n    toModelDisplayName\n    toColumnId\n    toColumnName\n    toColumnDisplayName\n    description\n  }\n"]);return l=function(){return e},e}function i(){let e=(0,a._)(["\n  fragment NestedField on DiagramModelNestedField {\n    id\n    nestedColumnId\n    columnPath\n    type\n    displayName\n    referenceName\n    description\n  }\n"]);return i=function(){return e},e}function s(){let e=(0,a._)(["\n  fragment Field on DiagramModelField {\n    id\n    columnId\n    type\n    nodeType\n    displayName\n    referenceName\n    description\n    isPrimaryKey\n    expression\n    aggregation\n    lineage\n    nestedFields {\n      ...NestedField\n    }\n  }\n  ","\n"]);return s=function(){return e},e}function d(){let e=(0,a._)(["\n  query Diagram {\n    diagram {\n      models {\n        id\n        modelId\n        nodeType\n        displayName\n        referenceName\n        sourceTableName\n        refSql\n        cached\n        refreshTime\n        description\n        fields {\n          ...Field\n        }\n        calculatedFields {\n          ...Field\n        }\n        relationFields {\n          ...RelationField\n        }\n      }\n      views {\n        id\n        viewId\n        nodeType\n        displayName\n        description\n        referenceName\n        statement\n        fields {\n          ...ViewField\n        }\n      }\n    }\n  }\n  ","\n  ","\n  ","\n"]);return d=function(){return e},e}let c=(0,r.Ps)(o()),u=(0,r.Ps)(l()),m=(0,r.Ps)(i()),p=(0,r.Ps)(s(),m),g=(0,r.Ps)(d(),p,u,c)},58923:function(e,n,t){t.d(n,{uG:function(){return x}});var a=t(82729),r=t(68806);function o(){let e=(0,a._)(["\n  fragment CommonColumn on DetailedColumn {\n    displayName\n    referenceName\n    sourceColumnName\n    type\n    isCalculated\n    notNull\n    properties\n  }\n"]);return o=function(){return e},e}function l(){let e=(0,a._)(["\n  fragment CommonField on FieldInfo {\n    id\n    displayName\n    referenceName\n    sourceColumnName\n    type\n    isCalculated\n    notNull\n    expression\n    properties\n  }\n"]);return l=function(){return e},e}function i(){let e=(0,a._)(["\n  fragment CommonRelation on DetailedRelation {\n    fromModelId\n    fromColumnId\n    toModelId\n    toColumnId\n    type\n    name\n  }\n"]);return i=function(){return e},e}function s(){let e=(0,a._)(["\n  query ListModels {\n    listModels {\n      id\n      displayName\n      referenceName\n      sourceTableName\n      refSql\n      primaryKey\n      cached\n      refreshTime\n      description\n      fields {\n        ...CommonField\n      }\n      calculatedFields {\n        ...CommonField\n      }\n    }\n  }\n  ","\n"]);return s=function(){return e},e}function d(){let e=(0,a._)(["\n  query GetModel($where: ModelWhereInput!) {\n    model(where: $where) {\n      displayName\n      referenceName\n      sourceTableName\n      refSql\n      primaryKey\n      cached\n      refreshTime\n      description\n      fields {\n        ...CommonColumn\n      }\n      calculatedFields {\n        ...CommonColumn\n      }\n      relations {\n        ...CommonRelation\n      }\n      properties\n    }\n  }\n  ","\n  ","\n"]);return d=function(){return e},e}function c(){let e=(0,a._)(["\n  mutation CreateModel($data: CreateModelInput!) {\n    createModel(data: $data)\n  }\n"]);return c=function(){return e},e}function u(){let e=(0,a._)(["\n  mutation UpdateModel($where: ModelWhereInput!, $data: UpdateModelInput!) {\n    updateModel(where: $where, data: $data)\n  }\n"]);return u=function(){return e},e}function m(){let e=(0,a._)(["\n  mutation DeleteModel($where: ModelWhereInput!) {\n    deleteModel(where: $where)\n  }\n"]);return m=function(){return e},e}function p(){let e=(0,a._)(["\n  mutation PreviewModelData($where: WhereIdInput!) {\n    previewModelData(where: $where)\n  }\n"]);return p=function(){return e},e}let g=(0,r.Ps)(o()),h=(0,r.Ps)(l()),f=(0,r.Ps)(i()),x=(0,r.Ps)(s(),h);(0,r.Ps)(d(),g,f),(0,r.Ps)(c()),(0,r.Ps)(u()),(0,r.Ps)(m()),(0,r.Ps)(p())},10102:function(e,n,t){t.d(n,{JO:function(){return y},bB:function(){return g},in:function(){return f},ks:function(){return p}});var a=t(82729),r=t(68806),o=t(6812),l=t(73359),i=t(50319);function s(){let e=(0,a._)(["\n    query GetSettings {\n  settings {\n    productVersion\n    dataSource {\n      type\n      properties\n      sampleDataset\n    }\n    language\n  }\n}\n    "]);return s=function(){return e},e}function d(){let e=(0,a._)(["\n    mutation ResetCurrentProject {\n  resetCurrentProject\n}\n    "]);return d=function(){return e},e}function c(){let e=(0,a._)(["\n    mutation UpdateCurrentProject($data: UpdateCurrentProjectInput!) {\n  updateCurrentProject(data: $data)\n}\n    "]);return c=function(){return e},e}let u={},m=(0,r.Ps)(s());function p(e){let n={...u,...e};return o.aM(m,n)}function g(e){let n={...u,...e};return l.t(m,n)}let h=(0,r.Ps)(d());function f(e){let n={...u,...e};return i.D(h,n)}let x=(0,r.Ps)(c());function y(e){let n={...u,...e};return i.D(x,n)}},5019:function(e,n,t){t.d(n,{Z:function(){return l}});var a=t(85893),r=t(67294);let o=t(19521).ZP.div.withConfig({displayName:"EllipsisWrapper__Wrapper",componentId:"sc-d437ba0c-0"})(["overflow:hidden;text-overflow:ellipsis;",""],e=>e.multipleLine?"\n  display: -webkit-box;\n  -webkit-line-clamp: ".concat(e.multipleLine,";\n  -webkit-box-orient: vertical;\n"):"\n  white-space: nowrap;\n");function l(e){let{text:n,multipleLine:t,minHeight:l,children:i,showMoreCount:s}=e,d=(0,r.useRef)(null),c=(0,r.useRef)(null),[u,m]=(0,r.useState)(void 0),p=void 0!==u,[g,h]=(0,r.useState)([]),f=(0,r.useRef)(!1),x=()=>{var e;!f.current&&((null===(e=c.current)||void 0===e?void 0:e.clientWidth)<u-60?i.length>g.length&&h([...g,i[g.length]]):(h(g.slice(0,g.length-1)),f.current=!0))};(0,r.useEffect)(()=>{if(d.current&&!p){let e=d.current.clientWidth;0===e?m("auto"):m(e)}return()=>{f.current=!1,h([]),m(void 0)}},[]),(0,r.useEffect)(()=>{if(s){if(0===g.length){h([i[0]]);return}x()}},[s,g]);let y=Array.isArray(n)?n.join(""):n;return(0,a.jsx)(o,{ref:d,title:y,multipleLine:t,style:{width:u,minHeight:l},children:p?(()=>{if(!i)return n||"-";if(s){let e=i.length-g.length;return(0,a.jsxs)("span",{className:"d-inline-block",ref:c,children:[g,e>0&&(0,a.jsxs)("span",{className:"gray-7",children:["...",e," more"]})]})}return i})():null})}},30848:function(e,n,t){t.d(n,{Z:function(){return tf}});var a,r,o=t(85893),l=t(61782),i=t(19521),s=t(59709),d=t(41664),c=t.n(d),u=t(11163),m=t(21367),p=t(84908),g=t(39616),h=t(4266),f=t.n(h),x=t(90512),y=t(67294),C=t(39332),v=t(70464),N=t.n(v),w=t(45554);let j=(0,i.iv)(["[class^='anticon anticon-']{transition:background-color ease-out 0.12s;border-radius:2px;width:12px;height:12px;font-size:12px;vertical-align:middle;&:hover{background-color:var(--gray-5);}&:active{background-color:var(--gray-6);}&[disabled]{cursor:not-allowed;color:var(--gray-6);&:hover,&:active{background-color:transparent;}}}.anticon + .anticon{margin-left:4px;}"]),b=(0,i.ZP)(w.default).withConfig({displayName:"SidebarTree__StyledTree",componentId:"sc-6c96e180-0"})(["&.ant-tree{background-color:transparent;color:var(--gray-8);.ant-tree-indent-unit{width:12px;}.ant-tree-node-content-wrapper{display:flex;align-items:center;line-height:18px;min-height:28px;min-width:1px;padding:0;}.ant-tree-node-content-wrapper:hover,.ant-tree-node-content-wrapper.ant-tree-node-selected{background-color:transparent;}.ant-tree-treenode{padding:0 16px;background-color:transparent;transition:background-color ease-out 0.12s;&-selected{color:var(--geekblue-6);background-color:var(--gray-4);}.ant-tree-switcher{width:12px;align-self:center;.ant-tree-switcher-icon{font-size:12px;vertical-align:middle;}","}.ant-tree-iconEle{flex-shrink:0;}}.adm{&-treeTitle__title{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}&-treeNode{&:hover{background-color:var(--gray-4);}&:active{background-color:var(--gray-6);}.ant-tree-title{display:inline-flex;flex-wrap:nowrap;min-width:1px;}&--relation,&--primary{margin-left:4px;}&--group{color:var(--gray-8);margin-top:16px;font-size:14px;font-weight:500;.ant-tree-switcher-noop{display:none;}> *{cursor:inherit;}}&--empty{color:var(--gray-7);font-size:12px;.ant-tree-switcher{display:none;}.ant-tree-node-content-wrapper{min-height:auto;}}&--selectNode{*{cursor:auto;}&:hover,&:active{background-color:transparent;}}&--subtitle{color:var(--gray-7);font-size:12px;font-weight:500;.ant-tree-switcher{display:none;}.ant-tree-node-content-wrapper{min-height:auto;}}&--selectNone{*{cursor:auto;}&:hover,&:active{background-color:transparent;}}}&-actionIcon{font-size:14px;border-radius:2px;margin-right:-3px;&:not(.adm-actionIcon--disabled){cursor:pointer;&:hover{background-color:var(--gray-5);}}.anticon{padding:2px;cursor:inherit;}&--disabled{color:var(--gray-6);cursor:not-allowed;}}}}"],j),_=(0,i.iv)([".ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}"]),E=(0,i.ZP)(c()).withConfig({displayName:"SidebarTree__StyledTreeNodeLink",componentId:"sc-6c96e180-1"})(["display:block;cursor:pointer;user-select:none;margin-top:16px;padding:0 16px;line-height:28px;color:var(--gray-8);&:hover{background-color:var(--gray-4);}&:active{background-color:var(--gray-6);}&.adm-treeNode--selected{background-color:var(--gray-4);color:var(--geekblue-6);}"]),L=()=>{let[e,n]=(0,y.useState)([]),[t,a]=(0,y.useState)([]),[r,o]=(0,y.useState)([]),[l,i]=(0,y.useState)(!0);return{treeSelectedKeys:e,treeExpandKeys:t,treeLoadedKeys:r,autoExpandParent:l,setTreeSelectedKeys:n,setTreeExpandKeys:a,setTreeLoadedKeys:o,setAutoExpandParent:i}};function T(e){return(0,o.jsx)(b,{blockNode:!0,showIcon:!0,motion:null,...e})}var S=t(31682),I=t.n(S),A=t(70474),D=t(28583),R=t.n(D),k=t(41609),P=t.n(k),M=t(11865),O=t.n(M),G=t(45021),H=t.n(G),Z=t(49397);let F=e=>{let{actions:n}=e,t=(n||[]).map(e=>{let{key:n,icon:t,render:a,disabled:r=!1,className:l="",...i}=e;return t?(0,o.jsx)(Z.Z,{component:t,className:"adm-actionIcon ".concat(l," ").concat(r?"adm-actionIcon--disabled":""),...i},n):a?(0,o.jsx)(y.Fragment,{children:a({key:n,disabled:r})},n):null});return(0,o.jsx)("span",{className:"d-inline-flex align-center flex-shrink-0 g-2",children:t})};function B(e){let{title:n,quotaUsage:t=0,appendSlot:a,...r}=e;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("span",{className:"d-inline-flex align-center",children:[n,(0,o.jsxs)("span",{className:"adm-treeNode-group-count ml-1 text-xs flex-grow-0",children:["(",t,")"]}),a]}),(0,o.jsx)(F,{...r})]})}var U=t(44280),W=t(50608);let q=e=>{let{title:n,relation:t,primary:a}=e,r=(0,o.jsxs)(o.Fragment,{children:[t&&(0,o.jsx)("span",{className:"adm-treeNode--relation",title:"".concat(t.name,": ").concat((0,U.I)(t.joinType)),children:(0,o.jsx)(g.NA,{})}),a&&(0,o.jsx)("span",{className:"adm-treeNode--primary",title:"Primary Key",children:(0,o.jsx)(g.EU,{})})]});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{title:n,children:n}),r]})},V=(e,n)=>[{title:n,key:"".concat(e,"_").concat(O()(n)),className:"adm-treeNode--subtitle adm-treeNode--selectNone",selectable:!1,isLeaf:!0}],K=(e,n,t)=>0===n.length?[]:[...t?V(e,t):[],...n.map(e=>{let n=e.nodeType===p.QZ.RELATION;return{icon:n?(0,W.i)({nodeType:p.QZ.MODEL}):(0,A.u)(e,{title:e.type}),className:"adm-treeNode adm-treeNode-column adm-treeNode--selectNode",title:(0,o.jsx)(q,{title:e.displayName,relation:n?e:null,primary:null==e?void 0:e.isPrimaryKey}),key:e.id,selectable:!1,isLeaf:!0}})],Q=e=>n=>{let{groupName:t="",groupKey:a="",quotaUsage:r,actions:l,children:i=[],appendSlot:s}=R()(e,n),d=[{title:"No ".concat(H()(t)),key:"".concat(a,"-empty"),selectable:!1,className:"adm-treeNode adm-treeNode--empty adm-treeNode--selectNode"}],c=P()(i)?d:i;return[{className:"adm-treeNode--group",title:(0,o.jsx)(B,{title:t,quotaUsage:r,appendSlot:s,actions:l}),key:a,selectable:!1,isLeaf:!0},...c]},$=(0,i.ZP)(m.default).withConfig({displayName:"utils__GroupActionButton",componentId:"sc-752a4c65-0"})(["font-size:12px;height:auto;background:transparent;color:var(--gray-8);&:hover{background-color:transparent;}&:focus{border-color:var(--gray-5);background:transparent;color:var(--gray-8);}"]);var z=t(69371),Y=t(59046),J=t(93181),X=t.n(J),ee=t(98509),en=t.n(ee);function et(e){let{title:n,appendIcon:t=null}=e;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:"adm-treeTitle__title",title:n,children:n}),t&&(0,o.jsx)("span",{className:"adm-actionIcon",children:t})]})}var ea=t(98885);function er(e){let{title:n,onCancelChange:t,onRename:a,onSetTitle:r}=e;return(0,o.jsx)(ea.default,{autoFocus:!0,size:"small",value:n,onClick:e=>e.stopPropagation(),onKeyDown:e=>{"escape"===e.key.toLowerCase()&&t()},onChange:e=>r(e.target.value),onPressEnter:e=>a(n),onBlur:e=>a(n)})}var eo=t(26123);let el={RENAME:"rename",DELETE:"delete"},ei=(0,i.ZP)(z.default).withConfig({displayName:"TreeTitle__StyledMenu",componentId:"sc-9f797d5d-0"})(["a:hover{color:white;}"]);function es(e){let{id:n,onDelete:t,onRename:a}=e,[r,l]=(0,y.useState)(e.title),[i,s]=(0,y.useState)(!1),d=e=>{t&&t(e)};return i?(0,o.jsx)(er,{title:r,onCancelChange:()=>{s(!1),l(e.title)},onSetTitle:l,onRename:e=>{s(!1),l(e),a&&a(n,e)}}):(0,o.jsx)(et,{title:r,appendIcon:(0,o.jsx)(Y.default,{trigger:["click"],overlayStyle:{userSelect:"none",minWidth:150},overlay:(0,o.jsx)(ei,{items:[{label:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(X(),{className:"mr-2"}),"Rename"]}),key:el.RENAME,onClick:e=>{let{domEvent:n}=e;n.stopPropagation(),s(!0)}},{label:(0,o.jsx)(eo.fP,{onConfirm:()=>d(n)}),key:el.DELETE,onClick:e=>{let{domEvent:n}=e;n.stopPropagation()}}]}),children:(0,o.jsx)(en(),{onClick:e=>e.stopPropagation()})})})}let ed=(0,i.ZP)(T).withConfig({displayName:"ThreadTree__StyledSidebarTree",componentId:"sc-ed81b4d0-0"})([""," .adm-treeNode{&.adm-treeNode__thread{padding:0px 16px 0px 4px !important;.ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}}}"],_);function ec(e){let n=(0,C.useParams)(),t=(0,C.useRouter)(),{threads:a=[],selectedKeys:r,onSelect:l,onRename:i,onDeleteThread:s}=e,d=Q({groupName:"Threads",groupKey:"threads",actions:[{key:"new-thread",render:()=>(0,o.jsx)($,{size:"small",icon:(0,o.jsx)(I(),{}),onClick:()=>t.push(p.y$.Home),children:"New"})}]}),[c,u]=(0,y.useState)(d());return(0,y.useEffect)(()=>{u(e=>d({quotaUsage:a.length,children:a.map(e=>{let n=e.id;return{className:"adm-treeNode adm-treeNode__thread",id:n,isLeaf:!0,key:n,title:(0,o.jsx)(es,{id:n,title:e.name,onRename:i,onDelete:s})}})}))},[null==n?void 0:n.id,a]),(0,o.jsx)(ed,{treeData:c,selectedKeys:r,onSelect:l})}function eu(e){let{data:n,onSelect:t,onRename:a,onDelete:r}=e,l=(0,u.useRouter)(),i=(0,C.useParams)(),{threads:s}=n,{treeSelectedKeys:d,setTreeSelectedKeys:c}=L();(0,y.useEffect)(()=>{(null==i?void 0:i.id)&&c([i.id])},[null==i?void 0:i.id]);let m=async e=>{await r(e),(null==i?void 0:i.id)==e&&l.push(p.y$.Home)};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(E,{className:(0,x.Z)({"adm-treeNode--selected":l.pathname===p.y$.HomeDashboard}),href:p.y$.HomeDashboard,children:[(0,o.jsx)(N(),{className:"mr-2"}),(0,o.jsx)("span",{className:"text-medium",children:"Dashboard"})]}),(0,o.jsx)(ec,{threads:s,selectedKeys:d,onSelect:(e,n)=>{0!==e.length&&(c(e),t(e))},onRename:a,onDeleteThread:m})]})}(0,i.ZP)(T).withConfig({displayName:"Home__StyledSidebarTree",componentId:"sc-6442ba22-0"})([".adm-treeNode{&.adm-treeNode__thread{padding:0px 16px 0px 4px !important;.ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}}}"]);var em=t(37031),ep=t(90883),eg=t(30046),eh=t.n(eg),ef=t(7337),ex=t.n(ef),ey=t(42187),eC=t(17579),ev=t(51618),eN=t(80002),ew=t(64822),ej=t(62819),eb=t(69828),e_=t(4071),eE=t.n(e_),eL=t(60388),eT=t.n(eL),eS=t(96873),eI=t.n(eS),eA=t(5019),eD=t(46140);let eR=(0,i.ZP)(ey.default).withConfig({displayName:"SchemaChangeModal__StyledCollapse",componentId:"sc-8dbeeb02-0"})(["border:none;background-color:white;.ant-collapse-item:last-child,.ant-collapse-item:last-child > .ant-collapse-header{border-radius:0;}.ant-collapse-item,.ant-collapse-content{border-color:var(--gray-4);}.ant-collapse-content-box{padding:0;}"]),ek=(0,i.ZP)(eC.Z).withConfig({displayName:"SchemaChangeModal__StyledTable",componentId:"sc-8dbeeb02-1"})(["padding-left:36px;.ant-table{border:none;border-radius:0;.non-expandable{.ant-table-row-expand-icon{display:none;}}.ant-table-expanded-row{.ant-table-cell{background-color:white;}}}"]),eP=[{title:"Affected Resource",dataIndex:"resourceType",width:200,render:e=>e===eD.Jq.CALCULATED_FIELD?(0,o.jsx)(ev.default,{className:"ant-tag--geekblue",children:"Calculated Field"}):e===eD.Jq.RELATION?(0,o.jsx)(ev.default,{className:"ant-tag--citrus",children:"Relationship"}):null},{title:"Name",dataIndex:"displayName"}],eM=e=>e.calculatedFields.length+e.relationships.length>0?"":"non-expandable",eO=e=>{let{title:n,count:t,onResolve:a,isResolving:r}=e;return(0,o.jsxs)("div",{className:"d-flex align-center flex-grow-1",style:{userSelect:"none"},children:[(0,o.jsx)("b",{className:"text-medium",children:n}),(0,o.jsxs)("span",{className:"flex-grow-1 text-right d-flex justify-end",children:[(0,o.jsxs)(eN.default.Text,{className:"gray-6",children:[t," table(s) affected"]}),(0,o.jsx)("div",{style:{width:150},children:!!a&&(0,o.jsx)(ew.Z,{title:"Are you sure?",okText:"Confirm",okButtonProps:{danger:!0},onConfirm:e=>{e.stopPropagation(),a()},onCancel:e=>e.stopPropagation(),children:(0,o.jsx)(m.default,{type:"text",size:"small",className:"red-5",onClick:e=>e.stopPropagation(),loading:r,icon:(0,o.jsx)(eI(),{}),children:"Resolve"})})})]})]})},eG=e=>{let{record:n,tipMessage:t}=e;return 0===n.resources.length?null:(0,o.jsxs)("div",{className:"pl-12",children:[(0,o.jsx)(ej.default,{showIcon:!0,icon:(0,o.jsx)(eh(),{className:"orange-5"}),className:"gray-6 ml-2 bg-gray-1 pl-0",style:{border:"none"},message:t}),(0,o.jsx)(eC.Z,{columns:eP,dataSource:n.resources||[],pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowKey:"rowKey",size:"small",className:"adm-nested-table"})]})};function eH(e){let{visible:n,onClose:t,defaultValue:a,payload:r}=e,{onResolveSchemaChange:l,isResolving:i}=r||{},{deletedTables:s,deletedColumns:d,modifiedColumns:c}=(0,y.useMemo)(()=>{let{deletedTables:e,deletedColumns:n,modifiedColumns:t}=a||{};if(!a)return{deletedTables:e,deletedColumns:n,modifiedColumns:t};let r=e=>({...e,resources:[...e.calculatedFields.map((n,t)=>({...n,resourceType:eD.Jq.CALCULATED_FIELD,rowKey:"".concat(e.sourceTableName,"-").concat(n.referenceName,"-").concat(t)})),...e.relationships.map((n,t)=>({...n,resourceType:eD.Jq.RELATION,rowKey:"".concat(e.sourceTableName,"-").concat(n.referenceName,"-").concat(t)}))],rowKey:e.sourceTableName});return{deletedTables:null==e?void 0:e.map(r),deletedColumns:null==n?void 0:n.map(r),modifiedColumns:null==t?void 0:t.map(r)}},[a]);return(0,o.jsxs)(eb.Z,{title:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eh(),{className:"orange-5 mr-2"}),"Schema Changes"]}),width:750,visible:n,onCancel:t,destroyOnClose:!0,footer:null,children:[(0,o.jsx)(eN.default.Paragraph,{className:"gray-6 mb-4",children:"We have detected schema changes from your connected data source. Please review the impacts of these changes."}),(0,o.jsx)(ej.default,{showIcon:!0,type:"warning",className:"gray-8 mb-6",message:'Please note that clicking "Resolve" may automatically delete all affected models, relationships, and calculated fields.'}),(0,o.jsxs)(eR,{expandIcon:e=>e.isActive?(0,o.jsx)(eT(),{}):(0,o.jsx)(eE(),{}),children:[s&&(0,o.jsx)(ey.default.Panel,{header:(0,o.jsx)(eO,{title:"Source table deleted",count:s.length,onResolve:()=>l(eD.a_.DELETED_TABLES),isResolving:i}),children:(0,o.jsx)(ek,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Source table name",dataIndex:"sourceTableName"}],dataSource:s,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:eM,expandable:{expandedRowRender:e=>(0,o.jsx)(eG,{record:e,tipMessage:"The following table shows resources affected by this model and will be deleted when resolving."})}})},"deleteTables"),d&&(0,o.jsx)(ey.default.Panel,{header:(0,o.jsx)(eO,{title:"Source column deleted",count:d.length,onResolve:()=>l(eD.a_.DELETED_COLUMNS),isResolving:i}),children:(0,o.jsx)(ek,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Deleted columns",dataIndex:"columns",render:e=>(0,o.jsx)(eA.Z,{showMoreCount:!0,children:e.map(e=>(0,o.jsx)(ev.default,{className:"ant-tag--geekblue",children:e.displayName},e.sourceColumnName))})}],dataSource:d,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:eM,expandable:{expandedRowRender:e=>(0,o.jsx)(eG,{record:e,tipMessage:"The following table shows resources affected by this column of the model and will be deleted when resolving."})}})},"deleteColumns"),c&&(0,o.jsx)(ey.default.Panel,{header:(0,o.jsx)(eO,{title:"Source column type changed",count:c.length}),children:(0,o.jsx)(ek,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Affected columns",dataIndex:"columns",render:e=>(0,o.jsx)(eA.Z,{showMoreCount:!0,children:e.map(e=>(0,o.jsx)(ev.default,{className:"ant-tag--geekblue",children:e.displayName},e.sourceColumnName))})}],dataSource:c,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:eM,expandable:{expandedRowRender:e=>(0,o.jsx)(eG,{record:e,tipMessage:"The following table shows the resources utilized by this column of the model. Please review each resource and manually update the relevant ones if any changes are required."})}})},"modifiedColumns")]})]})}var eZ=t(34217),eF=t(25739),eB=t(58923),eU=t(36238);let eW=e=>[null==e?void 0:e.deletedTables,null==e?void 0:e.deletedColumns,null==e?void 0:e.modifiedColumns].some(e=>!!e);function eq(e){let{onOpenModelDrawer:n,models:t}=e,a=(0,ep.Z)(),[r,{loading:l}]=(0,eZ.t_)({onError:e=>console.error(e),onCompleted:async e=>{e.triggerDataSourceDetection?em.default.warning("Schema change detected."):em.default.success("There is no schema change."),await c()}}),[i,{loading:s}]=(0,eZ.v5)({onError:e=>console.error(e),onCompleted:async(e,n)=>{var t;let{type:r}=null===(t=n.variables)||void 0===t?void 0:t.where;r===eD.a_.DELETED_TABLES?em.default.success("Source table deleted resolved successfully."):r===eD.a_.DELETED_COLUMNS&&em.default.success("Source column deleted resolved successfully.");let{data:o}=await c();eW(o.schemaChange)||a.closeModal()},refetchQueries:[{query:eF.W},{query:eB.uG}]}),{data:d,refetch:c}=(0,eZ.xY)({fetchPolicy:"cache-and-network"}),u=(0,y.useMemo)(()=>eW(null==d?void 0:d.schemaChange),[d]),m=()=>{a.openModal()},p=Q({groupName:"Models",groupKey:"models",actions:[{key:"trigger-schema-detection",disabled:l,icon:()=>(0,o.jsx)(ex(),{spin:l,title:(null==d?void 0:d.schemaChange.lastSchemaChangeTime)?"Last refresh ".concat((0,eU.ni)(null==d?void 0:d.schemaChange.lastSchemaChangeTime)):"",onClick:()=>r()})},{key:"add-model",render:()=>(0,o.jsx)($,{"data-guideid":"add-model","data-testid":"add-model",icon:(0,o.jsx)(I(),{}),size:"small",onClick:()=>n(),children:"New"})}]}),[g,h]=(0,y.useState)(p());return(0,y.useEffect)(()=>{h(e=>p({quotaUsage:t.length,appendSlot:u&&(0,o.jsx)("span",{className:"adm-actionIcon mx-2",onClick:m,children:(0,o.jsx)(eh(),{className:"orange-5",title:"Review schema change impacts"})}),children:t.map(e=>{let n=e.id;return{children:[...K(n,[...e.fields,...e.calculatedFields])],className:"adm-treeNode",icon:(0,W.i)({nodeType:e.nodeType}),id:n,isLeaf:!1,key:n,title:(0,o.jsx)(et,{title:e.displayName}),type:e.nodeType}})}))},[t,u,d,l]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eK,{...e,treeData:g}),(0,o.jsx)(eH,{...a.state,defaultValue:null==d?void 0:d.schemaChange,payload:{onResolveSchemaChange:e=>{i({variables:{where:{type:e}}})},isResolving:s},onClose:a.closeModal})]})}function eV(e){let{views:n}=e,t=()=>{eb.Z.info({title:"How to create a View?",content:(0,o.jsxs)("div",{children:["Pose your questions at"," ",(0,o.jsx)(c(),{href:p.y$.Home,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_add_view_navigate_to_home",children:"homepage"}),", and get some helpful answers to save as views."]}),okButtonProps:{"data-ph-capture":!0,"data-ph-capture-attribute-name":"cta_add_view_ok_btn"}})},a=Q({groupName:"Views",groupKey:"views",actions:[{key:"add-view-info",render:()=>(0,o.jsx)($,{icon:(0,o.jsx)(I(),{}),size:"small",onClick:t,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_add_view",children:"New"})}]}),[r,l]=(0,y.useState)(a());return(0,y.useEffect)(()=>{l(e=>a({quotaUsage:n.length,children:n.map(e=>{let n=e.id;return{children:K(n,e.fields||[]),className:"adm-treeNode",icon:(0,W.i)({nodeType:e.nodeType}),id:n,isLeaf:!1,key:n,title:(0,o.jsx)(et,{title:e.displayName}),type:e.nodeType}})}))},[n]),(0,o.jsx)(eK,{...e,treeData:r})}let eK=(0,i.ZP)(T).withConfig({displayName:"Modeling__StyledSidebarTree",componentId:"sc-2d34f56a-0"})([""," .adm-treeNode{.ant-tree-title{display:inline-flex;flex-wrap:nowrap;min-width:1px;flex-grow:0;}}"],_);function eQ(e){let{data:n,onSelect:t,onOpenModelDrawer:a}=e,{models:r=[],views:l=[]}=n||{};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eq,{models:r,onSelect:t,selectedKeys:[],onOpenModelDrawer:a}),(0,o.jsx)(eV,{views:l,onSelect:t,selectedKeys:[]})]})}var e$=t(29078),ez=t.n(e$),eY=t(32815);let eJ=(0,i.ZP)(z.default).withConfig({displayName:"SidebarMenu__StyledMenu",componentId:"sc-ae3c8b69-0"})(["&.ant-menu{background-color:transparent;border-right:0;color:var(--gray-8);&:not(.ant-menu-horizontal){.ant-menu-item-selected{color:var(--gray-8);background-color:var(--gray-5);}}.ant-menu-item-group{margin-top:20px;&:first-child{margin-top:0;}}.ant-menu-item-group-title{font-size:12px;font-weight:700;padding:5px 16px;}.ant-menu-item{line-height:28px;height:auto;margin:0;font-weight:500;&:not(last-child){margin-bottom:0;}&:not(.ant-menu-item-disabled):hover{color:inherit;background-color:var(--gray-4);}&:not(.ant-menu-item-disabled):active{background-color:var(--gray-6);}&:active{background-color:transparent;}&-selected{color:var(--gray-8);&:after{display:none;}&:hover{color:var(--gray-8);}}}}"]);function eX(e){let{items:n,selectedKeys:t,onSelect:a}=e;return(0,o.jsx)(eJ,{mode:"inline",items:n,selectedKeys:t,onSelect:a})}let e1=i.ZP.div.withConfig({displayName:"Knowledge__Layout",componentId:"sc-55b886c-0"})(["padding:16px 0;position:absolute;z-index:1;left:0;top:0;width:100%;background-color:var(--gray-2);overflow:hidden;"]),e0={[p.y$.KnowledgeQuestionSQLPairs]:p.mo.QUESTION_SQL_PAIRS,[p.y$.KnowledgeInstructions]:p.mo.INSTRUCTIONS},e2={color:"inherit",transition:"none"};function e5(){let e=(0,u.useRouter)(),n=[{"data-guideid":"question-sql-pairs",label:(0,o.jsx)(c(),{style:e2,href:p.y$.KnowledgeQuestionSQLPairs,children:"Question-SQL pairs"}),icon:(0,o.jsx)(ez(),{}),key:p.mo.QUESTION_SQL_PAIRS,className:"pl-4"},{"data-guideid":"instructions",label:(0,o.jsx)(c(),{style:e2,href:p.y$.KnowledgeInstructions,children:"Instructions"}),icon:(0,o.jsx)(eY.pB,{}),key:p.mo.INSTRUCTIONS,className:"pl-4"}];return(0,o.jsx)(e1,{children:(0,o.jsx)(eX,{items:n,selectedKeys:e0[e.pathname]})})}var e3=t(85571),e6=t.n(e3),e4=t(82637),e9=t.n(e4);let e7=i.ZP.div.withConfig({displayName:"APIManagement__Layout",componentId:"sc-3f38168c-0"})(["padding:16px 0;position:absolute;z-index:1;left:0;top:0;width:100%;background-color:var(--gray-2);overflow:hidden;"]),e8={[p.y$.APIManagementHistory]:p.mo.API_HISTORY},ne={color:"inherit",transition:"none"};function nn(){let e=(0,u.useRouter)(),n=[{"data-guideid":"api-history",label:(0,o.jsx)(c(),{style:ne,href:p.y$.APIManagementHistory,children:"API history"}),icon:(0,o.jsx)(e6(),{}),key:p.mo.API_HISTORY,className:"pl-4"},{label:(0,o.jsxs)(c(),{className:"gray-8 d-inline-flex align-center",href:"https://wrenai.readme.io/reference/sql-generation",target:"_blank",rel:"noopener noreferrer",children:["API reference",(0,o.jsx)(g.r$,{className:"ml-1"})]}),icon:(0,o.jsx)(e9(),{}),key:p.mo.API_REFERENCE,className:"pl-4"}];return(0,o.jsx)(e7,{children:(0,o.jsx)(eX,{items:n,selectedKeys:e8[e.pathname]})})}var nt=t(89734),na=t.n(nt),nr=t(21035),no=t.n(nr),nl=t(77840),ni=t(3942),ns=t(20745),nd=t(66590),nc=t(97762);(a=r||(r={})).DATA_MODELING_GUIDE="DATA_MODELING_GUIDE",a.CREATING_MODEL="CREATING_MODEL",a.CREATING_VIEW="CREATING_VIEW",a.WORKING_RELATIONSHIP="WORKING_RELATIONSHIP",a.CONNECT_OTHER_DATA_SOURCES="CONNECT_OTHER_DATA_SOURCES",a.SWITCH_PROJECT_LANGUAGE="SWITCH_PROJECT_LANGUAGE",a.SHARE_RESULTS="SHARE_RESULTS",a.VIEW_FULL_SQL="VIEW_FULL_SQL",a.KNOWLEDGE_GUIDE="KNOWLEDGE_GUIDE",a.SAVE_TO_KNOWLEDGE="SAVE_TO_KNOWLEDGE";var nu=t(25063);let nm=e=>({[eD.y9.EN]:"English",[eD.y9.ES]:"Spanish",[eD.y9.FR]:"French",[eD.y9.ZH_TW]:"Traditional Chinese",[eD.y9.ZH_CN]:"Simplified Chinese",[eD.y9.DE]:"German",[eD.y9.PT]:"Portuguese",[eD.y9.RU]:"Russian",[eD.y9.JA]:"Japanese",[eD.y9.KO]:"Korean"})[e]||e;var np=t(89997);let ng=(0,i.ZP)(eY.Hx).withConfig({displayName:"stories__RobotIcon",componentId:"sc-f9259573-0"})(["width:24px;height:24px;"]),nh={progressText:"{{current}} / {{total}}",nextBtnText:"Next",prevBtnText:"Previous",showButtons:["next"],allowClose:!1},nf=function(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];return(e,t)=>{let a={[r.DATA_MODELING_GUIDE]:()=>ny(...n,t),[r.SWITCH_PROJECT_LANGUAGE]:()=>nv(...n,t),[r.KNOWLEDGE_GUIDE]:()=>nN(...n,t),[r.SAVE_TO_KNOWLEDGE]:()=>nw(...n,t)}[e]||null;return a&&a()}},nx=(e,n)=>{let t=e.wrapper;t.style.maxWidth="none",t.style.width="".concat(n,"px")},ny=(e,n,t,a)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy();let r=!!t.sampleDataset,l=nu.QC[t.sampleDataset];e.setConfig({...nh,showProgress:!0}),e.setSteps([{popover:{title:(0,nc.Dq)((0,o.jsxs)("div",{className:"pt-4",children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:331},children:(0,o.jsx)("img",{className:"mb-4",src:"/images/learning/data-modeling.jpg",alt:"data-modeling-guide"})}),"Data modeling guide"]})),description:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:["Data modeling adds a logical layer over your original data schema, organizing relationships, semantics, and calculations. This helps AI align with business logic, retrieve precise data, and generate meaningful insights."," ",(0,o.jsx)("a",{href:"https://docs.getwren.ai/oss/guide/modeling/overview",target:"_blank",rel:"noopener noreferrer",children:"More details"}),(0,o.jsx)("br",{}),(0,o.jsx)("br",{}),r?(0,o.jsxs)(o.Fragment,{children:["We use ",l.label," Dataset to present the guide. To know more, please visit"," ",(0,o.jsxs)("a",{href:l.guide,target:"_blank",rel:"noopener noreferrer",children:["about the ",l.label," Dataset."]})]}):null]})),showButtons:["next","close"],onPopoverRender:e=>{nx(e,720)},onCloseClick:()=>{e.destroy(),window.sessionStorage.setItem("skipDataModelingGuide","1")}}},{element:'[data-guideid="add-model"]',popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-1",children:(0,o.jsx)(g.ZA,{style:{fontSize:24}})}),"Create a model"]})),description:(0,nc.Dq)((0,o.jsx)(o.Fragment,{children:"Click the add icon to start create your first model."}))}},{element:'[data-guideid="edit-model-0"]',popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:175},children:(0,o.jsx)("img",{className:"mb-2",src:"/images/learning/edit-model.gif",alt:"edit-model"})}),"Edit a model"]})),description:(0,nc.Dq)((0,o.jsx)(o.Fragment,{children:"Click the more icon to update the columns of model or delete it."}))}},{element:'[data-guideid="model-0"]',popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:214},children:(0,o.jsx)("img",{className:"mb-2",src:"/images/learning/edit-metadata.gif",alt:"edit-metadata"})}),"Edit metadata"]})),description:(0,nc.Dq)((0,o.jsx)(o.Fragment,{children:"You could edit alias (alternative name) and descriptions of models and columns."})),onPopoverRender:e=>{nx(e,360)}}},{element:'[data-guideid="deploy-model"]',popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:102},children:(0,o.jsx)("img",{className:"mb-2",src:"/images/learning/deploy-modeling.jpg",alt:"deploy-modeling"})}),"Deploy modeling"]})),description:(0,nc.Dq)((0,o.jsx)(o.Fragment,{children:"After editing the models, remember to deploy the changes."}))}},{popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:331},children:(0,o.jsx)("img",{className:"mb-2",src:"/images/learning/ask-question.jpg",alt:"ask-question"})}),"Ask questions"]})),description:(0,nc.Dq)((0,o.jsx)(o.Fragment,{children:"When you finish editing your models, you can visit “Home” and start asking questions."})),onPopoverRender:e=>{nx(e,720)},doneBtnText:"Go to Home",onNextClick:()=>{n.push(p.y$.Home),e.destroy(),(null==a?void 0:a.onDone)&&a.onDone()}}}]),np.WI(np.W1.GO_TO_FIRST_MODEL),e.drive()},nC=e=>{let[n,t]=(0,y.useState)(e.defaultValue),a=Object.keys(eD.y9).map(e=>({label:nm(e),value:e}));return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("label",{className:"d-block mb-2",children:"Project language"}),(0,o.jsx)(nd.default,{showSearch:!0,style:{width:"100%"},options:a,getPopupContainer:e=>e.parentElement,onChange:e=>{t(e)},value:n}),(0,o.jsx)("input",{name:"language",type:"hidden",value:n})]})},nv=(e,n,t,a)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...nh,showProgress:!1}),e.setSteps([{popover:{title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-1",children:(0,o.jsx)(g.T9,{style:{fontSize:24}})}),"Switch the language"]})),description:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:["Choose your preferred language. Once set up, AI will respond in your chosen language.",(0,o.jsx)("div",{className:"my-3",children:(0,o.jsx)("div",{id:"projectLanguageContainer"})}),"You can go to project settings to change it if you change your mind."]})),onPopoverRender:e=>{nx(e,400);let n=document.getElementById("projectLanguageContainer");n&&(0,ns.createRoot)(n).render((0,o.jsx)(nC,{defaultValue:t.language}))},showButtons:["next","close"],nextBtnText:"Submit",onCloseClick:()=>{e.destroy(),window.sessionStorage.setItem("skipSwitchProjectLanguageGuide","1")},onNextClick:async()=>{let n=document.getElementById("projectLanguageContainer");if(n){let e=n.querySelector('input[name="language"]'),t=document.querySelectorAll(".driver-popover-next-btn")[0],r=document.createElement("span");r.setAttribute("aria-hidden","loading"),r.setAttribute("role","img"),r.className="anticon anticon-loading anticon-spin text-sm gray-6 ml-2",r.innerHTML='<svg viewBox="0 0 1024 1024" focusable="false" data-icon="loading" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg>',t.setAttribute("disabled","true"),t.appendChild(r),await (null==a?void 0:a.onSaveLanguage(e.value).catch(e=>console.error(e)).finally(()=>{t.removeAttribute("disabled"),t.removeChild(r)}))}e.destroy(),null==a||a.onDone()}}}]),e.drive()},nN=(e,n,t,a)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...nh,showProgress:!0}),e.setSteps([{element:'[data-guideid="question-sql-pairs"]',popover:{title:(0,nc.Dq)((0,o.jsxs)("div",{className:"pt-4",children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:317},children:(0,o.jsx)("img",{className:"mb-4",src:"/images/learning/save-to-knowledge.gif",alt:"question-sql-pairs-guide"})}),"Build knowledge base: Question-SQL pairs"]})),description:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:["Create and manage ",(0,o.jsx)("b",{children:"Question-SQL pairs"})," to refine Wren AI’s SQL generation. You can manually add pairs here or go to Home, ask a question, and save the correct answer to Knowledge. The more you save, the smarter Wren AI becomes!"]})),onPopoverRender:e=>{nx(e,640)}}},{element:'[data-guideid="instructions"]',popover:{title:(0,nc.Dq)((0,o.jsxs)("div",{className:"pt-4",children:[(0,o.jsx)("div",{className:"-mx-4",style:{minHeight:260},children:(0,o.jsx)("img",{className:"mb-4",src:"/images/learning/instructions.png",alt:"instructions-guide"})}),"Build knowledge base: Instructions"]})),description:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:["In addition to Question-SQL pairs, you can create instructions to define ",(0,o.jsx)("b",{children:"business rules"})," and ",(0,o.jsx)("b",{children:"query logic"}),". These rules guide Wren AI in applying consistent filters, constraints, and best practices to SQL queries."]})),onPopoverRender:e=>{nx(e,520)},doneBtnText:"Got it",onNextClick:()=>{e.destroy(),(null==a?void 0:a.onDone)&&a.onDone()}}}]),e.drive()},nw=async(e,n,t,a)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...nh,showProgress:!1}),e.setSteps([{element:'[data-guideid="last-answer-result"] [data-guideid="save-to-knowledge"]',popover:{side:"top",align:"start",title:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-1",children:(0,o.jsx)(ng,{})}),"Save to knowledge"]})),description:(0,nc.Dq)((0,o.jsxs)(o.Fragment,{children:["If the AI-generated answer is correct, save it as a"," ",(0,o.jsx)("b",{children:"Question-SQL pair"})," to improve AI learning. If it's incorrect, refine it with follow-ups before saving to ensure accuracy."]})),onPopoverRender:e=>{nx(e,360)},doneBtnText:"Got it",onNextClick:()=>{e.destroy(),(null==a?void 0:a.onDone)&&a.onDone()}}}]);let r=null,l=null,i=()=>{r&&(r.disconnect(),r=null)},s=()=>{l&&(l.disconnect(),l=null)},d=()=>{let n=document.querySelector('[data-guideid="last-answer-result"] [data-guideid="text-answer-preview-data"]');return!!n&&(i(),(l=new IntersectionObserver(async n=>{for(let t of n)if(t.isIntersecting){s(),await (0,eU.Y3)(700),e.drive();return}},{threshold:.5})).observe(n),!0)};d()||((r=new MutationObserver(()=>{d()&&i()})).observe(document.body,{childList:!0,subtree:!0}),await (0,eU.Y3)(6e4),i(),s())};var nj=t(10102);t(12068);var nb=(0,y.forwardRef)(function(e,n){let t=(0,u.useRouter)(),a=(0,y.useRef)(null),{data:r}=(0,nj.ks)(),o=(0,y.useMemo)(()=>{var e,n;return{sampleDataset:null==r?void 0:null===(e=r.settings)||void 0===e?void 0:e.dataSource.sampleDataset,language:null==r?void 0:null===(n=r.settings)||void 0===n?void 0:n.language}},[null==r?void 0:r.settings]);(0,y.useEffect)(()=>{if(null===a.current)return a.current=(0,ni.v)(),()=>{a.current.destroy(),a.current=null}},[]);let l=(e,n)=>{nf(a.current,t,o)(e,n)};return(0,y.useImperativeHandle)(n,()=>({play:l}),[a.current,o,t]),null}),n_=t(82729),nE=t(68806),nL=t(6812),nT=t(50319);function nS(){let e=(0,n_._)(["\n    query LearningRecord {\n  learningRecord {\n    paths\n  }\n}\n    "]);return nS=function(){return e},e}function nI(){let e=(0,n_._)(["\n    mutation SaveLearningRecord($data: SaveLearningRecordInput!) {\n  saveLearningRecord(data: $data) {\n    paths\n  }\n}\n    "]);return nI=function(){return e},e}let nA={},nD=(0,nE.Ps)(nS()),nR=(0,nE.Ps)(nI()),nk=i.ZP.div.withConfig({displayName:"learning__Progress",componentId:"sc-7a8ec05a-0"})(["display:block;border-radius:999px;height:6px;width:100%;background-color:var(--gray-4);&::before{content:'';display:block;border-radius:999px;width:",";height:100%;background:linear-gradient(to left,#75eaff,#6150e0);transition:width 0.3s;}"],e=>{let{total:n,current:t}=e;return"".concat(t/n*100,"%")}),nP=i.ZP.div.withConfig({displayName:"learning__CollapseBlock",componentId:"sc-7a8ec05a-1"})(["overflow:hidden;"]),nM=i.ZP.div.withConfig({displayName:"learning__PlayIcon",componentId:"sc-7a8ec05a-2"})(["position:relative;width:16px;height:16px;border-radius:50%;background-color:var(--gray-5);&::before{content:'';display:block;position:absolute;top:50%;left:50%;margin-top:-4px;margin-left:-2px;border-top:4px solid transparent;border-left:6px solid var(--gray-8);border-bottom:4px solid transparent;}"]),nO=i.ZP.div.withConfig({displayName:"learning__List",componentId:"sc-7a8ec05a-3"})(["display:flex;align-items:center;justify-content:space-between;cursor:pointer;font-size:12px;color:",";text-decoration:",";padding:2px 16px;&:hover{transition:background-color 0.3s;background-color:var(--gray-4);color:",";text-decoration:",";}"],e=>{let{finished:n}=e;return n?"var(--gray-6)":"var(--gray-8)"},e=>{let{finished:n}=e;return n?"line-through":"none"},e=>{let{finished:n}=e;return n?"var(--gray-6)":"var(--gray-8)"},e=>{let{finished:n}=e;return n?"line-through":"none"}),nG=(0,nl.x)(e=>{let{title:n,onClick:t,href:a,finished:r}=e;return(0,o.jsxs)(nO,{className:"select-none",finished:r,onClick:t,as:a?"a":"div",...a?{href:a,target:"_blank",rel:"noopener noreferrer"}:{},children:[n,(0,o.jsx)(nM,{})]})}),nH=(e,n,t,a)=>{let o=e=>({onDone:()=>t(e),onSaveLanguage:a}),l=[{id:r.DATA_MODELING_GUIDE,title:"Data modeling guide",onClick:()=>{var n;return null==e?void 0:null===(n=e.current)||void 0===n?void 0:n.play(r.DATA_MODELING_GUIDE,o(r.DATA_MODELING_GUIDE))}},{id:r.CREATING_MODEL,title:"Creating a model",href:"https://docs.getwren.ai/oss/guide/modeling/models",onClick:()=>t(r.CREATING_MODEL)},{id:r.CREATING_VIEW,title:"Creating a view",href:"https://docs.getwren.ai/oss/guide/modeling/views",onClick:()=>t(r.CREATING_VIEW)},{id:r.WORKING_RELATIONSHIP,title:"Working on relationship",href:"https://docs.getwren.ai/oss/guide/modeling/relationships",onClick:()=>t(r.WORKING_RELATIONSHIP)},{id:r.CONNECT_OTHER_DATA_SOURCES,title:"Connect to other data sources",href:"https://docs.getwren.ai/oss/guide/connect/overview",onClick:()=>t(r.CONNECT_OTHER_DATA_SOURCES)}],i=[{id:r.SWITCH_PROJECT_LANGUAGE,title:"Switch the language",onClick:()=>{var n;return null==e?void 0:null===(n=e.current)||void 0===n?void 0:n.play(r.SWITCH_PROJECT_LANGUAGE,o(r.SWITCH_PROJECT_LANGUAGE))}},{id:r.SHARE_RESULTS,title:"Export to Excel/Sheets",href:"https://docs.getwren.ai/oss/guide/integrations/excel-add-in",onClick:()=>t(r.SHARE_RESULTS)},{id:r.VIEW_FULL_SQL,title:"View full SQL",href:"https://docs.getwren.ai/oss/guide/home/<USER>",onClick:()=>t(r.VIEW_FULL_SQL)}];return n.startsWith(p.y$.Modeling)?l:n.startsWith(p.y$.Home)?i:[]},nZ=e=>e.startsWith(p.y$.Modeling)||e.startsWith(p.y$.Home);function nF(e){let n=(0,u.useRouter)(),[t,a]=(0,y.useState)(!0),l=(0,y.useRef)(null),i=(0,y.useRef)(null),{data:s}=function(e){let n={...nA};return nL.aM(nD,n)}(),[d]=function(e){let n={...nA,...e};return nT.D(nR,n)}({refetchQueries:["LearningRecord"]}),[c]=(0,nj.JO)({refetchQueries:["GetSettings"]}),m=async e=>{await d({variables:{data:{path:e}}})},g=async e=>{await c({variables:{data:{language:e}}})},h=(0,y.useMemo)(()=>{let e=nH(l,n.pathname,m,g),t=(null==s?void 0:s.learningRecord.paths)||[];return na()(e.map(e=>({...e,finished:t.includes(e.id)})),"finished")},[null==s?void 0:s.learningRecord]),f=(0,y.useMemo)(()=>h.length,[h]),x=(0,y.useMemo)(()=>h.filter(e=>null==e?void 0:e.finished).length,[h]),C=async e=>{if(i.current){let n=i.current.scrollHeight;i.current.style.height=e?"".concat(n,"px"):"0px",await (0,eU.Y3)(300),i.current&&(i.current.style.transition="height 0.3s")}};return(0,y.useEffect)(()=>{let e=null==s?void 0:s.learningRecord;if(e){a(h.some(n=>!e.paths.includes(n.id)));let t={[p.y$.Modeling]:async()=>{let n=e.paths.includes(r.DATA_MODELING_GUIDE),t=!!window.sessionStorage.getItem("skipDataModelingGuide");if(!(n||t)){var a;await (0,eU.Y3)(1e3),null===(a=l.current)||void 0===a||a.play(r.DATA_MODELING_GUIDE,{onDone:()=>m(r.DATA_MODELING_GUIDE)})}},[p.y$.Home]:async()=>{let n=e.paths.includes(r.SWITCH_PROJECT_LANGUAGE),t=!!window.sessionStorage.getItem("skipSwitchProjectLanguageGuide");if(!(n||t)){var a;await (0,eU.Y3)(1e3),null===(a=l.current)||void 0===a||a.play(r.SWITCH_PROJECT_LANGUAGE,{onDone:()=>m(r.SWITCH_PROJECT_LANGUAGE),onSaveLanguage:g})}},[p.y$.Thread]:async()=>{if(!e.paths.includes(r.SAVE_TO_KNOWLEDGE)){var n;await (0,eU.Y3)(1500),null===(n=l.current)||void 0===n||n.play(r.SAVE_TO_KNOWLEDGE,{onDone:()=>m(r.SAVE_TO_KNOWLEDGE)})}},[p.y$.KnowledgeQuestionSQLPairs]:async()=>{if(!e.paths.includes(r.KNOWLEDGE_GUIDE)){var n;await (0,eU.Y3)(1e3),null===(n=l.current)||void 0===n||n.play(r.KNOWLEDGE_GUIDE,{onDone:()=>m(r.KNOWLEDGE_GUIDE)})}}};t[n.pathname]&&t[n.pathname]()}},[null==s?void 0:s.learningRecord,n.pathname]),(0,y.useEffect)(()=>{C(t)},[t]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(nb,{ref:l}),nZ(n.pathname)&&(0,o.jsxs)("div",{className:"border-t border-gray-4",children:[(0,o.jsxs)("div",{className:"px-4 py-1 d-flex align-center cursor-pointer select-none",onClick:()=>{a(!t)},children:[(0,o.jsxs)("div",{className:"flex-grow-1",children:[(0,o.jsx)(e9(),{className:"mr-1"}),"Learning"]}),(0,o.jsx)(no(),{className:"text-sm",style:{transform:"rotate(".concat(t?"90deg":"0deg",")")}})]}),(0,o.jsxs)(nP,{ref:i,children:[(0,o.jsx)(nG,{data:h}),(0,o.jsxs)("div",{className:"px-4 py-2 d-flex align-center",children:[(0,o.jsx)(nk,{total:f,current:x}),(0,o.jsxs)("span",{className:"text-xs gray-6 text-nowrap pl-2",children:[x,"/",f," Finished"]})]})]})]})]})}let nB=i.ZP.div.withConfig({displayName:"sidebar__Layout",componentId:"sc-3f58aa6f-0"})(["position:relative;height:100%;background-color:var(--gray-2);color:var(--gray-8);padding-bottom:12px;overflow-x:hidden;"]),nU=i.ZP.div.withConfig({displayName:"sidebar__Content",componentId:"sc-3f58aa6f-1"})(["flex-grow:1;overflow-y:auto;"]),nW=(0,i.ZP)(m.default).withConfig({displayName:"sidebar__StyledButton",componentId:"sc-3f58aa6f-2"})(["cursor:pointer;display:flex;align-items:center;padding-left:16px;padding-right:16px;color:var(--gray-8) !important;border-radius:0;&:hover,&:focus{background-color:var(--gray-4);}"]),nq=e=>{let{pathname:n,...t}=e;return(0,o.jsx)(nU,{children:n.startsWith(p.y$.Home)?(0,o.jsx)(eu,{...t}):n.startsWith(p.y$.Modeling)?(0,o.jsx)(eQ,{...t}):n.startsWith(p.y$.Knowledge)?(0,o.jsx)(e5,{}):n.startsWith(p.y$.APIManagement)?(0,o.jsx)(nn,{}):null})};function nV(e){let{onOpenSettings:n}=e,t=(0,u.useRouter)();return(0,o.jsxs)(nB,{className:"d-flex flex-column",children:[(0,o.jsx)(nq,{...e,pathname:t.pathname}),(0,o.jsx)(nF,{}),(0,o.jsxs)("div",{className:"border-t border-gray-4 pt-2",children:[(0,o.jsxs)(nW,{type:"text",block:!0,onClick:e=>{n&&n(),e.target.blur()},children:[(0,o.jsx)(f(),{className:"text-md"}),"Settings"]}),(0,o.jsx)(nW,{type:"text",block:!0,children:(0,o.jsxs)(c(),{className:"d-flex align-center",href:"https://discord.com/invite/5DvshJqG8Z",target:"_blank",rel:"noopener noreferrer","data-ph-capture":"true","data-ph-capture-attribute-name":"cta_go_to_discord",children:[(0,o.jsx)(g.D7,{className:"mr-2",style:{width:16}})," Discord"]})}),(0,o.jsx)(nW,{type:"text",block:!0,children:(0,o.jsxs)(c(),{className:"d-flex align-center",href:"https://github.com/Canner/WrenAI",target:"_blank",rel:"noopener noreferrer","data-ph-capture":"true","data-ph-capture-attribute-name":"cta_go_to_github",children:[(0,o.jsx)(g.ET,{className:"mr-2",style:{width:16}})," GitHub"]})})]})]})}var nK=t(18310),nQ=t.n(nK),n$=t(25675),nz=t.n(n$),nY=t(13518),nJ=t(33190),nX=t(12891),n1=t(65613),n0=t(94638);let n2=(0,nl.x)(nX.Z),n5=e=>{let n=(0,u.useRouter)(),{sampleDataset:t,closeModal:a}=e,r=(0,nu.uR)(),[l]=(0,eZ.xv)({onError:e=>console.error(e),onCompleted:()=>{n.push(p.y$.Home),a()},refetchQueries:"active"});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-2",children:"Change sample dataset"}),(0,o.jsx)("div",{className:"d-grid grid-columns-3 g-4",children:(0,o.jsx)(n2,{data:r,selectedTemplate:t,onSelect:e=>{if(t!==e){let n=r.find(n=>n.value===e);eb.Z.confirm({title:'Are you sure you want to change to "'.concat(n.label,'" dataset?'),okButtonProps:{danger:!0},okText:"Change",onOk:async()=>{await l({variables:{data:{name:e}}})}})}}})}),(0,o.jsx)("div",{className:"gray-6 mt-1",children:"Please be aware that choosing another sample dataset will delete all thread records in the Home page."})]})},n3=e=>{let{type:n,properties:t,refetchSettings:a}=e,r=(0,nu.f6)(n),[l]=nY.Z.useForm(),[i,{loading:s,error:d}]=(0,eZ.Lh)({onError:e=>console.error(e),onCompleted:async()=>{a(),em.default.success("Successfully update data source.")}}),c=(0,y.useMemo)(()=>(0,n0.Bx)(d),[d]);(0,y.useEffect)(()=>t&&u(),[t]);let u=()=>{l.setFieldsValue((0,n1.yN)(t,n))};return n?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"d-flex align-center",children:[(0,o.jsx)(nz(),{className:"mr-2",src:r.logo,alt:r.label,width:"24",height:"24"}),r.label]}),(0,o.jsxs)(nY.Z,{form:l,layout:"vertical",className:"py-3 px-4",children:[(0,o.jsx)(r.component,{mode:p.SD.EDIT}),c&&(0,o.jsx)(ej.default,{message:c.shortMessage,description:c.message,type:"error",showIcon:!0,className:"my-6"}),(0,o.jsxs)("div",{className:"py-2 text-right",children:[(0,o.jsx)(m.default,{className:"mr-2",style:{width:80},onClick:u,children:"Cancel"}),(0,o.jsx)(m.default,{type:"primary",style:{width:80},onClick:()=>{l.validateFields().then(e=>{i({variables:{data:{properties:(0,n1.TR)(e,n)}}})}).catch(e=>{console.error(e)})},loading:s,children:"Save"})]})]})]}):(0,o.jsx)(nJ.OL,{align:"center",height:150})};function n6(e){let{sampleDataset:n}=e;return(0,o.jsx)("div",{className:"py-3 px-4",children:(0,o.jsx)(n?n5:n3,{...e})})}var n4=t(40582),n9=t(55041);function n7(e){let{data:n}=e,t=(0,u.useRouter)(),[a]=nY.Z.useForm(),[r,{client:l}]=(0,nj.in)(),i=Object.keys(eD.y9).map(e=>({label:nm(e),value:e})),[s,{loading:d}]=(0,nj.JO)({refetchQueries:["GetSettings"],onError:e=>console.error(e),onCompleted:()=>{em.default.success("Successfully updated project language.")}});return(0,o.jsxs)("div",{className:"py-3 px-4",children:[(0,o.jsx)(nY.Z,{form:a,layout:"vertical",initialValues:{language:n.language},children:(0,o.jsx)(nY.Z.Item,{label:"Project language",extra:"This setting will affect the language in which the AI responds to you.",children:(0,o.jsxs)(n4.default,{gutter:16,wrap:!1,children:[(0,o.jsx)(n9.default,{className:"flex-grow-1",children:(0,o.jsx)(nY.Z.Item,{name:"language",noStyle:!0,children:(0,o.jsx)(nd.default,{placeholder:"Select a language",showSearch:!0,options:i})})}),(0,o.jsx)(n9.default,{children:(0,o.jsx)(m.default,{type:"primary",style:{width:70},onClick:()=>{a.validateFields().then(e=>{s({variables:{data:e}})}).catch(e=>console.error(e))},loading:d,children:"Save"})})]})})}),(0,o.jsx)("div",{className:"gray-8 mb-2",children:"Reset project"}),(0,o.jsx)(m.default,{type:"primary",style:{width:70},danger:!0,onClick:()=>{eb.Z.confirm({title:"Are you sure you want to reset?",okButtonProps:{danger:!0},okText:"Reset",onOk:async()=>{await r(),l.clearStore(),t.push(p.y$.OnboardingConnection)}})},children:"Reset"}),(0,o.jsx)("div",{className:"gray-6 mt-1",children:"Please be aware that resetting will delete all current settings and records, including those in the Modeling Page and Home Page threads."})]})}var n8=t(40492),te=t.n(n8),tn=t(27639),tt=t.n(tn);let ta=e=>({[p.L6.DATA_SOURCE]:{icon:te(),label:"Data source settings"},[p.L6.PROJECT]:{icon:tt(),label:"Project settings"}})[e]||null,{Sider:tr,Content:to}=l.default,tl=(0,i.ZP)(tr).withConfig({displayName:"settings__StyledSider",componentId:"sc-b16e58f-0"})([".ant-layout-sider-children{display:flex;flex-direction:column;height:100%;}"]),ti=(0,i.ZP)(eb.Z).withConfig({displayName:"settings__StyledModal",componentId:"sc-b16e58f-1"})([".ant-modal-content{overflow:hidden;}.ant-modal-close-x{width:48px;height:48px;line-height:48px;}"]),ts=(0,i.ZP)(m.default).withConfig({displayName:"settings__StyledButton",componentId:"sc-b16e58f-2"})(["display:flex;align-items:center;padding:12px 8px;margin-bottom:4px;"]),td=e=>{let{menu:n,data:t,refetch:a,closeModal:r}=e,{dataSource:l,language:i}=t||{};return({[p.L6.DATA_SOURCE]:(0,o.jsx)(n6,{type:null==l?void 0:l.type,sampleDataset:null==l?void 0:l.sampleDataset,properties:null==l?void 0:l.properties,refetchSettings:a,closeModal:r}),[p.L6.PROJECT]:(0,o.jsx)(n7,{data:{language:i}})})[n]||null},tc=(0,nl.x)(e=>{let{currentMenu:n,value:t,onClick:a}=e,r=ta(t);return(0,o.jsx)(ts,{className:n===t?"geekblue-6 bg-gray-4":"gray-8",type:"text",block:!0,onClick:()=>a({value:t}),icon:(0,o.jsx)(r.icon,{}),children:r.label})});function tu(e){let{onClose:n,visible:t}=e,[a,r]=(0,y.useState)(p.L6.DATA_SOURCE),i=ta(a),s=Object.keys(p.L6).map(e=>({key:e,value:p.L6[e]})),[d,{data:c,refetch:u}]=(0,nj.bB)({fetchPolicy:"cache-and-network"}),m=(0,y.useMemo)(()=>{var e;return null==c?void 0:null===(e=c.settings)||void 0===e?void 0:e.productVersion},[null==c?void 0:c.settings]);return(0,y.useEffect)(()=>{t&&d()},[t]),(0,o.jsx)(ti,{width:950,bodyStyle:{padding:0,height:700},visible:t,footer:null,onCancel:n,destroyOnClose:!0,centered:!0,children:(0,o.jsxs)(l.default,{style:{height:"100%"},children:[(0,o.jsxs)(tl,{width:310,className:"border-r border-gray-4",children:[(0,o.jsxs)("div",{className:"gray-9 text-bold py-3 px-5",children:[(0,o.jsx)(f(),{className:"mr-2"}),"Settings"]}),(0,o.jsx)("div",{className:"p-3 flex-grow-1",children:(0,o.jsx)(tc,{data:s,currentMenu:a,onClick:e=>{let{value:n}=e;return r(n)}})}),!!m&&(0,o.jsxs)("div",{className:"gray-7 d-flex align-center p-3 px-5",children:[(0,o.jsx)(nQ(),{className:"mr-2 text-sm"}),"Wren AI version: ",m]})]}),(0,o.jsxs)(to,{className:"d-flex flex-column",children:[(0,o.jsxs)("div",{className:"d-flex align-center gray-9 border-b border-gray-4 text-bold py-3 px-4",children:[(0,o.jsx)(i.icon,{className:"mr-2"}),i.label]}),(0,o.jsx)("div",{className:"flex-grow-1",style:{overflowY:"auto"},children:(0,o.jsx)(td,{menu:a,data:null==c?void 0:c.settings,refetch:u,closeModal:n})})]})]})})}let{Sider:tm}=l.default,tp=(0,i.iv)(["height:calc(100vh - 48px);overflow:auto;"]),tg=(0,i.ZP)(l.default).withConfig({displayName:"SiderLayout__StyledContentLayout",componentId:"sc-3cb7a1b1-0"})(["position:relative;"," ",""],tp,e=>e.color&&"background-color: var(--".concat(e.color,");")),th=(0,i.ZP)(tm).withConfig({displayName:"SiderLayout__StyledSider",componentId:"sc-3cb7a1b1-1"})(["",""],tp);function tf(e){let{sidebar:n,loading:t,color:a}=e,r=(0,ep.Z)();return(0,o.jsxs)(s.Z,{loading:t,children:[(0,o.jsxs)(l.default,{className:"adm-layout",children:[(0,o.jsx)(th,{width:280,children:(0,o.jsx)(nV,{...n,onOpenSettings:r.openModal})}),(0,o.jsx)(tg,{color:a,children:e.children})]}),(0,o.jsx)(tu,{...r.state,onClose:r.closeModal})]})}},26123:function(e,n,t){t.d(n,{ZQ:function(){return p},ck:function(){return h},e0:function(){return m},fP:function(){return u},mU:function(){return f},pn:function(){return g},pv:function(){return x},py:function(){return y}});var a=t(85893),r=t(69828),o=t(48527),l=t.n(o),i=t(81506),s=t.n(i);let d=(e,n)=>t=>{let{title:o,content:i,modalProps:s={},onConfirm:d,...c}=t;return(0,a.jsx)(e,{icon:n.icon,onClick:()=>r.Z.confirm({autoFocusButton:null,cancelText:"Cancel",content:(null==n?void 0:n.content)||"This will be permanently deleted, please confirm you want to delete it.",icon:(0,a.jsx)(l(),{}),okText:"Delete",onOk:d,title:"Are you sure you want to delete this ".concat(null==n?void 0:n.itemName,"?"),width:464,...s,okButtonProps:{...s.okButtonProps,danger:!0}}),...c})},c=e=>{let{icon:n=null,disabled:t,...r}=e;return(0,a.jsxs)("a",{className:t?"":"red-5",...r,children:[n,"Delete"]})};d(c);let u=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"thread",content:"This will permanently delete all results history in this thread, please confirm you want to delete it."}),m=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"view",content:"This will be permanently deleted, please confirm you want to delete it."}),p=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"model",content:"This will be permanently deleted, please confirm you want to delete it."}),g=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"calculated field",content:"This will be permanently deleted, please confirm you want to delete it."}),h=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"relationship",content:"This will be permanently deleted, please confirm you want to delete it."}),f=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"dashboard item",content:"This will be permanently deleted, please confirm you want to delete it."}),x=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"question-SQL pair",content:"This action is permanent and cannot be undone. Are you sure you want to proceed?"}),y=d(c,{icon:(0,a.jsx)(s(),{className:"mr-2"}),itemName:"instruction",content:"This action is permanent and cannot be undone. Are you sure you want to proceed?"})},65613:function(e,n,t){t.d(n,{ZP:function(){return h},TR:function(){return p},yN:function(){return g}});var a=t(67294),r=t(84908),o=t(11163),l=t(94638),i=t(34217),s=t(46140),d=t(82729);function c(){let e=(0,d._)(["\n  query OnboardingStatus {\n    onboardingStatus {\n      status\n    }\n  }\n"]);return c=function(){return e},e}let u=(0,t(68806).Ps)(c()),m="************",p=(e,n)=>{if(n===s.ri.DUCKDB){let n=e.configurations.reduce((e,n)=>(n.key&&n.value&&(e[n.key]=n.value),e),{});return{...e,configurations:n,extensions:e.extensions.filter(e=>e)}}return{...e,password:(null==e?void 0:e.password)===m?void 0:null==e?void 0:e.password}},g=(e,n)=>{if(n===s.ri.BIG_QUERY)return{...e,credentials:void 0};if(n===s.ri.DUCKDB){let n=Object.entries((null==e?void 0:e.configurations)||{}).map(e=>{let[n,t]=e;return{key:n,value:t}}),t=(null==e?void 0:e.extensions)||[];return{...e,configurations:n.length?n:[{key:"",value:""}],extensions:t.length?t:[""]}}return{...e,password:(null==e?void 0:e.password)||m}};function h(){let[e,n]=(0,a.useState)(r.ye.STARTER),[t,s]=(0,a.useState)(),[d,c]=(0,a.useState)(null),m=(0,o.useRouter)();(0,a.useEffect)(()=>{e===r.ye.CREATE_DATA_SOURCE&&c(null)},[e]);let[g,{loading:h,error:f}]=(0,i.tl)({onError:e=>console.error(e),onCompleted:()=>m.push(r.y$.OnboardingModels)}),[x,{loading:y}]=(0,i.xv)({onError:e=>console.error(e),onCompleted:()=>m.push(r.y$.Modeling),refetchQueries:[{query:u}],awaitRefetchQueries:!0});(0,a.useEffect)(()=>{c((0,l.Bx)(f))},[f]);let C=async e=>{await g({variables:{data:{type:t,properties:p(e,t)}}})},v=async e=>{await x({variables:{data:{name:e}}})};return{stepKey:e,dataSource:t,onBack:()=>{e===r.ye.CREATE_DATA_SOURCE&&n(r.ye.STARTER)},onNext:t=>{e===r.ye.STARTER?t.dataSource?(s(null==t?void 0:t.dataSource),n(r.ye.CREATE_DATA_SOURCE)):v(t.template):e===r.ye.CREATE_DATA_SOURCE&&C(t.properties)},submitting:h||y,connectError:d}}},70474:function(e,n,t){t.d(n,{u:function(){return l}});var a=t(85893),r=t(39616),o=t(84908);let l=(e,n)=>{let{type:t}=e;switch(t.toUpperCase()){case o.BA.BOOLEAN:return(0,a.jsx)(r.Ye,{...n});case o.BA.JSON:case o.BA.RECORD:return(0,a.jsx)(r.tT,{...n});case o.BA.TEXT:return(0,a.jsx)(r.VL,{...n});case o.BA.BYTEA:case o.BA.VARBINARY:return(0,a.jsx)(r.cJ,{...n});case o.BA.UUID:case o.BA.OID:return(0,a.jsx)(r.Vu,{...n});case o.BA.TINYINT:case o.BA.INT2:case o.BA.SMALLINT:case o.BA.INT4:case o.BA.INTEGER:case o.BA.INT8:case o.BA.BIGINT:case o.BA.INT64:case o.BA.NUMERIC:case o.BA.DECIMAL:case o.BA.FLOAT4:case o.BA.REAL:case o.BA.FLOAT8:case o.BA.DOUBLE:case o.BA.INET:return(0,a.jsx)(r.r3,{...n});case o.BA.VARCHAR:case o.BA.CHAR:case o.BA.BPCHAR:case o.BA.STRING:case o.BA.NAME:return(0,a.jsx)(r.Rh,{...n});case o.BA.TIMESTAMP:case o.BA.TIMESTAMPTZ:case o.BA.DATE:case o.BA.INTERVAL:return(0,a.jsx)(r.Qu,{...n});default:return(0,a.jsx)(r.Se,{...n})}}},89997:function(e,n,t){t.d(n,{Ld:function(){return a},W1:function(){return l},WI:function(){return o},r1:function(){return r}});let a=(e,n)=>{document.addEventListener(e,n)},r=(e,n)=>{document.removeEventListener(e,n)},o=(e,n)=>{let t=new CustomEvent(e,{detail:n});document.dispatchEvent(t)},l={GO_TO_FIRST_MODEL:"goToFirstModel"}},50608:function(e,n,t){t.d(n,{i:function(){return i}});var a=t(85893),r=t(70474),o=t(84908),l=t(39616);let i=(e,n)=>{let{nodeType:t,type:i}=e;switch(t){case o.QZ.MODEL:return(0,a.jsx)(l.ZA,{title:"Model",...n});case o.QZ.METRIC:return(0,a.jsx)(l.km,{title:"Metric",...n});case o.QZ.VIEW:return(0,a.jsx)(l.ON,{title:"View",...n});case o.QZ.RELATION:return(0,a.jsx)(l.NA,{title:"Relationship",...n});case o.QZ.FIELD:return i?(0,r.u)({type:i},n):null;default:return null}}},32815:function(e,n,t){t.d(n,{gq:function(){return r},Wi:function(){return i},pB:function(){return l},Hx:function(){return o}});var a=t(85893);let r=e=>{let{fillCurrentColor:n=!0,className:t}=e;return(0,a.jsx)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M18.6562 11.7478L13.9791 10.0247L12.2522 5.34375C12.1201 4.98542 11.8813 4.67621 11.5679 4.45781C11.2546 4.2394 10.8819 4.12231 10.5 4.12231C10.1181 4.12231 9.74535 4.2394 9.43205 4.45781C9.11875 4.67621 8.87993 4.98542 8.74781 5.34375L7.02469 10.0247L2.34375 11.7478C1.98542 11.8799 1.67621 12.1187 1.45781 12.432C1.2394 12.7454 1.12231 13.1181 1.12231 13.5C1.12231 13.8819 1.2394 14.2546 1.45781 14.568C1.67621 14.8813 1.98542 15.1201 2.34375 15.2522L7.02094 16.9753L8.74781 21.6562C8.87993 22.0146 9.11875 22.3238 9.43205 22.5422C9.74535 22.7606 10.1181 22.8777 10.5 22.8777C10.8819 22.8777 11.2546 22.7606 11.5679 22.5422C11.8813 22.3238 12.1201 22.0146 12.2522 21.6562L13.9753 16.9791L18.6562 15.2522C19.0146 15.1201 19.3238 14.8813 19.5422 14.568C19.7606 14.2546 19.8777 13.8819 19.8777 13.5C19.8777 13.1181 19.7606 12.7454 19.5422 12.432C19.3238 12.1187 19.0146 11.8799 18.6562 11.7478ZM13.0312 14.9259C12.7777 15.0192 12.5475 15.1664 12.3565 15.3574C12.1655 15.5484 12.0183 15.7787 11.925 16.0322L10.5 19.9012L9.07406 16.0312C8.98079 15.778 8.83365 15.548 8.64282 15.3572C8.45198 15.1663 8.222 15.0192 7.96875 14.9259L4.09875 13.5L7.96875 12.0741C8.222 11.9808 8.45198 11.8337 8.64282 11.6428C8.83365 11.452 8.98079 11.222 9.07406 10.9688L10.5 7.09875L11.9259 10.9688C12.0192 11.2223 12.1664 11.4525 12.3574 11.6435C12.5484 11.8345 12.7787 11.9817 13.0322 12.075L16.9012 13.5L13.0312 14.9259ZM13.125 3.75C13.125 3.45163 13.2435 3.16548 13.4545 2.9545C13.6655 2.74353 13.9516 2.625 14.25 2.625H15.375V1.5C15.375 1.20163 15.4935 0.915483 15.7045 0.704505C15.9155 0.493526 16.2016 0.375 16.5 0.375C16.7984 0.375 17.0845 0.493526 17.2955 0.704505C17.5065 0.915483 17.625 1.20163 17.625 1.5V2.625H18.75C19.0484 2.625 19.3345 2.74353 19.5455 2.9545C19.7565 3.16548 19.875 3.45163 19.875 3.75C19.875 4.04837 19.7565 4.33452 19.5455 4.5455C19.3345 4.75647 19.0484 4.875 18.75 4.875H17.625V6C17.625 6.29837 17.5065 6.58452 17.2955 6.7955C17.0845 7.00647 16.7984 7.125 16.5 7.125C16.2016 7.125 15.9155 7.00647 15.7045 6.7955C15.4935 6.58452 15.375 6.29837 15.375 6V4.875H14.25C13.9516 4.875 13.6655 4.75647 13.4545 4.5455C13.2435 4.33452 13.125 4.04837 13.125 3.75ZM23.625 8.25C23.625 8.54837 23.5065 8.83452 23.2955 9.0455C23.0845 9.25647 22.7984 9.375 22.5 9.375H22.125V9.75C22.125 10.0484 22.0065 10.3345 21.7955 10.5455C21.5845 10.7565 21.2984 10.875 21 10.875C20.7016 10.875 20.4155 10.7565 20.2045 10.5455C19.9935 10.3345 19.875 10.0484 19.875 9.75V9.375H19.5C19.2016 9.375 18.9155 9.25647 18.7045 9.0455C18.4935 8.83452 18.375 8.54837 18.375 8.25C18.375 7.95163 18.4935 7.66548 18.7045 7.4545C18.9155 7.24353 19.2016 7.125 19.5 7.125H19.875V6.75C19.875 6.45163 19.9935 6.16548 20.2045 5.9545C20.4155 5.74353 20.7016 5.625 21 5.625C21.2984 5.625 21.5845 5.74353 21.7955 5.9545C22.0065 6.16548 22.125 6.45163 22.125 6.75V7.125H22.5C22.7984 7.125 23.0845 7.24353 23.2955 7.4545C23.5065 7.66548 23.625 7.95163 23.625 8.25Z",fill:n?"currentColor":void 0})})},o=e=>{let{fillCurrentColor:n=!0,className:t}=e;return(0,a.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 17 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.21025 5.02381H11.0755C11.8038 5.0238 12.4049 5.02379 12.8945 5.06379C13.4031 5.10535 13.8706 5.19452 14.3097 5.41826C14.9906 5.76523 15.5443 6.31888 15.8913 6.99984C16.115 7.43896 16.2042 7.90645 16.2457 8.41501C16.2857 8.90466 16.2857 9.50574 16.2857 10.234V14.2898C16.2857 15.0181 16.2857 15.6191 16.2457 16.1088C16.2042 16.6174 16.115 17.0849 15.8913 17.524C15.5443 18.2049 14.9906 18.7586 14.3097 19.1055C13.8706 19.3293 13.4031 19.4185 12.8945 19.46C12.4049 19.5 11.8038 19.5 11.0754 19.5H5.21027C4.48196 19.5 3.88086 19.5 3.39121 19.46C2.88264 19.4185 2.41515 19.3293 1.97603 19.1055C1.29507 18.7586 0.741424 18.2049 0.394453 17.524C0.170714 17.0849 0.0815369 16.6174 0.0399855 16.1088C-2.06409e-05 15.6191 -1.11504e-05 15.0181 3.90216e-07 14.2897V10.2341C-1.11504e-05 9.50576 -2.06409e-05 8.90467 0.0399855 8.41502C0.0815369 7.90645 0.170714 7.43896 0.394453 6.99984C0.741424 6.31888 1.29507 5.76523 1.97603 5.41826C2.41515 5.19452 2.88264 5.10535 3.39121 5.06379C3.88086 5.02379 4.48195 5.0238 5.21025 5.02381ZM3.53856 6.86731C3.1419 6.89972 2.93905 6.95846 2.79754 7.03056C2.45706 7.20404 2.18024 7.48087 2.00675 7.82135C1.93465 7.96285 1.87591 8.16571 1.8435 8.56237C1.81023 8.96959 1.80952 9.49643 1.80952 10.2714V14.2524C1.80952 15.0274 1.81023 15.5542 1.8435 15.9614C1.87591 16.3581 1.93465 16.561 2.00675 16.7025C2.18024 17.0429 2.45706 17.3198 2.79754 17.4932C2.93905 17.5653 3.1419 17.6241 3.53856 17.6565C3.94578 17.6898 4.47262 17.6905 5.24762 17.6905H11.0381C11.8131 17.6905 12.3399 17.6898 12.7472 17.6565C13.1438 17.6241 13.3467 17.5653 13.4882 17.4932C13.8287 17.3198 14.1055 17.0429 14.279 16.7025C14.3511 16.561 14.4098 16.3581 14.4422 15.9614C14.4755 15.5542 14.4762 15.0274 14.4762 14.2524V10.2714C14.4762 9.49643 14.4755 8.96959 14.4422 8.56237C14.4098 8.16571 14.3511 7.96285 14.279 7.82135C14.1055 7.48087 13.8287 7.20404 13.4882 7.03056C13.3467 6.95846 13.1438 6.89972 12.7472 6.86731C12.3399 6.83404 11.8131 6.83333 11.0381 6.83333H5.24762C4.47262 6.83333 3.94578 6.83404 3.53856 6.86731Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.33337 2.69857L6.33333 3.21434C6.3333 3.71403 5.9282 4.11908 5.42852 4.11905C4.92883 4.11902 4.52378 3.71392 4.52381 3.21423L4.52387 2.30947C4.52388 2.07611 4.61406 1.85178 4.77557 1.68334C4.99499 1.4545 5.32229 1.12171 5.89443 0.873475C6.44775 0.6334 7.16129 0.5 8.1429 0.5C9.12451 0.5 9.83805 0.63341 10.3914 0.873493C10.9635 1.12173 11.2908 1.45452 11.5102 1.68332C11.6717 1.85177 11.7619 2.07613 11.7619 2.30952V3.21429C11.7619 3.71397 11.3568 4.11905 10.8571 4.11905C10.3575 4.11905 9.95238 3.71397 9.95238 3.21429V2.69856C9.8723 2.63607 9.78449 2.58269 9.67111 2.53349C9.4179 2.42363 8.97077 2.30952 8.1429 2.30952C7.31502 2.30952 6.86788 2.42362 6.61467 2.53348C6.50127 2.58269 6.41345 2.63607 6.33337 2.69857Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.70476 13.5286C5.00457 13.1288 5.57168 13.0478 5.97143 13.3476C6.70123 13.895 7.4087 14.0718 8.14237 14.0714C8.87603 14.071 9.58508 13.8945 10.3143 13.3476C10.714 13.0478 11.2811 13.1288 11.581 13.5286C11.8808 13.9283 11.7997 14.4954 11.4 14.7952C10.3197 15.6055 9.21764 15.8804 8.14335 15.881C7.06593 15.8815 5.96543 15.605 4.88572 14.7952C4.48597 14.4954 4.40495 13.9283 4.70476 13.5286Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{d:"M3.61905 10.9048C3.61905 10.1552 4.22666 9.54762 4.97619 9.54762C5.72572 9.54762 6.33333 10.1552 6.33333 10.9048C6.33333 11.6543 5.72572 12.2619 4.97619 12.2619C4.22666 12.2619 3.61905 11.6543 3.61905 10.9048Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{d:"M9.95238 10.9048C9.95238 10.1552 10.56 9.54762 11.3095 9.54762C12.0591 9.54762 12.6667 10.1552 12.6667 10.9048C12.6667 11.6543 12.0591 12.2619 11.3095 12.2619C10.56 12.2619 9.95238 11.6543 9.95238 10.9048Z",fill:n?"currentColor":void 0})]})},l=e=>{let{fillCurrentColor:n=!0,className:t}=e;return(0,a.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.63962 1.97296L3.36755 -0.210815H4.63246L5.36038 1.97296L7.54415 2.70088V3.96579L5.36038 4.69372L4.63246 6.87749H3.36755L2.63962 4.69372L0.455849 3.96579V2.70088L2.63962 1.97296ZM4 2.10819L3.79912 2.71082L3.37749 3.13246L2.77485 3.33334L3.37749 3.53421L3.79912 3.95585L4 4.55849L4.20088 3.95585L4.62252 3.53421L5.22515 3.33334L4.62252 3.13246L4.20088 2.71082L4 2.10819ZM11.3926 4.02504C11.0926 4.00053 10.7044 4.00001 10.1333 4.00001H9.33332V2.66668H10.1609C10.6975 2.66667 11.1404 2.66666 11.5012 2.69614C11.8759 2.72675 12.2204 2.79246 12.544 2.95732C13.0457 3.21299 13.4537 3.62094 13.7093 4.1227C13.8742 4.44626 13.9399 4.79073 13.9705 5.16546C14 5.52625 14 5.96915 14 6.50579V8.82719C14 9.36383 14 9.80673 13.9705 10.1675C13.9399 10.5423 13.8742 10.8867 13.7093 11.2103C13.4537 11.712 13.0457 12.12 12.544 12.3757C12.2204 12.5405 11.8759 12.6062 11.5012 12.6368C11.1404 12.6663 10.6975 12.6663 10.1609 12.6663H10.0209L8.5062 14.4338L7.49378 14.4338L5.97905 12.6663H5.83911C5.30247 12.6663 4.85957 12.6663 4.49877 12.6368C4.12404 12.6062 3.77957 12.5405 3.45602 12.3757C2.95425 12.12 2.5463 11.712 2.29064 11.2103C2.12578 10.8867 2.06007 10.5422 2.02945 10.1675C1.99997 9.80672 1.99998 9.36382 1.99999 8.82717L1.99999 8.33315H3.33332V8.79963C3.33332 9.37069 3.33384 9.75888 3.35836 10.0589C3.38224 10.3512 3.42552 10.5007 3.47865 10.605C3.60648 10.8558 3.81045 11.0598 4.06134 11.1876C4.1656 11.2408 4.31507 11.2841 4.60735 11.3079C4.90741 11.3324 5.2956 11.333 5.86666 11.333H6.59237L7.99999 12.9755L9.40763 11.333H10.1333C10.7044 11.333 11.0926 11.3324 11.3926 11.3079C11.6849 11.2841 11.8344 11.2408 11.9386 11.1876C12.1895 11.0598 12.3935 10.8558 12.5213 10.605C12.5745 10.5007 12.6177 10.3512 12.6416 10.0589C12.6661 9.75888 12.6667 9.37069 12.6667 8.79964V6.53334C12.6667 5.96229 12.6661 5.57409 12.6416 5.27403C12.6177 4.98176 12.5745 4.83229 12.5213 4.72802C12.3935 4.47714 12.1895 4.27316 11.9386 4.14533C11.8344 4.09221 11.6849 4.04892 11.3926 4.02504Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.18377 7.18378L7.62053 5.87351H8.37947L8.81623 7.18378L10.1265 7.62053V8.37948L8.81623 8.81623L8.37947 10.1265H7.62053L7.18377 8.81623L5.87351 8.37948V7.62053L7.18377 7.18378Z",fill:n?"currentColor":void 0})]})},i=e=>{let{fillCurrentColor:n=!0,className:t}=e;return(0,a.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.9929 5.04652L10.3083 5.7311L11.602 7.0247L12.2867 6.34012C12.2652 6.26694 12.2367 6.18523 12.2012 6.0998C12.1016 5.86038 11.968 5.64205 11.8296 5.5036C11.6911 5.36514 11.4727 5.23157 11.2333 5.13201C11.1479 5.09648 11.0661 5.06797 10.9929 5.04652ZM10.6592 7.96748L9.36549 6.67388L5.92291 10.1162C5.50551 10.5335 5.25873 11.2125 5.12676 11.8724C5.09852 12.0136 5.07652 12.1489 5.0594 12.2738C5.18415 12.2566 5.31921 12.2346 5.46023 12.2064C6.11984 12.0744 6.79883 11.8275 7.21669 11.4097L10.6592 7.96748ZM4.33325 13C3.66659 13 3.66659 12.9996 3.66659 12.9996L3.66659 12.9983L3.66659 12.9957L3.66664 12.9882L3.66695 12.9637C3.66728 12.9432 3.66793 12.9146 3.66917 12.8787C3.67164 12.807 3.67649 12.7058 3.68602 12.5818C3.70502 12.3349 3.74295 11.9928 3.81931 11.611C3.96692 10.8728 4.27927 9.87414 4.98014 9.17333L10.2919 3.8619C10.4156 3.73825 10.5829 3.66813 10.7578 3.66667C11.0504 3.66422 11.4208 3.76595 11.7452 3.90087C12.0828 4.04124 12.47 4.25846 12.7723 4.56075C13.0747 4.86305 13.2919 5.25027 13.4323 5.58782C13.5672 5.91229 13.669 6.28262 13.6665 6.57528C13.6651 6.75017 13.5949 6.91748 13.4713 7.04115L8.15946 12.3525C7.45837 13.0536 6.45995 13.3661 5.72188 13.5138C5.34011 13.5902 4.99815 13.6282 4.75126 13.6472C4.62736 13.6567 4.5262 13.6616 4.4545 13.6641C4.41863 13.6653 4.39004 13.6659 4.36957 13.6663L4.34504 13.6666L4.33751 13.6666L4.33497 13.6666L4.33401 13.6666C4.33401 13.6666 4.33325 13.6666 4.33325 13ZM4.33325 13V13.6666C3.96506 13.6666 3.66659 13.3678 3.66659 12.9996L4.33325 13Z",fill:n?"currentColor":void 0}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.99992 0.333313C4.28687 0.333313 4.54163 0.516933 4.63237 0.789161L5.27697 2.72293L7.21074 3.36752C7.48297 3.45827 7.66659 3.71303 7.66659 3.99998C7.66659 4.28693 7.48297 4.54169 7.21074 4.63244L5.27697 5.27703L4.63237 7.2108C4.54163 7.48303 4.28687 7.66665 3.99992 7.66665C3.71297 7.66665 3.45821 7.48303 3.36746 7.2108L2.72287 5.27703L0.7891 4.63244C0.516872 4.54169 0.333252 4.28693 0.333252 3.99998C0.333252 3.71303 0.516872 3.45827 0.7891 3.36752L2.72287 2.72293L3.36746 0.789161C3.45821 0.516933 3.71297 0.333313 3.99992 0.333313ZM3.99992 3.10816L3.88237 3.4608C3.81602 3.65987 3.65981 3.81608 3.46074 3.88244L3.1081 3.99998L3.46074 4.11752C3.65981 4.18388 3.81602 4.34009 3.88237 4.53916L3.99992 4.89179L4.11746 4.53916C4.18382 4.34009 4.34003 4.18388 4.5391 4.11752L4.89173 3.99998L4.5391 3.88244C4.34003 3.81608 4.18382 3.65987 4.11746 3.4608L3.99992 3.10816Z",fill:n?"currentColor":void 0})]})}},36238:function(e,n,t){t.d(n,{Eq:function(){return p},Y3:function(){return c},ni:function(){return u},tq:function(){return d},uy:function(){return m}});var a=t(27484),r=t.n(a),o=t(70178),l=t.n(o),i=t(84110),s=t.n(i);r().extend(l()),r().extend(s());let d=Intl.DateTimeFormat().resolvedOptions().timeZone,c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return new Promise(n=>setTimeout(n,e))},u=e=>r()(e).fromNow(),m=e=>r()(e).format("YYYY-MM-DD HH:mm:ss"),p=e=>r()(e).format("YYYY-MM-DD HH:mm")}}]);