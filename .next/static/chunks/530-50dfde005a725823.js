"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[530],{68806:function(e,t,r){r.d(t,{Ps:function(){return G}});var n,i,s,a,o,l=r(97582);let u=/\r\n|[\n\r]/g;function c(e,t){let r=0,n=1;for(let i of e.body.matchAll(u)){if("number"==typeof i.index||function(e,t){if(!e)throw Error("Unexpected invariant triggered.")}(!1),i.index>=t)break;r=i.index+i[0].length,n+=1}return{line:n,column:t+1-r}}function h(e,t){let r=e.locationOffset.column-1,n="".padStart(r)+e.body,i=t.line-1,s=e.locationOffset.line-1,a=t.line+s,o=1===t.line?r:0,l=t.column+o,u=`${e.name}:${a}:${l}
`,c=n.split(/\r\n|[\n\r]/g),h=c[i];if(h.length>120){let e=Math.floor(l/80),t=[];for(let e=0;e<h.length;e+=80)t.push(h.slice(e,e+80));return u+p([[`${a} |`,t[0]],...t.slice(1,e+1).map(e=>["|",e]),["|","^".padStart(l%80)],["|",t[e+1]]])}return u+p([[`${a-1} |`,c[i-1]],[`${a} |`,h],["|","^".padStart(l)],[`${a+1} |`,c[i+1]]])}function p(e){let t=e.filter(([e,t])=>void 0!==t),r=Math.max(...t.map(([e])=>e.length));return t.map(([e,t])=>e.padStart(r)+(t?" "+t:"")).join("\n")}class d extends Error{constructor(e,...t){var r,n,i,s;let{nodes:a,source:o,positions:l,path:u,originalError:h,extensions:p}=function(e){let t=e[0];return null==t||"kind"in t||"length"in t?{nodes:t,source:e[1],positions:e[2],path:e[3],originalError:e[4],extensions:e[5]}:t}(t);super(e),this.name="GraphQLError",this.path=null!=u?u:void 0,this.originalError=null!=h?h:void 0,this.nodes=f(Array.isArray(a)?a:a?[a]:void 0);let E=f(null===(r=this.nodes)||void 0===r?void 0:r.map(e=>e.loc).filter(e=>null!=e));this.source=null!=o?o:null==E?void 0:null===(n=E[0])||void 0===n?void 0:n.source,this.positions=null!=l?l:null==E?void 0:E.map(e=>e.start),this.locations=l&&o?l.map(e=>c(o,e)):null==E?void 0:E.map(e=>c(e.source,e.start));let v="object"==typeof(s=null==h?void 0:h.extensions)&&null!==s?null==h?void 0:h.extensions:void 0;this.extensions=null!==(i=null!=p?p:v)&&void 0!==i?i:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),null!=h&&h.stack?Object.defineProperty(this,"stack",{value:h.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,d):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let e=this.message;if(this.nodes){for(let r of this.nodes)if(r.loc){var t;e+="\n\n"+h((t=r.loc).source,c(t.source,t.start))}}else if(this.source&&this.locations)for(let t of this.locations)e+="\n\n"+h(this.source,t);return e}toJSON(){let e={message:this.message};return null!=this.locations&&(e.locations=this.locations),null!=this.path&&(e.path=this.path),null!=this.extensions&&Object.keys(this.extensions).length>0&&(e.extensions=this.extensions),e}}function f(e){return void 0===e||0===e.length?void 0:e}function E(e,t,r){return new d(`Syntax Error: ${r}`,{source:e,positions:[t]})}var v=r(72380);(n=a||(a={})).QUERY="QUERY",n.MUTATION="MUTATION",n.SUBSCRIPTION="SUBSCRIPTION",n.FIELD="FIELD",n.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",n.FRAGMENT_SPREAD="FRAGMENT_SPREAD",n.INLINE_FRAGMENT="INLINE_FRAGMENT",n.VARIABLE_DEFINITION="VARIABLE_DEFINITION",n.SCHEMA="SCHEMA",n.SCALAR="SCALAR",n.OBJECT="OBJECT",n.FIELD_DEFINITION="FIELD_DEFINITION",n.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",n.INTERFACE="INTERFACE",n.UNION="UNION",n.ENUM="ENUM",n.ENUM_VALUE="ENUM_VALUE",n.INPUT_OBJECT="INPUT_OBJECT",n.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION";var k=r(97359),T=r(87392),m=r(68297);(i=o||(o={})).SOF="<SOF>",i.EOF="<EOF>",i.BANG="!",i.DOLLAR="$",i.AMP="&",i.PAREN_L="(",i.PAREN_R=")",i.SPREAD="...",i.COLON=":",i.EQUALS="=",i.AT="@",i.BRACKET_L="[",i.BRACKET_R="]",i.BRACE_L="{",i.PIPE="|",i.BRACE_R="}",i.NAME="Name",i.INT="Int",i.FLOAT="Float",i.STRING="String",i.BLOCK_STRING="BlockString",i.COMMENT="Comment";class N{constructor(e){let t=new v.WU(o.SOF,0,0,0,0);this.source=e,this.lastToken=t,this.token=t,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){return this.lastToken=this.token,this.token=this.lookahead()}lookahead(){let e=this.token;if(e.kind!==o.EOF)do if(e.next)e=e.next;else{let t=function(e,t){let r=e.source.body,n=r.length,i=t;for(;i<n;){let t=r.charCodeAt(i);switch(t){case 65279:case 9:case 32:case 44:++i;continue;case 10:++i,++e.line,e.lineStart=i;continue;case 13:10===r.charCodeAt(i+1)?i+=2:++i,++e.line,e.lineStart=i;continue;case 35:return function(e,t){let r=e.source.body,n=r.length,i=t+1;for(;i<n;){let e=r.charCodeAt(i);if(10===e||13===e)break;if(_(e))++i;else if(x(r,i))i+=2;else break}return O(e,o.COMMENT,t,i,r.slice(t+1,i))}(e,i);case 33:return O(e,o.BANG,i,i+1);case 36:return O(e,o.DOLLAR,i,i+1);case 38:return O(e,o.AMP,i,i+1);case 40:return O(e,o.PAREN_L,i,i+1);case 41:return O(e,o.PAREN_R,i,i+1);case 46:if(46===r.charCodeAt(i+1)&&46===r.charCodeAt(i+2))return O(e,o.SPREAD,i,i+3);break;case 58:return O(e,o.COLON,i,i+1);case 61:return O(e,o.EQUALS,i,i+1);case 64:return O(e,o.AT,i,i+1);case 91:return O(e,o.BRACKET_L,i,i+1);case 93:return O(e,o.BRACKET_R,i,i+1);case 123:return O(e,o.BRACE_L,i,i+1);case 124:return O(e,o.PIPE,i,i+1);case 125:return O(e,o.BRACE_R,i,i+1);case 34:if(34===r.charCodeAt(i+1)&&34===r.charCodeAt(i+2))return function(e,t){let r=e.source.body,n=r.length,i=e.lineStart,s=t+3,a=s,l="",u=[];for(;s<n;){let n=r.charCodeAt(s);if(34===n&&34===r.charCodeAt(s+1)&&34===r.charCodeAt(s+2)){l+=r.slice(a,s),u.push(l);let n=O(e,o.BLOCK_STRING,t,s+3,(0,T.wv)(u).join("\n"));return e.line+=u.length-1,e.lineStart=i,n}if(92===n&&34===r.charCodeAt(s+1)&&34===r.charCodeAt(s+2)&&34===r.charCodeAt(s+3)){l+=r.slice(a,s),a=s+1,s+=4;continue}if(10===n||13===n){l+=r.slice(a,s),u.push(l),13===n&&10===r.charCodeAt(s+1)?s+=2:++s,l="",a=s,i=s;continue}if(_(n))++s;else if(x(r,s))s+=2;else throw E(e.source,s,`Invalid character within String: ${I(e,s)}.`)}throw E(e.source,s,"Unterminated string.")}(e,i);return function(e,t){let r=e.source.body,n=r.length,i=t+1,s=i,a="";for(;i<n;){let n=r.charCodeAt(i);if(34===n)return a+=r.slice(s,i),O(e,o.STRING,t,i+1,a);if(92===n){a+=r.slice(s,i);let t=117===r.charCodeAt(i+1)?123===r.charCodeAt(i+2)?function(e,t){let r=e.source.body,n=0,i=3;for(;i<12;){let e=r.charCodeAt(t+i++);if(125===e){if(i<5||!_(n))break;return{value:String.fromCodePoint(n),size:i}}if((n=n<<4|D(e))<0)break}throw E(e.source,t,`Invalid Unicode escape sequence: "${r.slice(t,t+i)}".`)}(e,i):function(e,t){let r=e.source.body,n=b(r,t+2);if(_(n))return{value:String.fromCodePoint(n),size:6};if(y(n)&&92===r.charCodeAt(t+6)&&117===r.charCodeAt(t+7)){let e=b(r,t+8);if(A(e))return{value:String.fromCodePoint(n,e),size:12}}throw E(e.source,t,`Invalid Unicode escape sequence: "${r.slice(t,t+6)}".`)}(e,i):function(e,t){let r=e.source.body;switch(r.charCodeAt(t+1)){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:"\n",size:2};case 114:return{value:"\r",size:2};case 116:return{value:"	",size:2}}throw E(e.source,t,`Invalid character escape sequence: "${r.slice(t,t+2)}".`)}(e,i);a+=t.value,i+=t.size,s=i;continue}if(10===n||13===n)break;if(_(n))++i;else if(x(r,i))i+=2;else throw E(e.source,i,`Invalid character within String: ${I(e,i)}.`)}throw E(e.source,i,"Unterminated string.")}(e,i)}if((0,m.X1)(t)||45===t)return function(e,t,r){let n=e.source.body,i=t,s=r,a=!1;if(45===s&&(s=n.charCodeAt(++i)),48===s){if(s=n.charCodeAt(++i),(0,m.X1)(s))throw E(e.source,i,`Invalid number, unexpected digit after 0: ${I(e,i)}.`)}else i=g(e,i,s),s=n.charCodeAt(i);if(46===s&&(a=!0,s=n.charCodeAt(++i),i=g(e,i,s),s=n.charCodeAt(i)),(69===s||101===s)&&(a=!0,(43===(s=n.charCodeAt(++i))||45===s)&&(s=n.charCodeAt(++i)),i=g(e,i,s),s=n.charCodeAt(i)),46===s||(0,m.LQ)(s))throw E(e.source,i,`Invalid number, expected digit but got: ${I(e,i)}.`);return O(e,a?o.FLOAT:o.INT,t,i,n.slice(t,i))}(e,i,t);if((0,m.LQ)(t))return function(e,t){let r=e.source.body,n=r.length,i=t+1;for(;i<n;){let e=r.charCodeAt(i);if((0,m.HQ)(e))++i;else break}return O(e,o.NAME,t,i,r.slice(t,i))}(e,i);throw E(e.source,i,39===t?"Unexpected single quote character ('), did you mean to use a double quote (\")?":_(t)||x(r,i)?`Unexpected character: ${I(e,i)}.`:`Invalid character: ${I(e,i)}.`)}return O(e,o.EOF,n,n)}(this,e.end);e.next=t,t.prev=e,e=t}while(e.kind===o.COMMENT);return e}}function _(e){return e>=0&&e<=55295||e>=57344&&e<=1114111}function x(e,t){return y(e.charCodeAt(t))&&A(e.charCodeAt(t+1))}function y(e){return e>=55296&&e<=56319}function A(e){return e>=56320&&e<=57343}function I(e,t){let r=e.source.body.codePointAt(t);if(void 0===r)return o.EOF;if(r>=32&&r<=126){let e=String.fromCodePoint(r);return'"'===e?"'\"'":`"${e}"`}return"U+"+r.toString(16).toUpperCase().padStart(4,"0")}function O(e,t,r,n,i){let s=e.line,a=1+r-e.lineStart;return new v.WU(t,r,n,s,a,i)}function g(e,t,r){if(!(0,m.X1)(r))throw E(e.source,t,`Invalid number, expected digit but got: ${I(e,t)}.`);let n=e.source.body,i=t+1;for(;(0,m.X1)(n.charCodeAt(i));)++i;return i}function b(e,t){return D(e.charCodeAt(t))<<12|D(e.charCodeAt(t+1))<<8|D(e.charCodeAt(t+2))<<4|D(e.charCodeAt(t+3))}function D(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}var C=r(37826),S=r(25821);let R=globalThis.process?function(e,t){return e instanceof t}:function(e,t){if(e instanceof t)return!0;if("object"==typeof e&&null!==e){var r;let n=t.prototype[Symbol.toStringTag];if(n===(Symbol.toStringTag in e?e[Symbol.toStringTag]:null===(r=e.constructor)||void 0===r?void 0:r.name)){let t=(0,S.X)(e);throw Error(`Cannot use ${n} "${t}" from another module or realm.

Ensure that there is only one instance of "graphql" in the node_modules
directory. If different versions of "graphql" are the dependencies of other
relied on modules, use "resolutions" to ensure only one version is installed.

https://yarnpkg.com/en/docs/selective-version-resolutions

Duplicate "graphql" modules cannot be used at the same time since different
versions may have different capabilities and behavior. The data from one
version used in the function from another could produce confusing and
spurious results.`)}}return!1};class L{constructor(e,t="GraphQL request",r={line:1,column:1}){"string"==typeof e||(0,C.a)(!1,`Body must be a string. Received: ${(0,S.X)(e)}.`),this.body=e,this.name=t,this.locationOffset=r,this.locationOffset.line>0||(0,C.a)(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,C.a)(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}}class w{constructor(e,t={}){let r=R(e,L)?e:new L(e);this._lexer=new N(r),this._options=t,this._tokenCounter=0}get tokenCount(){return this._tokenCounter}parseName(){let e=this.expectToken(o.NAME);return this.node(e,{kind:k.h.NAME,value:e.value})}parseDocument(){return this.node(this._lexer.token,{kind:k.h.DOCUMENT,definitions:this.many(o.SOF,this.parseDefinition,o.EOF)})}parseDefinition(){if(this.peek(o.BRACE_L))return this.parseOperationDefinition();let e=this.peekDescription(),t=e?this._lexer.lookahead():this._lexer.token;if(t.kind===o.NAME){switch(t.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(e)throw E(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(t.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(t)}parseOperationDefinition(){let e;let t=this._lexer.token;if(this.peek(o.BRACE_L))return this.node(t,{kind:k.h.OPERATION_DEFINITION,operation:v.ku.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});let r=this.parseOperationType();return this.peek(o.NAME)&&(e=this.parseName()),this.node(t,{kind:k.h.OPERATION_DEFINITION,operation:r,name:e,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){let e=this.expectToken(o.NAME);switch(e.value){case"query":return v.ku.QUERY;case"mutation":return v.ku.MUTATION;case"subscription":return v.ku.SUBSCRIPTION}throw this.unexpected(e)}parseVariableDefinitions(){return this.optionalMany(o.PAREN_L,this.parseVariableDefinition,o.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:k.h.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(o.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(o.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){let e=this._lexer.token;return this.expectToken(o.DOLLAR),this.node(e,{kind:k.h.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:k.h.SELECTION_SET,selections:this.many(o.BRACE_L,this.parseSelection,o.BRACE_R)})}parseSelection(){return this.peek(o.SPREAD)?this.parseFragment():this.parseField()}parseField(){let e,t;let r=this._lexer.token,n=this.parseName();return this.expectOptionalToken(o.COLON)?(e=n,t=this.parseName()):t=n,this.node(r,{kind:k.h.FIELD,alias:e,name:t,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(o.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(e){let t=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(o.PAREN_L,t,o.PAREN_R)}parseArgument(e=!1){let t=this._lexer.token,r=this.parseName();return this.expectToken(o.COLON),this.node(t,{kind:k.h.ARGUMENT,name:r,value:this.parseValueLiteral(e)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){let e=this._lexer.token;this.expectToken(o.SPREAD);let t=this.expectOptionalKeyword("on");return!t&&this.peek(o.NAME)?this.node(e,{kind:k.h.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(e,{kind:k.h.INLINE_FRAGMENT,typeCondition:t?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){let e=this._lexer.token;return(this.expectKeyword("fragment"),!0===this._options.allowLegacyFragmentVariables)?this.node(e,{kind:k.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(e,{kind:k.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()}parseValueLiteral(e){let t=this._lexer.token;switch(t.kind){case o.BRACKET_L:return this.parseList(e);case o.BRACE_L:return this.parseObject(e);case o.INT:return this.advanceLexer(),this.node(t,{kind:k.h.INT,value:t.value});case o.FLOAT:return this.advanceLexer(),this.node(t,{kind:k.h.FLOAT,value:t.value});case o.STRING:case o.BLOCK_STRING:return this.parseStringLiteral();case o.NAME:switch(this.advanceLexer(),t.value){case"true":return this.node(t,{kind:k.h.BOOLEAN,value:!0});case"false":return this.node(t,{kind:k.h.BOOLEAN,value:!1});case"null":return this.node(t,{kind:k.h.NULL});default:return this.node(t,{kind:k.h.ENUM,value:t.value})}case o.DOLLAR:if(e){if(this.expectToken(o.DOLLAR),this._lexer.token.kind===o.NAME){let e=this._lexer.token.value;throw E(this._lexer.source,t.start,`Unexpected variable "$${e}" in constant value.`)}throw this.unexpected(t)}return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){let e=this._lexer.token;return this.advanceLexer(),this.node(e,{kind:k.h.STRING,value:e.value,block:e.kind===o.BLOCK_STRING})}parseList(e){return this.node(this._lexer.token,{kind:k.h.LIST,values:this.any(o.BRACKET_L,()=>this.parseValueLiteral(e),o.BRACKET_R)})}parseObject(e){return this.node(this._lexer.token,{kind:k.h.OBJECT,fields:this.any(o.BRACE_L,()=>this.parseObjectField(e),o.BRACE_R)})}parseObjectField(e){let t=this._lexer.token,r=this.parseName();return this.expectToken(o.COLON),this.node(t,{kind:k.h.OBJECT_FIELD,name:r,value:this.parseValueLiteral(e)})}parseDirectives(e){let t=[];for(;this.peek(o.AT);)t.push(this.parseDirective(e));return t}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(e){let t=this._lexer.token;return this.expectToken(o.AT),this.node(t,{kind:k.h.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e)})}parseTypeReference(){let e;let t=this._lexer.token;if(this.expectOptionalToken(o.BRACKET_L)){let r=this.parseTypeReference();this.expectToken(o.BRACKET_R),e=this.node(t,{kind:k.h.LIST_TYPE,type:r})}else e=this.parseNamedType();return this.expectOptionalToken(o.BANG)?this.node(t,{kind:k.h.NON_NULL_TYPE,type:e}):e}parseNamedType(){return this.node(this._lexer.token,{kind:k.h.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(o.STRING)||this.peek(o.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("schema");let r=this.parseConstDirectives(),n=this.many(o.BRACE_L,this.parseOperationTypeDefinition,o.BRACE_R);return this.node(e,{kind:k.h.SCHEMA_DEFINITION,description:t,directives:r,operationTypes:n})}parseOperationTypeDefinition(){let e=this._lexer.token,t=this.parseOperationType();this.expectToken(o.COLON);let r=this.parseNamedType();return this.node(e,{kind:k.h.OPERATION_TYPE_DEFINITION,operation:t,type:r})}parseScalarTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("scalar");let r=this.parseName(),n=this.parseConstDirectives();return this.node(e,{kind:k.h.SCALAR_TYPE_DEFINITION,description:t,name:r,directives:n})}parseObjectTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("type");let r=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),s=this.parseFieldsDefinition();return this.node(e,{kind:k.h.OBJECT_TYPE_DEFINITION,description:t,name:r,interfaces:n,directives:i,fields:s})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(o.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(o.BRACE_L,this.parseFieldDefinition,o.BRACE_R)}parseFieldDefinition(){let e=this._lexer.token,t=this.parseDescription(),r=this.parseName(),n=this.parseArgumentDefs();this.expectToken(o.COLON);let i=this.parseTypeReference(),s=this.parseConstDirectives();return this.node(e,{kind:k.h.FIELD_DEFINITION,description:t,name:r,arguments:n,type:i,directives:s})}parseArgumentDefs(){return this.optionalMany(o.PAREN_L,this.parseInputValueDef,o.PAREN_R)}parseInputValueDef(){let e;let t=this._lexer.token,r=this.parseDescription(),n=this.parseName();this.expectToken(o.COLON);let i=this.parseTypeReference();this.expectOptionalToken(o.EQUALS)&&(e=this.parseConstValueLiteral());let s=this.parseConstDirectives();return this.node(t,{kind:k.h.INPUT_VALUE_DEFINITION,description:r,name:n,type:i,defaultValue:e,directives:s})}parseInterfaceTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("interface");let r=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),s=this.parseFieldsDefinition();return this.node(e,{kind:k.h.INTERFACE_TYPE_DEFINITION,description:t,name:r,interfaces:n,directives:i,fields:s})}parseUnionTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("union");let r=this.parseName(),n=this.parseConstDirectives(),i=this.parseUnionMemberTypes();return this.node(e,{kind:k.h.UNION_TYPE_DEFINITION,description:t,name:r,directives:n,types:i})}parseUnionMemberTypes(){return this.expectOptionalToken(o.EQUALS)?this.delimitedMany(o.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("enum");let r=this.parseName(),n=this.parseConstDirectives(),i=this.parseEnumValuesDefinition();return this.node(e,{kind:k.h.ENUM_TYPE_DEFINITION,description:t,name:r,directives:n,values:i})}parseEnumValuesDefinition(){return this.optionalMany(o.BRACE_L,this.parseEnumValueDefinition,o.BRACE_R)}parseEnumValueDefinition(){let e=this._lexer.token,t=this.parseDescription(),r=this.parseEnumValueName(),n=this.parseConstDirectives();return this.node(e,{kind:k.h.ENUM_VALUE_DEFINITION,description:t,name:r,directives:n})}parseEnumValueName(){if("true"===this._lexer.token.value||"false"===this._lexer.token.value||"null"===this._lexer.token.value)throw E(this._lexer.source,this._lexer.token.start,`${F(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("input");let r=this.parseName(),n=this.parseConstDirectives(),i=this.parseInputFieldsDefinition();return this.node(e,{kind:k.h.INPUT_OBJECT_TYPE_DEFINITION,description:t,name:r,directives:n,fields:i})}parseInputFieldsDefinition(){return this.optionalMany(o.BRACE_L,this.parseInputValueDef,o.BRACE_R)}parseTypeSystemExtension(){let e=this._lexer.lookahead();if(e.kind===o.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)}parseSchemaExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");let t=this.parseConstDirectives(),r=this.optionalMany(o.BRACE_L,this.parseOperationTypeDefinition,o.BRACE_R);if(0===t.length&&0===r.length)throw this.unexpected();return this.node(e,{kind:k.h.SCHEMA_EXTENSION,directives:t,operationTypes:r})}parseScalarTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");let t=this.parseName(),r=this.parseConstDirectives();if(0===r.length)throw this.unexpected();return this.node(e,{kind:k.h.SCALAR_TYPE_EXTENSION,name:t,directives:r})}parseObjectTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");let t=this.parseName(),r=this.parseImplementsInterfaces(),n=this.parseConstDirectives(),i=this.parseFieldsDefinition();if(0===r.length&&0===n.length&&0===i.length)throw this.unexpected();return this.node(e,{kind:k.h.OBJECT_TYPE_EXTENSION,name:t,interfaces:r,directives:n,fields:i})}parseInterfaceTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");let t=this.parseName(),r=this.parseImplementsInterfaces(),n=this.parseConstDirectives(),i=this.parseFieldsDefinition();if(0===r.length&&0===n.length&&0===i.length)throw this.unexpected();return this.node(e,{kind:k.h.INTERFACE_TYPE_EXTENSION,name:t,interfaces:r,directives:n,fields:i})}parseUnionTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");let t=this.parseName(),r=this.parseConstDirectives(),n=this.parseUnionMemberTypes();if(0===r.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:k.h.UNION_TYPE_EXTENSION,name:t,directives:r,types:n})}parseEnumTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");let t=this.parseName(),r=this.parseConstDirectives(),n=this.parseEnumValuesDefinition();if(0===r.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:k.h.ENUM_TYPE_EXTENSION,name:t,directives:r,values:n})}parseInputObjectTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");let t=this.parseName(),r=this.parseConstDirectives(),n=this.parseInputFieldsDefinition();if(0===r.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:k.h.INPUT_OBJECT_TYPE_EXTENSION,name:t,directives:r,fields:n})}parseDirectiveDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("directive"),this.expectToken(o.AT);let r=this.parseName(),n=this.parseArgumentDefs(),i=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");let s=this.parseDirectiveLocations();return this.node(e,{kind:k.h.DIRECTIVE_DEFINITION,description:t,name:r,arguments:n,repeatable:i,locations:s})}parseDirectiveLocations(){return this.delimitedMany(o.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){let e=this._lexer.token,t=this.parseName();if(Object.prototype.hasOwnProperty.call(a,t.value))return t;throw this.unexpected(e)}node(e,t){return!0!==this._options.noLocation&&(t.loc=new v.Ye(e,this._lexer.lastToken,this._lexer.source)),t}peek(e){return this._lexer.token.kind===e}expectToken(e){let t=this._lexer.token;if(t.kind===e)return this.advanceLexer(),t;throw E(this._lexer.source,t.start,`Expected ${P(e)}, found ${F(t)}.`)}expectOptionalToken(e){return this._lexer.token.kind===e&&(this.advanceLexer(),!0)}expectKeyword(e){let t=this._lexer.token;if(t.kind===o.NAME&&t.value===e)this.advanceLexer();else throw E(this._lexer.source,t.start,`Expected "${e}", found ${F(t)}.`)}expectOptionalKeyword(e){let t=this._lexer.token;return t.kind===o.NAME&&t.value===e&&(this.advanceLexer(),!0)}unexpected(e){let t=null!=e?e:this._lexer.token;return E(this._lexer.source,t.start,`Unexpected ${F(t)}.`)}any(e,t,r){this.expectToken(e);let n=[];for(;!this.expectOptionalToken(r);)n.push(t.call(this));return n}optionalMany(e,t,r){if(this.expectOptionalToken(e)){let e=[];do e.push(t.call(this));while(!this.expectOptionalToken(r));return e}return[]}many(e,t,r){this.expectToken(e);let n=[];do n.push(t.call(this));while(!this.expectOptionalToken(r));return n}delimitedMany(e,t){this.expectOptionalToken(e);let r=[];do r.push(t.call(this));while(this.expectOptionalToken(e));return r}advanceLexer(){let{maxTokens:e}=this._options,t=this._lexer.advance();if(t.kind!==o.EOF&&(++this._tokenCounter,void 0!==e&&this._tokenCounter>e))throw E(this._lexer.source,t.start,`Document contains more that ${e} tokens. Parsing aborted.`)}}function F(e){let t=e.value;return P(e.kind)+(null!=t?` "${t}"`:"")}function P(e){return e===o.BANG||e===o.DOLLAR||e===o.AMP||e===o.PAREN_L||e===o.PAREN_R||e===o.SPREAD||e===o.COLON||e===o.EQUALS||e===o.AT||e===o.BRACKET_L||e===o.BRACKET_R||e===o.BRACE_L||e===o.PIPE||e===o.BRACE_R?`"${e}"`:e}var M=new Map,U=new Map,B=!0,K=!1;function V(e){return e.replace(/[\s,]+/g," ").trim()}function G(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];"string"==typeof e&&(e=[e]);var n=e[0];return t.forEach(function(t,r){t&&"Document"===t.kind?n+=t.loc.source.body:n+=t,n+=e[r+1]}),function(e){var t=V(e);if(!M.has(t)){var r,n,i,s,a,o=function(e,t){let r=new w(e,t),n=r.parseDocument();return Object.defineProperty(n,"tokenCount",{enumerable:!1,value:r.tokenCount}),n}(e,{experimentalFragmentVariables:K,allowLegacyFragmentVariables:K});if(!o||"Document"!==o.kind)throw Error("Not a valid GraphQL document.");M.set(t,((s=new Set((r=new Set,n=[],o.definitions.forEach(function(e){if("FragmentDefinition"===e.kind){var t,i=e.name.value,s=V((t=e.loc).source.body.substring(t.start,t.end)),a=U.get(i);a&&!a.has(s)?B&&console.warn("Warning: fragment with name "+i+" already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names"):a||U.set(i,a=new Set),a.add(s),r.has(s)||(r.add(s),n.push(e))}else n.push(e)}),i=(0,l.pi)((0,l.pi)({},o),{definitions:n})).definitions)).forEach(function(e){e.loc&&delete e.loc,Object.keys(e).forEach(function(t){var r=e[t];r&&"object"==typeof r&&s.add(r)})}),(a=i.loc)&&(delete a.startToken,delete a.endToken),i))}return M.get(t)}(n)}var Q=G;(s=G||(G={})).gql=Q,s.resetCaches=function(){M.clear(),U.clear()},s.disableFragmentWarnings=function(){B=!1},s.enableExperimentalFragmentVariables=function(){K=!0},s.disableExperimentalFragmentVariables=function(){K=!1},G.default=G},66252:function(e,t,r){r.d(t,{x:function(){return a}});var n=r(78287),i=r(73914),s=r(85317);function a(e){var t=i.useContext((0,s.K)()),r=e||t.client;return(0,n.kG)(!!r,58),r}},6812:function(e,t,r){r.d(t,{mp:function(){return D},_F:function(){return L},RN:function(){return C},KH:function(){return w},aM:function(){return O},p1:function(){return b}});var n=r(97582),i=r(78287),s=r(73914),a=r.t(s,2),o=r(30320),l=!1,u=a.useSyncExternalStore||function(e,t,r){var n=t();!1===globalThis.__DEV__||l||n===t()||(l=!0,!1!==globalThis.__DEV__&&i.kG.error(68));var a=s.useState({inst:{value:n,getSnapshot:t}}),u=a[0].inst,h=a[1];return o.JC?s.useLayoutEffect(function(){Object.assign(u,{value:n,getSnapshot:t}),c(u)&&h({inst:u})},[e,n,t]):Object.assign(u,{value:n,getSnapshot:t}),s.useEffect(function(){return c(u)&&h({inst:u}),e(function(){c(u)&&h({inst:u})})},[e]),n};function c(e){var t=e.value,r=e.getSnapshot;try{return t!==r()}catch(e){return!0}}var h=r(30020),p=r(14012),d=r(85317),f=r(30990),E=r(22599),v=r(1644),k=r(14692),T=r(66252),m=r(53712),N=r(21436),_=r(48702),x=Symbol.for("apollo.hook.wrappers"),y=Object.prototype.hasOwnProperty;function A(){}var I=Symbol();function O(e,t){var r,n,i,s;return void 0===t&&(t=Object.create(null)),(r=g,(s=(i=(n=(0,T.x)(t&&t.client).queryManager)&&n[x])&&i.useQuery)?s(r):r)(e,t)}function g(e,t){var r=b(e,t),i=r.result,a=r.obsQueryFields;return s.useMemo(function(){return(0,n.pi)((0,n.pi)({},i),a)},[i,a])}function b(e,t){var r,i,a,o,l,c,p=(0,T.x)(t.client),f=s.useContext((0,d.K)()).renderPromises,m=!!f,N=p.disableNetworkFetches,_=!1!==t.ssr&&!t.skip,x=t.partialRefetch,O=D(p,e,t,m),g=function(e,t,r,i,a){function o(n){var s;return(0,k.Vp)(t,k.n_.Query),{client:e,query:t,observable:i&&i.getSSRObservable(a())||E.u.inactiveOnCreation.withValue(!i,function(){return e.watchQuery(C(void 0,e,r,a()))}),resultData:{previousData:null===(s=null==n?void 0:n.resultData.current)||void 0===s?void 0:s.data}}}var l=s.useState(o),u=l[0],c=l[1];function h(e){Object.assign(u.observable,((t={})[I]=e,t));var t,r,i=u.resultData;c((0,n.pi)((0,n.pi)({},u),{query:e.query,resultData:Object.assign(i,{previousData:(null===(r=i.current)||void 0===r?void 0:r.data)||i.previousData,current:void 0})}))}if(e!==u.client||t!==u.query){var p=o(u);return c(p),[p,h]}return[u,h]}(p,e,t,f,O),b=g[0],L=b.observable,M=b.resultData,U=g[1],B=O(L);L[I]&&!(0,h.D)(L[I],B)&&(L.reobserve(C(L,p,t,B)),M.previousData=(null===(r=M.current)||void 0===r?void 0:r.data)||M.previousData,M.current=void 0),L[I]=B;var K=s.useMemo(function(){return{refetch:L.refetch.bind(L),reobserve:L.reobserve.bind(L),fetchMore:L.fetchMore.bind(L),updateQuery:L.updateQuery.bind(L),startPolling:L.startPolling.bind(L),stopPolling:L.stopPolling.bind(L),subscribeToMore:L.subscribeToMore.bind(L)}},[L]);return f&&_&&(f.registerSSRObservable(L),L.getCurrentResult().loading&&f.addObservableQueryPromise(L)),{result:(i={onCompleted:t.onCompleted||A,onError:t.onError||A},a=s.useRef(i),s.useEffect(function(){a.current=i}),o=(m||N)&&!1===t.ssr&&!t.skip?F:t.skip||"standby"===B.fetchPolicy?P:void 0,l=M.previousData,c=s.useMemo(function(){return o&&w(o,l,L,p)},[p,L,o,l]),u(s.useCallback(function(e){if(m)return function(){};var t=function(){var t=M.current,r=L.getCurrentResult();t&&t.loading===r.loading&&t.networkStatus===r.networkStatus&&(0,h.D)(t.data,r.data)||S(r,M,L,p,x,e,a.current)},r=function(i){if(n.current.unsubscribe(),n.current=L.resubscribeAfterError(t,r),!y.call(i,"graphQLErrors"))throw i;var s=M.current;(!s||s&&s.loading||!(0,h.D)(i,s.error))&&S({data:s&&s.data,error:i,loading:!1,networkStatus:v.Ie.error},M,L,p,x,e,a.current)},n={current:L.subscribe(t,r)};return function(){setTimeout(function(){return n.current.unsubscribe()})}},[N,m,L,M,x,p]),function(){return c||R(M,L,a.current,x,p)},function(){return c||R(M,L,a.current,x,p)})),obsQueryFields:K,observable:L,resultData:M,client:p,onQueryExecuted:U}}function D(e,t,r,i){void 0===r&&(r={});var s=r.skip,a=(r.ssr,r.onCompleted,r.onError,r.defaultOptions),o=(0,n._T)(r,["skip","ssr","onCompleted","onError","defaultOptions"]);return function(r){var n=Object.assign(o,{query:t});return i&&("network-only"===n.fetchPolicy||"cache-and-network"===n.fetchPolicy)&&(n.fetchPolicy="cache-first"),n.variables||(n.variables={}),s?(n.initialFetchPolicy=n.initialFetchPolicy||n.fetchPolicy||L(a,e.defaultOptions),n.fetchPolicy="standby"):n.fetchPolicy||(n.fetchPolicy=(null==r?void 0:r.options.initialFetchPolicy)||L(a,e.defaultOptions)),n}}function C(e,t,r,n){var i=[],s=t.defaultOptions.watchQuery;return s&&i.push(s),r.defaultOptions&&i.push(r.defaultOptions),i.push((0,m.o)(e&&e.options,n)),i.reduce(p.J)}function S(e,t,r,s,a,o,l){var u=t.current;u&&u.data&&(t.previousData=u.data),!e.error&&(0,N.O)(e.errors)&&(e.error=new f.cA({graphQLErrors:e.errors})),t.current=w(e.partial&&a&&!e.loading&&(!e.data||0===Object.keys(e.data).length)&&"cache-only"!==r.options.fetchPolicy?(r.refetch(),(0,n.pi)((0,n.pi)({},e),{loading:!0,networkStatus:v.Ie.refetch})):e,t.previousData,r,s),o(),function(e,t,r){if(!e.loading){var n=(0,N.O)(e.errors)?new f.cA({graphQLErrors:e.errors}):e.error;Promise.resolve().then(function(){n?r.onError(n):e.data&&t!==e.networkStatus&&e.networkStatus===v.Ie.ready&&r.onCompleted(e.data)}).catch(function(e){!1!==globalThis.__DEV__&&i.kG.warn(e)})}}(e,null==u?void 0:u.networkStatus,l)}function R(e,t,r,n,i){return e.current||S(t.getCurrentResult(),e,t,i,n,function(){},r),e.current}function L(e,t){var r;return(null==e?void 0:e.fetchPolicy)||(null===(r=null==t?void 0:t.watchQuery)||void 0===r?void 0:r.fetchPolicy)||"cache-first"}function w(e,t,r,i){var s=e.data,a=(e.partial,(0,n._T)(e,["data","partial"]));return(0,n.pi)((0,n.pi)({data:s},a),{client:i,observable:r,variables:r.variables,called:e!==F&&e!==P,previousData:t})}var F=(0,_.J)({loading:!0,data:void 0,error:void 0,networkStatus:v.Ie.loading}),P=(0,_.J)({loading:!1,data:void 0,error:void 0,networkStatus:v.Ie.ready})},14692:function(e,t,r){r.d(t,{Vp:function(){return p},n_:function(){return i}});var n,i,s,a=r(78287),o=r(38991),l=r(66331),u=r(88244);function c(e){var t;switch(e){case i.Query:t="Query";break;case i.Mutation:t="Mutation";break;case i.Subscription:t="Subscription"}return t}function h(e){s||(s=new o.s(l.Q.parser||1e3));var t,r,n=s.get(e);if(n)return n;(0,a.kG)(!!e&&!!e.kind,70,e);for(var u=[],c=[],h=[],p=[],d=0,f=e.definitions;d<f.length;d++){var E=f[d];if("FragmentDefinition"===E.kind){u.push(E);continue}if("OperationDefinition"===E.kind)switch(E.operation){case"query":c.push(E);break;case"mutation":h.push(E);break;case"subscription":p.push(E)}}(0,a.kG)(!u.length||c.length||h.length||p.length,71),(0,a.kG)(c.length+h.length+p.length<=1,72,e,c.length,p.length,h.length),r=c.length?i.Query:i.Mutation,c.length||h.length||(r=i.Subscription);var v=c.length?c:h.length?h:p;(0,a.kG)(1===v.length,73,e,v.length);var k=v[0];t=k.variableDefinitions||[];var T={name:k.name&&"Name"===k.name.kind?k.name.value:"data",type:r,variables:t};return s.set(e,T),T}function p(e,t){var r=h(e),n=c(t),i=c(r.type);(0,a.kG)(r.type===t,74,n,n,i)}(n=i||(i={}))[n.Query=0]="Query",n[n.Mutation=1]="Mutation",n[n.Subscription=2]="Subscription",h.resetCache=function(){s=void 0},!1!==globalThis.__DEV__&&(0,u.zP)("parser",function(){return s?s.size:0})},82729:function(e,t,r){r.d(t,{_:function(){return n}});function n(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}}}]);