"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[775],{37711:function(e,t,n){n.r(t),n.d(t,{default:function(){return C}});var l=n(85893),i=n(67294),s=n(90512),r=n(41609),a=n.n(r),c=n(62819),o=n(21367),u=n(29060),d=n(18496),f=n(6977),h=n(79421),x=n(7337),j=n.n(x),m=n(93181),p=n.n(m),v=n(92870),g=n.n(v),w=n(93646),b=n.n(w);let k={mode:"vega-lite",renderer:"svg",tooltip:{theme:"custom"},actions:{export:!0,editor:!1}};function C(e){let{className:t,spec:n,values:r,width:x=600,height:m=320,autoFilter:v,hideActions:w,hideTitle:C,hideLegend:y,forceUpdate:N,onReload:S,onEdit:E,onPin:_}=e,[H,P]=(0,i.useState)(null),[R,T]=(0,i.useState)(!1),Z=(0,i.useRef)(null),z=(0,i.useRef)(null),q=(0,i.useMemo)(()=>{var e;if(!n||!r)return;let t=new h.ZP({...n,data:{values:r}},{donutInner:H,isShowTopCategories:v||R,isHideLegend:y,isHideTitle:C}),l=t.getChartSpec();return a()(null==l?void 0:null===(e=l.data)||void 0===e?void 0:e.values)?null:(0,d.compile)(l,{config:t.config}).spec},[n,r,R,H,N]);return((0,i.useEffect)(()=>(z.current&&q&&(0,f.ZP)(z.current,q,k).then(e=>{Z.current=e}),()=>{Z.current&&Z.current.finalize()}),[q,N]),(0,i.useEffect)(()=>{z.current&&P(.15*z.current.clientHeight)},[N]),null===q)?0===r.length?(0,l.jsx)("div",{children:"No available data"}):(0,l.jsx)(c.default,{className:"mt-6 mb-4 mx-4",message:(0,l.jsxs)("div",{className:"d-flex align-center justify-space-between",children:[(0,l.jsx)("div",{children:"There are too many categories to display effectively. Click 'Show top 25' to view the top results, or ask a follow-up question to focus on a specific group or filter results."}),(0,l.jsx)(o.default,{size:"small",icon:(0,l.jsx)(g(),{}),onClick:()=>{T(!R)},children:"Show top 25"})]}),type:"warning"}):(0,l.jsxs)("div",{className:(0,s.Z)("adm-chart",{"adm-chart--no-actions":w},t),style:{width:x},children:[(!!S||!!E||!!_)&&(0,l.jsxs)("div",{className:"adm-chart-additional d-flex justify-content-between align-center",children:[!!S&&(0,l.jsx)(u.default,{title:"Regenerate chart",children:(0,l.jsx)("button",{onClick:S,children:(0,l.jsx)(j(),{})})}),!!E&&(0,l.jsx)(u.default,{title:"Edit chart",children:(0,l.jsx)("button",{onClick:E,children:(0,l.jsx)(p(),{})})}),!!_&&(0,l.jsx)(u.default,{title:"Pin chart to dashboard",children:(0,l.jsx)("button",{onClick:_,children:(0,l.jsx)(b(),{})})})]}),(0,l.jsx)("div",{style:{width:x,height:m},ref:z})]})}}}]);