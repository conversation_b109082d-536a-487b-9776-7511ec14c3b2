(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[143],{48838:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M632 888H392c-4.4 0-8 3.6-8 8v32c0 17.7 14.3 32 32 32h192c17.7 0 32-14.3 32-32v-32c0-4.4-3.6-8-8-8zM512 64c-181.1 0-328 146.9-328 328 0 121.4 66 227.4 164 284.1V792c0 17.7 14.3 32 32 32h264c17.7 0 32-14.3 32-32V676.1c98-56.7 164-162.7 164-284.1 0-181.1-146.9-328-328-328zm127.9 549.8L604 634.6V752H420V634.6l-35.9-20.8C305.4 568.3 256 484.5 256 392c0-141.4 114.6-256 256-256s256 114.6 256 256c0 92.5-49.4 176.3-128.1 221.8z"}}]},name:"bulb",theme:"outlined"}},54044:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"}}]},name:"caret-right",theme:"outlined"}},72048:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"}},11451:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm234.8 736.5L223.5 277.2c16-19.7 34-37.7 53.7-53.7l523.3 523.3c-16 19.6-34 37.7-53.7 53.7z"}}]},name:"stop",theme:"filled"}},94470:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},u=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,s,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=u(f,n),f!==(i=u(t,n))&&(h&&i&&(o(i)||(s=l(i)))?(s?(s=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(h,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},18139:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,u=/^\s+|\s+$/g;function s(e){return e?e.replace(u,""):""}e.exports=function(e,u){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];u=u||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function d(){var e={line:c,column:f};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:f},this.source=u.source}h.prototype.content=e;var m=[];function g(t){var n=Error(u.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=u.source,n.line=c,n.column=f,n.source=e,u.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function v(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}return y(r),function(){var e,n=[];for(v(n);e=function(){var e=d(),n=y(i);if(n){if(x(),!y(l))return g("property missing ':'");var r=y(o),u=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return y(a),u}}();)!1!==e&&(n.push(e),v(n));return n}()}},44174:function(e){e.exports=function(e,t,n,r){for(var i=-1,l=null==e?0:e.length;++i<l;){var o=e[i];t(r,o,n(o),e)}return r}},81119:function(e,t,n){var r=n(89881);e.exports=function(e,t,n,i){return r(e,function(e,r,l){t(i,e,n(e),l)}),i}},13127:function(e,t,n){var r=n(97786),i=n(10611),l=n(71811);e.exports=function(e,t,n){for(var o=-1,a=t.length,u={};++o<a;){var s=t[o],c=r(e,s);n(c,s)&&i(u,l(s,e),c)}return u}},10611:function(e,t,n){var r=n(34865),i=n(71811),l=n(65776),o=n(13218),a=n(40327);e.exports=function(e,t,n,u){if(!o(e))return e;t=i(t,e);for(var s=-1,c=t.length,f=c-1,p=e;null!=p&&++s<c;){var d=a(t[s]),h=n;if("__proto__"===d||"constructor"===d||"prototype"===d)break;if(s!=f){var m=p[d];void 0===(h=u?u(m,d,p):void 0)&&(h=o(m)?m:l(t[s+1])?[]:{})}r(p,d,h),p=p[d]}return e}},45652:function(e,t,n){var r=n(88668),i=n(47443),l=n(1196),o=n(74757),a=n(23593),u=n(21814);e.exports=function(e,t,n){var s=-1,c=i,f=e.length,p=!0,d=[],h=d;if(n)p=!1,c=l;else if(f>=200){var m=t?null:a(e);if(m)return u(m);p=!1,c=o,h=new r}else h=t?[]:d;e:for(;++s<f;){var g=e[s],y=t?t(g):g;if(g=n||0!==g?g:0,p&&y==y){for(var v=h.length;v--;)if(h[v]===y)continue e;t&&h.push(y),d.push(g)}else c(h,y,n)||(h!==d&&h.push(y),d.push(g))}return d}},55189:function(e,t,n){var r=n(44174),i=n(81119),l=n(67206),o=n(1469);e.exports=function(e,t){return function(n,a){var u=o(n)?r:i,s=t?t():{};return u(n,e,l(a,2),s)}}},23593:function(e,t,n){var r=n(58525),i=n(50308),l=n(21814),o=r&&1/l(new r([,-0]))[1]==1/0?function(e){return new r(e)}:i;e.exports=o},50361:function(e,t,n){var r=n(85990);e.exports=function(e){return r(e,5)}},52353:function(e){e.exports=function(e){return void 0===e}},94885:function(e){e.exports=function(e){if("function"!=typeof e)throw TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}},50308:function(e){e.exports=function(){}},14176:function(e,t,n){var r=n(67206),i=n(94885),l=n(35937);e.exports=function(e,t){return l(e,i(r(t)))}},35937:function(e,t,n){var r=n(29932),i=n(67206),l=n(13127),o=n(46904);e.exports=function(e,t){if(null==e)return{};var n=r(o(e),function(e){return[e]});return t=i(t),l(e,n,function(e,n){return t(e,n[0])})}},44908:function(e,t,n){var r=n(45652);e.exports=function(e){return e&&e.length?r(e):[]}},76774:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,i=(r=n(30111))&&r.__esModule?r:{default:r};t.default=i,e.exports=i},51228:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,i=(r=n(75491))&&r.__esModule?r:{default:r};t.default=i,e.exports=i},50604:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,i=(r=n(80265))&&r.__esModule?r:{default:r};t.default=i,e.exports=i},49374:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,i=(r=n(54946))&&r.__esModule?r:{default:r};t.default=i,e.exports=i},30111:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(42122)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var n=s(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(48838)),u=r(n(3247));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}var c=o.forwardRef(function(e,t){return o.createElement(u.default,(0,l.default)((0,l.default)({},e),{},{ref:t,icon:a.default}))});t.default=c},75491:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(42122)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var n=s(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(54044)),u=r(n(3247));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}var c=o.forwardRef(function(e,t){return o.createElement(u.default,(0,l.default)((0,l.default)({},e),{},{ref:t,icon:a.default}))});t.default=c},80265:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(42122)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var n=s(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(72048)),u=r(n(3247));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}var c=o.forwardRef(function(e,t){return o.createElement(u.default,(0,l.default)((0,l.default)({},e),{},{ref:t,icon:a.default}))});t.default=c},54946:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(42122)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var n=s(void 0);if(n&&n.has(e))return n.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(11451)),u=r(n(3247));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}var c=o.forwardRef(function(e,t){return o.createElement(u.default,(0,l.default)((0,l.default)({},e),{},{ref:t,icon:a.default}))});t.default=c},7471:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(10434)),o=r(n(38416)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=p(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),u=r(n(18475)),s=r(n(93967)),c=n(31407),f=r(n(34417));function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}var d=function(e){var t=e.prefixCls,n=e.className,r=e.active,i=(0,a.useContext(c.ConfigContext).getPrefixCls)("skeleton",t),p=(0,u.default)(e,["prefixCls","className"]),d=(0,s.default)(i,"".concat(i,"-element"),(0,o.default)({},"".concat(i,"-active"),r),n);return a.createElement("div",{className:d},a.createElement(f.default,(0,l.default)({prefixCls:"".concat(i,"-avatar")},p)))};d.defaultProps={size:"default",shape:"circle"},t.default=d},20937:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(10434)),o=r(n(38416)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=p(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),u=r(n(18475)),s=r(n(93967)),c=r(n(34417)),f=n(31407);function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}var d=function(e){var t,n=e.prefixCls,r=e.className,i=e.active,p=e.block,d=(0,a.useContext(f.ConfigContext).getPrefixCls)("skeleton",n),h=(0,u.default)(e,["prefixCls"]),m=(0,s.default)(d,"".concat(d,"-element"),(t={},(0,o.default)(t,"".concat(d,"-active"),i),(0,o.default)(t,"".concat(d,"-block"),void 0!==p&&p),t),r);return a.createElement("div",{className:m},a.createElement(c.default,(0,l.default)({prefixCls:"".concat(d,"-button")},h)))};d.defaultProps={size:"default"},t.default=d},34417:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(10434)),o=r(n(38416)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=s(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),u=r(n(93967));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}t.default=function(e){var t,n,r=e.prefixCls,i=e.className,s=e.style,c=e.size,f=e.shape,p=(0,u.default)((t={},(0,o.default)(t,"".concat(r,"-lg"),"large"===c),(0,o.default)(t,"".concat(r,"-sm"),"small"===c),t)),d=(0,u.default)((n={},(0,o.default)(n,"".concat(r,"-circle"),"circle"===f),(0,o.default)(n,"".concat(r,"-square"),"square"===f),(0,o.default)(n,"".concat(r,"-round"),"round"===f),n));return a.createElement("span",{className:(0,u.default)(r,p,d,i),style:(0,l.default)((0,l.default)({},"number"==typeof c?{width:c,height:c,lineHeight:"".concat(c,"px")}:{}),s)})}},53525:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=u(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),o=r(n(93967)),a=n(31407);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(u=function(e){return e?n:t})(e)}t.default=function(e){var t=e.prefixCls,n=e.className,r=e.style,i=(0,l.useContext(a.ConfigContext).getPrefixCls)("skeleton",t),u=(0,o.default)(i,"".concat(i,"-element"),n);return l.createElement("div",{className:u},l.createElement("div",{className:(0,o.default)("".concat(i,"-image"),n),style:r},l.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(i,"-image-svg")},l.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:"".concat(i,"-image-path")}))))}},84865:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(10434)),o=r(n(38416)),a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=p(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),u=r(n(18475)),s=r(n(93967)),c=r(n(34417)),f=n(31407);function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}var d=function(e){var t,n=e.prefixCls,r=e.className,i=e.active,p=e.block,d=(0,a.useContext(f.ConfigContext).getPrefixCls)("skeleton",n),h=(0,u.default)(e,["prefixCls"]),m=(0,s.default)(d,"".concat(d,"-element"),(t={},(0,o.default)(t,"".concat(d,"-active"),i),(0,o.default)(t,"".concat(d,"-block"),p),t),r);return a.createElement("div",{className:m},a.createElement(c.default,(0,l.default)({prefixCls:"".concat(d,"-input")},h)))};d.defaultProps={size:"default"},t.default=d},34913:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(861)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=u(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(93967));function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(u=function(e){return e?n:t})(e)}t.default=function(e){var t=function(t){var n=e.width,r=e.rows;return Array.isArray(n)?n[t]:(void 0===r?2:r)-1===t?n:void 0},n=e.prefixCls,r=e.className,i=e.style,u=e.rows,s=(0,l.default)(Array(u)).map(function(e,n){return o.createElement("li",{key:n,style:{width:t(n)}})});return o.createElement("ul",{className:(0,a.default)(n,r),style:i},s)}},79456:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(38416)),o=r(n(10434)),a=r(n(18698)),u=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=v(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),s=r(n(93967)),c=r(n(87476)),f=r(n(34913)),p=n(31407),d=r(n(34417)),h=r(n(7471)),m=r(n(20937)),g=r(n(84865)),y=r(n(53525));function v(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(v=function(e){return e?n:t})(e)}function x(e){return e&&"object"===(0,a.default)(e)?e:{}}var k=function(e){var t=e.prefixCls,n=e.loading,r=e.className,i=e.style,a=e.children,h=e.avatar,m=e.title,g=e.paragraph,y=e.active,v=e.round,k=u.useContext(p.ConfigContext),b=k.getPrefixCls,w=k.direction,C=b("skeleton",t);if(n||!("loading"in e)){var S=!!h,P=!!m,O=!!g;if(S){var E=(0,o.default)((0,o.default)({prefixCls:"".concat(C,"-avatar")},P&&!O?{size:"large",shape:"square"}:{size:"large",shape:"circle"}),x(h));M=u.createElement("div",{className:"".concat(C,"-header")},u.createElement(d.default,E))}if(P||O){if(P){var z,M,I,D,T,A=(0,o.default)((0,o.default)({prefixCls:"".concat(C,"-title")},!S&&O?{width:"38%"}:S&&O?{width:"50%"}:{}),x(m));D=u.createElement(c.default,A)}if(O){var _,j=(0,o.default)((0,o.default)({prefixCls:"".concat(C,"-paragraph")},(_={},S&&P||(_.width="61%"),!S&&P?_.rows=3:_.rows=2,_)),x(g));T=u.createElement(f.default,j)}I=u.createElement("div",{className:"".concat(C,"-content")},D,T)}var L=(0,s.default)(C,(z={},(0,l.default)(z,"".concat(C,"-with-avatar"),S),(0,l.default)(z,"".concat(C,"-active"),y),(0,l.default)(z,"".concat(C,"-rtl"),"rtl"===w),(0,l.default)(z,"".concat(C,"-round"),v),z),r);return u.createElement("div",{className:L,style:i},M,I)}return void 0!==a?a:null};k.defaultProps={avatar:!1,title:!0,paragraph:!0},k.Button=m.default,k.Avatar=h.default,k.Input=g.default,k.Image=y.default,t.default=k},87476:function(e,t,n){"use strict";var r=n(64836),i=n(18698);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(n(10434)),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=u(void 0);if(n&&n.has(e))return n.get(e);var r={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=l?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=r(n(93967));function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(u=function(e){return e?n:t})(e)}t.default=function(e){var t=e.prefixCls,n=e.className,r=e.width,i=e.style;return o.createElement("h3",{className:(0,a.default)(t,n),style:(0,l.default)({width:r},i)})}},81116:function(e,t,n){"use strict";var r=n(64836);t.Z=void 0;var i=r(n(79456)).default;t.Z=i},41476:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(5174)),i=n(26678);function l(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}l.default=l,e.exports=l},26678:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},u=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var s;return(void 0===t&&(t={}),!(s=e)||i.test(s)||n.test(s))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,u):e.replace(l,u)).replace(r,a))}},5174:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),l="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;l?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(18139))},24345:function(e,t,n){"use strict";function r(){}function i(){}n.d(t,{ok:function(){return r},t1:function(){return i}})},27962:function(e,t,n){"use strict";n.d(t,{B:function(){return i}});let r={};function i(e,t){let n=t||r;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return o(e.children,t,n)}return Array.isArray(e)?o(e,t,n):""}function o(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=l(e[i],t,n);return r.join("")}},23402:function(e,t,n){"use strict";n.d(t,{w:function(){return l}});var r=n(42761),i=n(15459);let l={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.xz)(t)?(0,r.f)(e,l,"linePrefix")(t):l(t)};function l(e){return null===e||(0,i.Ch)(e)?t(e):n(e)}}}},42761:function(e,t,n){"use strict";n.d(t,{f:function(){return i}});var r=n(15459);function i(e,t,n,i){let l=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.xz)(i)?(e.enter(n),function i(a){return(0,r.xz)(a)&&o++<l?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},15459:function(e,t,n){"use strict";n.d(t,{AF:function(){return u},Av:function(){return o},B8:function(){return h},Ch:function(){return c},H$:function(){return i},Xh:function(){return d},jv:function(){return r},n9:function(){return l},pY:function(){return a},sR:function(){return s},xz:function(){return p},z3:function(){return f}});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),l=m(/[#-'*+\--9=?A-Z^-~]/);function o(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),u=m(/[\dA-Fa-f]/),s=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let d=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},21905:function(e,t,n){"use strict";function r(e,t,n,r){let i;let l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:function(){return i},d:function(){return r}})},62987:function(e,t,n){"use strict";n.d(t,{r:function(){return i}});var r=n(15459);function i(e){return null===e||(0,r.z3)(e)||(0,r.B8)(e)?1:(0,r.Xh)(e)?2:void 0}},4663:function(e,t,n){"use strict";n.d(t,{W:function(){return l}});var r=n(21905);let i={}.hasOwnProperty;function l(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let l;let o=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(l in a){i.call(o,l)||(o[l]=[]);let e=a[l];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.d)(e,0,0,i)}(o[l],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},49411:function(e,t,n){"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{d:function(){return r}})},63233:function(e,t,n){"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}n.d(t,{C:function(){return r}})},25332:function(e,t,n){"use strict";n.d(t,{UG:function(){return np}});var r={};n.r(r),n.d(r,{boolean:function(){return y},booleanish:function(){return v},commaOrSpaceSeparated:function(){return C},commaSeparated:function(){return w},number:function(){return k},overloadedBoolean:function(){return x},spaceSeparated:function(){return b}});var i={};n.r(i),n.d(i,{attentionMarkers:function(){return td},contentInitial:function(){return ta},disable:function(){return th},document:function(){return to},flow:function(){return ts},flowInitial:function(){return tu},insideSpan:function(){return tp},string:function(){return tc},text:function(){return tf}});var l=n(24345);let o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,u={};function s(e,t){return((t||u).jsx?a:o).test(e)}let c=/[ \t\n\f\r]/g;function f(e){return""===e.replace(c,"")}class p{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function d(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new p(n,r,t)}function h(e){return e.toLowerCase()}p.prototype.normal={},p.prototype.property={},p.prototype.space=void 0;class m{constructor(e,t){this.attribute=t,this.property=e}}m.prototype.attribute="",m.prototype.booleanish=!1,m.prototype.boolean=!1,m.prototype.commaOrSpaceSeparated=!1,m.prototype.commaSeparated=!1,m.prototype.defined=!1,m.prototype.mustUseProperty=!1,m.prototype.number=!1,m.prototype.overloadedBoolean=!1,m.prototype.property="",m.prototype.spaceSeparated=!1,m.prototype.space=void 0;let g=0,y=S(),v=S(),x=S(),k=S(),b=S(),w=S(),C=S();function S(){return 2**++g}let P=Object.keys(r);class O extends m{constructor(e,t,n,i){var l,o;let a=-1;if(super(e,t),i&&(this.space=i),"number"==typeof n)for(;++a<P.length;){let e=P[a];l=P[a],(o=(n&r[e])===r[e])&&(this[l]=o)}}}function E(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let l=new O(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[h(r)]=r,n[h(l.attribute)]=r}return new p(t,n,e.space)}O.prototype.defined=!0;let z=E({properties:{ariaActiveDescendant:null,ariaAtomic:v,ariaAutoComplete:null,ariaBusy:v,ariaChecked:v,ariaColCount:k,ariaColIndex:k,ariaColSpan:k,ariaControls:b,ariaCurrent:null,ariaDescribedBy:b,ariaDetails:null,ariaDisabled:v,ariaDropEffect:b,ariaErrorMessage:null,ariaExpanded:v,ariaFlowTo:b,ariaGrabbed:v,ariaHasPopup:null,ariaHidden:v,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:b,ariaLevel:k,ariaLive:null,ariaModal:v,ariaMultiLine:v,ariaMultiSelectable:v,ariaOrientation:null,ariaOwns:b,ariaPlaceholder:null,ariaPosInSet:k,ariaPressed:v,ariaReadOnly:v,ariaRelevant:null,ariaRequired:v,ariaRoleDescription:b,ariaRowCount:k,ariaRowIndex:k,ariaRowSpan:k,ariaSelected:v,ariaSetSize:k,ariaSort:null,ariaValueMax:k,ariaValueMin:k,ariaValueNow:k,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function M(e,t){return t in e?e[t]:t}function I(e,t){return M(e,t.toLowerCase())}let D=E({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:w,acceptCharset:b,accessKey:b,action:null,allow:null,allowFullScreen:y,allowPaymentRequest:y,allowUserMedia:y,alt:null,as:null,async:y,autoCapitalize:null,autoComplete:b,autoFocus:y,autoPlay:y,blocking:b,capture:null,charSet:null,checked:y,cite:null,className:b,cols:k,colSpan:null,content:null,contentEditable:v,controls:y,controlsList:b,coords:k|w,crossOrigin:null,data:null,dateTime:null,decoding:null,default:y,defer:y,dir:null,dirName:null,disabled:y,download:x,draggable:v,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:y,formTarget:null,headers:b,height:k,hidden:x,high:k,href:null,hrefLang:null,htmlFor:b,httpEquiv:b,id:null,imageSizes:null,imageSrcSet:null,inert:y,inputMode:null,integrity:null,is:null,isMap:y,itemId:null,itemProp:b,itemRef:b,itemScope:y,itemType:b,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:y,low:k,manifest:null,max:null,maxLength:k,media:null,method:null,min:null,minLength:k,multiple:y,muted:y,name:null,nonce:null,noModule:y,noValidate:y,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:y,optimum:k,pattern:null,ping:b,placeholder:null,playsInline:y,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:y,referrerPolicy:null,rel:b,required:y,reversed:y,rows:k,rowSpan:k,sandbox:b,scope:null,scoped:y,seamless:y,selected:y,shadowRootClonable:y,shadowRootDelegatesFocus:y,shadowRootMode:null,shape:null,size:k,sizes:null,slot:null,span:k,spellCheck:v,src:null,srcDoc:null,srcLang:null,srcSet:null,start:k,step:null,style:null,tabIndex:k,target:null,title:null,translate:null,type:null,typeMustMatch:y,useMap:null,value:v,width:k,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:b,axis:null,background:null,bgColor:null,border:k,borderColor:null,bottomMargin:k,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:y,declare:y,event:null,face:null,frame:null,frameBorder:null,hSpace:k,leftMargin:k,link:null,longDesc:null,lowSrc:null,marginHeight:k,marginWidth:k,noResize:y,noHref:y,noShade:y,noWrap:y,object:null,profile:null,prompt:null,rev:null,rightMargin:k,rules:null,scheme:null,scrolling:v,standby:null,summary:null,text:null,topMargin:k,valueType:null,version:null,vAlign:null,vLink:null,vSpace:k,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:y,disableRemotePlayback:y,prefix:null,property:null,results:k,security:null,unselectable:null},space:"html",transform:I}),T=E({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:C,accentHeight:k,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:k,amplitude:k,arabicForm:null,ascent:k,attributeName:null,attributeType:null,azimuth:k,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:k,by:null,calcMode:null,capHeight:k,className:b,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:k,diffuseConstant:k,direction:null,display:null,dur:null,divisor:k,dominantBaseline:null,download:y,dx:null,dy:null,edgeMode:null,editable:null,elevation:k,enableBackground:null,end:null,event:null,exponent:k,externalResourcesRequired:null,fill:null,fillOpacity:k,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:w,g2:w,glyphName:w,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:k,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:k,horizOriginX:k,horizOriginY:k,id:null,ideographic:k,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:k,k:k,k1:k,k2:k,k3:k,k4:k,kernelMatrix:C,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:k,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:k,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:k,overlineThickness:k,paintOrder:null,panose1:null,path:null,pathLength:k,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:b,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:k,pointsAtY:k,pointsAtZ:k,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:C,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:C,rev:C,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:C,requiredFeatures:C,requiredFonts:C,requiredFormats:C,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:k,specularExponent:k,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:k,strikethroughThickness:k,string:null,stroke:null,strokeDashArray:C,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:k,strokeOpacity:k,strokeWidth:null,style:null,surfaceScale:k,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:C,tabIndex:k,tableValues:null,target:null,targetX:k,targetY:k,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:C,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:k,underlineThickness:k,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:k,values:null,vAlphabetic:k,vMathematical:k,vectorEffect:null,vHanging:k,vIdeographic:k,version:null,vertAdvY:k,vertOriginX:k,vertOriginY:k,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:k,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:M}),A=E({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),_=E({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:I}),j=E({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),L=d([z,D,A,_,j],"html"),F=d([z,T,A,_,j],"svg"),N=/[A-Z]/g,R=/-[a-z]/g,B=/^data[-\w.:]+$/i;function H(e){return"-"+e.toLowerCase()}function V(e){return e.charAt(1).toUpperCase()}let U={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var W=n(41476);let q=Y("end"),$=Y("start");function Y(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function K(e){return e&&"object"==typeof e?"position"in e||"type"in e?Q(e.position):"start"in e||"end"in e?Q(e):"line"in e||"column"in e?X(e):"":""}function X(e){return Z(e&&e.line)+":"+Z(e&&e.column)}function Q(e){return X(e&&e.start)+"-"+X(e&&e.end)}function Z(e){return e&&"number"==typeof e?e:1}class J extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},l=!1;if(t&&(i="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(l=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let o=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=o?o.line:void 0,this.name=K(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=l&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}J.prototype.file="",J.prototype.name="",J.prototype.reason="",J.prototype.message="",J.prototype.stack="",J.prototype.column=void 0,J.prototype.line=void 0,J.prototype.ancestors=void 0,J.prototype.cause=void 0,J.prototype.fatal=void 0,J.prototype.place=void 0,J.prototype.ruleId=void 0,J.prototype.source=void 0;let G={}.hasOwnProperty,ee=new Map,et=/[A-Z]/g,en=new Set(["table","tbody","thead","tfoot","tr"]),er=new Set(["td","th"]),ei="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function el(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=F,e.schema=i),e.ancestors.push(t);let l=es(e,t.tagName,!1),o=function(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&G.call(t.properties,r)){let l=function(e,t,n){let r=function(e,t){let n=h(t),r=t,i=m;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&B.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(R,V);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!R.test(e)){let n=e.replace(N,H);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=O}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return W(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new J("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=ei+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)G.call(e,t)&&(n[function(e){let t=e.replace(et,ef);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?U[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(l){let[r,o]=l;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&er.has(t.tagName)?n=o:i[r]=o}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),a=eu(e,t);return en.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&f(e.value):f(e))})),eo(e,o,l,t),ea(o,a),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,l.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}ec(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(i=F,e.schema=i),e.ancestors.push(t);let o=null===t.name?e.Fragment:es(e,t.name,!0),a=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,l.ok)("ExpressionStatement"===t.type);let i=t.expression;(0,l.ok)("ObjectExpression"===i.type);let o=i.properties[0];(0,l.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else ec(e,t.position)}else{let i;let o=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,l.ok)("ExpressionStatement"===t.type),i=e.evaluater.evaluateExpression(t.expression)}else ec(e,t.position)}else i=null===r.value||r.value;n[o]=i}return n}(e,t),u=eu(e,t);return eo(e,a,o,t),ea(a,u),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);ec(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return ea(r,eu(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function eo(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ea(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function eu(e,t){let n=[],r=-1,i=e.passKeys?new Map:ee;for(;++r<t.children.length;){let l;let o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=i.get(e)||0;l=e+"-"+t,i.set(e,t+1)}}let a=el(e,o,l);void 0!==a&&n.push(a)}return n}function es(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),i=-1;for(;++i<n.length;){let t=s(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}(0,l.ok)(e,"always a result"),r=e}else r=s(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return G.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);ec(e)}function ec(e,t){let n=new J("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=ei+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ef(e){return"-"+e.toLowerCase()}let ep={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ed=n(85893);n(67294);var eh=n(27962),em=n(21905);class eg{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ey(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ey(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ey(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ey(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ey(this.left,t.reverse())}}}}function ey(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ev(e){let t,n,r,i,l,o,a;let u={},s=-1,c=new eg(e);for(;++s<c.length;){for(;(s in u);)s=u[s];if(t=c.get(s),s&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(s-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(u,function(e,t){let n,r;let i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],u=i._tokenizer;!u&&(u=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(u._contentTypeTextTrailing=!0));let s=u.events,c=[],f={},p=-1,d=i,h=0,m=0,g=[0];for(;d;){for(;e.get(++o)[1]!==d;);a.push(o),!d._tokenizer&&(n=l.sliceStream(d),d.next||n.push(null),r&&u.defineSkip(d.start),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=!0),u.write(n),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=i;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(u.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){let t=s.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return f}(c,s)),s=u[s],a=!0);else if(t[1]._container){for(r=s,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(l=c.slice(n,s)).unshift(t),c.splice(n,s-n+1,l))}}return(0,em.d)(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}var ex=n(4663),ek=n(42761),eb=n(15459);let ew={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.f)(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,eb.Ch)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eC={tokenize:function(e){let t,n,r;let i=this,l=[],o=0;return a;function a(t){if(o<l.length){let n=l[o];return i.containerState=n[1],e.attempt(n[0].continuation,u,s)(t)}return s(t)}function u(e){if(o++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,l=r;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){n=i.events[l][1].end;break}g(o);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return(0,em.d)(i.events,l+1,0,i.events.slice(r)),i.events.length=a,s(e)}return a(e)}function s(n){if(o===l.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(eS,c,f)(n)}function c(e){return t&&y(),g(o),p(e)}function f(e){return i.parser.lazy[i.now().line]=o!==l.length,r=i.now().offset,h(e)}function p(t){return i.containerState={},e.attempt(eS,d,h)(t)}function d(e){return o++,l.push([i.currentConstruct,i.containerState]),p(e)}function h(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return(0,eb.Ch)(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,l){let a=i.sliceStream(e);if(l&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,l=t.events.length;for(;l--;)if(t.events[l][1].start.offset<r&&(!t.events[l][1].end||t.events[l][1].end.offset>r))return;let a=i.events.length,u=a;for(;u--;)if("exit"===i.events[u][0]&&"chunkFlow"===i.events[u][1].type){if(e){n=i.events[u][1].end;break}e=!0}for(g(o),l=a;l<i.events.length;)i.events[l][1].end={...n},l++;(0,em.d)(i.events,u+1,0,i.events.slice(a)),i.events.length=l}}function g(t){let n=l.length;for(;n-- >t;){let t=l[n];i.containerState=t[1],t[0].exit.call(i,e)}l.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},eS={tokenize:function(e,t,n){return(0,ek.f)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var eP=n(23402);let eO={resolve:function(e){return ev(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,eb.Ch)(t)?e.check(eE,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eE={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.f)(e,i,"linePrefix")};function i(i){if(null===i||(0,eb.Ch)(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},ez={tokenize:function(e){let t=this,n=e.attempt(eP.w,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,(0,ek.f)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eO,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},eM={resolveAll:eA()},eI=eT("string"),eD=eT("text");function eT(e){return{resolveAll:eA("text"===e?e_:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return u(e)?i(e):o(e)}function o(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),a}function a(e){return u(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function u(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eA(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function e_(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,u=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)u++,a--;if(a)break;a=-1}else if(-2===e)r=!0,u++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(u=0),u){let l={type:n===e.length||r||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-u,offset:i.end.offset-u},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let ej={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){return e.enter("thematicBreak"),r=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,eb.xz)(n)?(0,ek.f)(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||(0,eb.Ch)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(l)}}},eL={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eP.w,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,ek.f)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,eb.xz)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eN,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,ek.f)(e,e.attempt(eL,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,eb.pY)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(ej,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,eb.pY)(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eP.w,r.interrupt?n:u,e.attempt(eF,c,s))}function u(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function s(t){return(0,eb.xz)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eF={partial:!0,tokenize:function(e,t,n){let r=this;return(0,ek.f)(e,function(e){let i=r.events[r.events.length-1];return!(0,eb.xz)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eN={partial:!0,tokenize:function(e,t,n){let r=this;return(0,ek.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eR={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,eb.xz)(t)?(0,ek.f)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eR,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,eb.xz)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function eB(e,t,n,r,i,l,o,a,u){let s=u||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),f):null===t||32===t||41===t||(0,eb.Av)(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),h(t))};function f(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||(0,eb.Ch)(t)?n(t):(e.consume(t),92===t?d:p)}function d(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function h(i){return!c&&(null===i||41===i||(0,eb.z3)(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<s&&40===i?(e.consume(i),c++,h):41===i?(e.consume(i),c--,h):null===i||32===i||40===i||(0,eb.Av)(i)?n(i):(e.consume(i),92===i?m:h)}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function eH(e,t,n,r,i,l){let o;let a=this,u=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),s};function s(f){return u>999||null===f||91===f||93===f&&!o||94===f&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(f):93===f?(e.exit(l),e.enter(i),e.consume(f),e.exit(i),e.exit(r),t):(0,eb.Ch)(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||(0,eb.Ch)(t)||u++>999?(e.exit("chunkString"),s(t)):(e.consume(t),o||(o=!(0,eb.xz)(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}}function eV(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),u(n))}function u(t){return t===o?(e.exit(l),a(o)):null===t?n(t):(0,eb.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,ek.f)(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(t))}function s(t){return t===o||null===t||(0,eb.Ch)(t)?(e.exit("chunkString"),u(t)):(e.consume(t),92===t?c:s)}function c(t){return t===o||92===t?(e.consume(t),s):s(t)}}function eU(e,t){let n;return function r(i){return(0,eb.Ch)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,eb.xz)(i)?(0,ek.f)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var eW=n(49411);let eq={partial:!0,tokenize:function(e,t,n){return function(t){return(0,eb.z3)(t)?eU(e,r)(t):n(t)};function r(t){return eV(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,eb.xz)(t)?(0,ek.f)(e,l,"whitespace")(t):l(t)}function l(e){return null===e||(0,eb.Ch)(e)?t(e):n(e)}}},e$={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,ek.f)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):(0,eb.Ch)(n)?e.attempt(eY,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,eb.Ch)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},eY={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,eb.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,ek.f)(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):(0,eb.Ch)(e)?i(e):n(e)}}},eK={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r;let i=this;return function(t){let o,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){o="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||o)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,eb.xz)(n)?(0,ek.f)(e,l,"lineSuffix")(n):l(n))}(t)):n(t)};function l(r){return null===r||(0,eb.Ch)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},eX=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],eQ=["pre","script","style","textarea"],eZ={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eP.w,t,n)}}},eJ={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,eb.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},eG={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},e0={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r;let i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),(0,eb.xz)(t)?(0,ek.f)(e,u,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),(0,eb.xz)(i)?(0,ek.f)(e,s,"whitespace")(i):s(i)):n(i)}(t)):n(t)}function s(r){return null===r||(0,eb.Ch)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){return function(t){let l=i.events[i.events.length-1];return o=l&&"linePrefix"===l[1].type?l[2].sliceSerialize(l[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,eb.xz)(i)?(0,ek.f)(e,u,"whitespace")(i):u(i))}(t)}(t)};function u(l){return null===l||(0,eb.Ch)(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(eG,c,h)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,eb.Ch)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(i)):(0,eb.xz)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,ek.f)(e,s,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function s(t){return null===t||(0,eb.Ch)(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,eb.Ch)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(l,h,f)(t)}function f(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return o>0&&(0,eb.xz)(t)?(0,ek.f)(e,d,"linePrefix",o+1)(t):d(t)}function d(t){return null===t||(0,eb.Ch)(t)?e.check(eG,c,h)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,eb.Ch)(n)?(e.exit("codeFlowValue"),d(n)):(e.consume(n),t)}(t))}function h(n){return e.exit("codeFenced"),t(n)}}},e1=document.createElement("i");function e4(e){let t="&"+e+";";e1.innerHTML=t;let n=e1.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let e2={name:"characterReference",tokenize:function(e,t,n){let r,i;let l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),r=31,i=eb.H$,s(t))}function u(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=eb.AF,s):(e.enter("characterReferenceValue"),r=7,i=eb.pY,s(t))}function s(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==eb.H$||e4(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),s):n(a)}}},e3={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,eb.sR)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},e6={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,ek.f)(e,t,"linePrefix")}}};var e5=n(63233);let e9={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,em.d)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,l,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=o);let u={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},s={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return l=[["enter",u,t],["enter",s,t]],l=(0,em.V)(l,e.slice(r+1,r+a+3)),l=(0,em.V)(l,[["enter",c,t]]),l=(0,em.V)(l,(0,e5.C)(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),l=(0,em.V)(l,[["exit",c,t],e[i-2],e[i-1],["exit",s,t]]),l=(0,em.V)(l,e.slice(i+1)),l=(0,em.V)(l,[["exit",u,t]]),(0,em.d)(e,r,e.length,l),e},tokenize:function(e,t,n){let r,i;let l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes((0,eW.d)(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(e7,s,i?s:c)(t):91===t?e.attempt(e8,s,i?u:c)(t):i?s(t):c(t)}function u(t){return e.attempt(te,s,c)(t)}function s(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},e7={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,eb.z3)(t)?eU(e,i)(t):i(t)}function i(t){return 41===t?s(t):eB(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return(0,eb.z3)(t)?eU(e,a)(t):s(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?eV(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):s(t)}function u(t){return(0,eb.z3)(t)?eU(e,s)(t):s(t)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},e8={tokenize:function(e,t,n){let r=this;return function(t){return eH.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,eW.d)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},te={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tt={name:"labelStartImage",resolveAll:e9.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var tn=n(62987);let tr={name:"attention",resolveAll:function(e,t){let n,r,i,l,o,a,u,s,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let f={...e[n][1].end},p={...e[c][1].start};ti(f,-a),ti(p,a),l={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...l.start},end:{...o.end}},e[n][1].end={...l.start},e[c][1].start={...o.end},u=[],e[n][1].end.offset-e[n][1].start.offset&&(u=(0,em.V)(u,[["enter",e[n][1],t],["exit",e[n][1],t]])),u=(0,em.V)(u,[["enter",r,t],["enter",l,t],["exit",l,t],["enter",i,t]]),u=(0,em.V)(u,(0,e5.C)(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),u=(0,em.V)(u,[["exit",i,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(s=2,u=(0,em.V)(u,[["enter",e[c][1],t],["exit",e[c][1],t]])):s=0,(0,em.d)(e,n-1,c-n+3,u),c=n+u.length-s-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=(0,tn.r)(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let u=e.exit("attentionSequence"),s=(0,tn.r)(a),c=!s||2===s&&l||r.includes(a),f=!l||2===l&&s||r.includes(i);return u._open=!!(42===n?c:c&&(l||!f)),u._close=!!(42===n?f:f&&(s||!c)),t(a)}(o)}}};function ti(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tl={name:"labelStartLink",resolveAll:e9.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},to={42:eL,43:eL,45:eL,48:eL,49:eL,50:eL,51:eL,52:eL,53:eL,54:eL,55:eL,56:eL,57:eL,62:eR},ta={91:{name:"definition",tokenize:function(e,t,n){let r;let i=this;return function(t){return e.enter("definition"),eH.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function l(t){return(r=(0,eW.d)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return(0,eb.z3)(t)?eU(e,a)(t):a(t)}function a(t){return eB(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function u(t){return e.attempt(eq,s,s)(t)}function s(t){return(0,eb.xz)(t)?(0,ek.f)(e,c,"whitespace")(t):c(t)}function c(l){return null===l||(0,eb.Ch)(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},tu={[-2]:e$,[-1]:e$,32:e$},ts={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,l=3;return"whitespace"===e[3][1].type&&(l+=2),i-2>l&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(l===i-1||i-4>l&&"whitespace"===e[i-2][1].type)&&(i-=l+1===i?2:4),i>l&&(n={type:"atxHeadingText",start:e[l][1].start,end:e[i][1].end},r={type:"chunkText",start:e[l][1].start,end:e[i][1].end,contentType:"text"},(0,em.d)(e,l,i-l+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||(0,eb.z3)(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,eb.Ch)(r)?(e.exit("atxHeading"),t(r)):(0,eb.xz)(r)?(0,ek.f)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,eb.z3)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(i)}}},42:ej,45:[eK,ej],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a;let u=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),s};function s(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,d):63===o?(e.consume(o),r=3,u.interrupt?t:T):(0,eb.jv)(o)?(e.consume(o),l=String.fromCharCode(o),h):n(o)}function c(i){return 45===i?(e.consume(i),r=2,f):91===i?(e.consume(i),r=5,o=0,p):(0,eb.jv)(i)?(e.consume(i),r=4,u.interrupt?t:T):n(i)}function f(r){return 45===r?(e.consume(r),u.interrupt?t:T):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?u.interrupt?t:S:p:n(r)}function d(t){return(0,eb.jv)(t)?(e.consume(t),l=String.fromCharCode(t),h):n(t)}function h(o){if(null===o||47===o||62===o||(0,eb.z3)(o)){let a=47===o,s=l.toLowerCase();return!a&&!i&&eQ.includes(s)?(r=1,u.interrupt?t(o):S(o)):eX.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),m):u.interrupt?t(o):S(o):(r=7,u.interrupt&&!u.parser.lazy[u.now().line]?n(o):i?function t(n){return(0,eb.xz)(n)?(e.consume(n),t):w(n)}(o):g(o))}return 45===o||(0,eb.H$)(o)?(e.consume(o),l+=String.fromCharCode(o),h):n(o)}function m(r){return 62===r?(e.consume(r),u.interrupt?t:S):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||(0,eb.jv)(t)?(e.consume(t),y):(0,eb.xz)(t)?(e.consume(t),g):w(t)}function y(t){return 45===t||46===t||58===t||95===t||(0,eb.H$)(t)?(e.consume(t),y):v(t)}function v(t){return 61===t?(e.consume(t),x):(0,eb.xz)(t)?(e.consume(t),v):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,k):(0,eb.xz)(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,eb.z3)(n)?v(n):(e.consume(n),t)}(t)}function k(t){return t===a?(e.consume(t),a=null,b):null===t||(0,eb.Ch)(t)?n(t):(e.consume(t),k)}function b(e){return 47===e||62===e||(0,eb.xz)(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),C):n(t)}function C(t){return null===t||(0,eb.Ch)(t)?S(t):(0,eb.xz)(t)?(e.consume(t),C):n(t)}function S(t){return 45===t&&2===r?(e.consume(t),z):60===t&&1===r?(e.consume(t),M):62===t&&4===r?(e.consume(t),A):63===t&&3===r?(e.consume(t),T):93===t&&5===r?(e.consume(t),D):(0,eb.Ch)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(eZ,_,P)(t)):null===t||(0,eb.Ch)(t)?(e.exit("htmlFlowData"),P(t)):(e.consume(t),S)}function P(t){return e.check(eJ,O,_)(t)}function O(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),E}function E(t){return null===t||(0,eb.Ch)(t)?P(t):(e.enter("htmlFlowData"),S(t))}function z(t){return 45===t?(e.consume(t),T):S(t)}function M(t){return 47===t?(e.consume(t),l="",I):S(t)}function I(t){if(62===t){let n=l.toLowerCase();return eQ.includes(n)?(e.consume(t),A):S(t)}return(0,eb.jv)(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),I):S(t)}function D(t){return 93===t?(e.consume(t),T):S(t)}function T(t){return 62===t?(e.consume(t),A):45===t&&2===r?(e.consume(t),T):S(t)}function A(t){return null===t||(0,eb.Ch)(t)?(e.exit("htmlFlowData"),_(t)):(e.consume(t),A)}function _(n){return e.exit("htmlFlow"),t(n)}}},61:eK,95:ej,96:e0,126:e0},tc={38:e2,92:e3},tf={[-5]:e6,[-4]:e6,[-3]:e6,33:tt,38:e2,42:tr,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,eb.jv)(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||(0,eb.H$)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||(0,eb.H$)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,eb.Av)(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),u):(0,eb.n9)(t)?(e.consume(t),a):n(t)}function u(i){return(0,eb.H$)(i)?function i(l){return 46===l?(e.consume(l),r=0,u):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||(0,eb.H$)(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l;let o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),u):47===t?(e.consume(t),k):63===t?(e.consume(t),v):(0,eb.jv)(t)?(e.consume(t),w):n(t)}function u(t){return 45===t?(e.consume(t),s):91===t?(e.consume(t),i=0,d):(0,eb.jv)(t)?(e.consume(t),y):n(t)}function s(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):(0,eb.Ch)(t)?(l=c,I(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?M(e):45===e?f(e):c(e)}function d(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?h:d):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):(0,eb.Ch)(t)?(l=h,I(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?M(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?M(t):(0,eb.Ch)(t)?(l=y,I(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),x):(0,eb.Ch)(t)?(l=v,I(t)):(e.consume(t),v)}function x(e){return 62===e?M(e):v(e)}function k(t){return(0,eb.jv)(t)?(e.consume(t),b):n(t)}function b(t){return 45===t||(0,eb.H$)(t)?(e.consume(t),b):function t(n){return(0,eb.Ch)(n)?(l=t,I(n)):(0,eb.xz)(n)?(e.consume(n),t):M(n)}(t)}function w(t){return 45===t||(0,eb.H$)(t)?(e.consume(t),w):47===t||62===t||(0,eb.z3)(t)?C(t):n(t)}function C(t){return 47===t?(e.consume(t),M):58===t||95===t||(0,eb.jv)(t)?(e.consume(t),S):(0,eb.Ch)(t)?(l=C,I(t)):(0,eb.xz)(t)?(e.consume(t),C):M(t)}function S(t){return 45===t||46===t||58===t||95===t||(0,eb.H$)(t)?(e.consume(t),S):function t(n){return 61===n?(e.consume(n),P):(0,eb.Ch)(n)?(l=t,I(n)):(0,eb.xz)(n)?(e.consume(n),t):C(n)}(t)}function P(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,O):(0,eb.Ch)(t)?(l=P,I(t)):(0,eb.xz)(t)?(e.consume(t),P):(e.consume(t),E)}function O(t){return t===r?(e.consume(t),r=void 0,z):null===t?n(t):(0,eb.Ch)(t)?(l=O,I(t)):(e.consume(t),O)}function E(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,eb.z3)(t)?C(t):(e.consume(t),E)}function z(e){return 47===e||62===e||(0,eb.z3)(e)?C(e):n(e)}function M(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function I(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return(0,eb.xz)(t)?(0,ek.f)(e,T,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):T(t)}function T(t){return e.enter("htmlTextData"),l(t)}}}],91:tl,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,eb.Ch)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},e3],93:e9,95:tr,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(u){return null===u?n(u):32===u?(e.enter("space"),e.consume(u),e.exit("space"),o):96===u?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(u)):(0,eb.Ch)(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(u))}function a(t){return null===t||32===t||96===t||(0,eb.Ch)(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},tp={null:[tr,eM]},td={null:[42,95]},th={null:[]},tm=/[\0\t\n\r]/g;function tg(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let ty=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tv(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tg(n.slice(t?2:1),t?16:10)}return e4(n)||e}let tx={}.hasOwnProperty;function tk(e){return{line:e.line,column:e.column,offset:e.offset}}function tb(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+K({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+K({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+K({start:t.start,end:t.end})+") is still open")}function tw(e){let t=this;t.parser=function(n){var r,l;let o,a,u,s;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(l=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:s,autolinkEmail:s,atxHeading:r(h),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:s,characterReference:s,codeFenced:r(d),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(d,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:s,data:s,codeFlowValue:s,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:s,htmlText:r(g,i),htmlTextData:s,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(v,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(v),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(h),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tg(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=e4(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=tk(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eW.d)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(f),hardBreakTrailing:o(f),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(ty,tv),n.identifier=(0,eW.d)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tk(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(s.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eW.d)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};(function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tx.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:l,exit:a,buffer:i,resume:u,data:n},s=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?s.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,u=-1,s=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?u++:u--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||u||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!u&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===u&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=s,n}(e,s.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tx.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||tb).call(o,void 0,e[0])}for(r.position={start:tk(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tk(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){l.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tk(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tb).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+K({start:e.start,end:e.end})+"): it’s not open");n.position.end=tk(e.end)}function u(){return(0,eh.B)(this.stack.pop())}function s(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tk(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tk(e.end)}function f(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function d(){return{type:"code",lang:null,meta:null,value:""}}function h(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(l)(function(e){for(;!ev(e););return e}((function(e){let t={constructs:(0,ex.W)([i,...(e||{}).extensions||[]]),content:n(ew),defined:[],document:n(eC),flow:n(ez),lazy:{},string:n(eI),text:n(eD)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},l=[],o=[],a=[],u={attempt:h(function(e,t){m(e,t.from)}),check:h(d),consume:function(e){(0,eb.Ch)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),s.events.push(["enter",n,s]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=p(),s.events.push(["exit",t,s]),t},interrupt:h(d,{interrupt:!0})},s={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l;let o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(f(e),t)},sliceStream:f,write:function(e){return(o=(0,em.V)(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),s.events=(0,e5.C)(l,s.events,s),s.events)}},c=t.tokenize.call(s,u);return t.resolveAll&&l.push(t),s;function f(e){return function(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(o,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function d(e,t){t.restore()}function h(e,t){return function(n,i,l){let o,c,f,d;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return h([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function h(e){return(o=e,c=0,0===e.length)?l:m(e[c])}function m(e){return function(n){return(d=function(){let e=p(),t=s.previous,n=s.currentConstruct,i=s.events.length,l=Array.from(a);return{from:i,restore:function(){r=e,s.previous=t,s.currentConstruct=n,s.events.length=i,a=l,g()}}}(),f=e,e.partial||(s.currentConstruct=e),e.name&&s.parser.constructs.disable.null.includes(e.name))?v(n):e.tokenize.call(t?Object.assign(Object.create(s),t):s,u,y,v)(n)}}function y(t){return e(f,d),i}function v(e){return(d.restore(),++c<o.length)?m(o[c]):l}}}function m(e,t){e.resolveAll&&!l.includes(e)&&l.push(e),e.resolve&&(0,em.d)(s.events,t,s.events.length-t,e.resolve(s.events.slice(t),s)),e.resolveTo&&(s.events=e.resolveTo(s.events,s))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(l).document().write((a=1,u="",s=!0,function(e,t,n){let r,i,l,c,f;let p=[];for(e=u+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,u="",s&&(65279===e.charCodeAt(0)&&l++,s=void 0);l<e.length;){if(tm.lastIndex=l,c=(r=tm.exec(e))&&void 0!==r.index?r.index:e.length,f=e.charCodeAt(c),!r){u=e.slice(l);break}if(10===f&&l===c&&o)p.push(-3),o=void 0;else switch(o&&(p.push(-5),o=void 0),l<c&&(p.push(e.slice(l,c)),a+=c-l),f){case 0:p.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),p.push(-2);a++<i;)p.push(-1);break;case 10:p.push(-4),a=1;break;default:o=!0,a=1}l=c+1}return n&&(o&&p.push(-5),u&&p.push(u),p.push(null)),p})(n,r,!0))))}}let tC="object"==typeof self?self:globalThis,tS=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[l,o]=t[i];switch(l){case 0:case -1:return n(o,i);case 1:{let e=n([],i);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),i);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new tC[e](t),i)}case 8:return n(BigInt(o),i);case"BigInt":return n(Object(BigInt(o)),i);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new tC[l](o),i)};return r},tP=e=>tS(new Map,e)(0),{toString:tO}={},{keys:tE}=Object,tz=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tO.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tM=([e,t])=>0===e&&("function"===t||"symbol"===t),tI=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,a]=tz(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],u=i([o,n],r);for(let t of tE(r))(e||!tM(tz(r[t])))&&n.push([l(t),l(r[t])]);return u}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(tM(tz(n))||tM(tz(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!tM(tz(n)))&&t.push(l(n));return n}}let{message:u}=r;return i([o,{name:a,message:u}],r)};return l},tD=(e,{json:t,lossy:n}={})=>{let r=[];return tI(!(t||n),!!t,new Map,r)(e),r};var tT="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tP(tD(e,t)):structuredClone(e):(e,t)=>tP(tD(e,t));function tA(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let l=e.charCodeAt(n),o="";if(37===l&&(0,eb.H$)(e.charCodeAt(n+1))&&(0,eb.H$)(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){let t=e.charCodeAt(n+1);l<56320&&t>56319&&t<57344?(o=String.fromCharCode(l,t),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function t_(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tj(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var tL=n(21623);function tF(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function tN(e){let t=e.spread;return null==t?e.children.length>1:t}function tR(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let tB={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=tA(i.toLowerCase()),o=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(i,a);let u={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,u);let s={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(t,s),e.applyData(t,s)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tF(e,t);let i={src:tA(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:tA(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tF(e,t);let i={href:tA(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:tA(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=tN(n[r])}return t}(n):tN(t),l={},o=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let u=r[r.length-1];u&&(i||"element"!==u.type||"p"!==u.tagName)&&o.push({type:"text",value:"\n"});let s={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,s),e.applyData(t,s)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=$(t.children[1]),o=q(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,u=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let s={type:"element",tagName:i,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),u.push(s)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,s),e.applyData(t,s)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(tR(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(tR(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tH,yaml:tH,definition:tH,footnoteDefinition:tH};function tH(){}let tV={}.hasOwnProperty,tU={};function tW(e,t){e.position&&(t.position=function(e){let t=$(e),n=q(e);if(t&&n)return{start:t,end:n}}(e))}function tq(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,tT(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function t$(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function tY(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function tK(e,t){let n=function(e,t){let n=t||tU,r=new Map,i=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=tY(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=tY(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:tq,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...tB,...n.handlers},one:function(e,t){let n=e.type,r=l.handlers[n];if(tV.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tT(n);return r.children=l.all(e),r}return tT(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(tV.call(n,"hProperties")||tV.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(l,e,t)},options:n,patch:tW,wrap:t$};return(0,tL.Vn)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||t_,r=e.options.footnoteBackLabel||tj,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],u=-1;for(;++u<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[u]);if(!i)continue;let l=e.all(i),o=String(i.identifier).toUpperCase(),s=tA(o.toLowerCase()),c=0,f=[],p=e.footnoteCounts.get(o);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(u,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+s+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(u,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let d=l[l.length-1];if(d&&"element"===d.type&&"p"===d.tagName){let e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...f)}else l.push(...f);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+s},children:e.wrap(l,!0)};e.patch(i,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...tT(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&((0,l.ok)("children"in o),o.children.push({type:"text",value:"\n"},i)),o}function tX(e,t){return e&&"run"in e?async function(n,r){let i=tK(n,{file:r,...t});await e.run(i,r)}:function(n,r){return tK(n,{file:r,...e||t})}}function tQ(e){if(e)throw e}var tZ=n(94470);function tJ(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let tG={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');t0(e);let r=0,i=-1,l=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else i<0&&(n=!0,i=l+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let o=-1,a=t.length-1;for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else o<0&&(n=!0,o=l+1),a>-1&&(e.codePointAt(l)===t.codePointAt(a--)?a<0&&(i=l):(a=-1,i=o));return r===i?i=o:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(t0(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;t0(e);let n=e.length,r=-1,i=0,l=-1,o=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?l<0?l=n:1!==o&&(o=1):l>-1&&(o=-1)}return l<0||r<0||0===o||1===o&&l===r-1&&l===i+1?"":e.slice(l,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)t0(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){t0(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",l=0,o=-1,a=0,u=-1;for(;++u<=e.length;){if(u<e.length)n=e.codePointAt(u);else if(47===n)break;else n=47;if(47===n){if(o===u-1||1===a);else if(o!==u-1&&2===a){if(i.length<2||2!==l||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",l=0):l=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),o=u,a=0;continue}}else if(i.length>0){i="",l=0,o=u,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",l=2)}else i.length>0?i+="/"+e.slice(o+1,u):i=e.slice(o+1,u),l=u-o-1;o=u,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function t0(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let t1={cwd:function(){return"/"}};function t4(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let t2=["history","path","basename","stem","extname","dirname"];class t3{constructor(e){let t,n;t=e?t4(e)?{path:e}:"string"==typeof e||e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e?{value:e}:e:{},this.cwd="cwd"in t?"":t1.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<t2.length;){let e=t2[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)t2.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?tG.basename(this.path):void 0}set basename(e){t5(e,"basename"),t6(e,"basename"),this.path=tG.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?tG.dirname(this.path):void 0}set dirname(e){t9(this.basename,"dirname"),this.path=tG.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?tG.extname(this.path):void 0}set extname(e){if(t6(e,"extname"),t9(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=tG.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){t4(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!t4(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),t5(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?tG.basename(this.path,this.extname):void 0}set stem(e){t5(e,"stem"),t6(e,"stem"),this.path=tG.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new J(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function t6(e,t){if(e&&e.includes(tG.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+tG.sep+"`")}function t5(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function t9(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let t7=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},t8={}.hasOwnProperty;class ne extends t7{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function i(l,...o){let a=e[++n],u=-1;if(l){r(l);return}for(;++u<t.length;)(null===o[u]||void 0===o[u])&&(o[u]=t[u]);t=o,a?(function(e,t){let n;return function(...t){let l;let o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new ne,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(tZ(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(ni("data",this.frozen),this.namespace[e]=t,this):t8.call(this.namespace,e)&&this.namespace[e]||void 0:e?(ni("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=na(e),n=this.parser||this.Parser;return nn("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nn("process",this.parser||this.Parser),nr("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=na(e),a=n.parse(o);function u(e,n){e||!n?i(e):r?r(n):((0,l.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){if(e||!t||!r)return u(e);let i=n.stringify(t,r);"string"==typeof i||i&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=i:r.result=i,u(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nn("processSync",this.parser||this.Parser),nr("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,tQ(e),t=r}),no("processSync","process",n),(0,l.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){nl(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){(0,l.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=na(t);r.run(e,a,function(t,r,a){let u=r||e;t?o(t):i?i(u):((0,l.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,u,a))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){tQ(e),n=t,r=!0}),no("runSync","run",r),(0,l.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=na(t),r=this.compiler||this.Compiler;return nr("stringify",r),nl(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(ni("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?l(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=tZ(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)o(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;o(t,n)}else i(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...l]=t,o=n[i][1];tJ(o)&&tJ(r)&&(r=tZ(!0,o,r)),n[i]=[e,r,...l]}}}}let nt=new ne().freeze();function nn(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nr(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function ni(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nl(e){if(!tJ(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function no(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function na(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new t3(e)}let nu=[],ns={allowDangerousHtml:!0},nc=/^(https?|ircs?|mailto|xmpp)$/i,nf=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function np(e){let t=function(e){let t=e.rehypePlugins||nu,n=e.remarkPlugins||nu,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...ns}:ns;return nt().use(tw).use(n).use(tX,r).use(t)}(e),n=function(e){let t=e.children||"",n=new t3;return"string"==typeof t?n.value=t:(0,l.t1)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,o=t.disallowedElements,a=t.skipHtml,u=t.unwrapDisallowed,s=t.urlTransform||nd;for(let e of nf)Object.hasOwn(t,e.from)&&(0,l.t1)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&o&&(0,l.t1)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),(0,tL.Vn)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return a?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ep)if(Object.hasOwn(ep,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ep[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,i)),l&&i&&"number"==typeof t)return u&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i;let l;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let o=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,l=function(e,t,r,i){let l=Array.isArray(r.children),a=$(e);return n(t,r,i,l,{columnNumber:a?a.column-1:void 0,fileName:o,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,i=t.jsxs,l=function(e,t,n,l){let o=Array.isArray(n.children)?i:r;return l?o(t,n,l):o(t,n)}}let a={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:l,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:o,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?F:L,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},u=el(a,e,void 0);return u&&"string"!=typeof u?u:a.create(e,a.Fragment,{children:u||void 0},void 0)}(e,{Fragment:ed.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ed.jsx,jsxs:ed.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nd(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nc.test(e.slice(0,t))?e:""}},4958:function(e,t,n){"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{Z:function(){return e_}});var i=n(24345),l=n(15459),o=n(88718),a=n(96093);let u="phrasing",s=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function d(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function h(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.O)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<i.length;)(0,o.S4)(e,"text",u);function u(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[l][0],o=i[l][1],a=0,u=n.children.indexOf(e),s=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},l=o(...f,i);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(l)?c.push(...l):l&&c.push(l),a=n+f[0].length,s=!0),!r.global)break;f=r.exec(e.value)}return s?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(u,1,...c)):c=[e],u+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,v]],{ignore:["link","linkReference"]})}function y(e,t,n,i,l){let o="";if(!x(l)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),l=r(e,"("),o=r(e,")");for(;-1!==i&&l>o;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),o++;return[e,n]}(n+i);if(!a[0])return!1;let u={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function v(e,t,n,r){return!(!x(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function x(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.B8)(n)||(0,l.Xh)(n))&&(!t||47!==n)}var k=n(49411);function b(){this.buffer()}function w(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function C(){this.buffer()}function S(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function P(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,k.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function O(e){this.exit(e)}function E(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,k.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function z(e){this.exit(e)}function M(e,t,n,r){let i=n.createTracker(r),l=i.move("[^"),o=n.enter("footnoteReference"),a=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),a(),o(),l+=i.move("]")}function I(e,t,n){return 0===t?e:D(e,t,n)}function D(e,t,n){return(n?"":"    ")+e}M.peek=function(){return"["};let T=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function A(e){this.enter({type:"delete",children:[]},e)}function _(e){this.exit(e)}function j(e,t,n,r){let i=n.createTracker(r),l=n.enter("strikethrough"),o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"})+i.move("~~"),l(),o}function L(e){return e.length}function F(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}j.peek=function(){return"~"};var N=n(62987);n(21623);var R=n(27962);function B(e,t,n){let r=e.value||"",i="`",l=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){let e;let t=n.unsafe[l],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}B.peek=function(){return"`"},(0,a.O)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let H=function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let o=l.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(o=4*Math.ceil(o/4));let a=n.createTracker(r);a.move(l+" ".repeat(o-l.length)),a.shift(o);let u=n.enter("listItem"),s=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(o))+e:(n?l:l+" ".repeat(o-l.length))+e});return u(),s};function V(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function U(e){this.exit(e),this.data.inTable=void 0}function W(e){this.enter({type:"tableRow",children:[]},e)}function q(e){this.exit(e)}function $(e){this.enter({type:"tableCell",children:[]},e)}function Y(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,K));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function K(e,t){return"|"===t?t:e}function X(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function Q(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r;let i=t.children,l=-1;for(;++l<i.length;){let e=i[l];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Z(e,t,n,r){let i=e.children[0],l="boolean"==typeof e.checked&&i&&"paragraph"===i.type,o="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);l&&a.move(o);let u=H(e,t,n,{...r,...a.current()});return l&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+o})),u}var J=n(4663);let G={tokenize:function(e,t,n){let r=0;return function t(l){return(87===l||119===l)&&r<3?(r++,e.consume(l),t):46===l&&3===r?(e.consume(l),i):n(l)};function i(e){return null===e?n(e):t(e)}},partial:!0},ee={tokenize:function(e,t,n){let r,i,o;return a;function a(t){return 46===t||95===t?e.check(en,s,u)(t):null===t||(0,l.z3)(t)||(0,l.B8)(t)||45!==t&&(0,l.Xh)(t)?s(t):(o=!0,e.consume(t),a)}function u(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function s(e){return i||r||!o?n(e):t(e)}},partial:!0},et={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?o(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(en,t,o)(a):null===a||(0,l.z3)(a)||(0,l.B8)(a)?t(a):(e.consume(a),i)}function o(t){return 41===t&&r++,e.consume(t),i}},partial:!0},en={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),o):93===a?(e.consume(a),i):60===a||null===a||(0,l.z3)(a)||(0,l.B8)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,l.z3)(e)||(0,l.B8)(e)?t(e):r(e)}function o(t){return(0,l.jv)(t)?function t(i){return 59===i?(e.consume(i),r):(0,l.jv)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},er={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,l.H$)(e)?n(e):t(e)}},partial:!0},ei={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!es.call(r,r.previous)||ed(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(G,e.attempt(ee,e.attempt(et,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:es},el={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",o=!1;return function(t){return(72===t||104===t)&&ec.call(r,r.previous)&&!ed(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,l.jv)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),u}return n(t)}function u(t){return 47===t?(e.consume(t),o)?s:(o=!0,u):n(t)}function s(t){return null===t||(0,l.Av)(t)||(0,l.z3)(t)||(0,l.B8)(t)||(0,l.Xh)(t)?n(t):e.attempt(ee,e.attempt(et,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:ec},eo={name:"emailAutolink",tokenize:function(e,t,n){let r,i;let o=this;return function(t){return!ep(t)||!ef.call(o,o.previous)||ed(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return ep(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(er,s,u)(t):45===t||95===t||(0,l.H$)(t)?(i=!0,e.consume(t),a):s(t)}function u(t){return e.consume(t),r=!0,a}function s(a){return i&&r&&(0,l.jv)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:ef},ea={},eu=48;for(;eu<123;)ea[eu]=eo,58==++eu?eu=65:91===eu&&(eu=97);function es(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,l.z3)(e)}function ec(e){return!(0,l.jv)(e)}function ef(e){return!(47===e||ep(e))}function ep(e){return 43===e||45===e||46===e||95===e||(0,l.H$)(e)}function ed(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ea[43]=eo,ea[45]=eo,ea[46]=eo,ea[95]=eo,ea[72]=[eo,el],ea[104]=[eo,el],ea[87]=[eo,ei],ea[119]=[eo,ei];var eh=n(23402),em=n(42761);let eg={tokenize:function(e,t,n){let r=this;return(0,em.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ey(e,t,n){let r;let i=this,l=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;l--;){let e=i.events[l][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(l){if(!r||!r._balanced)return n(l);let a=(0,k.d)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&o.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l)):n(l)}}function ev(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function ex(e,t,n){let r;let i=this,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),u};function u(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(u){if(a>999||93===u&&!r||null===u||91===u||(0,l.z3)(u))return n(u);if(93===u){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,k.d)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(u)}return(0,l.z3)(u)||(r=!0),a++,e.consume(u),92===u?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,s):s(t)}}function ek(e,t,n){let r,i;let o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),u=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(u>999||93===t&&!i||null===t||91===t||(0,l.z3)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,k.d)(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,l.z3)(t)||(i=!0),u++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,em.f)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function eb(e,t,n){return e.check(eh.w,t,e.attempt(eg,t,n))}function ew(e){e.exit("gfmFootnoteDefinition")}var eC=n(21905),eS=n(63233);class eP{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eO(e,t,n){let r;let i=this,o=0,a=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,l="tableHead"===r||"tableRow"===r?x:u;return l===x&&i.parser.lazy[i.now().line]?n(e):l(e)};function u(t){return e.enter("tableHead"),e.enter("tableRow"),124===t||(r=!0,a+=1),s(t)}function s(t){return null===t?n(t):(0,l.Ch)(t)?a>1?(a=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p):n(t):(0,l.xz)(t)?(0,em.f)(e,s,"whitespace")(t):(a+=1,r&&(r=!1,o+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,s):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,l.z3)(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?f:c)}function f(t){return 92===t||124===t?(e.consume(t),c):c(t)}function p(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,l.xz)(t))?(0,em.f)(e,d,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t)}function d(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),h):n(t)}function h(t){return(0,l.xz)(t)?(0,em.f)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(a+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(a+=1,g(t)):null===t||(0,l.Ch)(t)?v(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(n))}(t)):n(t)}function y(t){return(0,l.xz)(t)?(0,em.f)(e,v,"whitespace")(t):v(t)}function v(i){return 124===i?d(i):null===i||(0,l.Ch)(i)?r&&o===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i):n(i)}function x(t){return e.enter("tableRow"),k(t)}function k(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),k):null===n||(0,l.Ch)(n)?(e.exit("tableRow"),t(n)):(0,l.xz)(n)?(0,em.f)(e,k,"whitespace")(n):(e.enter("data"),b(n))}function b(t){return null===t||124===t||(0,l.z3)(t)?(e.exit("data"),k(t)):(e.consume(t),92===t?w:b)}function w(t){return 92===t||124===t?(e.consume(t),b):b(t)}}function eE(e,t){let n,r,i,l=-1,o=!0,a=0,u=[0,0,0,0],s=[0,0,0,0],c=!1,f=0,p=new eP;for(;++l<e.length;){let d=e[l],h=d[1];"enter"===d[0]?"tableHead"===h.type?(c=!1,0!==f&&(eM(p,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(o=!0,i=void 0,u=[0,0,0,0],s=[0,l+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",r,t]])),a="tableDelimiterRow"===h.type?2:r?3:1):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)?(o=!1,0===s[2]&&(0!==u[1]&&(s[0]=s[1],i=ez(p,t,u,a,void 0,i),u=[0,0,0,0]),s[2]=l)):"tableCellDivider"===h.type&&(o?o=!1:(0!==u[1]&&(s[0]=s[1],i=ez(p,t,u,a,void 0,i)),s=[(u=s)[1],l,0,0])):"tableHead"===h.type?(c=!0,f=l):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=l,0!==u[1]?(s[0]=s[1],i=ez(p,t,u,a,l,i)):0!==s[1]&&(i=ez(p,t,s,a,l,i)),a=0):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)&&(s[3]=l)}for(0!==f&&eM(p,t,f,n,r),p.consume(t.events),l=-1;++l<t.events.length;){let e=t.events[l];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,l))}return e}function ez(e,t,n,r,i,l){0!==n[0]&&(l.end=Object.assign({},eI(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));let o=eI(t.events,n[1]);if(l={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(n[1],0,[["enter",l,t]]),0!==n[2]){let i=eI(t.events,n[2]),l=eI(t.events,n[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},l)};if(e.add(n[2],0,[["enter",o,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",o,t]])}return void 0!==i&&(l.end=Object.assign({},eI(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function eM(e,t,n,r,i){let l=[],o=eI(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function eI(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eD={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,l.z3)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return(0,l.Ch)(r)?t(r):(0,l.xz)(r)?e.check({tokenize:eT},t,n)(r):n(r)}}};function eT(e,t,n){return(0,em.f)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eA={};function e_(e){let t;let n=e||eA,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,J.W)([{text:ea},{document:{91:{name:"gfmFootnoteDefinition",tokenize:ek,continuation:{tokenize:eb},exit:ew}},text:{91:{name:"gfmFootnoteCall",tokenize:ex},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ey,resolveTo:ev}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,l=this.events,o=0;return function(a){return 126===i&&"characterEscape"!==l[l.length-1][1].type?r(a):(e.enter("strikethroughSequenceTemporary"),function l(a){let u=(0,N.r)(i);if(126===a)return o>1?r(a):(e.consume(a),o++,l);if(o<2&&!t)return r(a);let s=e.exit("strikethroughSequenceTemporary"),c=(0,N.r)(a);return s._open=!c||2===c&&!!u,s._close=!u||2===u&&!!c,n(a)}(a))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},l={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},o=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",l,t]],a=t.parser.constructs.insideSpan.null;a&&(0,eC.d)(o,o.length,0,(0,eS.C)(a,e.slice(r+1,n),t)),(0,eC.d)(o,o.length,0,[["exit",l,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,eC.d)(e,r-1,n-r+3,o),n=r+o.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eO,resolveAll:eE}}},{text:{91:eD}}])),l.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:h,literalAutolinkHttp:p,literalAutolinkWww:d}},{enter:{gfmFootnoteCallString:b,gfmFootnoteCall:w,gfmFootnoteDefinitionLabelString:C,gfmFootnoteDefinition:S},exit:{gfmFootnoteCallString:P,gfmFootnoteCall:O,gfmFootnoteDefinitionLabelString:E,gfmFootnoteDefinition:z}},{canContainEols:["delete"],enter:{strikethrough:A},exit:{strikethrough:_}},{enter:{table:V,tableData:$,tableHeader:$,tableRow:W},exit:{codeText:Y,table:U,tableData:q,tableHeader:q,tableRow:q}},{exit:{taskListCheckValueChecked:X,taskListCheckValueUnchecked:X,paragraph:Q}}]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:":",before:"[ps]",after:"\\/",inConstruct:u,notInConstruct:s}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let l=r.createTracker(i),o=l.move("[^"),a=r.enter("footnoteDefinition"),u=r.enter("label");return o+=l.move(r.safe(r.associationId(e),{before:o,after:"]"})),u(),o+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),o+=l.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,l.current()),t?D:I))),a(),o},footnoteReference:M},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:T}],handlers:{delete:j}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=B(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return a(function(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("table");for(;++i<r.length;)l[i]=u(r[i],t,n);return o(),l}(e,n,r),e.align)},tableCell:o,tableRow:function(e,t,n,r){let i=a([u(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function o(e,t,n,r){let i=n.enter("tableCell"),o=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:l,after:l});return o(),i(),a}function a(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||L,l=[],o=[],a=[],u=[],s=0,c=-1;for(;++c<e.length;){let t=[],r=[],l=-1;for(e[c].length>s&&(s=e[c].length);++l<e[c].length;){var f;let o=null==(f=e[c][l])?"":String(f);if(!1!==n.alignDelimiters){let e=i(o);r[l]=e,(void 0===u[l]||e>u[l])&&(u[l]=e)}t.push(o)}o[c]=t,a[c]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<s;)l[p]=F(r[p]);else{let e=F(r);for(;++p<s;)l[p]=e}p=-1;let d=[],h=[];for(;++p<s;){let e=l[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,u[p]-t.length-r.length),o=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>u[p]&&(u[p]=i),h[p]=i),d[p]=o}o.splice(1,0,d),a.splice(1,0,h),c=-1;let m=[];for(;++c<o.length;){let e=o[c],t=a[c];p=-1;let r=[];for(;++p<s;){let i=e[p]||"",o="",a="";if(!1!==n.alignDelimiters){let e=u[p]-(t[p]||0),n=l[p];114===n?o=" ".repeat(e):99===n?e%2?(o=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):a=o=" ".repeat(e/2):a=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&!(!1===n.alignDelimiters&&""===i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(o),r.push(i),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==s-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function u(e,t,n){let r=e.children,i=-1,l=[],a=t.enter("tableRow");for(;++i<r.length;)l[i]=o(r[i],e,t,n);return a(),l}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Z}}]})}},96093:function(e,t,n){"use strict";n.d(t,{O:function(){return r}});let r=function(e){if(null==e)return l;if("function"==typeof e)return i(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):i(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return i(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function l(){return!0}},88718:function(e,t,n){"use strict";n.d(t,{BK:function(){return l},S4:function(){return o}});var r=n(96093);let i=[],l=!1;function o(e,t,n,o){let a;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):a=t;let u=(0,r.O)(a),s=o?-1:1;(function e(r,a,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return p;function p(){var f;let p,d,h,m=i;if((!t||u(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===l)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(d=(o?r.children.length:-1)+s,h=c.concat(r);d>-1&&d<r.children.length;){if((p=e(r.children[d],d,h)())[0]===l)return p;d="number"==typeof p[1]?p[1]:d+s}return m}})(e,void 0,[])()}},21623:function(e,t,n){"use strict";n.d(t,{Vn:function(){return i}});var r=n(88718);function i(e,t,n,i){let l,o,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,l=n):(o=t,a=n,l=i),(0,r.S4)(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},l)}}}]);