(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[510],{82679:function(e,t,n){e=n.nmd(e),ace.define("ace/snippets",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter","ace/lib/lang","ace/range","ace/range_list","ace/keyboard/hash_handler","ace/tokenizer","ace/clipboard","ace/editor"],function(e,t,n){"use strict";var i=e("./lib/dom"),o=e("./lib/oop"),r=e("./lib/event_emitter").EventEmitter,s=e("./lib/lang"),a=e("./range").Range,c=e("./range_list").RangeList,l=e("./keyboard/hash_handler").<PERSON><PERSON><PERSON><PERSON><PERSON>,h=e("./tokenizer").Tokenizer,p=e("./clipboard"),u={CURRENT_WORD:function(e){return e.session.getTextRange(e.session.getWordRange())},SELECTION:function(e,t,n){var i=e.session.getTextRange();return n?i.replace(/\n\r?([ \t]*\S)/g,"\n"+n+"$1"):i},CURRENT_LINE:function(e){return e.session.getLine(e.getCursorPosition().row)},PREV_LINE:function(e){return e.session.getLine(e.getCursorPosition().row-1)},LINE_INDEX:function(e){return e.getCursorPosition().row},LINE_NUMBER:function(e){return e.getCursorPosition().row+1},SOFT_TABS:function(e){return e.session.getUseSoftTabs()?"YES":"NO"},TAB_SIZE:function(e){return e.session.getTabSize()},CLIPBOARD:function(e){return p.getText&&p.getText()},FILENAME:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0]},FILENAME_BASE:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0].replace(/\.[^.]*$/,"")},DIRECTORY:function(e){return this.FILEPATH(e).replace(/[^/\\]*$/,"")},FILEPATH:function(e){return"/not implemented.txt"},WORKSPACE_NAME:function(){return"Unknown"},FULLNAME:function(){return"Unknown"},BLOCK_COMMENT_START:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.start||""},BLOCK_COMMENT_END:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.end||""},LINE_COMMENT:function(e){return(e.session.$mode||{}).lineCommentStart||""},CURRENT_YEAR:d.bind(null,{year:"numeric"}),CURRENT_YEAR_SHORT:d.bind(null,{year:"2-digit"}),CURRENT_MONTH:d.bind(null,{month:"numeric"}),CURRENT_MONTH_NAME:d.bind(null,{month:"long"}),CURRENT_MONTH_NAME_SHORT:d.bind(null,{month:"short"}),CURRENT_DATE:d.bind(null,{day:"2-digit"}),CURRENT_DAY_NAME:d.bind(null,{weekday:"long"}),CURRENT_DAY_NAME_SHORT:d.bind(null,{weekday:"short"}),CURRENT_HOUR:d.bind(null,{hour:"2-digit",hour12:!1}),CURRENT_MINUTE:d.bind(null,{minute:"2-digit"}),CURRENT_SECOND:d.bind(null,{second:"2-digit"})};function d(e){var t=new Date().toLocaleString("en-us",e);return 1==t.length?"0"+t:t}u.SELECTED_TEXT=u.SELECTION;var f=function(){function e(){this.snippetMap={},this.snippetNameMap={},this.variables=u}return e.prototype.getTokenizer=function(){return e.$tokenizer||this.createTokenizer()},e.prototype.createTokenizer=function(){function t(e){return(e=e.substr(1),/^\d+$/.test(e))?[{tabstopId:parseInt(e,10)}]:[{text:e}]}function n(e){return"(?:[^\\\\"+e+"]|\\\\.)"}var i={regex:"/("+n("/")+"+)/",onMatch:function(e,t,n){var i=n[0];return i.fmtString=!0,i.guard=e.slice(1,-1),i.flag="",""},next:"formatString"};return e.$tokenizer=new h({start:[{regex:/\\./,onMatch:function(e,t,n){var i=e[1];return"}"==i&&n.length?e=i:-1!="`$\\".indexOf(i)&&(e=i),[e]}},{regex:/}/,onMatch:function(e,t,n){return[n.length?n.shift():e]}},{regex:/\$(?:\d+|\w+)/,onMatch:t},{regex:/\$\{[\dA-Z_a-z]+/,onMatch:function(e,n,i){var o=t(e.substr(1));return i.unshift(o[0]),o},next:"snippetVar"},{regex:/\n/,token:"newline",merge:!1}],snippetVar:[{regex:"\\|"+n("\\|")+"*\\|",onMatch:function(e,t,n){var i=e.slice(1,-1).replace(/\\[,|\\]|,/g,function(e){return 2==e.length?e[1]:"\0"}).split("\0").map(function(e){return{value:e}});return n[0].choices=i,[i[0]]},next:"start"},i,{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"start"}],formatString:[{regex:/:/,onMatch:function(e,t,n){return n.length&&n[0].expectElse?(n[0].expectElse=!1,n[0].ifEnd={elseEnd:n[0]},[n[0].ifEnd]):":"}},{regex:/\\./,onMatch:function(e,t,n){var i=e[1];return"}"==i&&n.length?e=i:-1!="`$\\".indexOf(i)?e=i:"n"==i?e="\n":"t"==i?e="	":-1!="ulULE".indexOf(i)&&(e={changeCase:i,local:i>"a"}),[e]}},{regex:"/\\w*}",onMatch:function(e,t,n){var i=n.shift();return i&&(i.flag=e.slice(1,-1)),this.next=i&&i.tabstopId?"start":"",[i||e]},next:"start"},{regex:/\$(?:\d+|\w+)/,onMatch:function(e,t,n){return[{text:e.slice(1)}]}},{regex:/\${\w+/,onMatch:function(e,t,n){var i={text:e.slice(2)};return n.unshift(i),[i]},next:"formatStringVar"},{regex:/\n/,token:"newline",merge:!1},{regex:/}/,onMatch:function(e,t,n){var i=n.shift();return this.next=i&&i.tabstopId?"start":"",[i||e]},next:"start"}],formatStringVar:[{regex:/:\/\w+}/,onMatch:function(e,t,n){return n[0].formatFunction=e.slice(2,-1),[n.shift()]},next:"formatString"},i,{regex:/:[\?\-+]?/,onMatch:function(e,t,n){"+"==e[1]&&(n[0].ifEnd=n[0]),"?"==e[1]&&(n[0].expectElse=!0)},next:"formatString"},{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"formatString"}]}),e.$tokenizer},e.prototype.tokenizeTmSnippet=function(e,t){return this.getTokenizer().getLineTokens(e,t).tokens.map(function(e){return e.value||e})},e.prototype.getVariableValue=function(e,t,n){if(/^\d+$/.test(t))return(this.variables.__||{})[t]||"";if(/^[A-Z]\d+$/.test(t))return(this.variables[t[0]+"__"]||{})[t.substr(1)]||"";if(t=t.replace(/^TM_/,""),!this.variables.hasOwnProperty(t))return"";var i=this.variables[t];return"function"==typeof i&&(i=this.variables[t](e,t,n)),null==i?"":i},e.prototype.tmStrFormat=function(e,t,n){if(!t.fmt)return e;var i=t.flag||"",o=t.guard;o=new RegExp(o,i.replace(/[^gim]/g,""));var r="string"==typeof t.fmt?this.tokenizeTmSnippet(t.fmt,"formatString"):t.fmt,s=this;return e.replace(o,function(){var e=s.variables.__;s.variables.__=[].slice.call(arguments);for(var t=s.resolveVariables(r,n),i="E",o=0;o<t.length;o++){var a=t[o];if("object"==typeof a){if(t[o]="",a.changeCase&&a.local){var c=t[o+1];c&&"string"==typeof c&&("u"==a.changeCase?t[o]=c[0].toUpperCase():t[o]=c[0].toLowerCase(),t[o+1]=c.substr(1))}else a.changeCase&&(i=a.changeCase)}else"U"==i?t[o]=a.toUpperCase():"L"==i&&(t[o]=a.toLowerCase())}return s.variables.__=e,t.join("")})},e.prototype.tmFormatFunction=function(e,t,n){return"upcase"==t.formatFunction?e.toUpperCase():"downcase"==t.formatFunction?e.toLowerCase():e},e.prototype.resolveVariables=function(e,t){for(var n=[],i="",o=!0,r=0;r<e.length;r++){var s=e[r];if("string"==typeof s){n.push(s),"\n"==s?(o=!0,i=""):o&&(i=/^\t*/.exec(s)[0],o=/\S/.test(s));continue}if(s){if(o=!1,s.fmtString){var a=e.indexOf(s,r+1);-1==a&&(a=e.length),s.fmt=e.slice(r+1,a),r=a}if(s.text){var c=this.getVariableValue(t,s.text,i)+"";s.fmtString&&(c=this.tmStrFormat(c,s,t)),s.formatFunction&&(c=this.tmFormatFunction(c,s,t)),c&&!s.ifEnd?(n.push(c),l(s)):!c&&s.ifEnd&&l(s.ifEnd)}else s.elseEnd?l(s.elseEnd):null!=s.tabstopId?n.push(s):null!=s.changeCase&&n.push(s)}}function l(t){var n=e.indexOf(t,r+1);-1!=n&&(r=n)}return n},e.prototype.getDisplayTextForSnippet=function(e,t){return g.call(this,e,t).text},e.prototype.insertSnippetForSelection=function(e,t,n){void 0===n&&(n={});var i=g.call(this,e,t,n),o=e.getSelectionRange(),r=e.session.replace(o,i.text),s=new m(e),a=e.inVirtualSelectionMode&&e.selection.index;s.addTabstops(i.tabstops,o.start,r,a)},e.prototype.insertSnippet=function(e,t,n){void 0===n&&(n={});var i=this;if(e.inVirtualSelectionMode)return i.insertSnippetForSelection(e,t,n);e.forEachSelection(function(){i.insertSnippetForSelection(e,t,n)},null,{keepOrder:!0}),e.tabstopManager&&e.tabstopManager.tabNext()},e.prototype.$getScope=function(e){var t=e.session.$mode.$id||"";if("html"===(t=t.split("/").pop())||"php"===t){"php"!==t||e.session.$mode.inlinePhp||(t="html");var n=e.getCursorPosition(),i=e.session.getState(n.row);"object"==typeof i&&(i=i[0]),i.substring&&("js-"==i.substring(0,3)?t="javascript":"css-"==i.substring(0,4)?t="css":"php-"==i.substring(0,4)&&(t="php"))}return t},e.prototype.getActiveScopes=function(e){var t=this.$getScope(e),n=[t],i=this.snippetMap;return i[t]&&i[t].includeScopes&&n.push.apply(n,i[t].includeScopes),n.push("_"),n},e.prototype.expandWithTab=function(e,t){var n=this,i=e.forEachSelection(function(){return n.expandSnippetForSelection(e,t)},null,{keepOrder:!0});return i&&e.tabstopManager&&e.tabstopManager.tabNext(),i},e.prototype.expandSnippetForSelection=function(e,t){var n,i=e.getCursorPosition(),o=e.session.getLine(i.row),r=o.substring(0,i.column),s=o.substr(i.column),a=this.snippetMap;return this.getActiveScopes(e).some(function(e){var t=a[e];return t&&(n=this.findMatchingSnippet(t,r,s)),!!n},this),!!n&&(!!t&&!!t.dryRun||(e.session.doc.removeInLine(i.row,i.column-n.replaceBefore.length,i.column+n.replaceAfter.length),this.variables.M__=n.matchBefore,this.variables.T__=n.matchAfter,this.insertSnippetForSelection(e,n.content),this.variables.M__=this.variables.T__=null,!0))},e.prototype.findMatchingSnippet=function(e,t,n){for(var i=e.length;i--;){var o=e[i];if((!o.startRe||o.startRe.test(t))&&(!o.endRe||o.endRe.test(n))&&(o.startRe||o.endRe))return o.matchBefore=o.startRe?o.startRe.exec(t):[""],o.matchAfter=o.endRe?o.endRe.exec(n):[""],o.replaceBefore=o.triggerRe?o.triggerRe.exec(t)[0]:"",o.replaceAfter=o.endTriggerRe?o.endTriggerRe.exec(n)[0]:"",o}},e.prototype.register=function(e,t){var n=this.snippetMap,i=this.snippetNameMap,o=this;function r(e){return e&&!/^\^?\(.*\)\$?$|^\\b$/.test(e)&&(e="(?:"+e+")"),e||""}function a(e,t,n){return e=r(e),t=r(t),n?(e=t+e)&&"$"!=e[e.length-1]&&(e+="$"):(e+=t)&&"^"!=e[0]&&(e="^"+e),new RegExp(e)}function c(e){e.scope||(e.scope=t||"_"),n[t=e.scope]||(n[t]=[],i[t]={});var r=i[t];if(e.name){var c=r[e.name];c&&o.unregister(c),r[e.name]=e}n[t].push(e),e.prefix&&(e.tabTrigger=e.prefix),!e.content&&e.body&&(e.content=Array.isArray(e.body)?e.body.join("\n"):e.body),e.tabTrigger&&!e.trigger&&(!e.guard&&/^\w/.test(e.tabTrigger)&&(e.guard="\\b"),e.trigger=s.escapeRegExp(e.tabTrigger)),(e.trigger||e.guard||e.endTrigger||e.endGuard)&&(e.startRe=a(e.trigger,e.guard,!0),e.triggerRe=new RegExp(e.trigger),e.endRe=a(e.endTrigger,e.endGuard,!0),e.endTriggerRe=new RegExp(e.endTrigger))}e||(e=[]),Array.isArray(e)?e.forEach(c):Object.keys(e).forEach(function(t){c(e[t])}),this._signal("registerSnippets",{scope:t})},e.prototype.unregister=function(e,t){var n=this.snippetMap,i=this.snippetNameMap;function o(e){var o=i[e.scope||t];if(o&&o[e.name]){delete o[e.name];var r=n[e.scope||t],s=r&&r.indexOf(e);s>=0&&r.splice(s,1)}}e.content?o(e):Array.isArray(e)&&e.forEach(o)},e.prototype.parseSnippetFile=function(e){e=e.replace(/\r/g,"");for(var t,n=[],i={},o=/^#.*|^({[\s\S]*})\s*$|^(\S+) (.*)$|^((?:\n*\t.*)+)/gm;t=o.exec(e);){if(t[1])try{i=JSON.parse(t[1]),n.push(i)}catch(e){}if(t[4])i.content=t[4].replace(/^\t/gm,""),n.push(i),i={};else{var r=t[2],s=t[3];if("regex"==r){var a=/\/((?:[^\/\\]|\\.)*)|$/g;i.guard=a.exec(s)[1],i.trigger=a.exec(s)[1],i.endTrigger=a.exec(s)[1],i.endGuard=a.exec(s)[1]}else"snippet"==r?(i.tabTrigger=s.match(/^\S*/)[0],i.name||(i.name=s)):r&&(i[r]=s)}}return n},e.prototype.getSnippetByName=function(e,t){var n,i=this.snippetNameMap;return this.getActiveScopes(t).some(function(t){var o=i[t];return o&&(n=o[e]),!!n},this),n},e}();o.implement(f.prototype,r);var g=function(e,t,n){void 0===n&&(n={});var i=e.getCursorPosition(),o=e.session.getLine(i.row),r=e.session.getTabString(),s=o.match(/^\s*/)[0];i.column<s.length&&(s=s.slice(0,i.column)),t=t.replace(/\r/g,"");var a=this.tokenizeTmSnippet(t);a=(a=this.resolveVariables(a,e)).map(function(e){return"\n"!=e||n.excludeExtraIndent?"string"==typeof e?e.replace(/\t/g,r):e:e+s});var c=[];a.forEach(function(e,t){if("object"==typeof e){var n=e.tabstopId,i=c[n];if(i||((i=c[n]=[]).index=n,i.value="",i.parents={}),-1===i.indexOf(e)){e.choices&&!i.choices&&(i.choices=e.choices),i.push(e);var o=a.indexOf(e,t+1);if(-1!==o){var r=a.slice(t+1,o);r.some(function(e){return"object"==typeof e})&&!i.value?i.value=r:r.length&&(!i.value||"string"!=typeof i.value)&&(i.value=r.join(""))}}}}),c.forEach(function(e){e.length=0});for(var l={},h=0;h<a.length;h++){var p=a[h];if("object"==typeof p){var u=p.tabstopId,d=c[u],f=a.indexOf(p,h+1);if(l[u]){l[u]===p&&(delete l[u],Object.keys(l).forEach(function(e){d.parents[e]=!0}));continue}l[u]=p;var g=d.value;"string"!=typeof g?g=function(e){for(var t=[],n=0;n<e.length;n++){var i=e[n];if("object"==typeof i){if(l[i.tabstopId])continue;i=t[e.lastIndexOf(i,n-1)]||{tabstopId:i.tabstopId}}t[n]=i}return t}(g):p.fmt&&(g=this.tmStrFormat(g,p,e)),a.splice.apply(a,[h+1,Math.max(0,f-h)].concat(g,p)),-1===d.indexOf(p)&&d.push(p)}}var m=0,b=0,v="";return a.forEach(function(e){if("string"==typeof e){var t=e.split("\n");t.length>1?(b=t[t.length-1].length,m+=t.length-1):b+=e.length,v+=e}else e&&(e.start?e.end={row:m,column:b}:e.start={row:m,column:b})}),{text:v,tabstops:c,tokens:a}},m=function(){function e(e){if(this.index=0,this.ranges=[],this.tabstops=[],e.tabstopManager)return e.tabstopManager;e.tabstopManager=this,this.$onChange=this.onChange.bind(this),this.$onChangeSelection=s.delayedCall(this.onChangeSelection.bind(this)).schedule,this.$onChangeSession=this.onChangeSession.bind(this),this.$onAfterExec=this.onAfterExec.bind(this),this.attach(e)}return e.prototype.attach=function(e){this.$openTabstops=null,this.selectedTabstop=null,this.editor=e,this.session=e.session,this.editor.on("change",this.$onChange),this.editor.on("changeSelection",this.$onChangeSelection),this.editor.on("changeSession",this.$onChangeSession),this.editor.commands.on("afterExec",this.$onAfterExec),this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler)},e.prototype.detach=function(){this.tabstops.forEach(this.removeTabstopMarkers,this),this.ranges.length=0,this.tabstops.length=0,this.selectedTabstop=null,this.editor.off("change",this.$onChange),this.editor.off("changeSelection",this.$onChangeSelection),this.editor.off("changeSession",this.$onChangeSession),this.editor.commands.off("afterExec",this.$onAfterExec),this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.tabstopManager=null,this.session=null,this.editor=null},e.prototype.onChange=function(e){for(var t="r"==e.action[0],n=this.selectedTabstop||{},i=n.parents||{},o=this.tabstops.slice(),r=0;r<o.length;r++){var s=o[r],a=s==n||i[s.index];if(s.rangeList.$bias=a?0:1,"remove"==e.action&&s!==n){var c=s.parents&&s.parents[n.index],l=s.rangeList.pointIndex(e.start,c);l=l<0?-l-1:l+1;var h=s.rangeList.pointIndex(e.end,c);h=h<0?-h-1:h-1;for(var p=s.rangeList.ranges.slice(l,h),u=0;u<p.length;u++)this.removeRange(p[u])}s.rangeList.$onChange(e)}var d=this.session;this.$inChange||!t||1!=d.getLength()||d.getValue()||this.detach()},e.prototype.updateLinkedFields=function(){var e=this.selectedTabstop;if(e&&e.hasLinkedRanges&&e.firstNonLinked){this.$inChange=!0;for(var n=this.session,i=n.getTextRange(e.firstNonLinked),o=0;o<e.length;o++){var r=e[o];if(r.linked){var s=r.original,a=t.snippetManager.tmStrFormat(i,s,this.editor);n.replace(r,a)}}this.$inChange=!1}},e.prototype.onAfterExec=function(e){e.command&&!e.command.readOnly&&this.updateLinkedFields()},e.prototype.onChangeSelection=function(){if(this.editor){for(var e=this.editor.selection.lead,t=this.editor.selection.anchor,n=this.editor.selection.isEmpty(),i=0;i<this.ranges.length;i++)if(!this.ranges[i].linked){var o=this.ranges[i].contains(e.row,e.column),r=n||this.ranges[i].contains(t.row,t.column);if(o&&r)return}this.detach()}},e.prototype.onChangeSession=function(){this.detach()},e.prototype.tabNext=function(e){var t=this.tabstops.length,n=this.index+(e||1);(n=Math.min(Math.max(n,1),t))==t&&(n=0),this.selectTabstop(n),this.updateTabstopMarkers(),0===n&&this.detach()},e.prototype.selectTabstop=function(e){this.$openTabstops=null;var t=this.tabstops[this.index];if(t&&this.addTabstopMarkers(t),this.index=e,(t=this.tabstops[this.index])&&t.length){this.selectedTabstop=t;var n=t.firstNonLinked||t;if(t.choices&&(n.cursor=n.start),this.editor.inVirtualSelectionMode)this.editor.selection.fromOrientedRange(n);else{var i=this.editor.multiSelect;i.toSingleRange(n);for(var o=0;o<t.length;o++)t.hasLinkedRanges&&t[o].linked||i.addRange(t[o].clone(),!0)}this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler),this.selectedTabstop&&this.selectedTabstop.choices&&this.editor.execCommand("startAutocomplete",{matches:this.selectedTabstop.choices})}},e.prototype.addTabstops=function(e,t,n){var i=this.useLink||!this.editor.getOption("enableMultiselect");if(this.$openTabstops||(this.$openTabstops=[]),!e[0]){var o=a.fromPoints(n,n);v(o.start,t),v(o.end,t),e[0]=[o],e[0].index=0}var r=[this.index+1,0],s=this.ranges,l=this.snippetId=(this.snippetId||0)+1;e.forEach(function(e,n){var o=this.$openTabstops[n]||e;o.snippetId=l;for(var h=0;h<e.length;h++){var p=e[h],u=a.fromPoints(p.start,p.end||p.start);b(u.start,t),b(u.end,t),u.original=p,u.tabstop=o,s.push(u),o!=e?o.unshift(u):o[h]=u,p.fmtString||o.firstNonLinked&&i?(u.linked=!0,o.hasLinkedRanges=!0):o.firstNonLinked||(o.firstNonLinked=u)}o.firstNonLinked||(o.hasLinkedRanges=!1),o===e&&(r.push(o),this.$openTabstops[n]=o),this.addTabstopMarkers(o),o.rangeList=o.rangeList||new c,o.rangeList.$bias=0,o.rangeList.addList(o)},this),r.length>2&&(this.tabstops.length&&r.push(r.splice(2,1)[0]),this.tabstops.splice.apply(this.tabstops,r))},e.prototype.addTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){e.markerId||(e.markerId=t.addMarker(e,"ace_snippet-marker","text"))})},e.prototype.removeTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){t.removeMarker(e.markerId),e.markerId=null})},e.prototype.updateTabstopMarkers=function(){if(this.selectedTabstop){var e=this.selectedTabstop.snippetId;0===this.selectedTabstop.index&&e--,this.tabstops.forEach(function(t){t.snippetId===e?this.addTabstopMarkers(t):this.removeTabstopMarkers(t)},this)}},e.prototype.removeRange=function(e){var t=e.tabstop.indexOf(e);-1!=t&&e.tabstop.splice(t,1),-1!=(t=this.ranges.indexOf(e))&&this.ranges.splice(t,1),-1!=(t=e.tabstop.rangeList.ranges.indexOf(e))&&e.tabstop.splice(t,1),this.session.removeMarker(e.markerId),e.tabstop.length||(-1!=(t=this.tabstops.indexOf(e.tabstop))&&this.tabstops.splice(t,1),this.tabstops.length||this.detach())},e}();m.prototype.keyboardHandler=new l,m.prototype.keyboardHandler.bindKeys({Tab:function(e){t.snippetManager&&t.snippetManager.expandWithTab(e)||(e.tabstopManager.tabNext(1),e.renderer.scrollCursorIntoView())},"Shift-Tab":function(e){e.tabstopManager.tabNext(-1),e.renderer.scrollCursorIntoView()},Esc:function(e){e.tabstopManager.detach()}});var b=function(e,t){0==e.row&&(e.column+=t.column),e.row+=t.row},v=function(e,t){e.row==t.row&&(e.column-=t.column),e.row-=t.row};i.importCssString("\n.ace_snippet-marker {\n    -moz-box-sizing: border-box;\n    box-sizing: border-box;\n    background: rgba(194, 193, 208, 0.09);\n    border: 1px dotted rgba(211, 208, 235, 0.62);\n    position: absolute;\n}","snippets.css",!1),t.snippetManager=new f,(function(){this.insertSnippet=function(e,n){return t.snippetManager.insertSnippet(this,e,n)},this.expandSnippet=function(e){return t.snippetManager.expandWithTab(this,e)}}).call(e("./editor").Editor.prototype)}),ace.define("ace/autocomplete/popup",["require","exports","module","ace/virtual_renderer","ace/editor","ace/range","ace/lib/event","ace/lib/lang","ace/lib/dom","ace/config","ace/lib/useragent"],function(e,t,n){"use strict";var i=e("../virtual_renderer").VirtualRenderer,o=e("../editor").Editor,r=e("../range").Range,s=e("../lib/event"),a=e("../lib/lang"),c=e("../lib/dom"),l=e("../config").nls,h=e("./../lib/useragent"),p=function(e){return"suggest-aria-id:".concat(e)},u=h.isSafari?"menu":"listbox",d=h.isSafari?"menuitem":"option",f=h.isSafari?"aria-current":"aria-selected",g=function(e){var t=new i(e);t.$maxLines=4;var n=new o(t);return n.setHighlightActiveLine(!1),n.setShowPrintMargin(!1),n.renderer.setShowGutter(!1),n.renderer.setHighlightGutterLine(!1),n.$mouseHandler.$focusTimeout=0,n.$highlightTagPending=!0,n};c.importCssString('\n.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #CAD6FA;\n    z-index: 1;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #3a674e;\n}\n.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid #abbffe;\n    margin-top: -1px;\n    background: rgba(233,233,253,0.4);\n    position: absolute;\n    z-index: 2;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid rgba(109, 150, 13, 0.8);\n    background: rgba(58, 103, 78, 0.62);\n}\n.ace_completion-meta {\n    opacity: 0.5;\n    margin-left: 0.9em;\n}\n.ace_completion-message {\n    margin-left: 0.9em;\n    color: blue;\n}\n.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #2d69c7;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #93ca12;\n}\n.ace_editor.ace_autocomplete {\n    width: 300px;\n    z-index: 200000;\n    border: 1px lightgray solid;\n    position: fixed;\n    box-shadow: 2px 3px 5px rgba(0,0,0,.2);\n    line-height: 1.4;\n    background: #fefefe;\n    color: #111;\n}\n.ace_dark.ace_editor.ace_autocomplete {\n    border: 1px #484747 solid;\n    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);\n    line-height: 1.4;\n    background: #25282c;\n    color: #c1c1c1;\n}\n.ace_autocomplete .ace_text-layer  {\n    width: calc(100% - 8px);\n}\n.ace_autocomplete .ace_line {\n    display: flex;\n    align-items: center;\n}\n.ace_autocomplete .ace_line > * {\n    min-width: 0;\n    flex: 0 0 auto;\n}\n.ace_autocomplete .ace_line .ace_ {\n    flex: 0 1 auto;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n.ace_autocomplete .ace_completion-spacer {\n    flex: 1;\n}\n.ace_autocomplete.ace_loading:after  {\n    content: "";\n    position: absolute;\n    top: 0px;\n    height: 2px;\n    width: 8%;\n    background: blue;\n    z-index: 100;\n    animation: ace_progress 3s infinite linear;\n    animation-delay: 300ms;\n    transform: translateX(-100%) scaleX(1);\n}\n@keyframes ace_progress {\n    0% { transform: translateX(-100%) scaleX(1) }\n    50% { transform: translateX(625%) scaleX(2) } \n    100% { transform: translateX(1500%) scaleX(3) } \n}\n@media (prefers-reduced-motion) {\n    .ace_autocomplete.ace_loading:after {\n        transform: translateX(625%) scaleX(2);\n        animation: none;\n     }\n}\n',"autocompletion.css",!1),t.AcePopup=function(e){var t,n=c.createElement("div"),i=g(n);e&&e.appendChild(n),n.style.display="none",i.renderer.content.style.cursor="default",i.renderer.setStyle("ace_autocomplete"),i.renderer.$textLayer.element.setAttribute("role",u),i.renderer.$textLayer.element.setAttribute("aria-roledescription",l("autocomplete.popup.aria-roledescription","Autocomplete suggestions")),i.renderer.$textLayer.element.setAttribute("aria-label",l("autocomplete.popup.aria-label","Autocomplete suggestions")),i.renderer.textarea.setAttribute("aria-hidden","true"),i.setOption("displayIndentGuides",!1),i.setOption("dragDelay",150);var o=function(){};i.focus=o,i.$isFocused=!0,i.renderer.$cursorLayer.restartTimer=o,i.renderer.$cursorLayer.element.style.opacity="0",i.renderer.$maxLines=8,i.renderer.$keepTextAreaAtCursor=!1,i.setHighlightActiveLine(!1),i.session.highlight(""),i.session.$searchHighlight.clazz="ace_highlight-marker",i.on("mousedown",function(e){var t=e.getDocumentPosition();i.selection.moveToPosition(t),m.start.row=m.end.row=t.row,e.stop()});var h=new r(-1,0,-1,1/0),m=new r(-1,0,-1,1/0);m.id=i.session.addMarker(m,"ace_active-line","fullLine"),i.setSelectOnHover=function(e){e?h.id&&(i.session.removeMarker(h.id),h.id=null):h.id=i.session.addMarker(h,"ace_line-hover","fullLine")},i.setSelectOnHover(!1),i.on("mousemove",function(e){if(!t){t=e;return}if(t.x!=e.x||t.y!=e.y){(t=e).scrollTop=i.renderer.scrollTop,i.isMouseOver=!0;var n=t.getDocumentPosition().row;h.start.row!=n&&(h.id||i.setRow(n),v(n))}}),i.renderer.on("beforeRender",function(){if(t&&-1!=h.start.row){t.$pos=null;var e=t.getDocumentPosition().row;h.id||i.setRow(e),v(e,!0)}}),i.renderer.on("afterRender",function(){for(var e=i.renderer.$textLayer,t=e.config.firstRow,n=e.config.lastRow;t<=n;t++){var o=e.element.childNodes[t-e.config.firstRow];o.setAttribute("role",d),o.setAttribute("aria-roledescription",l("autocomplete.popup.item.aria-roledescription","item")),o.setAttribute("aria-setsize",i.data.length),o.setAttribute("aria-describedby","doc-tooltip"),o.setAttribute("aria-posinset",t+1);var r=i.getData(t);if(r){var s="".concat(r.caption||r.value).concat(r.meta?", ".concat(r.meta):"");o.setAttribute("aria-label",s)}o.querySelectorAll(".ace_completion-highlight").forEach(function(e){e.setAttribute("role","mark")})}}),i.renderer.on("afterRender",function(){var e=i.getRow(),t=i.renderer.$textLayer,n=t.element.childNodes[e-t.config.firstRow],o=document.activeElement;if(n!==i.selectedNode&&i.selectedNode&&(c.removeCssClass(i.selectedNode,"ace_selected"),i.selectedNode.removeAttribute(f),i.selectedNode.removeAttribute("id")),o.removeAttribute("aria-activedescendant"),i.selectedNode=n,n){var r=p(e);c.addCssClass(n,"ace_selected"),n.id=r,t.element.setAttribute("aria-activedescendant",r),o.setAttribute("aria-activedescendant",r),n.setAttribute(f,"true")}});var b=function(){v(-1)},v=function(e,t){e!==h.start.row&&(h.start.row=h.end.row=e,t||i.session._emit("changeBackMarker"),i._emit("changeHoverMarker"))};i.getHoveredRow=function(){return h.start.row},s.addListener(i.container,"mouseout",function(){i.isMouseOver=!1,b()}),i.on("hide",b),i.on("changeSelection",b),i.session.doc.getLength=function(){return i.data.length},i.session.doc.getLine=function(e){var t=i.data[e];return"string"==typeof t?t:t&&t.value||""};var y=i.session.bgTokenizer;return y.$tokenizeRow=function(e){var t=i.data[e],n=[];if(!t)return n;"string"==typeof t&&(t={value:t});var o=t.caption||t.value||t.name;function r(e,i){e&&n.push({type:(t.className||"")+(i||""),value:e})}for(var s=o.toLowerCase(),a=(i.filterText||"").toLowerCase(),c=0,l=0,h=0;h<=a.length;h++)if(h!=l&&(t.matchMask&1<<h||h==a.length)){var p=a.slice(l,h);l=h;var u=s.indexOf(p,c);if(-1==u)continue;r(o.slice(c,u),""),c=u+p.length,r(o.slice(u,c),"completion-highlight")}return r(o.slice(c,o.length),""),n.push({type:"completion-spacer",value:" "}),t.meta&&n.push({type:"completion-meta",value:t.meta}),t.message&&n.push({type:"completion-message",value:t.message}),n},y.$updateOnChange=o,y.start=o,i.session.$computeWidth=function(){return this.screenWidth=0},i.isOpen=!1,i.isTopdown=!1,i.autoSelect=!0,i.filterText="",i.isMouseOver=!1,i.data=[],i.setData=function(e,t){i.filterText=t||"",i.setValue(a.stringRepeat("\n",e.length),-1),i.data=e||[],i.setRow(0)},i.getData=function(e){return i.data[e]},i.getRow=function(){return m.start.row},i.setRow=function(e){e=Math.max(this.autoSelect?0:-1,Math.min(this.data.length-1,e)),m.start.row!=e&&(i.selection.clearSelection(),m.start.row=m.end.row=e||0,i.session._emit("changeBackMarker"),i.moveCursorTo(e||0,0),i.isOpen&&i._signal("select"))},i.on("changeSelection",function(){i.isOpen&&i.setRow(i.selection.lead.row),i.renderer.scrollCursorIntoView()}),i.hide=function(){this.container.style.display="none",i.anchorPos=null,i.anchor=null,i.isOpen&&(i.isOpen=!1,this._signal("hide"))},i.tryShow=function(e,n,o,r){if(!r&&i.isOpen&&i.anchorPos&&i.anchor&&i.anchorPos.top===e.top&&i.anchorPos.left===e.left&&i.anchor===o)return!0;var s=this.container,a=this.renderer.scrollBar.width||10,c=window.innerHeight-a,l=window.innerWidth-a,h=this.renderer,p=h.$maxLines*n*1.4,u={top:0,bottom:0},d=c-e.top-3*this.$borderSize-n,f=e.top-3*this.$borderSize;o||(o=f<=d||d>=p?"bottom":"top"),"top"===o?(u.bottom=e.top-this.$borderSize,u.top=u.bottom-p):"bottom"===o&&(u.top=e.top+n+this.$borderSize,u.bottom=u.top+p);var g=u.top>=0&&u.bottom<=c;if(!r&&!g)return!1;g?h.$maxPixelHeight=null:"top"===o?h.$maxPixelHeight=f:h.$maxPixelHeight=d,"top"===o?(s.style.top="",s.style.bottom=c+a-u.bottom+"px",i.isTopdown=!1):(s.style.top=u.top+"px",s.style.bottom="",i.isTopdown=!0),s.style.display="";var m=e.left;return m+s.offsetWidth>l&&(m=l-s.offsetWidth),s.style.left=m+"px",s.style.right="",i.isOpen||(i.isOpen=!0,this._signal("show"),t=null),i.anchorPos=e,i.anchor=o,!0},i.show=function(e,t,n){this.tryShow(e,t,n?"bottom":void 0,!0)},i.goTo=function(e){var t=this.getRow(),n=this.session.getLength()-1;switch(e){case"up":t=t<=0?n:t-1;break;case"down":t=t>=n?-1:t+1;break;case"start":t=0;break;case"end":t=n}this.setRow(t)},i.getTextLeftOffset=function(){return this.$borderSize+this.renderer.$padding+this.$imageSize},i.$imageSize=0,i.$borderSize=1,i},t.$singleLineEditor=g,t.getAriaId=p}),ace.define("ace/autocomplete/inline_screenreader",["require","exports","module"],function(e,t,n){"use strict";var i=function(){function e(e){this.editor=e,this.screenReaderDiv=document.createElement("div"),this.screenReaderDiv.classList.add("ace_screenreader-only"),this.editor.container.appendChild(this.screenReaderDiv)}return e.prototype.setScreenReaderContent=function(e){for(!this.popup&&this.editor.completer&&this.editor.completer.popup&&(this.popup=this.editor.completer.popup,this.popup.renderer.on("afterRender",(function(){var e=this.popup.getRow(),t=this.popup.renderer.$textLayer,n=t.element.childNodes[e-t.config.firstRow];if(n){for(var i="doc-tooltip ",o=0;o<this._lines.length;o++)i+="ace-inline-screenreader-line-".concat(o," ");n.setAttribute("aria-describedby",i)}}).bind(this)));this.screenReaderDiv.firstChild;)this.screenReaderDiv.removeChild(this.screenReaderDiv.firstChild);this._lines=e.split(/\r\n|\r|\n/);var t=this.createCodeBlock();this.screenReaderDiv.appendChild(t)},e.prototype.destroy=function(){this.screenReaderDiv.remove()},e.prototype.createCodeBlock=function(){var e=document.createElement("pre");e.setAttribute("id","ace-inline-screenreader");for(var t=0;t<this._lines.length;t++){var n=document.createElement("code");n.setAttribute("id","ace-inline-screenreader-line-".concat(t));var i=document.createTextNode(this._lines[t]);n.appendChild(i),e.appendChild(n)}return e},e}();t.AceInlineScreenReader=i}),ace.define("ace/autocomplete/inline",["require","exports","module","ace/snippets","ace/autocomplete/inline_screenreader"],function(e,t,n){"use strict";var i=e("../snippets").snippetManager,o=e("./inline_screenreader").AceInlineScreenReader,r=function(){function e(){this.editor=null}return e.prototype.show=function(e,t,n){if(n=n||"",e&&this.editor&&this.editor!==e&&(this.hide(),this.editor=null,this.inlineScreenReader=null),!e||!t)return!1;this.inlineScreenReader||(this.inlineScreenReader=new o(e));var r=t.snippet?i.getDisplayTextForSnippet(e,t.snippet):t.value;return!!(!t.hideInlinePreview&&r&&r.startsWith(n))&&(this.editor=e,this.inlineScreenReader.setScreenReaderContent(r),""===(r=r.slice(n.length))?e.removeGhostText():e.setGhostText(r),!0)},e.prototype.isOpen=function(){return!!this.editor&&!!this.editor.renderer.$ghostText},e.prototype.hide=function(){return!!this.editor&&(this.editor.removeGhostText(),!0)},e.prototype.destroy=function(){this.hide(),this.editor=null,this.inlineScreenReader&&(this.inlineScreenReader.destroy(),this.inlineScreenReader=null)},e}();t.AceInline=r}),ace.define("ace/autocomplete/util",["require","exports","module"],function(e,t,n){"use strict";t.parForEach=function(e,t,n){var i=0,o=e.length;0===o&&n();for(var r=0;r<o;r++)t(e[r],function(e,t){++i===o&&n(e,t)})};var i=/[a-zA-Z_0-9\$\-\u00A2-\u2000\u2070-\uFFFF]/;t.retrievePrecedingIdentifier=function(e,t,n){n=n||i;for(var o=[],r=t-1;r>=0&&n.test(e[r]);r--)o.push(e[r]);return o.reverse().join("")},t.retrieveFollowingIdentifier=function(e,t,n){n=n||i;for(var o=[],r=t;r<e.length&&n.test(e[r]);r++)o.push(e[r]);return o},t.getCompletionPrefix=function(e){var t,n=e.getCursorPosition(),i=e.session.getLine(n.row);return e.completers.forEach((function(e){e.identifierRegexps&&e.identifierRegexps.forEach((function(e){!t&&e&&(t=this.retrievePrecedingIdentifier(i,n.column,e))}).bind(this))}).bind(this)),t||this.retrievePrecedingIdentifier(i,n.column)},t.triggerAutocomplete=function(e,t){var t=null==t?e.session.getPrecedingCharacter():t;return e.completers.some(function(e){if(e.triggerCharacters&&Array.isArray(e.triggerCharacters))return e.triggerCharacters.includes(t)})}}),ace.define("ace/autocomplete",["require","exports","module","ace/keyboard/hash_handler","ace/autocomplete/popup","ace/autocomplete/inline","ace/autocomplete/popup","ace/autocomplete/util","ace/lib/lang","ace/lib/dom","ace/snippets","ace/config","ace/lib/event","ace/lib/scroll"],function(e,t,n){"use strict";var i=e("./keyboard/hash_handler").HashHandler,o=e("./autocomplete/popup").AcePopup,r=e("./autocomplete/inline").AceInline,s=e("./autocomplete/popup").getAriaId,a=e("./autocomplete/util"),c=e("./lib/lang"),l=e("./lib/dom"),h=e("./snippets").snippetManager,p=e("./config"),u=e("./lib/event"),d=e("./lib/scroll").preventParentScroll,f=function(e,t){t.completer&&t.completer.destroy()},g=function(){function e(){this.autoInsert=!1,this.autoSelect=!0,this.autoShown=!1,this.exactMatch=!1,this.inlineEnabled=!1,this.keyboardHandler=new i,this.keyboardHandler.bindKeys(this.commands),this.parentNode=null,this.setSelectOnHover=!1,this.hasSeen=new Set,this.showLoadingState=!1,this.stickySelectionDelay=500,this.blurListener=this.blurListener.bind(this),this.changeListener=this.changeListener.bind(this),this.mousedownListener=this.mousedownListener.bind(this),this.mousewheelListener=this.mousewheelListener.bind(this),this.onLayoutChange=this.onLayoutChange.bind(this),this.changeTimer=c.delayedCall((function(){this.updateCompletions(!0)}).bind(this)),this.tooltipTimer=c.delayedCall(this.updateDocTooltip.bind(this),50),this.popupTimer=c.delayedCall(this.$updatePopupPosition.bind(this),50),this.stickySelectionTimer=c.delayedCall((function(){this.stickySelection=!0}).bind(this),this.stickySelectionDelay),this.$firstOpenTimer=c.delayedCall((function(){var t=this.completionProvider&&this.completionProvider.initialPosition;this.autoShown||this.popup&&this.popup.isOpen||!t||0===this.editor.completers.length||(this.completions=new b(e.completionsForLoading),this.openPopup(this.editor,t.prefix,!1),this.popup.renderer.setStyle("ace_loading",!0))}).bind(this),this.stickySelectionDelay)}return Object.defineProperty(e,"completionsForLoading",{get:function(){return[{caption:p.nls("autocomplete.loading","Loading..."),value:""}]},enumerable:!1,configurable:!0}),e.prototype.$init=function(){return this.popup=new o(this.parentNode||document.body||document.documentElement),this.popup.on("click",(function(e){this.insertMatch(),e.stop()}).bind(this)),this.popup.focus=this.editor.focus.bind(this.editor),this.popup.on("show",this.$onPopupShow.bind(this)),this.popup.on("hide",this.$onHidePopup.bind(this)),this.popup.on("select",this.$onPopupChange.bind(this)),u.addListener(this.popup.container,"mouseout",this.mouseOutListener.bind(this)),this.popup.on("changeHoverMarker",this.tooltipTimer.bind(null,null)),this.popup.renderer.on("afterRender",this.$onPopupRender.bind(this)),this.popup},e.prototype.$initInline=function(){if(this.inlineEnabled&&!this.inlineRenderer)return this.inlineRenderer=new r,this.inlineRenderer},e.prototype.getPopup=function(){return this.popup||this.$init()},e.prototype.$onHidePopup=function(){this.inlineRenderer&&this.inlineRenderer.hide(),this.hideDocTooltip(),this.stickySelectionTimer.cancel(),this.popupTimer.cancel(),this.stickySelection=!1},e.prototype.$seen=function(e){!this.hasSeen.has(e)&&e&&e.completer&&e.completer.onSeen&&"function"==typeof e.completer.onSeen&&(e.completer.onSeen(this.editor,e),this.hasSeen.add(e))},e.prototype.$onPopupChange=function(e){if(this.inlineRenderer&&this.inlineEnabled){var t=e?null:this.popup.getData(this.popup.getRow());if(this.$updateGhostText(t),this.popup.isMouseOver&&this.setSelectOnHover){this.tooltipTimer.call(null,null);return}this.popupTimer.schedule(),this.tooltipTimer.schedule()}else this.popupTimer.call(null,null),this.tooltipTimer.call(null,null)},e.prototype.$updateGhostText=function(e){var t=this.base.row,n=this.base.column,i=this.editor.getCursorPosition().column,o=this.editor.session.getLine(t).slice(n,i);this.inlineRenderer.show(this.editor,e,o)?this.$seen(e):this.inlineRenderer.hide()},e.prototype.$onPopupRender=function(){var e=this.inlineRenderer&&this.inlineEnabled;if(this.completions&&this.completions.filtered&&this.completions.filtered.length>0)for(var t=this.popup.getFirstVisibleRow();t<=this.popup.getLastVisibleRow();t++){var n=this.popup.getData(t);n&&(!e||n.hideInlinePreview)&&this.$seen(n)}},e.prototype.$onPopupShow=function(e){this.$onPopupChange(e),this.stickySelection=!1,this.stickySelectionDelay>=0&&this.stickySelectionTimer.schedule(this.stickySelectionDelay)},e.prototype.observeLayoutChanges=function(){if(!this.$elements&&this.editor){window.addEventListener("resize",this.onLayoutChange,{passive:!0}),window.addEventListener("wheel",this.mousewheelListener);for(var e=this.editor.container.parentNode,t=[];e;)t.push(e),e.addEventListener("scroll",this.onLayoutChange,{passive:!0}),e=e.parentNode;this.$elements=t}},e.prototype.unObserveLayoutChanges=function(){var e=this;window.removeEventListener("resize",this.onLayoutChange,{passive:!0}),window.removeEventListener("wheel",this.mousewheelListener),this.$elements&&this.$elements.forEach(function(t){t.removeEventListener("scroll",e.onLayoutChange,{passive:!0})}),this.$elements=null},e.prototype.onLayoutChange=function(){if(!this.popup.isOpen)return this.unObserveLayoutChanges();this.$updatePopupPosition(),this.updateDocTooltip()},e.prototype.$updatePopupPosition=function(){var e=this.editor,t=e.renderer,n=t.layerConfig.lineHeight,i=t.$cursorLayer.getPixelPosition(this.base,!0);i.left-=this.popup.getTextLeftOffset();var o=e.container.getBoundingClientRect();i.top+=o.top-t.layerConfig.offset,i.left+=o.left-e.renderer.scrollLeft,i.left+=t.gutterWidth;var r={top:i.top,left:i.left};t.$ghostText&&t.$ghostTextWidget&&this.base.row===t.$ghostText.position.row&&(r.top+=t.$ghostTextWidget.el.offsetHeight);var s=e.container.getBoundingClientRect().bottom-n,a=s<r.top?{top:s,left:r.left}:r;this.popup.tryShow(a,n,"bottom")||this.popup.tryShow(i,n,"top")||this.popup.show(i,n)},e.prototype.openPopup=function(e,t,n){this.$firstOpenTimer.cancel(),this.popup||this.$init(),this.inlineEnabled&&!this.inlineRenderer&&this.$initInline(),this.popup.autoSelect=this.autoSelect,this.popup.setSelectOnHover(this.setSelectOnHover);var i,o=this.popup.getRow(),r=this.popup.data[o];this.popup.setData(this.completions.filtered,this.completions.filterText),this.editor.textInput.setAriaOptions&&this.editor.textInput.setAriaOptions({activeDescendant:s(this.popup.getRow()),inline:this.inlineEnabled}),e.keyBinding.addKeyboardHandler(this.keyboardHandler),this.stickySelection&&(i=this.popup.data.indexOf(r)),i&&-1!==i||(i=0),this.popup.setRow(this.autoSelect?i:-1),i===o&&r!==this.completions.filtered[i]&&this.$onPopupChange();var a=this.inlineRenderer&&this.inlineEnabled;if(i===o&&a){var c=this.popup.getData(this.popup.getRow());this.$updateGhostText(c)}!n&&(this.popup.setTheme(e.getTheme()),this.popup.setFontSize(e.getFontSize()),this.$updatePopupPosition(),this.tooltipNode&&this.updateDocTooltip()),this.changeTimer.cancel(),this.observeLayoutChanges()},e.prototype.detach=function(){this.editor&&(this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.off("changeSelection",this.changeListener),this.editor.off("blur",this.blurListener),this.editor.off("mousedown",this.mousedownListener),this.editor.off("mousewheel",this.mousewheelListener)),this.$firstOpenTimer.cancel(),this.changeTimer.cancel(),this.hideDocTooltip(),this.completionProvider&&this.completionProvider.detach(),this.popup&&this.popup.isOpen&&this.popup.hide(),this.popup&&this.popup.renderer&&this.popup.renderer.off("afterRender",this.$onPopupRender),this.base&&this.base.detach(),this.activated=!1,this.completionProvider=this.completions=this.base=null,this.unObserveLayoutChanges()},e.prototype.changeListener=function(e){var t=this.editor.selection.lead;(t.row!=this.base.row||t.column<this.base.column)&&this.detach(),this.activated?this.changeTimer.schedule():this.detach()},e.prototype.blurListener=function(e){var t=document.activeElement,n=this.editor.textInput.getElement(),i=e.relatedTarget&&this.tooltipNode&&this.tooltipNode.contains(e.relatedTarget),o=this.popup&&this.popup.container;t==n||t.parentNode==o||i||t==this.tooltipNode||e.relatedTarget==n||this.detach()},e.prototype.mousedownListener=function(e){this.detach()},e.prototype.mousewheelListener=function(e){this.popup&&!this.popup.isMouseOver&&this.detach()},e.prototype.mouseOutListener=function(e){this.popup.isOpen&&this.$updatePopupPosition()},e.prototype.goTo=function(e){this.popup.goTo(e)},e.prototype.insertMatch=function(e,t){if(e||(e=this.popup.getData(this.popup.getRow())),!e)return!1;if(""===e.value)return this.detach();var n=this.completions,i=this.getCompletionProvider().insertMatch(this.editor,e,n.filterText,t);return this.completions==n&&this.detach(),i},e.prototype.showPopup=function(e,t){this.editor&&this.detach(),this.activated=!0,this.editor=e,e.completer!=this&&(e.completer&&e.completer.detach(),e.completer=this),e.on("changeSelection",this.changeListener),e.on("blur",this.blurListener),e.on("mousedown",this.mousedownListener),e.on("mousewheel",this.mousewheelListener),this.updateCompletions(!1,t)},e.prototype.getCompletionProvider=function(e){return this.completionProvider||(this.completionProvider=new m(e)),this.completionProvider},e.prototype.gatherCompletions=function(e,t){return this.getCompletionProvider().gatherCompletions(e,t)},e.prototype.updateCompletions=function(t,n){if(t&&this.base&&this.completions){var i=this.editor.getCursorPosition(),o=this.editor.session.getTextRange({start:this.base,end:i});return o==this.completions.filterText?void 0:(this.completions.setFilter(o),this.completions.filtered.length&&(1!=this.completions.filtered.length||this.completions.filtered[0].value!=o||this.completions.filtered[0].snippet))?void this.openPopup(this.editor,o,t):this.detach()}if(n&&n.matches){var i=this.editor.getSelectionRange().start;return this.base=this.editor.session.doc.createAnchor(i.row,i.column),this.base.$insertRight=!0,this.completions=new b(n.matches),this.getCompletionProvider().completions=this.completions,this.openPopup(this.editor,"",t)}var r=this.editor.getSession(),i=this.editor.getCursorPosition(),o=a.getCompletionPrefix(this.editor);this.base=r.doc.createAnchor(i.row,i.column-o.length),this.base.$insertRight=!0;var s={exactMatch:this.exactMatch,ignoreCaption:this.ignoreCaption};this.getCompletionProvider({prefix:o,pos:i}).provideCompletions(this.editor,s,(function(n,i,o){var r=i.filtered,s=a.getCompletionPrefix(this.editor);if(this.$firstOpenTimer.cancel(),o){if(!r.length){var c=!this.autoShown&&this.emptyMessage;if("function"==typeof c&&(c=this.emptyMessage(s)),c){var l=[{caption:c,value:""}];this.completions=new b(l),this.openPopup(this.editor,s,t),this.popup.renderer.setStyle("ace_loading",!1),this.popup.renderer.setStyle("ace_empty-message",!0);return}return this.detach()}if(1==r.length&&r[0].value==s&&!r[0].snippet)return this.detach();if(this.autoInsert&&!this.autoShown&&1==r.length)return this.insertMatch(r[0])}this.completions=!o&&this.showLoadingState?new b(e.completionsForLoading.concat(r),i.filterText):i,this.openPopup(this.editor,s,t),this.popup.renderer.setStyle("ace_empty-message",!1),this.popup.renderer.setStyle("ace_loading",!o)}).bind(this)),!this.showLoadingState||this.autoShown||this.popup&&this.popup.isOpen||this.$firstOpenTimer.delay(this.stickySelectionDelay/2)},e.prototype.cancelContextMenu=function(){this.editor.$mouseHandler.cancelContextMenu()},e.prototype.updateDocTooltip=function(){var e=this.popup,t=this.completions&&this.completions.filtered,n=t&&(t[e.getHoveredRow()]||t[e.getRow()]),i=null;if(!n||!this.editor||!this.popup.isOpen)return this.hideDocTooltip();for(var o=this.editor.completers.length,r=0;r<o;r++){var s=this.editor.completers[r];if(s.getDocTooltip&&n.completerId===s.id){i=s.getDocTooltip(n);break}}if(i||"string"==typeof n||(i=n),"string"==typeof i&&(i={docText:i}),!i||!(i.docHTML||i.docText))return this.hideDocTooltip();this.showDocTooltip(i)},e.prototype.showDocTooltip=function(e){this.tooltipNode||(this.tooltipNode=l.createElement("div"),this.tooltipNode.style.margin="0",this.tooltipNode.style.pointerEvents="auto",this.tooltipNode.style.overscrollBehavior="contain",this.tooltipNode.tabIndex=-1,this.tooltipNode.onblur=this.blurListener.bind(this),this.tooltipNode.onclick=this.onTooltipClick.bind(this),this.tooltipNode.id="doc-tooltip",this.tooltipNode.setAttribute("role","tooltip"),this.tooltipNode.addEventListener("wheel",d));var t=this.editor.renderer.theme;this.tooltipNode.className="ace_tooltip ace_doc-tooltip "+(t.isDark?"ace_dark ":"")+(t.cssClass||"");var n=this.tooltipNode;e.docHTML?n.innerHTML=e.docHTML:e.docText&&(n.textContent=e.docText),n.parentNode||this.popup.container.appendChild(this.tooltipNode);var i=this.popup,o=i.container.getBoundingClientRect(),r=i.renderer.scrollBar.width||10,s=o.left,a=[Math.min((window.innerWidth-o.right-r)/400,1),Math.min(s/400,1),Math.min((i.isTopdown?o.top:window.innerHeight-r-o.bottom)/300*.9)],c=Math.max.apply(Math,a),h=n.style;h.display="block",c==a[0]?(h.left=o.right+1+"px",h.right="",h.maxWidth=400*c+"px",h.top=o.top+"px",h.bottom="",h.maxHeight=Math.min(window.innerHeight-r-o.top,300)+"px"):c==a[1]?(h.right=window.innerWidth-o.left+"px",h.left="",h.maxWidth=400*c+"px",h.top=o.top+"px",h.bottom="",h.maxHeight=Math.min(window.innerHeight-r-o.top,300)+"px"):c==a[2]&&(h.left=window.innerWidth-o.left+"px",h.maxWidth=Math.min(400,window.innerWidth)+"px",i.isTopdown?(h.top=o.bottom+"px",h.left=o.left+"px",h.right="",h.bottom="",h.maxHeight=Math.min(window.innerHeight-r-o.bottom,300)+"px"):(h.top=i.container.offsetTop-n.offsetHeight+"px",h.left=o.left+"px",h.right="",h.bottom="",h.maxHeight=Math.min(i.container.offsetTop,300)+"px"))},e.prototype.hideDocTooltip=function(){if(this.tooltipTimer.cancel(),this.tooltipNode){var e=this.tooltipNode;this.editor.isFocused()||document.activeElement!=e||this.editor.focus(),this.tooltipNode=null,e.parentNode&&e.parentNode.removeChild(e)}},e.prototype.onTooltipClick=function(e){for(var t=e.target;t&&t!=this.tooltipNode;){if("A"==t.nodeName&&t.href){t.rel="noreferrer",t.target="_blank";break}t=t.parentNode}},e.prototype.destroy=function(){if(this.detach(),this.popup){this.popup.destroy();var e=this.popup.container;e&&e.parentNode&&e.parentNode.removeChild(e)}this.editor&&this.editor.completer==this&&(this.editor.off("destroy",f),this.editor.completer=null),this.inlineRenderer=this.popup=this.editor=null},e.for=function(t){return t.completer instanceof e||(t.completer&&(t.completer.destroy(),t.completer=null),p.get("sharedPopups")?(e.$sharedInstance||(e.$sharedInstance=new e),t.completer=e.$sharedInstance):(t.completer=new e,t.once("destroy",f))),t.completer},e}();g.prototype.commands={Up:function(e){e.completer.goTo("up")},Down:function(e){e.completer.goTo("down")},"Ctrl-Up|Ctrl-Home":function(e){e.completer.goTo("start")},"Ctrl-Down|Ctrl-End":function(e){e.completer.goTo("end")},Esc:function(e){e.completer.detach()},Return:function(e){return e.completer.insertMatch()},"Shift-Return":function(e){e.completer.insertMatch(null,{deleteSuffix:!0})},Tab:function(e){var t=e.completer.insertMatch();if(t||e.tabstopManager)return t;e.completer.goTo("down")},Backspace:function(e){e.execCommand("backspace"),!a.getCompletionPrefix(e)&&e.completer&&e.completer.detach()},PageUp:function(e){e.completer.popup.gotoPageUp()},PageDown:function(e){e.completer.popup.gotoPageDown()}},g.startCommand={name:"startAutocomplete",exec:function(e,t){var n=g.for(e);n.autoInsert=!1,n.autoSelect=!0,n.autoShown=!1,n.showPopup(e,t),n.cancelContextMenu()},bindKey:"Ctrl-Space|Ctrl-Shift-Space|Alt-Space"};var m=function(){function e(e){this.initialPosition=e,this.active=!0}return e.prototype.insertByIndex=function(e,t,n){return!!this.completions&&!!this.completions.filtered&&this.insertMatch(e,this.completions.filtered[t],n)},e.prototype.insertMatch=function(e,t,n){if(!t)return!1;if(e.startOperation({command:{name:"insertMatch"}}),t.completer&&t.completer.insertMatch)t.completer.insertMatch(e,t);else{if(!this.completions)return!1;var i=this.completions.filterText.length,o=0;if(t.range&&t.range.start.row===t.range.end.row&&(i-=this.initialPosition.prefix.length,i+=this.initialPosition.pos.column-t.range.start.column,o+=t.range.end.column-this.initialPosition.pos.column),i||o){r=e.selection.getAllRanges?e.selection.getAllRanges():[e.getSelectionRange()];for(var r,s,a=0;s=r[a];a++)s.start.column-=i,s.end.column+=o,e.session.remove(s)}t.snippet?h.insertSnippet(e,t.snippet):this.$insertString(e,t),t.completer&&t.completer.onInsert&&"function"==typeof t.completer.onInsert&&t.completer.onInsert(e,t),t.command&&"startAutocomplete"===t.command&&e.execCommand(t.command)}return e.endOperation(),!0},e.prototype.$insertString=function(e,t){var n=t.value||t;e.execCommand("insertstring",n)},e.prototype.gatherCompletions=function(e,t){var n=e.getSession(),i=e.getCursorPosition(),o=a.getCompletionPrefix(e),r=[];this.completers=e.completers;var s=e.completers.length;return e.completers.forEach(function(c,l){c.getCompletions(e,n,i,o,function(n,i){c.hideInlinePreview&&(i=i.map(function(e){return Object.assign(e,{hideInlinePreview:c.hideInlinePreview})})),!n&&i&&(r=r.concat(i)),t(null,{prefix:a.getCompletionPrefix(e),matches:r,finished:0==--s})})}),!0},e.prototype.provideCompletions=function(e,t,n){var i=(function(e){var i=e.prefix,o=e.matches;this.completions=new b(o),t.exactMatch&&(this.completions.exactMatch=!0),t.ignoreCaption&&(this.completions.ignoreCaption=!0),this.completions.setFilter(i),(e.finished||this.completions.filtered.length)&&n(null,this.completions,e.finished)}).bind(this),o=!0,r=null;if(this.gatherCompletions(e,(function(e,t){if(this.active&&(e&&(n(e,[],!0),this.detach()),0===t.prefix.indexOf(t.prefix))){if(o){r=t;return}i(t)}}).bind(this)),o=!1,r){var s=r;r=null,i(s)}},e.prototype.detach=function(){this.active=!1,this.completers&&this.completers.forEach(function(e){"function"==typeof e.cancel&&e.cancel()})},e}(),b=function(){function e(e,t){this.all=e,this.filtered=e,this.filterText=t||"",this.exactMatch=!1,this.ignoreCaption=!1}return e.prototype.setFilter=function(e){if(e.length>this.filterText&&0===e.lastIndexOf(this.filterText,0))var t=this.filtered;else var t=this.all;this.filterText=e;var n=null;t=(t=(t=this.filterCompletions(t,this.filterText)).sort(function(e,t){return t.exactMatch-e.exactMatch||t.$score-e.$score||(e.caption||e.value).localeCompare(t.caption||t.value)})).filter(function(e){var t=e.snippet||e.caption||e.value;return t!==n&&(n=t,!0)}),this.filtered=t},e.prototype.filterCompletions=function(e,t){var n=[],i=t.toUpperCase(),o=t.toLowerCase();e:for(var r,s=0;r=e[s];s++){if(r.skipFilter){r.$score=r.score,n.push(r);continue}var a,c,l=!this.ignoreCaption&&r.caption||r.value||r.snippet;if(l){var h=-1,p=0,u=0;if(this.exactMatch){if(t!==l.substr(0,t.length))continue}else{var d=l.toLowerCase().indexOf(o);if(d>-1)u=d;else for(var f=0;f<t.length;f++){var g=l.indexOf(o[f],h+1),m=l.indexOf(i[f],h+1);if((a=g>=0&&(m<0||g<m)?g:m)<0)continue e;(c=a-h-1)>0&&(-1===h&&(u+=10),u+=c,p|=1<<f),h=a}}r.matchMask=p,r.exactMatch=u?0:1,r.$score=(r.score||0)-u,n.push(r)}}return n},e}();t.Autocomplete=g,t.CompletionProvider=m,t.FilteredList=b}),ace.define("ace/autocomplete/text_completer",["require","exports","module","ace/range"],function(e,t,n){var i=e("../range").Range,o=/[^a-zA-Z_0-9\$\-\u00C0-\u1FFF\u2C00-\uD7FF\w]+/;t.getCompletions=function(e,t,n,r,s){var a,c,l,h,p=(a=t.getTextRange(i.fromPoints({row:0,column:0},n)).split(o).length-1,c=t.getValue().split(o),l=Object.create(null),h=c[a],c.forEach(function(e,t){if(e&&e!==h){var n=Math.abs(a-t),i=c.length-n;l[e]?l[e]=Math.max(i,l[e]):l[e]=i}}),l);s(null,Object.keys(p).map(function(e){return{caption:e,value:e,score:p[e],meta:"local"}}))}}),ace.define("ace/ext/language_tools",["require","exports","module","ace/snippets","ace/autocomplete","ace/config","ace/lib/lang","ace/autocomplete/util","ace/autocomplete/text_completer","ace/editor","ace/config"],function(e,t,n){"use strict";var i,o=e("../snippets").snippetManager,r=e("../autocomplete").Autocomplete,s=e("../config"),a=e("../lib/lang"),c=e("../autocomplete/util"),l=e("../autocomplete/text_completer"),h={getCompletions:function(e,t,n,i,o){if(t.$mode.completer)return t.$mode.completer.getCompletions(e,t,n,i,o);var r=e.session.getState(n.row),s=t.$mode.getCompletions(r,t,n,i);o(null,s=s.map(function(e){return e.completerId=h.id,e}))},id:"keywordCompleter"},p=function(e){var t={};return e.replace(/\${(\d+)(:(.*?))?}/g,function(e,n,i,o){return t[n]=o||""}).replace(/\$(\d+?)/g,function(e,n){return t[n]})},u={getCompletions:function(e,t,n,i,r){var s=[],a=t.getTokenAt(n.row,n.column);a&&a.type.match(/(tag-name|tag-open|tag-whitespace|attribute-name|attribute-value)\.xml$/)?s.push("html-tag"):s=o.getActiveScopes(e);var c=o.snippetMap,l=[];s.forEach(function(e){for(var t=c[e]||[],n=t.length;n--;){var i=t[n],o=i.name||i.tabTrigger;o&&l.push({caption:o,snippet:i.content,meta:i.tabTrigger&&!i.name?i.tabTrigger+"⇥ ":"snippet",completerId:u.id})}},this),r(null,l)},getDocTooltip:function(e){e.snippet&&!e.docHTML&&(e.docHTML=["<b>",a.escapeHTML(e.caption),"</b>","<hr></hr>",a.escapeHTML(p(e.snippet))].join(""))},id:"snippetCompleter"},d=[u,l,h];t.setCompleters=function(e){d.length=0,e&&d.push.apply(d,e)},t.addCompleter=function(e){d.push(e)},t.textCompleter=l,t.keyWordCompleter=h,t.snippetCompleter=u;var f={name:"expandSnippet",exec:function(e){return o.expandWithTab(e)},bindKey:"Tab"},g=function(e,t){m(t.session.$mode)},m=function(e){"string"==typeof e&&(e=s.$modes[e]),e&&(o.files||(o.files={}),b(e.$id,e.snippetFileId),e.modes&&e.modes.forEach(m))},b=function(e,t){t&&e&&!o.files[e]&&(o.files[e]={},s.loadModule(t,function(t){t&&(o.files[e]=t,!t.snippets&&t.snippetText&&(t.snippets=o.parseSnippetFile(t.snippetText)),o.register(t.snippets||[],t.scope),t.includeScopes&&(o.snippetMap[t.scope].includeScopes=t.includeScopes,t.includeScopes.forEach(function(e){m("ace/mode/"+e)})))}))},v=function(e){var t=e.editor,n=t.completer&&t.completer.activated;if("backspace"===e.command.name)n&&!c.getCompletionPrefix(t)&&t.completer.detach();else if("insertstring"===e.command.name&&!n){i=e;var o=e.editor.$liveAutocompletionDelay;o?y.delay(o):_(e)}},y=a.delayedCall(function(){_(i)},0),_=function(e){var t=e.editor,n=c.getCompletionPrefix(t),i=e.args,o=c.triggerAutocomplete(t,i);if(n&&n.length>=t.$liveAutocompletionThreshold||o){var s=r.for(t);s.autoShown=!0,s.showPopup(t)}},w=e("../editor").Editor;e("../config").defineOptions(w.prototype,"editor",{enableBasicAutocompletion:{set:function(e){e?(this.completers||(this.completers=Array.isArray(e)?e:d),this.commands.addCommand(r.startCommand)):this.commands.removeCommand(r.startCommand)},value:!1},enableLiveAutocompletion:{set:function(e){e?(this.completers||(this.completers=Array.isArray(e)?e:d),this.commands.on("afterExec",v)):this.commands.off("afterExec",v)},value:!1},liveAutocompletionDelay:{initialValue:0},liveAutocompletionThreshold:{initialValue:0},enableSnippets:{set:function(e){e?(this.commands.addCommand(f),this.on("changeMode",g),g(null,this)):(this.commands.removeCommand(f),this.off("changeMode",g))},value:!1}})}),ace.require(["ace/ext/language_tools"],function(t){e&&(e.exports=t)})},93083:function(e,t,n){e=n.nmd(e),ace.define("ace/split",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/editor","ace/virtual_renderer","ace/edit_session"],function(e,t,n){"use strict";var i,o=e("./lib/oop");e("./lib/lang");var r=e("./lib/event_emitter").EventEmitter,s=e("./editor").Editor,a=e("./virtual_renderer").VirtualRenderer,c=e("./edit_session").EditSession;(function(){o.implement(this,r),this.$createEditor=function(){var e=document.createElement("div");e.className=this.$editorCSS,e.style.cssText="position: absolute; top:0px; bottom:0px",this.$container.appendChild(e);var t=new s(new a(e,this.$theme));return t.on("focus",(function(){this._emit("focus",t)}).bind(this)),this.$editors.push(t),t.setFontSize(this.$fontSize),t},this.setSplits=function(e){var t;if(e<1)throw"The number of splits have to be > 0!";if(e!=this.$splits){if(e>this.$splits){for(;this.$splits<this.$editors.length&&this.$splits<e;)t=this.$editors[this.$splits],this.$container.appendChild(t.container),t.setFontSize(this.$fontSize),this.$splits++;for(;this.$splits<e;)this.$createEditor(),this.$splits++}else for(;this.$splits>e;)t=this.$editors[this.$splits-1],this.$container.removeChild(t.container),this.$splits--;this.resize()}},this.getSplits=function(){return this.$splits},this.getEditor=function(e){return this.$editors[e]},this.getCurrentEditor=function(){return this.$cEditor},this.focus=function(){this.$cEditor.focus()},this.blur=function(){this.$cEditor.blur()},this.setTheme=function(e){this.$editors.forEach(function(t){t.setTheme(e)})},this.setKeyboardHandler=function(e){this.$editors.forEach(function(t){t.setKeyboardHandler(e)})},this.forEach=function(e,t){this.$editors.forEach(e,t)},this.$fontSize="",this.setFontSize=function(e){this.$fontSize=e,this.forEach(function(t){t.setFontSize(e)})},this.$cloneSession=function(e){var t=new c(e.getDocument(),e.getMode()),n=e.getUndoManager();return t.setUndoManager(n),t.setTabSize(e.getTabSize()),t.setUseSoftTabs(e.getUseSoftTabs()),t.setOverwrite(e.getOverwrite()),t.setBreakpoints(e.getBreakpoints()),t.setUseWrapMode(e.getUseWrapMode()),t.setUseWorker(e.getUseWorker()),t.setWrapLimitRange(e.$wrapLimitRange.min,e.$wrapLimitRange.max),t.$foldData=e.$cloneFoldData(),t},this.setSession=function(e,t){var n;return n=null==t?this.$cEditor:this.$editors[t],this.$editors.some(function(t){return t.session===e})&&(e=this.$cloneSession(e)),n.setSession(e),e},this.getOrientation=function(){return this.$orientation},this.setOrientation=function(e){this.$orientation!=e&&(this.$orientation=e,this.resize())},this.resize=function(){var e,t=this.$container.clientWidth,n=this.$container.clientHeight;if(this.$orientation==this.BESIDE)for(var i=t/this.$splits,o=0;o<this.$splits;o++)(e=this.$editors[o]).container.style.width=i+"px",e.container.style.top="0px",e.container.style.left=o*i+"px",e.container.style.height=n+"px",e.resize();else for(var r=n/this.$splits,o=0;o<this.$splits;o++)(e=this.$editors[o]).container.style.width=t+"px",e.container.style.top=o*r+"px",e.container.style.left="0px",e.container.style.height=r+"px",e.resize()}}).call((i=function(e,t,n){this.BELOW=1,this.BESIDE=0,this.$container=e,this.$theme=t,this.$splits=0,this.$editorCSS="",this.$editors=[],this.$orientation=this.BESIDE,this.setSplits(n||1),this.$cEditor=this.$editors[0],this.on("focus",(function(e){this.$cEditor=e}).bind(this))}).prototype),t.Split=i}),ace.define("ace/ext/split",["require","exports","module","ace/split"],function(e,t,n){"use strict";n.exports=e("../split")}),ace.require(["ace/ext/split"],function(t){e&&(e.exports=t)})},90252:function(e,t,n){e=n.nmd(e),ace.define("ace/mode/json_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var i=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(){this.$rules={start:[{token:"variable",regex:'["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]\\s*(?=:)'},{token:"string",regex:'"',next:"string"},{token:"constant.numeric",regex:"0[xX][0-9a-fA-F]+\\b"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?\\b"},{token:"constant.language.boolean",regex:"(?:true|false)\\b"},{token:"text",regex:"['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']"},{token:"comment",regex:"\\/\\/.*$"},{token:"comment.start",regex:"\\/\\*",next:"comment"},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"punctuation.operator",regex:/[,]/},{token:"text",regex:"\\s+"}],string:[{token:"constant.language.escape",regex:/\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|["\\\/bfnrt])/},{token:"string",regex:'"|$',next:"start"},{defaultToken:"string"}],comment:[{token:"comment.end",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}]}};i.inherits(r,o),t.JsonHighlightRules=r}),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],function(e,t,n){"use strict";var i=e("../range").Range,o=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var o=n[1].length,r=e.findMatchingBracket({row:t,column:o});if(!r||r.row==t)return 0;var s=this.$getIndent(e.getLine(r.row));e.replace(new i(t,0,t,o-1),s)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(o.prototype),t.MatchingBraceOutdent=o}),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],function(e,t,n){"use strict";var i=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,s=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};i.inherits(s,r),(function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var i=e.getLine(n);if(this.singleLineBlockCommentRe.test(i)&&!this.startRegionRe.test(i)&&!this.tripleStarBlockCommentRe.test(i))return"";var o=this._getFoldWidgetBase(e,t,n);return!o&&this.startRegionRe.test(i)?"start":o},this.getFoldWidgetRange=function(e,t,n,i){var o=e.getLine(n);if(this.startRegionRe.test(o))return this.getCommentRegionBlock(e,o,n);var r=o.match(this.foldingStartMarker);if(r){var s=r.index;if(r[1])return this.openingBracketBlock(e,r[1],n,s);var a=e.getCommentFoldRange(n,s+r[0].length,1);return a&&!a.isMultiLine()&&(i?a=this.getSectionRange(e,n):"all"!=t&&(a=null)),a}if("markbegin"!==t){var r=o.match(this.foldingStopMarker);if(r){var s=r.index+r[0].length;return r[1]?this.closingBracketBlock(e,r[1],n,s):e.getCommentFoldRange(n,s,-1)}}},this.getSectionRange=function(e,t){for(var n=e.getLine(t),i=n.search(/\S/),r=t,s=n.length,a=t+=1,c=e.getLength();++t<c;){var l=(n=e.getLine(t)).search(/\S/);if(-1!==l){if(i>l)break;var h=this.getFoldWidgetRange(e,"all",t);if(h){if(h.start.row<=r)break;if(h.isMultiLine())t=h.end.row;else if(i==l)break}a=t}}return new o(r,s,a,e.getLine(a).length)},this.getCommentRegionBlock=function(e,t,n){for(var i=t.search(/\s*$/),r=e.getLength(),s=n,a=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,c=1;++n<r;){t=e.getLine(n);var l=a.exec(t);if(l&&(l[1]?c--:c++,!c))break}var h=n;if(h>s)return new o(s,i,h,t.length)}}).call(s.prototype)}),ace.define("ace/mode/json",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/json_highlight_rules","ace/mode/matching_brace_outdent","ace/mode/folding/cstyle","ace/worker/worker_client"],function(e,t,n){"use strict";var i=e("../lib/oop"),o=e("./text").Mode,r=e("./json_highlight_rules").JsonHighlightRules,s=e("./matching_brace_outdent").MatchingBraceOutdent,a=e("./folding/cstyle").FoldMode,c=e("../worker/worker_client").WorkerClient,l=function(){this.HighlightRules=r,this.$outdent=new s,this.$behaviour=this.$defaultBehaviour,this.foldingRules=new a};i.inherits(l,o),(function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.getNextLineIndent=function(e,t,n){var i=this.$getIndent(t);return"start"==e&&t.match(/^.*[\{\(\[]\s*$/)&&(i+=n),i},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.createWorker=function(e){var t=new c(["ace"],"ace/mode/json_worker","JsonWorker");return t.attachToDocument(e.getDocument()),t.on("annotate",function(t){e.setAnnotations(t.data)}),t.on("terminate",function(){e.clearAnnotations()}),t},this.$id="ace/mode/json"}).call(l.prototype),t.Mode=l}),ace.require(["ace/mode/json"],function(t){e&&(e.exports=t)})},89899:function(e,t,n){e=n.nmd(e),ace.define("ace/mode/sql_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var i=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(){var e=this.createKeywordMapper({"support.function":"avg|count|first|last|max|min|sum|ucase|lcase|mid|len|round|rank|now|format|coalesce|ifnull|isnull|nvl",keyword:"select|insert|update|delete|from|where|and|or|group|by|order|limit|offset|having|as|case|when|then|else|end|type|left|right|join|on|outer|desc|asc|union|create|table|primary|key|if|foreign|not|references|default|null|inner|cross|natural|database|drop|grant|distinct|is|in|all|alter|any|array|at|authorization|between|both|cast|check|collate|column|commit|constraint|cube|current|current_date|current_time|current_timestamp|current_user|describe|escape|except|exists|external|extract|fetch|filter|for|full|function|global|grouping|intersect|interval|into|leading|like|local|no|of|only|out|overlaps|partition|position|range|revoke|rollback|rollup|row|rows|session_user|set|some|start|tablesample|time|to|trailing|truncate|unique|unknown|user|using|values|window|with","constant.language":"true|false","storage.type":"int|numeric|decimal|date|varchar|char|bigint|float|double|bit|binary|text|set|timestamp|money|real|number|integer|string"},"identifier",!0);this.$rules={start:[{token:"comment",regex:"--.*$"},{token:"comment",start:"/\\*",end:"\\*/"},{token:"string",regex:'".*?"'},{token:"string",regex:"'.*?'"},{token:"string",regex:"`.*?`"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?\\b"},{token:e,regex:"[a-zA-Z_$][a-zA-Z0-9_$]*\\b"},{token:"keyword.operator",regex:"\\+|\\-|\\/|\\/\\/|%|<@>|@>|<@|&|\\^|~|<|>|<=|=>|==|!=|<>|="},{token:"paren.lparen",regex:"[\\(]"},{token:"paren.rparen",regex:"[\\)]"},{token:"text",regex:"\\s+"}]},this.normalizeRules()};i.inherits(r,o),t.SqlHighlightRules=r}),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],function(e,t,n){"use strict";var i=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,s=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};i.inherits(s,r),(function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var i=e.getLine(n);if(this.singleLineBlockCommentRe.test(i)&&!this.startRegionRe.test(i)&&!this.tripleStarBlockCommentRe.test(i))return"";var o=this._getFoldWidgetBase(e,t,n);return!o&&this.startRegionRe.test(i)?"start":o},this.getFoldWidgetRange=function(e,t,n,i){var o=e.getLine(n);if(this.startRegionRe.test(o))return this.getCommentRegionBlock(e,o,n);var r=o.match(this.foldingStartMarker);if(r){var s=r.index;if(r[1])return this.openingBracketBlock(e,r[1],n,s);var a=e.getCommentFoldRange(n,s+r[0].length,1);return a&&!a.isMultiLine()&&(i?a=this.getSectionRange(e,n):"all"!=t&&(a=null)),a}if("markbegin"!==t){var r=o.match(this.foldingStopMarker);if(r){var s=r.index+r[0].length;return r[1]?this.closingBracketBlock(e,r[1],n,s):e.getCommentFoldRange(n,s,-1)}}},this.getSectionRange=function(e,t){for(var n=e.getLine(t),i=n.search(/\S/),r=t,s=n.length,a=t+=1,c=e.getLength();++t<c;){var l=(n=e.getLine(t)).search(/\S/);if(-1!==l){if(i>l)break;var h=this.getFoldWidgetRange(e,"all",t);if(h){if(h.start.row<=r)break;if(h.isMultiLine())t=h.end.row;else if(i==l)break}a=t}}return new o(r,s,a,e.getLine(a).length)},this.getCommentRegionBlock=function(e,t,n){for(var i=t.search(/\s*$/),r=e.getLength(),s=n,a=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,c=1;++n<r;){t=e.getLine(n);var l=a.exec(t);if(l&&(l[1]?c--:c++,!c))break}var h=n;if(h>s)return new o(s,i,h,t.length)}}).call(s.prototype)}),ace.define("ace/mode/folding/sql",["require","exports","module","ace/lib/oop","ace/mode/folding/cstyle"],function(e,t,n){"use strict";var i=e("../../lib/oop"),o=e("./cstyle").FoldMode,r=t.FoldMode=function(){};i.inherits(r,o),(function(){}).call(r.prototype)}),ace.define("ace/mode/sql",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/sql_highlight_rules","ace/mode/folding/sql"],function(e,t,n){"use strict";var i=e("../lib/oop"),o=e("./text").Mode,r=e("./sql_highlight_rules").SqlHighlightRules,s=e("./folding/sql").FoldMode,a=function(){this.HighlightRules=r,this.foldingRules=new s,this.$behaviour=this.$defaultBehaviour};i.inherits(a,o),(function(){this.lineCommentStart="--",this.blockComment={start:"/*",end:"*/"},this.$id="ace/mode/sql",this.snippetFileId="ace/snippets/sql"}).call(a.prototype),t.Mode=a}),ace.require(["ace/mode/sql"],function(t){e&&(e.exports=t)})},42557:function(e,t,n){e=n.nmd(e),ace.define("ace/theme/tomorrow-css",["require","exports","module"],function(e,t,n){n.exports='.ace-tomorrow .ace_gutter {\n  background: #f6f6f6;\n  color: #4D4D4C\n}\n\n.ace-tomorrow .ace_print-margin {\n  width: 1px;\n  background: #f6f6f6\n}\n\n.ace-tomorrow {\n  background-color: #FFFFFF;\n  color: #4D4D4C\n}\n\n.ace-tomorrow .ace_cursor {\n  color: #AEAFAD\n}\n\n.ace-tomorrow .ace_marker-layer .ace_selection {\n  background: #D6D6D6\n}\n\n.ace-tomorrow.ace_multiselect .ace_selection.ace_start {\n  box-shadow: 0 0 3px 0px #FFFFFF;\n}\n\n.ace-tomorrow .ace_marker-layer .ace_step {\n  background: rgb(255, 255, 0)\n}\n\n.ace-tomorrow .ace_marker-layer .ace_bracket {\n  margin: -1px 0 0 -1px;\n  border: 1px solid #D1D1D1\n}\n\n.ace-tomorrow .ace_marker-layer .ace_active-line {\n  background: #EFEFEF\n}\n\n.ace-tomorrow .ace_gutter-active-line {\n  background-color : #dcdcdc\n}\n\n.ace-tomorrow .ace_marker-layer .ace_selected-word {\n  border: 1px solid #D6D6D6\n}\n\n.ace-tomorrow .ace_invisible {\n  color: #D1D1D1\n}\n\n.ace-tomorrow .ace_keyword,\n.ace-tomorrow .ace_meta,\n.ace-tomorrow .ace_storage,\n.ace-tomorrow .ace_storage.ace_type,\n.ace-tomorrow .ace_support.ace_type {\n  color: #8959A8\n}\n\n.ace-tomorrow .ace_keyword.ace_operator {\n  color: #3E999F\n}\n\n.ace-tomorrow .ace_constant.ace_character,\n.ace-tomorrow .ace_constant.ace_language,\n.ace-tomorrow .ace_constant.ace_numeric,\n.ace-tomorrow .ace_keyword.ace_other.ace_unit,\n.ace-tomorrow .ace_support.ace_constant,\n.ace-tomorrow .ace_variable.ace_parameter {\n  color: #F5871F\n}\n\n.ace-tomorrow .ace_constant.ace_other {\n  color: #666969\n}\n\n.ace-tomorrow .ace_invalid {\n  color: #FFFFFF;\n  background-color: #C82829\n}\n\n.ace-tomorrow .ace_invalid.ace_deprecated {\n  color: #FFFFFF;\n  background-color: #8959A8\n}\n\n.ace-tomorrow .ace_fold {\n  background-color: #4271AE;\n  border-color: #4D4D4C\n}\n\n.ace-tomorrow .ace_entity.ace_name.ace_function,\n.ace-tomorrow .ace_support.ace_function,\n.ace-tomorrow .ace_variable {\n  color: #4271AE\n}\n\n.ace-tomorrow .ace_support.ace_class,\n.ace-tomorrow .ace_support.ace_type {\n  color: #C99E00\n}\n\n.ace-tomorrow .ace_heading,\n.ace-tomorrow .ace_markup.ace_heading,\n.ace-tomorrow .ace_string {\n  color: #718C00\n}\n\n.ace-tomorrow .ace_entity.ace_name.ace_tag,\n.ace-tomorrow .ace_entity.ace_other.ace_attribute-name,\n.ace-tomorrow .ace_meta.ace_tag,\n.ace-tomorrow .ace_string.ace_regexp,\n.ace-tomorrow .ace_variable {\n  color: #C82829\n}\n\n.ace-tomorrow .ace_comment {\n  color: #8E908C\n}\n\n.ace-tomorrow .ace_indent-guide {\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bdu3f/BwAlfgctduB85QAAAABJRU5ErkJggg==) right repeat-y\n}\n\n.ace-tomorrow .ace_indent-guide-active {\n  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAZSURBVHjaYvj///9/hivKyv8BAAAA//8DACLqBhbvk+/eAAAAAElFTkSuQmCC") right repeat-y;\n} \n'}),ace.define("ace/theme/tomorrow",["require","exports","module","ace/theme/tomorrow-css","ace/lib/dom"],function(e,t,n){t.isDark=!1,t.cssClass="ace-tomorrow",t.cssText=e("./tomorrow-css"),e("../lib/dom").importCssString(t.cssText,t.cssClass,!1)}),ace.require(["ace/theme/tomorrow"],function(t){e&&(e.exports=t)})},52027:function(e){var t=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32};t.Diff=function(e,t){return[e,t]},t.prototype.diff_main=function(e,n,i,o){void 0===o&&(o=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout);var r=o;if(null==e||null==n)throw Error("Null input. (diff_main)");if(e==n)return e?[new t.Diff(0,e)]:[];void 0===i&&(i=!0);var s=i,a=this.diff_commonPrefix(e,n),c=e.substring(0,a);e=e.substring(a),n=n.substring(a),a=this.diff_commonSuffix(e,n);var l=e.substring(e.length-a);e=e.substring(0,e.length-a),n=n.substring(0,n.length-a);var h=this.diff_compute_(e,n,s,r);return c&&h.unshift(new t.Diff(0,c)),l&&h.push(new t.Diff(0,l)),this.diff_cleanupMerge(h),h},t.prototype.diff_compute_=function(e,n,i,o){if(!e)return[new t.Diff(1,n)];if(!n)return[new t.Diff(-1,e)];var r,s=e.length>n.length?e:n,a=e.length>n.length?n:e,c=s.indexOf(a);if(-1!=c)return r=[new t.Diff(1,s.substring(0,c)),new t.Diff(0,a),new t.Diff(1,s.substring(c+a.length))],e.length>n.length&&(r[0][0]=r[2][0]=-1),r;if(1==a.length)return[new t.Diff(-1,e),new t.Diff(1,n)];var l=this.diff_halfMatch_(e,n);if(l){var h=l[0],p=l[1],u=l[2],d=l[3],f=l[4],g=this.diff_main(h,u,i,o),m=this.diff_main(p,d,i,o);return g.concat([new t.Diff(0,f)],m)}return i&&e.length>100&&n.length>100?this.diff_lineMode_(e,n,o):this.diff_bisect_(e,n,o)},t.prototype.diff_lineMode_=function(e,n,i){var o=this.diff_linesToChars_(e,n);e=o.chars1,n=o.chars2;var r=o.lineArray,s=this.diff_main(e,n,!1,i);this.diff_charsToLines_(s,r),this.diff_cleanupSemantic(s),s.push(new t.Diff(0,""));for(var a=0,c=0,l=0,h="",p="";a<s.length;){switch(s[a][0]){case 1:l++,p+=s[a][1];break;case -1:c++,h+=s[a][1];break;case 0:if(c>=1&&l>=1){s.splice(a-c-l,c+l),a=a-c-l;for(var u=this.diff_main(h,p,!1,i),d=u.length-1;d>=0;d--)s.splice(a,0,u[d]);a+=u.length}l=0,c=0,h="",p=""}a++}return s.pop(),s},t.prototype.diff_bisect_=function(e,n,i){for(var o=e.length,r=n.length,s=Math.ceil((o+r)/2),a=2*s,c=Array(a),l=Array(a),h=0;h<a;h++)c[h]=-1,l[h]=-1;c[s+1]=0,l[s+1]=0;for(var p=o-r,u=p%2!=0,d=0,f=0,g=0,m=0,b=0;b<s&&!(new Date().getTime()>i);b++){for(var v=-b+d;v<=b-f;v+=2){for(var y,_=s+v,w=(y=v==-b||v!=b&&c[_-1]<c[_+1]?c[_+1]:c[_-1]+1)-v;y<o&&w<r&&e.charAt(y)==n.charAt(w);)y++,w++;if(c[_]=y,y>o)f+=2;else if(w>r)d+=2;else if(u){var x=s+p-v;if(x>=0&&x<a&&-1!=l[x]){var S=o-l[x];if(y>=S)return this.diff_bisectSplit_(e,n,y,w,i)}}}for(var k=-b+g;k<=b-m;k+=2){for(var S,x=s+k,C=(S=k==-b||k!=b&&l[x-1]<l[x+1]?l[x+1]:l[x-1]+1)-k;S<o&&C<r&&e.charAt(o-S-1)==n.charAt(r-C-1);)S++,C++;if(l[x]=S,S>o)m+=2;else if(C>r)g+=2;else if(!u){var _=s+p-k;if(_>=0&&_<a&&-1!=c[_]){var y=c[_],w=s+y-_;if(y>=(S=o-S))return this.diff_bisectSplit_(e,n,y,w,i)}}}}return[new t.Diff(-1,e),new t.Diff(1,n)]},t.prototype.diff_bisectSplit_=function(e,t,n,i,o){var r=e.substring(0,n),s=t.substring(0,i),a=e.substring(n),c=t.substring(i),l=this.diff_main(r,s,!1,o),h=this.diff_main(a,c,!1,o);return l.concat(h)},t.prototype.diff_linesToChars_=function(e,t){var n=[],i={};function o(e){for(var t="",o=0,s=-1,a=n.length;s<e.length-1;){-1==(s=e.indexOf("\n",o))&&(s=e.length-1);var c=e.substring(o,s+1);(i.hasOwnProperty?i.hasOwnProperty(c):void 0!==i[c])?t+=String.fromCharCode(i[c]):(a==r&&(c=e.substring(o),s=e.length),t+=String.fromCharCode(a),i[c]=a,n[a++]=c),o=s+1}return t}n[0]="";var r=4e4,s=o(e);return r=65535,{chars1:s,chars2:o(t),lineArray:n}},t.prototype.diff_charsToLines_=function(e,t){for(var n=0;n<e.length;n++){for(var i=e[n][1],o=[],r=0;r<i.length;r++)o[r]=t[i.charCodeAt(r)];e[n][1]=o.join("")}},t.prototype.diff_commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var n=0,i=Math.min(e.length,t.length),o=i,r=0;n<o;)e.substring(r,o)==t.substring(r,o)?r=n=o:i=o,o=Math.floor((i-n)/2+n);return o},t.prototype.diff_commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var n=0,i=Math.min(e.length,t.length),o=i,r=0;n<o;)e.substring(e.length-o,e.length-r)==t.substring(t.length-o,t.length-r)?r=n=o:i=o,o=Math.floor((i-n)/2+n);return o},t.prototype.diff_commonOverlap_=function(e,t){var n=e.length,i=t.length;if(0==n||0==i)return 0;n>i?e=e.substring(n-i):n<i&&(t=t.substring(0,n));var o=Math.min(n,i);if(e==t)return o;for(var r=0,s=1;;){var a=e.substring(o-s),c=t.indexOf(a);if(-1==c)return r;s+=c,(0==c||e.substring(o-s)==t.substring(0,s))&&(r=s,s++)}},t.prototype.diff_halfMatch_=function(e,t){if(this.Diff_Timeout<=0)return null;var n,i,o,r,s,a=e.length>t.length?e:t,c=e.length>t.length?t:e;if(a.length<4||2*c.length<a.length)return null;var l=this;function h(e,t,n){for(var i,o,r,s,a=e.substring(n,n+Math.floor(e.length/4)),c=-1,h="";-1!=(c=t.indexOf(a,c+1));){var p=l.diff_commonPrefix(e.substring(n),t.substring(c)),u=l.diff_commonSuffix(e.substring(0,n),t.substring(0,c));h.length<u+p&&(h=t.substring(c-u,c)+t.substring(c,c+p),i=e.substring(0,n-u),o=e.substring(n+p),r=t.substring(0,c-u),s=t.substring(c+p))}return 2*h.length>=e.length?[i,o,r,s,h]:null}var p=h(a,c,Math.ceil(a.length/4)),u=h(a,c,Math.ceil(a.length/2));return p||u?(n=u?p&&p[4].length>u[4].length?p:u:p,e.length>t.length?(i=n[0],o=n[1],r=n[2],s=n[3]):(r=n[0],s=n[1],i=n[2],o=n[3]),[i,o,r,s,n[4]]):null},t.prototype.diff_cleanupSemantic=function(e){for(var n=!1,i=[],o=0,r=null,s=0,a=0,c=0,l=0,h=0;s<e.length;)0==e[s][0]?(i[o++]=s,a=l,c=h,l=0,h=0,r=e[s][1]):(1==e[s][0]?l+=e[s][1].length:h+=e[s][1].length,r&&r.length<=Math.max(a,c)&&r.length<=Math.max(l,h)&&(e.splice(i[o-1],0,new t.Diff(-1,r)),e[i[o-1]+1][0]=1,o--,s=--o>0?i[o-1]:-1,a=0,c=0,l=0,h=0,r=null,n=!0)),s++;for(n&&this.diff_cleanupMerge(e),this.diff_cleanupSemanticLossless(e),s=1;s<e.length;){if(-1==e[s-1][0]&&1==e[s][0]){var p=e[s-1][1],u=e[s][1],d=this.diff_commonOverlap_(p,u),f=this.diff_commonOverlap_(u,p);d>=f?(d>=p.length/2||d>=u.length/2)&&(e.splice(s,0,new t.Diff(0,u.substring(0,d))),e[s-1][1]=p.substring(0,p.length-d),e[s+1][1]=u.substring(d),s++):(f>=p.length/2||f>=u.length/2)&&(e.splice(s,0,new t.Diff(0,p.substring(0,f))),e[s-1][0]=1,e[s-1][1]=u.substring(0,u.length-f),e[s+1][0]=-1,e[s+1][1]=p.substring(f),s++),s++}s++}},t.prototype.diff_cleanupSemanticLossless=function(e){function n(e,n){if(!e||!n)return 6;var i=e.charAt(e.length-1),o=n.charAt(0),r=i.match(t.nonAlphaNumericRegex_),s=o.match(t.nonAlphaNumericRegex_),a=r&&i.match(t.whitespaceRegex_),c=s&&o.match(t.whitespaceRegex_),l=a&&i.match(t.linebreakRegex_),h=c&&o.match(t.linebreakRegex_),p=l&&e.match(t.blanklineEndRegex_),u=h&&n.match(t.blanklineStartRegex_);return p||u?5:l||h?4:r&&!a&&c?3:a||c?2:r||s?1:0}for(var i=1;i<e.length-1;){if(0==e[i-1][0]&&0==e[i+1][0]){var o=e[i-1][1],r=e[i][1],s=e[i+1][1],a=this.diff_commonSuffix(o,r);if(a){var c=r.substring(r.length-a);o=o.substring(0,o.length-a),r=c+r.substring(0,r.length-a),s=c+s}for(var l=o,h=r,p=s,u=n(o,r)+n(r,s);r.charAt(0)===s.charAt(0);){o+=r.charAt(0),r=r.substring(1)+s.charAt(0),s=s.substring(1);var d=n(o,r)+n(r,s);d>=u&&(u=d,l=o,h=r,p=s)}e[i-1][1]!=l&&(l?e[i-1][1]=l:(e.splice(i-1,1),i--),e[i][1]=h,p?e[i+1][1]=p:(e.splice(i+1,1),i--))}i++}},t.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,t.whitespaceRegex_=/\s/,t.linebreakRegex_=/[\r\n]/,t.blanklineEndRegex_=/\n\r?\n$/,t.blanklineStartRegex_=/^\r?\n\r?\n/,t.prototype.diff_cleanupEfficiency=function(e){for(var n=!1,i=[],o=0,r=null,s=0,a=!1,c=!1,l=!1,h=!1;s<e.length;)0==e[s][0]?(e[s][1].length<this.Diff_EditCost&&(l||h)?(i[o++]=s,a=l,c=h,r=e[s][1]):(o=0,r=null),l=h=!1):(-1==e[s][0]?h=!0:l=!0,r&&(a&&c&&l&&h||r.length<this.Diff_EditCost/2&&a+c+l+h==3)&&(e.splice(i[o-1],0,new t.Diff(-1,r)),e[i[o-1]+1][0]=1,o--,r=null,a&&c?(l=h=!0,o=0):(s=--o>0?i[o-1]:-1,l=h=!1),n=!0)),s++;n&&this.diff_cleanupMerge(e)},t.prototype.diff_cleanupMerge=function(e){e.push(new t.Diff(0,""));for(var n,i=0,o=0,r=0,s="",a="";i<e.length;)switch(e[i][0]){case 1:r++,a+=e[i][1],i++;break;case -1:o++,s+=e[i][1],i++;break;case 0:o+r>1?(0!==o&&0!==r&&(0!==(n=this.diff_commonPrefix(a,s))&&(i-o-r>0&&0==e[i-o-r-1][0]?e[i-o-r-1][1]+=a.substring(0,n):(e.splice(0,0,new t.Diff(0,a.substring(0,n))),i++),a=a.substring(n),s=s.substring(n)),0!==(n=this.diff_commonSuffix(a,s))&&(e[i][1]=a.substring(a.length-n)+e[i][1],a=a.substring(0,a.length-n),s=s.substring(0,s.length-n))),i-=o+r,e.splice(i,o+r),s.length&&(e.splice(i,0,new t.Diff(-1,s)),i++),a.length&&(e.splice(i,0,new t.Diff(1,a)),i++),i++):0!==i&&0==e[i-1][0]?(e[i-1][1]+=e[i][1],e.splice(i,1)):i++,r=0,o=0,s="",a=""}""===e[e.length-1][1]&&e.pop();var c=!1;for(i=1;i<e.length-1;)0==e[i-1][0]&&0==e[i+1][0]&&(e[i][1].substring(e[i][1].length-e[i-1][1].length)==e[i-1][1]?(e[i][1]=e[i-1][1]+e[i][1].substring(0,e[i][1].length-e[i-1][1].length),e[i+1][1]=e[i-1][1]+e[i+1][1],e.splice(i-1,1),c=!0):e[i][1].substring(0,e[i+1][1].length)==e[i+1][1]&&(e[i-1][1]+=e[i+1][1],e[i][1]=e[i][1].substring(e[i+1][1].length)+e[i+1][1],e.splice(i+1,1),c=!0)),i++;c&&this.diff_cleanupMerge(e)},t.prototype.diff_xIndex=function(e,t){var n,i=0,o=0,r=0,s=0;for(n=0;n<e.length&&(1!==e[n][0]&&(i+=e[n][1].length),-1!==e[n][0]&&(o+=e[n][1].length),!(i>t));n++)r=i,s=o;return e.length!=n&&-1===e[n][0]?s:s+(t-r)},t.prototype.diff_prettyHtml=function(e){for(var t=[],n=/&/g,i=/</g,o=/>/g,r=/\n/g,s=0;s<e.length;s++){var a=e[s][0],c=e[s][1].replace(n,"&amp;").replace(i,"&lt;").replace(o,"&gt;").replace(r,"&para;<br>");switch(a){case 1:t[s]='<ins style="background:#e6ffe6;">'+c+"</ins>";break;case -1:t[s]='<del style="background:#ffe6e6;">'+c+"</del>";break;case 0:t[s]="<span>"+c+"</span>"}}return t.join("")},t.prototype.diff_text1=function(e){for(var t=[],n=0;n<e.length;n++)1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},t.prototype.diff_text2=function(e){for(var t=[],n=0;n<e.length;n++)-1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},t.prototype.diff_levenshtein=function(e){for(var t=0,n=0,i=0,o=0;o<e.length;o++){var r=e[o][0],s=e[o][1];switch(r){case 1:n+=s.length;break;case -1:i+=s.length;break;case 0:t+=Math.max(n,i),n=0,i=0}}return t+Math.max(n,i)},t.prototype.diff_toDelta=function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n][0]){case 1:t[n]="+"+encodeURI(e[n][1]);break;case -1:t[n]="-"+e[n][1].length;break;case 0:t[n]="="+e[n][1].length}return t.join("	").replace(/%20/g," ")},t.prototype.diff_fromDelta=function(e,n){for(var i=[],o=0,r=0,s=n.split(/\t/g),a=0;a<s.length;a++){var c=s[a].substring(1);switch(s[a].charAt(0)){case"+":try{i[o++]=new t.Diff(1,decodeURI(c))}catch(e){throw Error("Illegal escape in diff_fromDelta: "+c)}break;case"-":case"=":var l=parseInt(c,10);if(isNaN(l)||l<0)throw Error("Invalid number in diff_fromDelta: "+c);var h=e.substring(r,r+=l);"="==s[a].charAt(0)?i[o++]=new t.Diff(0,h):i[o++]=new t.Diff(-1,h);break;default:if(s[a])throw Error("Invalid diff operation in diff_fromDelta: "+s[a])}}if(r!=e.length)throw Error("Delta length ("+r+") does not equal source text length ("+e.length+").");return i},t.prototype.match_main=function(e,t,n){if(null==e||null==t||null==n)throw Error("Null input. (match_main)");return(n=Math.max(0,Math.min(n,e.length)),e==t)?0:e.length?e.substring(n,n+t.length)==t?n:this.match_bitap_(e,t,n):-1},t.prototype.match_bitap_=function(e,t,n){if(t.length>this.Match_MaxBits)throw Error("Pattern too long for this browser.");var i,o,r,s=this.match_alphabet_(t),a=this;function c(e,i){var o=e/t.length,r=Math.abs(n-i);return a.Match_Distance?o+r/a.Match_Distance:r?1:o}var l=this.Match_Threshold,h=e.indexOf(t,n);-1!=h&&(l=Math.min(c(0,h),l),-1!=(h=e.lastIndexOf(t,n+t.length))&&(l=Math.min(c(0,h),l)));var p=1<<t.length-1;h=-1;for(var u=t.length+e.length,d=0;d<t.length;d++){for(i=0,o=u;i<o;)c(d,n+o)<=l?i=o:u=o,o=Math.floor((u-i)/2+i);u=o;var f=Math.max(1,n-o+1),g=Math.min(n+o,e.length)+t.length,m=Array(g+2);m[g+1]=(1<<d)-1;for(var b=g;b>=f;b--){var v=s[e.charAt(b-1)];if(0===d?m[b]=(m[b+1]<<1|1)&v:m[b]=(m[b+1]<<1|1)&v|((r[b+1]|r[b])<<1|1)|r[b+1],m[b]&p){var y=c(d,b-1);if(y<=l){if(l=y,(h=b-1)>n)f=Math.max(1,2*n-h);else break}}}if(c(d+1,n)>l)break;r=m}return h},t.prototype.match_alphabet_=function(e){for(var t={},n=0;n<e.length;n++)t[e.charAt(n)]=0;for(var n=0;n<e.length;n++)t[e.charAt(n)]|=1<<e.length-n-1;return t},t.prototype.patch_addContext_=function(e,n){if(0!=n.length){if(null===e.start2)throw Error("patch not initialized");for(var i=n.substring(e.start2,e.start2+e.length1),o=0;n.indexOf(i)!=n.lastIndexOf(i)&&i.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)o+=this.Patch_Margin,i=n.substring(e.start2-o,e.start2+e.length1+o);o+=this.Patch_Margin;var r=n.substring(e.start2-o,e.start2);r&&e.diffs.unshift(new t.Diff(0,r));var s=n.substring(e.start2+e.length1,e.start2+e.length1+o);s&&e.diffs.push(new t.Diff(0,s)),e.start1-=r.length,e.start2-=r.length,e.length1+=r.length+s.length,e.length2+=r.length+s.length}},t.prototype.patch_make=function(e,n,i){if("string"==typeof e&&"string"==typeof n&&void 0===i)o=e,(r=this.diff_main(o,n,!0)).length>2&&(this.diff_cleanupSemantic(r),this.diff_cleanupEfficiency(r));else if(e&&"object"==typeof e&&void 0===n&&void 0===i)r=e,o=this.diff_text1(r);else if("string"==typeof e&&n&&"object"==typeof n&&void 0===i)o=e,r=n;else if("string"==typeof e&&"string"==typeof n&&i&&"object"==typeof i)o=e,r=i;else throw Error("Unknown call format to patch_make.");if(0===r.length)return[];for(var o,r,s=[],a=new t.patch_obj,c=0,l=0,h=0,p=o,u=o,d=0;d<r.length;d++){var f=r[d][0],g=r[d][1];switch(c||0===f||(a.start1=l,a.start2=h),f){case 1:a.diffs[c++]=r[d],a.length2+=g.length,u=u.substring(0,h)+g+u.substring(h);break;case -1:a.length1+=g.length,a.diffs[c++]=r[d],u=u.substring(0,h)+u.substring(h+g.length);break;case 0:g.length<=2*this.Patch_Margin&&c&&r.length!=d+1?(a.diffs[c++]=r[d],a.length1+=g.length,a.length2+=g.length):g.length>=2*this.Patch_Margin&&c&&(this.patch_addContext_(a,p),s.push(a),a=new t.patch_obj,c=0,p=u,l=h)}1!==f&&(l+=g.length),-1!==f&&(h+=g.length)}return c&&(this.patch_addContext_(a,p),s.push(a)),s},t.prototype.patch_deepCopy=function(e){for(var n=[],i=0;i<e.length;i++){var o=e[i],r=new t.patch_obj;r.diffs=[];for(var s=0;s<o.diffs.length;s++)r.diffs[s]=new t.Diff(o.diffs[s][0],o.diffs[s][1]);r.start1=o.start1,r.start2=o.start2,r.length1=o.length1,r.length2=o.length2,n[i]=r}return n},t.prototype.patch_apply=function(e,t){if(0==e.length)return[t,[]];e=this.patch_deepCopy(e);var n=this.patch_addPadding(e);t=n+t+n,this.patch_splitMax(e);for(var i=0,o=[],r=0;r<e.length;r++){var s=e[r].start2+i,a=this.diff_text1(e[r].diffs),c=-1;if(a.length>this.Match_MaxBits?-1!=(h=this.match_main(t,a.substring(0,this.Match_MaxBits),s))&&(-1==(c=this.match_main(t,a.substring(a.length-this.Match_MaxBits),s+a.length-this.Match_MaxBits))||h>=c)&&(h=-1):h=this.match_main(t,a,s),-1==h)o[r]=!1,i-=e[r].length2-e[r].length1;else if(o[r]=!0,i=h-s,p=-1==c?t.substring(h,h+a.length):t.substring(h,c+this.Match_MaxBits),a==p)t=t.substring(0,h)+this.diff_text2(e[r].diffs)+t.substring(h+a.length);else{var l=this.diff_main(a,p,!1);if(a.length>this.Match_MaxBits&&this.diff_levenshtein(l)/a.length>this.Patch_DeleteThreshold)o[r]=!1;else{this.diff_cleanupSemanticLossless(l);for(var h,p,u,d=0,f=0;f<e[r].diffs.length;f++){var g=e[r].diffs[f];0!==g[0]&&(u=this.diff_xIndex(l,d)),1===g[0]?t=t.substring(0,h+u)+g[1]+t.substring(h+u):-1===g[0]&&(t=t.substring(0,h+u)+t.substring(h+this.diff_xIndex(l,d+g[1].length))),-1!==g[0]&&(d+=g[1].length)}}}}return[t=t.substring(n.length,t.length-n.length),o]},t.prototype.patch_addPadding=function(e){for(var n=this.Patch_Margin,i="",o=1;o<=n;o++)i+=String.fromCharCode(o);for(var o=0;o<e.length;o++)e[o].start1+=n,e[o].start2+=n;var r=e[0],s=r.diffs;if(0==s.length||0!=s[0][0])s.unshift(new t.Diff(0,i)),r.start1-=n,r.start2-=n,r.length1+=n,r.length2+=n;else if(n>s[0][1].length){var a=n-s[0][1].length;s[0][1]=i.substring(s[0][1].length)+s[0][1],r.start1-=a,r.start2-=a,r.length1+=a,r.length2+=a}if(0==(s=(r=e[e.length-1]).diffs).length||0!=s[s.length-1][0])s.push(new t.Diff(0,i)),r.length1+=n,r.length2+=n;else if(n>s[s.length-1][1].length){var a=n-s[s.length-1][1].length;s[s.length-1][1]+=i.substring(0,a),r.length1+=a,r.length2+=a}return i},t.prototype.patch_splitMax=function(e){for(var n=this.Match_MaxBits,i=0;i<e.length;i++)if(!(e[i].length1<=n)){var o=e[i];e.splice(i--,1);for(var r=o.start1,s=o.start2,a="";0!==o.diffs.length;){var c=new t.patch_obj,l=!0;for(c.start1=r-a.length,c.start2=s-a.length,""!==a&&(c.length1=c.length2=a.length,c.diffs.push(new t.Diff(0,a)));0!==o.diffs.length&&c.length1<n-this.Patch_Margin;){var h=o.diffs[0][0],p=o.diffs[0][1];1===h?(c.length2+=p.length,s+=p.length,c.diffs.push(o.diffs.shift()),l=!1):-1===h&&1==c.diffs.length&&0==c.diffs[0][0]&&p.length>2*n?(c.length1+=p.length,r+=p.length,l=!1,c.diffs.push(new t.Diff(h,p)),o.diffs.shift()):(p=p.substring(0,n-c.length1-this.Patch_Margin),c.length1+=p.length,r+=p.length,0===h?(c.length2+=p.length,s+=p.length):l=!1,c.diffs.push(new t.Diff(h,p)),p==o.diffs[0][1]?o.diffs.shift():o.diffs[0][1]=o.diffs[0][1].substring(p.length))}a=(a=this.diff_text2(c.diffs)).substring(a.length-this.Patch_Margin);var u=this.diff_text1(o.diffs).substring(0,this.Patch_Margin);""!==u&&(c.length1+=u.length,c.length2+=u.length,0!==c.diffs.length&&0===c.diffs[c.diffs.length-1][0]?c.diffs[c.diffs.length-1][1]+=u:c.diffs.push(new t.Diff(0,u))),l||e.splice(++i,0,c)}}},t.prototype.patch_toText=function(e){for(var t=[],n=0;n<e.length;n++)t[n]=e[n];return t.join("")},t.prototype.patch_fromText=function(e){var n=[];if(!e)return n;for(var i=e.split("\n"),o=0,r=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;o<i.length;){var s=i[o].match(r);if(!s)throw Error("Invalid patch string: "+i[o]);var a=new t.patch_obj;for(n.push(a),a.start1=parseInt(s[1],10),""===s[2]?(a.start1--,a.length1=1):"0"==s[2]?a.length1=0:(a.start1--,a.length1=parseInt(s[2],10)),a.start2=parseInt(s[3],10),""===s[4]?(a.start2--,a.length2=1):"0"==s[4]?a.length2=0:(a.start2--,a.length2=parseInt(s[4],10)),o++;o<i.length;){var c=i[o].charAt(0);try{var l=decodeURI(i[o].substring(1))}catch(e){throw Error("Illegal escape in patch_fromText: "+l)}if("-"==c)a.diffs.push(new t.Diff(-1,l));else if("+"==c)a.diffs.push(new t.Diff(1,l));else if(" "==c)a.diffs.push(new t.Diff(0,l));else if("@"==c)break;else if(""===c);else throw Error('Invalid patch mode "'+c+'" in: '+l);o++}}return n},t.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},t.patch_obj.prototype.toString=function(){e=0===this.length1?this.start1+",0":1==this.length1?this.start1+1:this.start1+1+","+this.length1;for(var e,t,n=["@@ -"+e+" +"+(0===this.length2?this.start2+",0":1==this.length2?this.start2+1:this.start2+1+","+this.length2)+" @@\n"],i=0;i<this.diffs.length;i++){switch(this.diffs[i][0]){case 1:t="+";break;case -1:t="-";break;case 0:t=" "}n[i+1]=t+encodeURI(this.diffs[i][1])+"\n"}return n.join("").replace(/%20/g," ")},e.exports=t,e.exports.diff_match_patch=t,e.exports.DIFF_DELETE=-1,e.exports.DIFF_INSERT=1,e.exports.DIFF_EQUAL=0},29208:function(e,t,n){var i,o="__lodash_hash_undefined__",r=1/0,s=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/,c=/^\./,l=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,h=/\\(\\)?/g,p=/^\[object .+?Constructor\]$/,u="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,d="object"==typeof self&&self&&self.Object===Object&&self,f=u||d||Function("return this")(),g=Array.prototype,m=Function.prototype,b=Object.prototype,v=f["__core-js_shared__"],y=(i=/[^.]+$/.exec(v&&v.keys&&v.keys.IE_PROTO||""))?"Symbol(src)_1."+i:"",_=m.toString,w=b.hasOwnProperty,x=b.toString,S=RegExp("^"+_.call(w).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),k=f.Symbol,C=g.splice,M=D(f,"Map"),A=D(Object,"create"),E=k?k.prototype:void 0,T=E?E.toString:void 0;function R(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function $(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function O(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function L(e,t){for(var n,i=e.length;i--;)if((n=e[i][0])===t||n!=n&&t!=t)return i;return -1}function P(e,t){var n,i=e.__data__;return("string"==(n=typeof t)||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==t:null===t)?i["string"==typeof t?"string":"hash"]:i.map}function D(e,t){var n,i=null==e?void 0:e[t];return!(!j(i)||y&&y in i)&&("[object Function]"==(n=j(i)?x.call(i):"")||"[object GeneratorFunction]"==n||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(i)?S:p).test(function(e){if(null!=e){try{return _.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(i))?i:void 0}R.prototype.clear=function(){this.__data__=A?A(null):{}},R.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},R.prototype.get=function(e){var t=this.__data__;if(A){var n=t[e];return n===o?void 0:n}return w.call(t,e)?t[e]:void 0},R.prototype.has=function(e){var t=this.__data__;return A?void 0!==t[e]:w.call(t,e)},R.prototype.set=function(e,t){return this.__data__[e]=A&&void 0===t?o:t,this},$.prototype.clear=function(){this.__data__=[]},$.prototype.delete=function(e){var t=this.__data__,n=L(t,e);return!(n<0)&&(n==t.length-1?t.pop():C.call(t,n,1),!0)},$.prototype.get=function(e){var t=this.__data__,n=L(t,e);return n<0?void 0:t[n][1]},$.prototype.has=function(e){return L(this.__data__,e)>-1},$.prototype.set=function(e,t){var n=this.__data__,i=L(n,e);return i<0?n.push([e,t]):n[i][1]=t,this},O.prototype.clear=function(){this.__data__={hash:new R,map:new(M||$),string:new R}},O.prototype.delete=function(e){return P(this,e).delete(e)},O.prototype.get=function(e){return P(this,e).get(e)},O.prototype.has=function(e){return P(this,e).has(e)},O.prototype.set=function(e,t){return P(this,e).set(e,t),this};var F=N(function(e){e=null==(t=e)?"":function(e){if("string"==typeof e)return e;if(z(e))return T?T.call(e):"";var t=e+"";return"0"==t&&1/e==-r?"-0":t}(t);var t,n=[];return c.test(e)&&n.push(""),e.replace(l,function(e,t,i,o){n.push(i?o.replace(h,"$1"):t||e)}),n});function N(e,t){if("function"!=typeof e||t&&"function"!=typeof t)throw TypeError("Expected a function");var n=function(){var i=arguments,o=t?t.apply(this,i):i[0],r=n.cache;if(r.has(o))return r.get(o);var s=e.apply(this,i);return n.cache=r.set(o,s),s};return n.cache=new(N.Cache||O),n}N.Cache=O;var I=Array.isArray;function j(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function z(e){return"symbol"==typeof e||!!e&&"object"==typeof e&&"[object Symbol]"==x.call(e)}e.exports=function(e,t,n){var i=null==e?void 0:function(e,t){var n;t=!function(e,t){if(I(e))return!1;var n=typeof e;return!!("number"==n||"symbol"==n||"boolean"==n||null==e||z(e))||a.test(e)||!s.test(e)||null!=t&&e in Object(t)}(t,e)?I(n=t)?n:F(n):[t];for(var i=0,o=t.length;null!=e&&i<o;)e=e[function(e){if("string"==typeof e||z(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}(t[i++])];return i&&i==o?e:void 0}(e,t);return void 0===i?n:i}},72307:function(e,t,n){e=n.nmd(e);var i,o,r,s="__lodash_hash_undefined__",a="[object Arguments]",c="[object Array]",l="[object Boolean]",h="[object Date]",p="[object Error]",u="[object Function]",d="[object Map]",f="[object Number]",g="[object Object]",m="[object Promise]",b="[object RegExp]",v="[object Set]",y="[object String]",_="[object WeakMap]",w="[object ArrayBuffer]",x="[object DataView]",S=/^\[object .+?Constructor\]$/,k=/^(?:0|[1-9]\d*)$/,C={};C["[object Float32Array]"]=C["[object Float64Array]"]=C["[object Int8Array]"]=C["[object Int16Array]"]=C["[object Int32Array]"]=C["[object Uint8Array]"]=C["[object Uint8ClampedArray]"]=C["[object Uint16Array]"]=C["[object Uint32Array]"]=!0,C[a]=C[c]=C[w]=C[l]=C[x]=C[h]=C[p]=C[u]=C[d]=C[f]=C[g]=C[b]=C[v]=C[y]=C[_]=!1;var M="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,A="object"==typeof self&&self&&self.Object===Object&&self,E=M||A||Function("return this")(),T=t&&!t.nodeType&&t,R=T&&e&&!e.nodeType&&e,$=R&&R.exports===T,O=$&&M.process,L=function(){try{return O&&O.binding&&O.binding("util")}catch(e){}}(),P=L&&L.isTypedArray;function D(e){var t=-1,n=Array(e.size);return e.forEach(function(e,i){n[++t]=[i,e]}),n}function F(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}var N=Array.prototype,I=Function.prototype,j=Object.prototype,z=E["__core-js_shared__"],B=I.toString,H=j.hasOwnProperty,U=(i=/[^.]+$/.exec(z&&z.keys&&z.keys.IE_PROTO||""))?"Symbol(src)_1."+i:"",W=j.toString,V=RegExp("^"+B.call(H).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),q=$?E.Buffer:void 0,G=E.Symbol,K=E.Uint8Array,X=j.propertyIsEnumerable,Y=N.splice,Z=G?G.toStringTag:void 0,J=Object.getOwnPropertySymbols,Q=q?q.isBuffer:void 0,ee=(o=Object.keys,r=Object,function(e){return o(r(e))}),et=eC(E,"DataView"),en=eC(E,"Map"),ei=eC(E,"Promise"),eo=eC(E,"Set"),er=eC(E,"WeakMap"),es=eC(Object,"create"),ea=eE(et),ec=eE(en),el=eE(ei),eh=eE(eo),ep=eE(er),eu=G?G.prototype:void 0,ed=eu?eu.valueOf:void 0;function ef(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function eg(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function em(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function eb(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new em;++t<n;)this.add(e[t])}function ev(e){var t=this.__data__=new eg(e);this.size=t.size}function ey(e,t){for(var n=e.length;n--;)if(eT(e[n][0],t))return n;return -1}function e_(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Z&&Z in Object(e)?function(e){var t=H.call(e,Z),n=e[Z];try{e[Z]=void 0;var i=!0}catch(e){}var o=W.call(e);return i&&(t?e[Z]=n:delete e[Z]),o}(e):W.call(e)}function ew(e){return eF(e)&&e_(e)==a}function ex(e,t,n,i,o,r){var s=1&n,a=e.length,c=t.length;if(a!=c&&!(s&&c>a))return!1;var l=r.get(e);if(l&&r.get(t))return l==t;var h=-1,p=!0,u=2&n?new eb:void 0;for(r.set(e,t),r.set(t,e);++h<a;){var d=e[h],f=t[h];if(i)var g=s?i(f,d,h,t,e,r):i(d,f,h,e,t,r);if(void 0!==g){if(g)continue;p=!1;break}if(u){if(!function(e,t){for(var n=-1,i=null==e?0:e.length;++n<i;)if(t(e[n],n,e))return!0;return!1}(t,function(e,t){if(!u.has(t)&&(d===e||o(d,e,n,i,r)))return u.push(t)})){p=!1;break}}else if(!(d===f||o(d,f,n,i,r))){p=!1;break}}return r.delete(e),r.delete(t),p}function eS(e){var t;return t=function(e){return null!=e&&eP(e.length)&&!eL(e)?function(e,t){var n,i=e$(e),o=!i&&eR(e),r=!i&&!o&&eO(e),s=!i&&!o&&!r&&eN(e),a=i||o||r||s,c=a?function(e,t){for(var n=-1,i=Array(e);++n<e;)i[n]=t(n);return i}(e.length,String):[],l=c.length;for(var h in e)H.call(e,h)&&!(a&&("length"==h||r&&("offset"==h||"parent"==h)||s&&("buffer"==h||"byteLength"==h||"byteOffset"==h)||(n=null==(n=l)?9007199254740991:n)&&("number"==typeof h||k.test(h))&&h>-1&&h%1==0&&h<n))&&c.push(h);return c}(e):function(e){if(t=e&&e.constructor,e!==("function"==typeof t&&t.prototype||j))return ee(e);var t,n=[];for(var i in Object(e))H.call(e,i)&&"constructor"!=i&&n.push(i);return n}(e)}(e),e$(e)?t:function(e,t){for(var n=-1,i=t.length,o=e.length;++n<i;)e[o+n]=t[n];return e}(t,eM(e))}function ek(e,t){var n,i=e.__data__;return("string"==(n=typeof t)||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==t:null===t)?i["string"==typeof t?"string":"hash"]:i.map}function eC(e,t){var n=null==e?void 0:e[t];return!(!eD(n)||U&&U in n)&&(eL(n)?V:S).test(eE(n))?n:void 0}ef.prototype.clear=function(){this.__data__=es?es(null):{},this.size=0},ef.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ef.prototype.get=function(e){var t=this.__data__;if(es){var n=t[e];return n===s?void 0:n}return H.call(t,e)?t[e]:void 0},ef.prototype.has=function(e){var t=this.__data__;return es?void 0!==t[e]:H.call(t,e)},ef.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=es&&void 0===t?s:t,this},eg.prototype.clear=function(){this.__data__=[],this.size=0},eg.prototype.delete=function(e){var t=this.__data__,n=ey(t,e);return!(n<0)&&(n==t.length-1?t.pop():Y.call(t,n,1),--this.size,!0)},eg.prototype.get=function(e){var t=this.__data__,n=ey(t,e);return n<0?void 0:t[n][1]},eg.prototype.has=function(e){return ey(this.__data__,e)>-1},eg.prototype.set=function(e,t){var n=this.__data__,i=ey(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this},em.prototype.clear=function(){this.size=0,this.__data__={hash:new ef,map:new(en||eg),string:new ef}},em.prototype.delete=function(e){var t=ek(this,e).delete(e);return this.size-=t?1:0,t},em.prototype.get=function(e){return ek(this,e).get(e)},em.prototype.has=function(e){return ek(this,e).has(e)},em.prototype.set=function(e,t){var n=ek(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this},eb.prototype.add=eb.prototype.push=function(e){return this.__data__.set(e,s),this},eb.prototype.has=function(e){return this.__data__.has(e)},ev.prototype.clear=function(){this.__data__=new eg,this.size=0},ev.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ev.prototype.get=function(e){return this.__data__.get(e)},ev.prototype.has=function(e){return this.__data__.has(e)},ev.prototype.set=function(e,t){var n=this.__data__;if(n instanceof eg){var i=n.__data__;if(!en||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new em(i)}return n.set(e,t),this.size=n.size,this};var eM=J?function(e){return null==e?[]:function(e,t){for(var n=-1,i=null==e?0:e.length,o=0,r=[];++n<i;){var s=e[n];t(s,n,e)&&(r[o++]=s)}return r}(J(e=Object(e)),function(t){return X.call(e,t)})}:function(){return[]},eA=e_;function eE(e){if(null!=e){try{return B.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function eT(e,t){return e===t||e!=e&&t!=t}(et&&eA(new et(new ArrayBuffer(1)))!=x||en&&eA(new en)!=d||ei&&eA(ei.resolve())!=m||eo&&eA(new eo)!=v||er&&eA(new er)!=_)&&(eA=function(e){var t=e_(e),n=t==g?e.constructor:void 0,i=n?eE(n):"";if(i)switch(i){case ea:return x;case ec:return d;case el:return m;case eh:return v;case ep:return _}return t});var eR=ew(function(){return arguments}())?ew:function(e){return eF(e)&&H.call(e,"callee")&&!X.call(e,"callee")},e$=Array.isArray,eO=Q||function(){return!1};function eL(e){if(!eD(e))return!1;var t=e_(e);return t==u||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function eP(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function eD(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function eF(e){return null!=e&&"object"==typeof e}var eN=P?function(e){return P(e)}:function(e){return eF(e)&&eP(e.length)&&!!C[e_(e)]};e.exports=function(e,t){return function e(t,n,i,o,r){return t===n||(null!=t&&null!=n&&(eF(t)||eF(n))?function(e,t,n,i,o,r){var s=e$(e),u=e$(t),m=s?c:eA(e),_=u?c:eA(t);m=m==a?g:m,_=_==a?g:_;var S=m==g,k=_==g,C=m==_;if(C&&eO(e)){if(!eO(t))return!1;s=!0,S=!1}if(C&&!S)return r||(r=new ev),s||eN(e)?ex(e,t,n,i,o,r):function(e,t,n,i,o,r,s){switch(n){case x:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case w:if(e.byteLength!=t.byteLength||!r(new K(e),new K(t)))break;return!0;case l:case h:case f:return eT(+e,+t);case p:return e.name==t.name&&e.message==t.message;case b:case y:return e==t+"";case d:var a=D;case v:var c=1&i;if(a||(a=F),e.size!=t.size&&!c)break;var u=s.get(e);if(u)return u==t;i|=2,s.set(e,t);var g=ex(a(e),a(t),i,o,r,s);return s.delete(e),g;case"[object Symbol]":if(ed)return ed.call(e)==ed.call(t)}return!1}(e,t,m,n,i,o,r);if(!(1&n)){var M=S&&H.call(e,"__wrapped__"),A=k&&H.call(t,"__wrapped__");if(M||A){var E=M?e.value():e,T=A?t.value():t;return r||(r=new ev),o(E,T,n,i,r)}}return!!C&&(r||(r=new ev),function(e,t,n,i,o,r){var s=1&n,a=eS(e),c=a.length;if(c!=eS(t).length&&!s)return!1;for(var l=c;l--;){var h=a[l];if(!(s?h in t:H.call(t,h)))return!1}var p=r.get(e);if(p&&r.get(t))return p==t;var u=!0;r.set(e,t),r.set(t,e);for(var d=s;++l<c;){var f=e[h=a[l]],g=t[h];if(i)var m=s?i(g,f,h,t,e,r):i(f,g,h,e,t,r);if(!(void 0===m?f===g||o(f,g,n,i,r):m)){u=!1;break}d||(d="constructor"==h)}if(u&&!d){var b=e.constructor,v=t.constructor;b!=v&&"constructor"in e&&"constructor"in t&&!("function"==typeof b&&b instanceof b&&"function"==typeof v&&v instanceof v)&&(u=!1)}return r.delete(e),r.delete(t),u}(e,t,n,i,o,r))}(t,n,i,o,e,r):t!=t&&n!=n)}(e,t)}},92703:function(e,t,n){"use strict";var i=n(50414);function o(){}function r(){}r.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,r,s){if(s!==i){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:o};return n.PropTypes=n,n}},45697:function(e,t,n){e.exports=n(92703)()},50414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},93946:function(e,t,n){"use strict";var i,o=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var s=n(53239),a=n(45697),c=n(67294),l=n(72307),h=n(1048),p=(0,h.getAceInstance)(),u=function(e){function t(t){var n=e.call(this,t)||this;return h.editorEvents.forEach(function(e){n[e]=n[e].bind(n)}),n.debounce=h.debounce,n}return o(t,e),t.prototype.isInShadow=function(e){for(var t=e&&e.parentNode;t;){if("[object ShadowRoot]"===t.toString())return!0;t=t.parentNode}return!1},t.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.className,i=t.onBeforeLoad,o=t.onValidate,r=t.mode,s=t.focus,a=t.theme,c=t.fontSize,l=t.value,u=t.defaultValue,d=t.showGutter,f=t.wrapEnabled,g=t.showPrintMargin,m=t.scrollMargin,b=void 0===m?[0,0,0,0]:m,v=t.keyboardHandler,y=t.onLoad,_=t.commands,w=t.annotations,x=t.markers,S=t.placeholder;this.editor=p.edit(this.refEditor),i&&i(p);for(var k=Object.keys(this.props.editorProps),C=0;C<k.length;C++)this.editor[k[C]]=this.props.editorProps[k[C]];this.props.debounceChangePeriod&&(this.onChange=this.debounce(this.onChange,this.props.debounceChangePeriod)),this.editor.renderer.setScrollMargin(b[0],b[1],b[2],b[3]),this.isInShadow(this.refEditor)&&this.editor.renderer.attachToShadowRoot(),this.editor.getSession().setMode("string"==typeof r?"ace/mode/".concat(r):r),a&&""!==a&&this.editor.setTheme("ace/theme/".concat(a)),this.editor.setFontSize("number"==typeof c?"".concat(c,"px"):c),this.editor.getSession().setValue(u||l||""),this.props.navigateToFileEnd&&this.editor.navigateFileEnd(),this.editor.renderer.setShowGutter(d),this.editor.getSession().setUseWrapMode(f),this.editor.setShowPrintMargin(g),this.editor.on("focus",this.onFocus),this.editor.on("blur",this.onBlur),this.editor.on("copy",this.onCopy),this.editor.on("paste",this.onPaste),this.editor.on("change",this.onChange),this.editor.on("input",this.onInput),S&&this.updatePlaceholder(),this.editor.getSession().selection.on("changeSelection",this.onSelectionChange),this.editor.getSession().selection.on("changeCursor",this.onCursorChange),o&&this.editor.getSession().on("changeAnnotation",function(){var t=e.editor.getSession().getAnnotations();e.props.onValidate(t)}),this.editor.session.on("changeScrollTop",this.onScroll),this.editor.getSession().setAnnotations(w||[]),x&&x.length>0&&this.handleMarkers(x);var M=this.editor.$options;h.editorOptions.forEach(function(t){M.hasOwnProperty(t)?e.editor.setOption(t,e.props[t]):e.props[t]&&console.warn("ReactAce: editor option ".concat(t," was activated but not found. Did you need to import a related tool or did you possibly mispell the option?"))}),this.handleOptions(this.props),Array.isArray(_)&&_.forEach(function(t){"string"==typeof t.exec?e.editor.commands.bindKey(t.bindKey,t.exec):e.editor.commands.addCommand(t)}),v&&this.editor.setKeyboardHandler("ace/keyboard/"+v),n&&(this.refEditor.className+=" "+n),y&&y(this.editor),this.editor.resize(),s&&this.editor.focus()},t.prototype.componentDidUpdate=function(e){for(var t=this.props,n=0;n<h.editorOptions.length;n++){var i=h.editorOptions[n];t[i]!==e[i]&&this.editor.setOption(i,t[i])}if(t.className!==e.className){var o=this.refEditor.className.trim().split(" ");e.className.trim().split(" ").forEach(function(e){var t=o.indexOf(e);o.splice(t,1)}),this.refEditor.className=" "+t.className+" "+o.join(" ")}var r=this.editor&&null!=t.value&&this.editor.getValue()!==t.value;if(r){this.silent=!0;var s=this.editor.session.selection.toJSON();this.editor.setValue(t.value,t.cursorStart),this.editor.session.selection.fromJSON(s),this.silent=!1}t.placeholder!==e.placeholder&&this.updatePlaceholder(),t.mode!==e.mode&&this.editor.getSession().setMode("string"==typeof t.mode?"ace/mode/".concat(t.mode):t.mode),t.theme!==e.theme&&this.editor.setTheme("ace/theme/"+t.theme),t.keyboardHandler!==e.keyboardHandler&&(t.keyboardHandler?this.editor.setKeyboardHandler("ace/keyboard/"+t.keyboardHandler):this.editor.setKeyboardHandler(null)),t.fontSize!==e.fontSize&&this.editor.setFontSize("number"==typeof t.fontSize?"".concat(t.fontSize,"px"):t.fontSize),t.wrapEnabled!==e.wrapEnabled&&this.editor.getSession().setUseWrapMode(t.wrapEnabled),t.showPrintMargin!==e.showPrintMargin&&this.editor.setShowPrintMargin(t.showPrintMargin),t.showGutter!==e.showGutter&&this.editor.renderer.setShowGutter(t.showGutter),l(t.setOptions,e.setOptions)||this.handleOptions(t),(r||!l(t.annotations,e.annotations))&&this.editor.getSession().setAnnotations(t.annotations||[]),!l(t.markers,e.markers)&&Array.isArray(t.markers)&&this.handleMarkers(t.markers),l(t.scrollMargin,e.scrollMargin)||this.handleScrollMargins(t.scrollMargin),(e.height!==this.props.height||e.width!==this.props.width)&&this.editor.resize(),this.props.focus&&!e.focus&&this.editor.focus()},t.prototype.handleScrollMargins=function(e){void 0===e&&(e=[0,0,0,0]),this.editor.renderer.setScrollMargin(e[0],e[1],e[2],e[3])},t.prototype.componentWillUnmount=function(){this.editor&&(this.editor.destroy(),this.editor=null)},t.prototype.onChange=function(e){if(this.props.onChange&&!this.silent){var t=this.editor.getValue();this.props.onChange(t,e)}},t.prototype.onSelectionChange=function(e){if(this.props.onSelectionChange){var t=this.editor.getSelection();this.props.onSelectionChange(t,e)}},t.prototype.onCursorChange=function(e){if(this.props.onCursorChange){var t=this.editor.getSelection();this.props.onCursorChange(t,e)}},t.prototype.onInput=function(e){this.props.onInput&&this.props.onInput(e),this.props.placeholder&&this.updatePlaceholder()},t.prototype.onFocus=function(e){this.props.onFocus&&this.props.onFocus(e,this.editor)},t.prototype.onBlur=function(e){this.props.onBlur&&this.props.onBlur(e,this.editor)},t.prototype.onCopy=function(e){var t=e.text;this.props.onCopy&&this.props.onCopy(t)},t.prototype.onPaste=function(e){var t=e.text;this.props.onPaste&&this.props.onPaste(t)},t.prototype.onScroll=function(){this.props.onScroll&&this.props.onScroll(this.editor)},t.prototype.handleOptions=function(e){for(var t=Object.keys(e.setOptions),n=0;n<t.length;n++)this.editor.setOption(t[n],e.setOptions[t[n]])},t.prototype.handleMarkers=function(e){var t=this,n=this.editor.getSession().getMarkers(!0);for(var i in n)n.hasOwnProperty(i)&&this.editor.getSession().removeMarker(n[i].id);for(var i in n=this.editor.getSession().getMarkers(!1))n.hasOwnProperty(i)&&"ace_active-line"!==n[i].clazz&&"ace_selected-word"!==n[i].clazz&&this.editor.getSession().removeMarker(n[i].id);e.forEach(function(e){var n=e.startRow,i=e.startCol,o=e.endRow,r=e.endCol,a=e.className,c=e.type,l=e.inFront,h=new s.Range(n,i,o,r);t.editor.getSession().addMarker(h,a,c,void 0!==l&&l)})},t.prototype.updatePlaceholder=function(){var e=this.editor,t=this.props.placeholder,n=!e.session.getValue().length,i=e.renderer.placeholderNode;!n&&i?(e.renderer.scroller.removeChild(e.renderer.placeholderNode),e.renderer.placeholderNode=null):n&&!i?((i=e.renderer.placeholderNode=document.createElement("div")).textContent=t||"",i.className="ace_comment ace_placeholder",i.style.padding="0 9px",i.style.position="absolute",i.style.zIndex="3",e.renderer.scroller.appendChild(i)):n&&i&&(i.textContent=t)},t.prototype.updateRef=function(e){this.refEditor=e},t.prototype.render=function(){var e=this.props,t=e.name,n=e.width,i=e.height,o=e.style,s=r({width:n,height:i},o);return c.createElement("div",{ref:this.updateRef,id:t,style:s})},t.propTypes={mode:a.oneOfType([a.string,a.object]),focus:a.bool,theme:a.string,name:a.string,className:a.string,height:a.string,width:a.string,fontSize:a.oneOfType([a.number,a.string]),showGutter:a.bool,onChange:a.func,onCopy:a.func,onPaste:a.func,onFocus:a.func,onInput:a.func,onBlur:a.func,onScroll:a.func,value:a.string,defaultValue:a.string,onLoad:a.func,onSelectionChange:a.func,onCursorChange:a.func,onBeforeLoad:a.func,onValidate:a.func,minLines:a.number,maxLines:a.number,readOnly:a.bool,highlightActiveLine:a.bool,tabSize:a.number,showPrintMargin:a.bool,cursorStart:a.number,debounceChangePeriod:a.number,editorProps:a.object,setOptions:a.object,style:a.object,scrollMargin:a.array,annotations:a.array,markers:a.array,keyboardHandler:a.string,wrapEnabled:a.bool,enableSnippets:a.bool,enableBasicAutocompletion:a.oneOfType([a.bool,a.array]),enableLiveAutocompletion:a.oneOfType([a.bool,a.array]),navigateToFileEnd:a.bool,commands:a.array,placeholder:a.string},t.defaultProps={name:"ace-editor",focus:!1,mode:"",theme:"",height:"500px",width:"500px",fontSize:12,enableSnippets:!1,showGutter:!0,onChange:null,onPaste:null,onLoad:null,onScroll:null,minLines:null,maxLines:null,readOnly:!1,highlightActiveLine:!0,showPrintMargin:!0,tabSize:4,cursorStart:1,editorProps:{},style:{},scrollMargin:[0,0,0,0],setOptions:{},wrapEnabled:!1,enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,placeholder:null,navigateToFileEnd:!0},t}(c.Component);t.default=u},63532:function(e,t,n){"use strict";var i,o=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0});var r=n(45697),s=n(67294),a=n(88663),c=n(52027),l=function(e){function t(t){var n=e.call(this,t)||this;return n.state={value:n.props.value},n.onChange=n.onChange.bind(n),n.diff=n.diff.bind(n),n}return o(t,e),t.prototype.componentDidUpdate=function(){var e=this.props.value;e!==this.state.value&&this.setState({value:e})},t.prototype.onChange=function(e){this.setState({value:e}),this.props.onChange&&this.props.onChange(e)},t.prototype.diff=function(){var e=new c,t=this.state.value[0],n=this.state.value[1];if(0===t.length&&0===n.length)return[];var i=e.diff_main(t,n);e.diff_cleanupSemantic(i);var o=this.generateDiffedLines(i);return this.setCodeMarkers(o)},t.prototype.generateDiffedLines=function(e){var t={DIFF_EQUAL:0,DIFF_DELETE:-1,DIFF_INSERT:1},n={left:[],right:[]},i={left:1,right:1};return e.forEach(function(e){var o=e[0],r=e[1],s=r.split("\n").length-1;if(0!==r.length){var a=r[0],c=r[r.length-1],l=0;switch(o){case t.DIFF_EQUAL:i.left+=s,i.right+=s;break;case t.DIFF_DELETE:"\n"===a&&(i.left++,s--),0===(l=s)&&n.right.push({startLine:i.right,endLine:i.right}),"\n"===c&&(l-=1),n.left.push({startLine:i.left,endLine:i.left+l}),i.left+=s;break;case t.DIFF_INSERT:"\n"===a&&(i.right++,s--),0===(l=s)&&n.left.push({startLine:i.left,endLine:i.left}),"\n"===c&&(l-=1),n.right.push({startLine:i.right,endLine:i.right+l}),i.right+=s;break;default:throw Error("Diff type was not defined.")}}}),n},t.prototype.setCodeMarkers=function(e){void 0===e&&(e={left:[],right:[]});for(var t=[],n={left:[],right:[]},i=0;i<e.left.length;i++){var o={startRow:e.left[i].startLine-1,endRow:e.left[i].endLine,type:"text",className:"codeMarker"};n.left.push(o)}for(var i=0;i<e.right.length;i++){var o={startRow:e.right[i].startLine-1,endRow:e.right[i].endLine,type:"text",className:"codeMarker"};n.right.push(o)}return t[0]=n.left,t[1]=n.right,t},t.prototype.render=function(){var e=this.diff();return s.createElement(a.default,{name:this.props.name,className:this.props.className,focus:this.props.focus,orientation:this.props.orientation,splits:this.props.splits,mode:this.props.mode,theme:this.props.theme,height:this.props.height,width:this.props.width,fontSize:this.props.fontSize,showGutter:this.props.showGutter,onChange:this.onChange,onPaste:this.props.onPaste,onLoad:this.props.onLoad,onScroll:this.props.onScroll,minLines:this.props.minLines,maxLines:this.props.maxLines,readOnly:this.props.readOnly,highlightActiveLine:this.props.highlightActiveLine,showPrintMargin:this.props.showPrintMargin,tabSize:this.props.tabSize,cursorStart:this.props.cursorStart,editorProps:this.props.editorProps,style:this.props.style,scrollMargin:this.props.scrollMargin,setOptions:this.props.setOptions,wrapEnabled:this.props.wrapEnabled,enableBasicAutocompletion:this.props.enableBasicAutocompletion,enableLiveAutocompletion:this.props.enableLiveAutocompletion,value:this.state.value,markers:e})},t.propTypes={cursorStart:r.number,editorProps:r.object,enableBasicAutocompletion:r.bool,enableLiveAutocompletion:r.bool,focus:r.bool,fontSize:r.number,height:r.string,highlightActiveLine:r.bool,maxLines:r.number,minLines:r.number,mode:r.string,name:r.string,className:r.string,onLoad:r.func,onPaste:r.func,onScroll:r.func,onChange:r.func,orientation:r.string,readOnly:r.bool,scrollMargin:r.array,setOptions:r.object,showGutter:r.bool,showPrintMargin:r.bool,splits:r.number,style:r.object,tabSize:r.number,theme:r.string,value:r.array,width:r.string,wrapEnabled:r.bool},t.defaultProps={cursorStart:1,editorProps:{},enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,focus:!1,fontSize:12,height:"500px",highlightActiveLine:!0,maxLines:null,minLines:null,mode:"",name:"ace-editor",onLoad:null,onScroll:null,onPaste:null,onChange:null,orientation:"beside",readOnly:!1,scrollMargin:[0,0,0,0],setOptions:{},showGutter:!0,showPrintMargin:!0,splits:2,style:{},tabSize:4,theme:"github",value:["",""],width:"500px",wrapEnabled:!0},t}(s.Component);t.default=l},1048:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getAceInstance=t.debounce=t.editorEvents=t.editorOptions=void 0,t.editorOptions=["minLines","maxLines","readOnly","highlightActiveLine","tabSize","enableBasicAutocompletion","enableLiveAutocompletion","enableSnippets"],t.editorEvents=["onChange","onFocus","onInput","onBlur","onCopy","onPaste","onSelectionChange","onCursorChange","onScroll","handleOptions","updateRef"],t.getAceInstance=function(){var e;return"undefined"==typeof window?(n.g.window={},e=n(53239),delete n.g.window):window.ace?(e=window.ace).acequire=window.ace.require||window.ace.acequire:e=n(53239),e},t.debounce=function(e,t){var n=null;return function(){var i=this,o=arguments;clearTimeout(n),n=setTimeout(function(){e.apply(i,o)},t)}}},74981:function(e,t,n){"use strict";var i=n(93946);n(63532).default,n(88663).default,t.ZP=i.default},88663:function(e,t,n){"use strict";var i,o=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var s=n(1048),a=(0,s.getAceInstance)(),c=n(53239),l=n(93083),h=n(45697),p=n(67294),u=n(72307),d=n(29208),f=function(e){function t(t){var n=e.call(this,t)||this;return s.editorEvents.forEach(function(e){n[e]=n[e].bind(n)}),n.debounce=s.debounce,n}return o(t,e),t.prototype.isInShadow=function(e){for(var t=e&&e.parentNode;t;){if("[object ShadowRoot]"===t.toString())return!0;t=t.parentNode}return!1},t.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.className,i=t.onBeforeLoad,o=t.mode,r=t.focus,c=t.theme,h=t.fontSize,p=t.value,u=t.defaultValue,f=t.cursorStart,g=t.showGutter,m=t.wrapEnabled,b=t.showPrintMargin,v=t.scrollMargin,y=void 0===v?[0,0,0,0]:v,_=t.keyboardHandler,w=t.onLoad,x=t.commands,S=t.annotations,k=t.markers,C=t.splits;this.editor=a.edit(this.refEditor),this.isInShadow(this.refEditor)&&this.editor.renderer.attachToShadowRoot(),this.editor.setTheme("ace/theme/".concat(c)),i&&i(a);var M=Object.keys(this.props.editorProps),A=new l.Split(this.editor.container,"ace/theme/".concat(c),C);this.editor.env.split=A,this.splitEditor=A.getEditor(0),this.split=A,this.editor.setShowPrintMargin(!1),this.editor.renderer.setShowGutter(!1);var E=this.splitEditor.$options;this.props.debounceChangePeriod&&(this.onChange=this.debounce(this.onChange,this.props.debounceChangePeriod)),A.forEach(function(t,n){for(var i=0;i<M.length;i++)t[M[i]]=e.props.editorProps[M[i]];var r=d(u,n),l=d(p,n,"");t.session.setUndoManager(new a.UndoManager),t.setTheme("ace/theme/".concat(c)),t.renderer.setScrollMargin(y[0],y[1],y[2],y[3]),t.getSession().setMode("ace/mode/".concat(o)),t.setFontSize(h),t.renderer.setShowGutter(g),t.getSession().setUseWrapMode(m),t.setShowPrintMargin(b),t.on("focus",e.onFocus),t.on("blur",e.onBlur),t.on("input",e.onInput),t.on("copy",e.onCopy),t.on("paste",e.onPaste),t.on("change",e.onChange),t.getSession().selection.on("changeSelection",e.onSelectionChange),t.getSession().selection.on("changeCursor",e.onCursorChange),t.session.on("changeScrollTop",e.onScroll),t.setValue(void 0===r?l:r,f);var v=d(S,n,[]),w=d(k,n,[]);t.getSession().setAnnotations(v),w&&w.length>0&&e.handleMarkers(w,t);for(var i=0;i<s.editorOptions.length;i++){var C=s.editorOptions[i];E.hasOwnProperty(C)?t.setOption(C,e.props[C]):e.props[C]&&console.warn("ReaceAce: editor option ".concat(C," was activated but not found. Did you need to import a related tool or did you possibly mispell the option?"))}e.handleOptions(e.props,t),Array.isArray(x)&&x.forEach(function(e){"string"==typeof e.exec?t.commands.bindKey(e.bindKey,e.exec):t.commands.addCommand(e)}),_&&t.setKeyboardHandler("ace/keyboard/"+_)}),n&&(this.refEditor.className+=" "+n),r&&this.splitEditor.focus();var T=this.editor.env.split;T.setOrientation("below"===this.props.orientation?T.BELOW:T.BESIDE),T.resize(!0),w&&w(T)},t.prototype.componentDidUpdate=function(e){var t=this,n=this.props,i=this.editor.env.split;if(n.splits!==e.splits&&i.setSplits(n.splits),n.orientation!==e.orientation&&i.setOrientation("below"===n.orientation?i.BELOW:i.BESIDE),i.forEach(function(i,o){n.mode!==e.mode&&i.getSession().setMode("ace/mode/"+n.mode),n.keyboardHandler!==e.keyboardHandler&&(n.keyboardHandler?i.setKeyboardHandler("ace/keyboard/"+n.keyboardHandler):i.setKeyboardHandler(null)),n.fontSize!==e.fontSize&&i.setFontSize(n.fontSize),n.wrapEnabled!==e.wrapEnabled&&i.getSession().setUseWrapMode(n.wrapEnabled),n.showPrintMargin!==e.showPrintMargin&&i.setShowPrintMargin(n.showPrintMargin),n.showGutter!==e.showGutter&&i.renderer.setShowGutter(n.showGutter);for(var r=0;r<s.editorOptions.length;r++){var a=s.editorOptions[r];n[a]!==e[a]&&i.setOption(a,n[a])}u(n.setOptions,e.setOptions)||t.handleOptions(n,i);var c=d(n.value,o,"");if(i.getValue()!==c){t.silent=!0;var l=i.session.selection.toJSON();i.setValue(c,n.cursorStart),i.session.selection.fromJSON(l),t.silent=!1}var h=d(n.annotations,o,[]);u(h,d(e.annotations,o,[]))||i.getSession().setAnnotations(h);var p=d(n.markers,o,[]);!u(p,d(e.markers,o,[]))&&Array.isArray(p)&&t.handleMarkers(p,i)}),n.className!==e.className){var o=this.refEditor.className.trim().split(" ");e.className.trim().split(" ").forEach(function(e){var t=o.indexOf(e);o.splice(t,1)}),this.refEditor.className=" "+n.className+" "+o.join(" ")}n.theme!==e.theme&&i.setTheme("ace/theme/"+n.theme),n.focus&&!e.focus&&this.splitEditor.focus(),(n.height!==this.props.height||n.width!==this.props.width)&&this.editor.resize()},t.prototype.componentWillUnmount=function(){this.editor.destroy(),this.editor=null},t.prototype.onChange=function(e){if(this.props.onChange&&!this.silent){var t=[];this.editor.env.split.forEach(function(e){t.push(e.getValue())}),this.props.onChange(t,e)}},t.prototype.onSelectionChange=function(e){if(this.props.onSelectionChange){var t=[];this.editor.env.split.forEach(function(e){t.push(e.getSelection())}),this.props.onSelectionChange(t,e)}},t.prototype.onCursorChange=function(e){if(this.props.onCursorChange){var t=[];this.editor.env.split.forEach(function(e){t.push(e.getSelection())}),this.props.onCursorChange(t,e)}},t.prototype.onFocus=function(e){this.props.onFocus&&this.props.onFocus(e)},t.prototype.onInput=function(e){this.props.onInput&&this.props.onInput(e)},t.prototype.onBlur=function(e){this.props.onBlur&&this.props.onBlur(e)},t.prototype.onCopy=function(e){this.props.onCopy&&this.props.onCopy(e)},t.prototype.onPaste=function(e){this.props.onPaste&&this.props.onPaste(e)},t.prototype.onScroll=function(){this.props.onScroll&&this.props.onScroll(this.editor)},t.prototype.handleOptions=function(e,t){for(var n=Object.keys(e.setOptions),i=0;i<n.length;i++)t.setOption(n[i],e.setOptions[n[i]])},t.prototype.handleMarkers=function(e,t){var n=t.getSession().getMarkers(!0);for(var i in n)n.hasOwnProperty(i)&&t.getSession().removeMarker(n[i].id);for(var i in n=t.getSession().getMarkers(!1))n.hasOwnProperty(i)&&t.getSession().removeMarker(n[i].id);e.forEach(function(e){var n=e.startRow,i=e.startCol,o=e.endRow,r=e.endCol,s=e.className,a=e.type,l=e.inFront,h=new c.Range(n,i,o,r);t.getSession().addMarker(h,s,a,void 0!==l&&l)})},t.prototype.updateRef=function(e){this.refEditor=e},t.prototype.render=function(){var e=this.props,t=e.name,n=e.width,i=e.height,o=e.style,s=r({width:n,height:i},o);return p.createElement("div",{ref:this.updateRef,id:t,style:s})},t.propTypes={className:h.string,debounceChangePeriod:h.number,defaultValue:h.arrayOf(h.string),focus:h.bool,fontSize:h.oneOfType([h.number,h.string]),height:h.string,mode:h.string,name:h.string,onBlur:h.func,onChange:h.func,onCopy:h.func,onFocus:h.func,onInput:h.func,onLoad:h.func,onPaste:h.func,onScroll:h.func,orientation:h.string,showGutter:h.bool,splits:h.number,theme:h.string,value:h.arrayOf(h.string),width:h.string,onSelectionChange:h.func,onCursorChange:h.func,onBeforeLoad:h.func,minLines:h.number,maxLines:h.number,readOnly:h.bool,highlightActiveLine:h.bool,tabSize:h.number,showPrintMargin:h.bool,cursorStart:h.number,editorProps:h.object,setOptions:h.object,style:h.object,scrollMargin:h.array,annotations:h.array,markers:h.array,keyboardHandler:h.string,wrapEnabled:h.bool,enableBasicAutocompletion:h.oneOfType([h.bool,h.array]),enableLiveAutocompletion:h.oneOfType([h.bool,h.array]),commands:h.array},t.defaultProps={name:"ace-editor",focus:!1,orientation:"beside",splits:2,mode:"",theme:"",height:"500px",width:"500px",value:[],fontSize:12,showGutter:!0,onChange:null,onPaste:null,onLoad:null,onScroll:null,minLines:null,maxLines:null,readOnly:!1,highlightActiveLine:!0,showPrintMargin:!0,tabSize:4,cursorStart:1,editorProps:{},style:{},scrollMargin:[0,0,0,0],setOptions:{},wrapEnabled:!1,enableBasicAutocompletion:!1,enableLiveAutocompletion:!1},t}(p.Component);t.default=f}}]);