"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{93373:function(e,n,t){t.d(n,{st:function(){return y}});var a=t(82729),r=t(68806),l=t(6812);function o(){let e=(0,a._)(["\n    fragment ViewField on DiagramViewField {\n  id\n  displayName\n  referenceName\n  type\n  nodeType\n  description\n}\n    "]);return o=function(){return e},e}function i(){let e=(0,a._)(["\n    fragment RelationField on DiagramModelRelationField {\n  id\n  relationId\n  type\n  nodeType\n  displayName\n  referenceName\n  fromModelId\n  fromModelName\n  fromModelDisplayName\n  fromColumnId\n  fromColumnName\n  fromColumnDisplayName\n  toModelId\n  toModelName\n  toModelDisplayName\n  toColumnId\n  toColumnName\n  toColumnDisplayName\n  description\n}\n    "]);return i=function(){return e},e}function s(){let e=(0,a._)(["\n    fragment NestedField on DiagramModelNestedField {\n  id\n  nestedColumnId\n  columnPath\n  type\n  displayName\n  referenceName\n  description\n}\n    "]);return s=function(){return e},e}function d(){let e=(0,a._)(["\n    fragment Field on DiagramModelField {\n  id\n  columnId\n  type\n  nodeType\n  displayName\n  referenceName\n  description\n  isPrimaryKey\n  expression\n  aggregation\n  lineage\n  nestedFields {\n    ...NestedField\n  }\n}\n    ",""]);return d=function(){return e},e}function c(){let e=(0,a._)(["\n    query Diagram {\n  diagram {\n    models {\n      id\n      modelId\n      nodeType\n      displayName\n      referenceName\n      sourceTableName\n      refSql\n      cached\n      refreshTime\n      description\n      fields {\n        ...Field\n      }\n      calculatedFields {\n        ...Field\n      }\n      relationFields {\n        ...RelationField\n      }\n    }\n    views {\n      id\n      viewId\n      nodeType\n      displayName\n      description\n      referenceName\n      statement\n      fields {\n        ...ViewField\n      }\n    }\n  }\n}\n    ","\n","\n",""]);return c=function(){return e},e}let u={},m=(0,r.Ps)(o()),p=(0,r.Ps)(i()),f=(0,r.Ps)(s()),x=(0,r.Ps)(d(),f),g=(0,r.Ps)(c(),x,p,m);function y(e){let n={...u,...e};return l.aM(g,n)}},22635:function(e,n,t){t.d(n,{QC:function(){return p},ZZ:function(){return x},Zg:function(){return u}});var a=t(82729),r=t(68806),l=t(50319);function o(){let e=(0,a._)(["\n    mutation PreviewSQL($data: PreviewSQLDataInput!) {\n  previewSql(data: $data)\n}\n    "]);return o=function(){return e},e}function i(){let e=(0,a._)(["\n    mutation GenerateQuestion($data: GenerateQuestionInput!) {\n  generateQuestion(data: $data)\n}\n    "]);return i=function(){return e},e}function s(){let e=(0,a._)(["\n    mutation ModelSubstitute($data: ModelSubstituteInput!) {\n  modelSubstitute(data: $data)\n}\n    "]);return s=function(){return e},e}let d={},c=(0,r.Ps)(o());function u(e){let n={...d,...e};return l.D(c,n)}let m=(0,r.Ps)(i());function p(e){let n={...d,...e};return l.D(m,n)}let f=(0,r.Ps)(s());function x(e){let n={...d,...e};return l.D(f,n)}},33454:function(e,n,t){t.d(n,{$i:function(){return b},Gd:function(){return x},Nz:function(){return y},jn:function(){return j}});var a=t(82729),r=t(68806),l=t(6812),o=t(50319);function i(){let e=(0,a._)(["\n    fragment SqlPair on SqlPair {\n  id\n  projectId\n  sql\n  question\n  createdAt\n  updatedAt\n}\n    "]);return i=function(){return e},e}function s(){let e=(0,a._)(["\n    query SqlPairs {\n  sqlPairs {\n    ...SqlPair\n  }\n}\n    ",""]);return s=function(){return e},e}function d(){let e=(0,a._)(["\n    mutation CreateSqlPair($data: CreateSqlPairInput!) {\n  createSqlPair(data: $data) {\n    ...SqlPair\n  }\n}\n    ",""]);return d=function(){return e},e}function c(){let e=(0,a._)(["\n    mutation UpdateSqlPair($where: SqlPairWhereUniqueInput!, $data: UpdateSqlPairInput!) {\n  updateSqlPair(where: $where, data: $data) {\n    ...SqlPair\n  }\n}\n    ",""]);return c=function(){return e},e}function u(){let e=(0,a._)(["\n    mutation DeleteSqlPair($where: SqlPairWhereUniqueInput!) {\n  deleteSqlPair(where: $where)\n}\n    "]);return u=function(){return e},e}let m={},p=(0,r.Ps)(i()),f=(0,r.Ps)(s(),p);function x(e){let n={...m,...e};return l.aM(f,n)}let g=(0,r.Ps)(d(),p);function y(e){let n={...m,...e};return o.D(g,n)}let h=(0,r.Ps)(c(),p);function b(e){let n={...m,...e};return o.D(h,n)}let v=(0,r.Ps)(u());function j(e){let n={...m,...e};return o.D(v,n)}},59976:function(e,n,t){t.d(n,{Z:function(){return c}});var a=t(85893),r=t(67294),l=t(42187),o=t(19521),i=t(51228),s=t.n(i);let d=(0,o.ZP)(l.default).withConfig({displayName:"ErrorCollapse__StyledCollapse",componentId:"sc-96b0dd67-0"})([".ant-collapse-item{> .ant-collapse-header{user-select:none;color:var(--gray-7);padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;.ant-collapse-arrow{margin-right:8px;}}> .ant-collapse-content .ant-collapse-content-box{color:var(--gray-7);padding:4px 0 0 0;}}"]);function c(e){let{message:n,className:t,defaultActive:o}=e,[i,c]=(0,r.useState)(o?["1"]:[]);return(0,a.jsx)(d,{className:t,ghost:!0,activeKey:i,onChange:e=>c(e),expandIcon:e=>{let{isActive:n}=e;return(0,a.jsx)(s(),{rotate:n?90:0})},children:(0,a.jsx)(l.default.Panel,{header:"Show error messages",children:(0,a.jsx)("pre",{className:"text-sm mb-0 pl-5",style:{whiteSpace:"pre-wrap"},children:n})},"1")})}},20411:function(e,n,t){t.d(n,{iD:function(){return g}});var a=t(85893),r=t(67294),l=t(19521),o=t(80002),i=t(21367),s=t(51903),d=t.n(s),c=t(72869),u=t.n(c),m=t(33190);t(84882);let p=l.ZP.div.withConfig({displayName:"BaseCodeBlock__Block",componentId:"sc-b28653c8-0"})(["position:relative;white-space:pre;font-size:13px;border:1px var(--gray-4) solid;border-radius:4px;font-family:'Source Code Pro',monospace;user-select:text;cursor:text;&:focus{outline:none;}"," .adm-code-wrap{"," "," user-select:text;}.adm-code-line{display:block;user-select:text;&-number{user-select:none;display:inline-block;min-width:14px;text-align:right;margin-right:1em;color:var(--gray-6);font-weight:700;font-size:12px;}}"],e=>e.inline?"\n      display: inline;\n      border: none;\n      background: transparent !important;\n      padding: 0;\n      * { display: inline !important; }\n    ":"\n    background: ".concat(e.backgroundColor||"var(--gray-1)"," !important;\n    padding: 8px;\n  "),e=>e.inline?"":"overflow: auto;",e=>e.maxHeight?"max-height: ".concat(e.maxHeight,"px;"):""),f=(0,l.ZP)(o.default.Text).withConfig({displayName:"BaseCodeBlock__CopyText",componentId:"sc-b28653c8-1"})(["position:absolute;top:0;right:",";font-size:0;button{background:var(--gray-1) !important;}.ant-typography-copy{font-size:12px;}.ant-btn:not(:hover){color:var(--gray-8);}"],e=>e.$hasVScrollbar?"20px":"0"),x=e=>{let n="ace-tomorrow";if(!document.getElementById(n)){let t=document.createElement("style");t.id=n,document.head.appendChild(t),t.appendChild(document.createTextNode(e))}},g=e=>function(n){let{code:t,copyable:l,maxHeight:o,inline:s,loading:c,showLineNumbers:g,backgroundColor:y,onCopy:h}=n,{ace:b}=window,{Tokenizer:v}=b.require("ace/tokenizer"),j=new v(new e().getRules()),S=(0,r.useRef)(null),[N,C]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let{cssText:e}=b.require("ace/theme/tomorrow");x(e)},[]),(0,r.useEffect)(()=>{let e=S.current;e&&C(e.scrollHeight>e.clientHeight)},[t]);let I=(t||"").split("\n").map((e,n)=>{let t=j.getLineTokens(e).tokens.map((e,n)=>{let t=e.type.split(".").map(e=>"ace_".concat(e));return(0,a.jsx)("span",{className:t.join(" "),children:e.value},n)});return(0,a.jsxs)("span",{className:"adm-code-line ace_line",children:[g&&(0,a.jsx)("span",{className:"adm-code-line-number",children:n+1}),t]},"".concat(e,"-").concat(n))});return(0,a.jsx)(p,{className:"ace_editor ace-tomorrow adm_code-block",maxHeight:o,inline:s,backgroundColor:y,tabIndex:0,onKeyDown:e=>{if((e.metaKey||e.ctrlKey)&&"a"===e.key){e.preventDefault();let n=window.getSelection(),t=document.createRange();t.selectNodeContents(e.currentTarget.querySelector(".adm-code-wrap")||e.currentTarget),null==n||n.removeAllRanges(),null==n||n.addRange(t)}},children:(0,a.jsx)(m.gb,{spinning:c,children:(0,a.jsxs)("div",{className:"adm-code-wrap",ref:S,children:[I,l&&(0,a.jsx)(f,{$hasVScrollbar:N,copyable:{onCopy:h,icon:[(0,a.jsx)(i.default,{icon:(0,a.jsx)(u(),{}),size:"small",style:{backgroundColor:"transparent"}},"copy-icon"),(0,a.jsx)(i.default,{icon:(0,a.jsx)(d(),{className:"green-6"}),size:"small"},"copied-icon")],text:t},children:t})]})})})}},63284:function(e,n,t){t.r(n);var a=t(85893),r=t(20411);n.default=e=>{let{ace:n}=window,{SqlHighlightRules:t}=n.require("ace/mode/sql_highlight_rules"),l=(0,r.iD)(t);return(0,a.jsx)(l,{...e})}},91397:function(e,n,t){t.d(n,{Z:function(){return S}});var a=t(85893),r=t(67294),l=t(80002),o=t(21367),i=t(62819),s=t(19521),d=t(70474),c=t(17579),u=t(47037),m=t.n(u);let p=e=>["boolean","object"].includes(typeof e)?JSON.stringify(e):e,f=(e,n)=>e.map((e,t)=>{let a={};return a.key=t,e.forEach((e,t)=>{a[n[t].dataIndex]=p(e)}),a});function x(e){let{columns:n=[],data:t=[],loading:l,locale:o}=e,i=!!n.length,s=(0,r.useMemo)(()=>n.reduce((e,n)=>e+(m()(n.titleText||n.title)?16*(n.titleText||n.title).length:100),0),[n]),d=(0,r.useMemo)(()=>n.map(e=>({...e,ellipsis:!0})),[n]),u=(0,r.useMemo)(()=>f(t,n),[t]);return(0,a.jsx)(c.Z,{className:"ph-no-capture ".concat(i?"ant-table-has-header":""),showHeader:i,dataSource:u,columns:d,pagination:!1,size:"small",scroll:{y:280,x:s},loading:l,locale:o})}var g=t(94638);let{Text:y}=l.default,h=s.ZP.div.withConfig({displayName:"PreviewData__StyledCell",componentId:"sc-81799b7d-0"})(["position:relative;.copy-icon{position:absolute;top:50%;right:0;transform:translateY(-50%);opacity:0;transition:opacity 0.3s;}.ant-typography-copy{margin:-4px;}&:hover .copy-icon{opacity:1;}"]),b=(0,r.memo)(e=>{let{name:n,type:t}=e,r=(0,d.u)({type:t},{title:t});return(0,a.jsxs)(a.Fragment,{children:[r,(0,a.jsx)(y,{title:n,className:"ml-1",children:n})]})}),v=(0,r.memo)(e=>{let{text:n,copyable:t}=e;return(0,a.jsxs)(h,{className:"text-truncate",children:[(0,a.jsx)("span",{title:n,className:"text text-container",children:n}),t&&(0,a.jsx)(o.default,{size:"small",className:"copy-icon",children:(0,a.jsx)(y,{copyable:{text:n,tooltips:!1},className:"gray-8"})})]})}),j=(e,n)=>{let{copyable:t}=n;return e.map(e=>{let{name:n,type:r}=e;return{dataIndex:n,titleText:n,key:n,ellipsis:!0,title:(0,a.jsx)(b,{name:n,type:r}),render:e=>(0,a.jsx)(v,{text:e,copyable:t}),onCell:()=>({style:{lineHeight:"24px"}})}})};function S(e){let{previewData:n,loading:t,error:l,locale:o,copyable:s=!0}=e,d=(0,r.useMemo)(()=>(null==n?void 0:n.columns)&&j(n.columns,{copyable:s}),[null==n?void 0:n.columns,s]),c=l&&l.message;if(!t&&c){let{message:e,shortMessage:n}=(0,g.Bx)(l);return(0,a.jsx)(i.default,{message:n,description:e,type:"error",showIcon:!0})}return(0,a.jsx)(x,{columns:d,data:(null==n?void 0:n.data)||[],loading:t,locale:o})}},4791:function(e,n,t){t.d(n,{At:function(){return C},Lb:function(){return I},aV:function(){return q},iI:function(){return P},px:function(){return E},rt:function(){return k},s_:function(){return w},uu:function(){return T}});var a=t(85893);t(67294);var r=t(19521),l=t(69371),o=t(59046),i=t(84908),s=t(93181),d=t.n(s),c=t(7337),u=t.n(c),m=t(80112),p=t.n(m),f=t(92870),x=t.n(f),g=t(31499),y=t.n(g),h=t(40492),b=t.n(h),v=t(32815),j=t(26123);let S=(0,r.ZP)(l.default).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),N=e=>n=>{let{children:t,onMenuEnter:r,onDropdownVisibleChange:l}=n,i=e(n);return(0,a.jsx)(o.default,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:(0,a.jsx)(S,{onClick:e=>e.domEvent.stopPropagation(),items:i,onMouseEnter:r}),onVisibleChange:l,children:t})},C=N(e=>{let{onMoreClick:n}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{className:"mr-2"}),"Update Columns"]}),key:i.dI.UPDATE_COLUMNS,onClick:()=>n(i.dI.UPDATE_COLUMNS)},{label:(0,a.jsx)(j.ZQ,{onConfirm:()=>n(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),I=N(e=>{let{onMoreClick:n}=e;return[{label:(0,a.jsx)(j.e0,{onConfirm:()=>n(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),w=N(e=>{let{onMoreClick:n,data:t}=e,{nodeType:r}=t,l={[i.QZ.CALCULATED_FIELD]:j.pn,[i.QZ.RELATION]:j.ck}[r]||j.pn;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>n(i.dI.EDIT)},{label:(0,a.jsx)(l,{onConfirm:()=>n(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),E=N(e=>{let{onMoreClick:n,isSupportCached:t}=e;return[t&&{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b(),{className:"mr-2"}),"Cache settings"]}),key:i.dI.CACHE_SETTINGS,onClick:()=>n(i.dI.CACHE_SETTINGS)},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u(),{className:"mr-2"}),t?"Refresh all caches":"Refresh all"]}),key:i.dI.REFRESH,onClick:()=>n(i.dI.REFRESH)}].filter(Boolean)}),k=N(e=>{let{onMoreClick:n,isHideLegend:t,isSupportCached:r}=e;return[{label:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x(),{className:"mr-2"}),"Show categories"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p(),{className:"mr-2"}),"Hide categories"]}),key:i.dI.HIDE_CATEGORY,onClick:()=>n(i.dI.HIDE_CATEGORY)},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u(),{className:"mr-2"}),r?"Refresh cache":"Refresh"]}),key:i.dI.REFRESH,onClick:()=>n(i.dI.REFRESH)},{label:(0,a.jsx)(j.mU,{onConfirm:()=>n(i.dI.DELETE)}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),P=N(e=>{let{onMoreClick:n,data:t}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x(),{className:"mr-2"}),"View"]}),key:i.dI.VIEW_SQL_PAIR,onClick:()=>n({type:i.dI.VIEW_SQL_PAIR,data:t})},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>n({type:i.dI.EDIT,data:t})},{label:(0,a.jsx)(j.pv,{onConfirm:()=>n({type:i.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),T=N(e=>{let{onMoreClick:n,data:t}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x(),{className:"mr-2"}),"View"]}),key:i.dI.VIEW_INSTRUCTION,onClick:()=>n({type:i.dI.VIEW_INSTRUCTION,data:t})},{label:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{className:"mr-2"}),"Edit"]}),key:i.dI.EDIT,onClick:()=>n({type:i.dI.EDIT,data:t})},{label:(0,a.jsx)(j.py,{onConfirm:()=>n({type:i.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:i.dI.DELETE,onClick:e=>{let{domEvent:n}=e;return n.stopPropagation()}}]}),q=N(e=>{let{onMoreClick:n,data:t}=e;return[{label:"Adjust steps",icon:(0,a.jsx)(v.Wi,{}),disabled:!t.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>n({type:i.dI.ADJUST_STEPS,data:t})},{label:"Adjust SQL",icon:(0,a.jsx)(y(),{className:"text-base"}),disabled:!t.sql,key:"adjust-sql",onClick:()=>n({type:i.dI.ADJUST_SQL,data:t})}]})},84882:function(e,n,t){var a=t(74981);t(89899),t(90252),t(42557),t(82679),n.Z=a.ZP},48076:function(e,n,t){t.d(n,{Z:function(){return p}});var a=t(85893),r=t(90512),l=t(67294),o=t(19521),i=t(84882),s=t(56144),d=t(88872);let c=o.ZP.div.withConfig({displayName:"SQLEditor__Wrapper",componentId:"sc-c8b626da-0"})(["transition:all 0.3s cubic-bezier(0.645,0.045,0.355,1);&:hover{border-color:var(--geekblue-5) !important;}&.adm-markdown-editor-error{border-color:var(--red-5) !important;.adm-markdown-editor-length{color:var(--red-5) !important;}}&:not(.adm-markdown-editor-error).adm-markdown-editor-focused{border-color:var(--geekblue-5) !important;box-shadow:0 0 0 2px rgba(47,84,235,0.2);}&.adm-markdown-editor-focused.adm-markdown-editor-error{borer-color:var(--red-4) !important;box-shadow:0 0 0 2px rgba(255,77,79,0.2);}"]),u=o.ZP.div.withConfig({displayName:"SQLEditor__Toolbar",componentId:"sc-c8b626da-1"})(["color:var(--gray-8);background-color:var(--gray-3);border-bottom:1px solid var(--gray-5);height:32px;padding:4px 8px;border-radius:4px 4px 0px 0px;"]),m=()=>{let{ace:e}=window;return e?e.require("ace/ext/language_tools"):null};function p(e){let{value:n,onChange:t,autoFocus:o,autoComplete:p,toolbar:f}=e,x=(0,l.useRef)(null),[g,y]=(0,l.useState)(!1),{status:h}=(0,l.useContext)(s.FormItemInputContext),b=(0,d.ZP)({includeColumns:!0,skip:!p}),v=()=>{let e=m();null==e||e.setCompleters([e.keyWordCompleter,e.snippetCompleter,e.textCompleter])};(0,l.useEffect)(()=>{if(v(),!p||0===b.length)return;let e=m();return null==e||e.addCompleter({getCompletions:(e,n,t,a,r)=>{r(null,b)}}),()=>v()},[g,p,b]);let[j,S]=(0,l.useState)(n||"");return(0,a.jsxs)(c,{ref:x,className:(0,r.Z)("border border-gray-5 rounded overflow-hidden",h?"adm-markdown-editor-".concat(h):"",g?"adm-markdown-editor-focused":""),tabIndex:-1,children:[!!f&&(0,a.jsx)(u,{children:f}),(0,a.jsx)(i.Z,{mode:"sql",width:"100%",height:"300px",fontSize:14,theme:"tomorrow",value:n||j,onChange:e=>{S(e),null==t||t(e)},onFocus:()=>y(!0),onBlur:()=>y(!1),name:"sql_editor",editorProps:{$blockScrolling:!0},enableLiveAutocompletion:!0,enableBasicAutocompletion:!0,showPrintMargin:!1,focus:o})]})}},49430:function(e,n,t){t.d(n,{Z:function(){return L}});var a=t(85893),r=t(67294),l=t(19521),o=t(13518),i=t(21367),s=t(69828),d=t(80002),c=t(98885),u=t(62819),m=t(87021),p=t(18310),f=t.n(p),x=t(99031),g=t.n(x),y=t(59530),h=t(84908),b=t(31069),v=t(90883),j=t(48076),S=t(94638),N=t(32235),C=t(59976),I=t(91397),w=t(22635),E=t(46140);let k=e=>{let{dataSource:n}=e;if(!n)return null;let t=(0,b.Ur)(n),r=(0,b.J9)(n);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("span",{className:"d-flex align-center gx-2",children:[(0,a.jsx)("img",{src:t,alt:"logo",width:"20",height:"20"}),r]})})},P=e=>!(null==e?void 0:e.sampleDataset)&&(null==e?void 0:e.type)!==E.ri.DUCKDB;function T(e){let{visible:n,defaultValue:t,onSubmit:l,onClose:i}=e,d=(0,b.J9)(null==t?void 0:t.dataSource)||"data source",[c,m]=(0,w.ZZ)(),p=(0,r.useMemo)(()=>m.error?{...(0,S.Bx)(m.error),shortMessage:"Invalid ".concat(d," SQL syntax")}:null,[m.error]),[f]=o.Z.useForm(),x=()=>{f.resetFields(),m.reset()},g=async()=>{f.validateFields().then(async e=>{var n;let t=await c({variables:{data:{sql:e.dialectSql}}});await l(null===(n=t.data)||void 0===n?void 0:n.modelSubstitute),i()}).catch(console.error)},h=m.loading;return(0,a.jsxs)(s.Z,{title:"Import from ".concat(d," SQL"),centered:!0,closable:!0,confirmLoading:h,destroyOnClose:!0,maskClosable:!1,onCancel:i,onOk:g,okText:"Convert",visible:n,width:600,cancelButtonProps:{disabled:h},afterClose:()=>x(),children:[(0,a.jsx)(o.Z,{form:f,layout:"vertical",children:(0,a.jsx)(o.Z.Item,{name:"dialectSql",label:"SQL statement",rules:[{required:!0,message:y.q.IMPORT_DATA_SOURCE_SQL.SQL.REQUIRED}],children:(0,a.jsx)(j.Z,{toolbar:(0,a.jsx)(k,{dataSource:null==t?void 0:t.dataSource}),autoFocus:!0})})}),!!p&&(0,a.jsx)(u.default,{showIcon:!0,type:"error",message:p.shortMessage,description:(0,a.jsx)(C.Z,{message:p.message})})]})}var q=t(10102);let _=(0,l.ZP)(o.Z).withConfig({displayName:"QuestionSQLPairModal__StyledForm",componentId:"sc-fbd18db5-0"})([".adm-question-form-item > div > label{width:100%;}"]),D=e=>{let{dataSource:n,onClick:t}=e,r=(0,b.J9)(n);return(0,a.jsxs)("div",{className:"d-flex justify-space-between align-center px-1",children:[(0,a.jsxs)("span",{className:"d-flex align-center gx-2",children:[(0,a.jsx)(m.T,{size:16}),"Wren SQL"]}),(0,a.jsxs)(i.default,{className:"px-0",type:"link",size:"small",onClick:t,children:[(0,a.jsx)(g(),{}),"Import from ",r," SQL"]})]})};function L(e){var n;let{defaultValue:t,formMode:l,loading:m,onClose:p,onSubmit:x,visible:g,payload:b}=e,E=l===h.SD.CREATE||(null==b?void 0:b.isCreateMode),k=(0,v.Z)(),{data:L}=(0,q.ks)(),F=null==L?void 0:L.settings,Z=(0,r.useMemo)(()=>{var e;return{isSupportSubstitute:P(null==F?void 0:F.dataSource),type:null==F?void 0:null===(e=F.dataSource)||void 0===e?void 0:e.type}},[null==F?void 0:F.dataSource]),[Q]=o.Z.useForm(),[R,M]=(0,r.useState)(null),[A,U]=(0,r.useState)(!1),[B,$]=(0,r.useState)(!1),[z,H]=(0,r.useState)(!1),[V,O]=(0,r.useState)(!1),[W,G]=(0,w.Zg)(),[J]=(0,w.QC)(),K=o.Z.useWatch("sql",Q);(0,r.useEffect)(()=>{g&&Q.setFieldsValue({question:null==t?void 0:t.question,sql:null==t?void 0:t.sql})},[g,t]);let Y=()=>{G.reset(),O(!1),M(null),Q.resetFields()},X=async()=>{await W({variables:{data:{sql:K,limit:1,dryRun:!0}}})},ee=e=>{let n=(0,S.Bx)(e);M({...n,shortMessage:"Invalid SQL syntax"}),console.error(n)},en=async()=>{M(null),U(!0);try{await X(),O(!0),await W({variables:{data:{sql:K,limit:50}}})}catch(e){ee(e)}finally{U(!1)}},et=async()=>{H(!0);let{data:e}=await J({variables:{data:{sql:K}}});Q.setFieldsValue({question:(null==e?void 0:e.generateQuestion)||""}),H(!1)},ea=m||B,er=!K;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(s.Z,{title:"".concat(E?"Add":"Update"," question-SQL pair"),centered:!0,closable:!0,confirmLoading:ea,destroyOnClose:!0,maskClosable:!1,onCancel:p,visible:g,width:640,cancelButtonProps:{disabled:ea},okButtonProps:{disabled:G.loading},afterClose:()=>Y(),footer:(0,a.jsxs)("div",{className:"d-flex justify-space-between align-center",children:[(0,a.jsxs)("div",{className:"text-sm ml-2 d-flex justify-space-between align-center",style:{width:300},children:[(0,a.jsx)(f(),{className:"mr-2 text-sm gray-7"}),(0,a.jsxs)(d.default.Text,{type:"secondary",className:"text-sm gray-7 text-left",children:["The SQL statement used here follows ",(0,a.jsx)("b",{children:"Wren SQL"}),", which is based on ANSI SQL and optimized for Wren AI."," ",(0,a.jsx)(d.default.Link,{type:"secondary",href:"https://docs.getwren.ai/oss/guide/home/<USER>",target:"_blank",rel:"noopener noreferrer",children:"Learn more about the syntax."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.default,{onClick:p,children:"Cancel"}),(0,a.jsx)(i.default,{type:"primary",onClick:()=>{M(null),$(!0),O(!1),Q.validateFields().then(async e=>{try{await X(),await x({data:e,id:null==t?void 0:t.id}),p()}catch(e){ee(e)}finally{$(!1)}}).catch(e=>{$(!1),console.error(e)})},loading:ea,children:"Submit"})]})]}),children:[(0,a.jsxs)(_,{form:Q,preserve:!1,layout:"vertical",children:[(0,a.jsx)(o.Z.Item,{className:"adm-question-form-item",label:(0,a.jsxs)("div",{className:"d-flex justify-space-between",style:{width:"100%"},children:[(0,a.jsx)("span",{children:"Question"}),(0,a.jsxs)("div",{className:"gray-8 text-sm",children:["Let AI create a matching question for this SQL statement.",(0,a.jsx)(i.default,{className:"ml-2",size:"small",loading:z,onClick:et,disabled:er,children:(0,a.jsx)("span",{className:"text-sm",children:"Generate question"})})]})]}),name:"question",required:!0,rules:[{validator:(0,N.bI)(y.q.SQL_PAIR.QUESTION)}],children:(0,a.jsx)(c.default,{})}),(0,a.jsx)(o.Z.Item,{label:"SQL statement",name:"sql",required:!0,rules:[{required:!0,message:y.q.SQL_PAIR.SQL.REQUIRED}],children:(0,a.jsx)(j.Z,{toolbar:Z.isSupportSubstitute&&(0,a.jsx)(D,{dataSource:Z.type,onClick:()=>k.openModal({dataSource:Z.type})}),autoComplete:!0,autoFocus:!0})})]}),(0,a.jsxs)("div",{className:"my-3",children:[(0,a.jsx)(d.default.Text,{className:"d-block gray-7 mb-2",children:"Data preview (50 rows)"}),(0,a.jsx)(i.default,{onClick:en,loading:A,disabled:er,children:"Preview data"}),V&&(0,a.jsx)("div",{className:"my-3",children:(0,a.jsx)(I.Z,{loading:A,previewData:null==G?void 0:null===(n=G.data)||void 0===n?void 0:n.previewSql,copyable:!1})})]}),!!R&&(0,a.jsx)(u.default,{showIcon:!0,type:"error",message:R.shortMessage,description:(0,a.jsx)(C.Z,{message:R.message})})]}),Z.isSupportSubstitute&&(0,a.jsx)(T,{...k.state,onClose:k.closeModal,onSubmit:async e=>{Q.setFieldsValue({sql:e})}})]})}},88872:function(e,n,t){t.d(n,{ZP:function(){return m},fV:function(){return c}});var a=t(67294),r=t(48403),l=t.n(r),o=t(93373),i=t(50608);let s=e=>['<div style="max-width: 380px;">','<b style="display: block;color: var(--gray-8); padding: 0 4px 4px;">'.concat(e.referenceName,"</b>"),e.description?'<div style="color: var(--gray-7); padding: 4px 4px 0; border-top: 1px var(--gray-4) solid;">'.concat(e.description,"</div>"):null,"</div>"].filter(Boolean).join(""),d=e=>/[^a-z0-9_]/.test(e)||/^\d/.test(e),c=e=>({id:"".concat(e.id,"-").concat(e.referenceName),label:e.displayName,value:e.referenceName,nodeType:l()(e.nodeType),meta:e.parent?"".concat(e.displayName,".").concat(e.displayName):void 0,icon:(0,i.i)({nodeType:e.nodeType,type:e.type},{className:"gray-8 mr-2"})}),u=e=>({caption:e.parent?"".concat(e.parent.displayName,".").concat(e.displayName):e.displayName,value:d(e.referenceName)?'"'.concat(e.referenceName,'"'):e.referenceName,meta:e.nodeType.toLowerCase(),score:e.parent?1:10,docHTML:s(e)});function m(e){let{includeColumns:n,skip:t}=e,{data:r}=(0,o.st)({skip:t}),l=e.convertor||u;return(0,a.useMemo)(()=>[...(null==r?void 0:r.diagram.models)||[],...(null==r?void 0:r.diagram.views)||[]].reduce((e,t)=>(e.push(l(t)),n&&t.fields.forEach(n=>{e.push(l({...n,parent:t}))}),e),[]),[null==r?void 0:r.diagram,n])}}}]);