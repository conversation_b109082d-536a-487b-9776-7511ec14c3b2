"use strict";exports.id=10,exports.ids=[10],exports.modules={46810:(e,t,a)=>{a.d(t,{z:()=>s.a});var n=a(53800),s=a.n(n)},23647:(e,t,a)=>{a.d(t,{JX:()=>s.a,X2:()=>l.a,ZT:()=>o.a});var n=a(28518),s=a.n(n),r=a(27050),l=a.n(r),i=a(58168),o=a.n(i)},89688:(e,t,a)=>{a.d(t,{I:()=>l.a,l:()=>s.a});var n=a(16190),s=a.n(n),r=a(30675),l=a.n(r)},35629:(e,t,a)=>{a.d(t,{Layout:()=>s.a});var n=a(45417),s=a.n(n)},82103:(e,t,a)=>{var n,s,r,l,i,o,d,c,m,u,E,I,N,h,p,R,g,S,T,f,D;a.d(t,{DN:()=>i,Ii:()=>n,Jq:()=>u,L6:()=>l,Tj:()=>N,Vr:()=>D,Y0:()=>R,a_:()=>S,fz:()=>f,i_:()=>m,j:()=>s,oB:()=>r,oX:()=>o,ri:()=>c,uT:()=>h,uo:()=>E,wR:()=>T,y9:()=>I}),function(e){e.GENERATE_SQL="GENERATE_SQL",e.GENERATE_VEGA_CHART="GENERATE_VEGA_CHART",e.RUN_SQL="RUN_SQL"}(n||(n={})),function(e){e.CORRECTING="CORRECTING",e.FAILED="FAILED",e.FINISHED="FINISHED",e.GENERATING="GENERATING",e.PLANNING="PLANNING",e.SEARCHING="SEARCHING",e.STOPPED="STOPPED",e.UNDERSTANDING="UNDERSTANDING"}(s||(s={})),function(e){e.GENERAL="GENERAL",e.MISLEADING_QUERY="MISLEADING_QUERY",e.TEXT_TO_SQL="TEXT_TO_SQL"}(r||(r={})),function(e){e.FRI="FRI",e.MON="MON",e.SAT="SAT",e.SUN="SUN",e.THU="THU",e.TUE="TUE",e.WED="WED"}(l||(l={})),function(e){e.FAILED="FAILED",e.FETCHING="FETCHING",e.FINISHED="FINISHED",e.GENERATING="GENERATING",e.STOPPED="STOPPED"}(i||(i={})),function(e){e.AREA="AREA",e.BAR="BAR",e.GROUPED_BAR="GROUPED_BAR",e.LINE="LINE",e.MULTI_LINE="MULTI_LINE",e.PIE="PIE",e.STACKED_BAR="STACKED_BAR"}(o||(o={})),function(e){e.AREA="AREA",e.BAR="BAR",e.GROUPED_BAR="GROUPED_BAR",e.LINE="LINE",e.MULTI_LINE="MULTI_LINE",e.NUMBER="NUMBER",e.PIE="PIE",e.STACKED_BAR="STACKED_BAR",e.TABLE="TABLE"}(d||(d={})),function(e){e.BIG_QUERY="BIG_QUERY",e.CLICK_HOUSE="CLICK_HOUSE",e.DUCKDB="DUCKDB",e.MSSQL="MSSQL",e.MYSQL="MYSQL",e.POSTGRES="POSTGRES",e.SNOWFLAKE="SNOWFLAKE",e.TRINO="TRINO"}(c||(c={})),function(e){e.ABS="ABS",e.AVG="AVG",e.CBRT="CBRT",e.CEIL="CEIL",e.CEILING="CEILING",e.COUNT="COUNT",e.COUNT_IF="COUNT_IF",e.EXP="EXP",e.FLOOR="FLOOR",e.LENGTH="LENGTH",e.LN="LN",e.LOG10="LOG10",e.MAX="MAX",e.MIN="MIN",e.REVERSE="REVERSE",e.ROUND="ROUND",e.SIGN="SIGN",e.SUM="SUM"}(m||(m={})),function(e){e.CALCULATED_FIELD="CALCULATED_FIELD",e.FIELD="FIELD",e.METRIC="METRIC",e.MODEL="MODEL",e.RELATION="RELATION",e.VIEW="VIEW"}(u||(u={})),function(e){e.DATASOURCE_SAVED="DATASOURCE_SAVED",e.NOT_STARTED="NOT_STARTED",e.ONBOARDING_FINISHED="ONBOARDING_FINISHED",e.WITH_SAMPLE_DATASET="WITH_SAMPLE_DATASET"}(E||(E={})),function(e){e.DE="DE",e.EN="EN",e.ES="ES",e.FR="FR",e.JA="JA",e.KO="KO",e.PT="PT",e.RU="RU",e.ZH_CN="ZH_CN",e.ZH_TW="ZH_TW"}(I||(I={})),function(e){e.FAILED="FAILED",e.FINISHED="FINISHED",e.GENERATING="GENERATING",e.NOT_STARTED="NOT_STARTED"}(N||(N={})),function(e){e.MANY_TO_ONE="MANY_TO_ONE",e.ONE_TO_MANY="ONE_TO_MANY",e.ONE_TO_ONE="ONE_TO_ONE"}(h||(h={})),function(e){e.LLM="LLM",e.SQL_PAIR="SQL_PAIR",e.VIEW="VIEW"}(p||(p={})),function(e){e.ECOMMERCE="ECOMMERCE",e.HR="HR",e.MUSIC="MUSIC",e.NBA="NBA"}(R||(R={})),function(e){e.CUSTOM="CUSTOM",e.DAILY="DAILY",e.NEVER="NEVER",e.WEEKLY="WEEKLY"}(g||(g={})),function(e){e.DELETED_COLUMNS="DELETED_COLUMNS",e.DELETED_TABLES="DELETED_TABLES",e.MODIFIED_COLUMNS="MODIFIED_COLUMNS"}(S||(S={})),function(e){e.IN_PROGRESS="IN_PROGRESS",e.SYNCRONIZED="SYNCRONIZED",e.UNSYNCRONIZED="UNSYNCRONIZED"}(T||(T={})),function(e){e.APPLY_SQL="APPLY_SQL",e.REASONING="REASONING"}(f||(f={})),function(e){e.FAILED="FAILED",e.FETCHING_DATA="FETCHING_DATA",e.FINISHED="FINISHED",e.INTERRUPTED="INTERRUPTED",e.NOT_STARTED="NOT_STARTED",e.PREPROCESSING="PREPROCESSING",e.STREAMING="STREAMING"}(D||(D={}))},31795:(e,t,a)=>{a.d(t,{Cu:()=>o,Jg:()=>R,Lh:()=>I,kI:()=>h,t_:()=>f,tl:()=>u,v5:()=>A,vD:()=>c,xY:()=>S,xv:()=>l});var n=a(29114);let s={},r=(0,n.gql)`
    mutation StartSampleDataset($data: SampleDatasetInput!) {
  startSampleDataset(data: $data)
}
    `;function l(e){let t={...s,...e};return n.useMutation(r,t)}let i=(0,n.gql)`
    query ListDataSourceTables {
  listDataSourceTables {
    name
    columns {
      name
      type
    }
  }
}
    `;function o(e){let t={...s,...e};return n.useQuery(i,t)}let d=(0,n.gql)`
    query AutoGeneratedRelations {
  autoGenerateRelation {
    id
    displayName
    referenceName
    relations {
      fromModelId
      fromModelReferenceName
      fromColumnId
      fromColumnReferenceName
      toModelId
      toModelReferenceName
      toColumnId
      toColumnReferenceName
      type
      name
    }
  }
}
    `;function c(e){let t={...s,...e};return n.useQuery(d,t)}let m=(0,n.gql)`
    mutation SaveDataSource($data: DataSourceInput!) {
  saveDataSource(data: $data) {
    type
    properties
  }
}
    `;function u(e){let t={...s,...e};return n.useMutation(m,t)}let E=(0,n.gql)`
    mutation UpdateDataSource($data: UpdateDataSourceInput!) {
  updateDataSource(data: $data) {
    type
    properties
  }
}
    `;function I(e){let t={...s,...e};return n.useMutation(E,t)}let N=(0,n.gql)`
    mutation SaveTables($data: SaveTablesInput!) {
  saveTables(data: $data)
}
    `;function h(e){let t={...s,...e};return n.useMutation(N,t)}let p=(0,n.gql)`
    mutation SaveRelations($data: SaveRelationInput!) {
  saveRelations(data: $data)
}
    `;function R(e){let t={...s,...e};return n.useMutation(p,t)}let g=(0,n.gql)`
    query SchemaChange {
  schemaChange {
    deletedTables {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    deletedColumns {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    modifiedColumns {
      sourceTableName
      displayName
      columns {
        sourceColumnName
        displayName
        type
      }
      relationships {
        displayName
        referenceName
      }
      calculatedFields {
        displayName
        referenceName
        type
      }
    }
    lastSchemaChangeTime
  }
}
    `;function S(e){let t={...s,...e};return n.useQuery(g,t)}let T=(0,n.gql)`
    mutation TriggerDataSourceDetection {
  triggerDataSourceDetection
}
    `;function f(e){let t={...s,...e};return n.useMutation(T,t)}let D=(0,n.gql)`
    mutation ResolveSchemaChange($where: ResolveSchemaChangeWhereInput!) {
  resolveSchemaChange(where: $where)
}
    `;function A(e){let t={...s,...e};return n.useMutation(D,t)}},1313:(e,t,a)=>{a.d(t,{J$:()=>l,wC:()=>o});var n=a(29114);let s={},r=(0,n.gql)`
    mutation Deploy {
  deploy
}
    `;function l(e){let t={...s,...e};return n.useMutation(r,t)}let i=(0,n.gql)`
    query DeployStatus {
  modelSync {
    status
  }
}
    `;function o(e){let t={...s,...e};return n.useQuery(i,t)}},57289:(e,t,a)=>{a.d(t,{Af:()=>N,Pw:()=>m,R5:()=>p,it:()=>d,ko:()=>E});var n=a(29114);let s={},r=(0,n.gql)`
    fragment CommonColumn on DetailedColumn {
  displayName
  referenceName
  sourceColumnName
  type
  isCalculated
  notNull
  properties
}
    `,l=(0,n.gql)`
    fragment CommonField on FieldInfo {
  id
  displayName
  referenceName
  sourceColumnName
  type
  isCalculated
  notNull
  expression
  properties
}
    `,i=(0,n.gql)`
    fragment CommonRelation on DetailedRelation {
  fromModelId
  fromColumnId
  toModelId
  toColumnId
  type
  name
}
    `,o=(0,n.gql)`
    query ListModels {
  listModels {
    id
    displayName
    referenceName
    sourceTableName
    refSql
    primaryKey
    cached
    refreshTime
    description
    fields {
      ...CommonField
    }
    calculatedFields {
      ...CommonField
    }
  }
}
    ${l}`;function d(e){let t={...s,...e};return n.useQuery(o,t)}(0,n.gql)`
    query GetModel($where: ModelWhereInput!) {
  model(where: $where) {
    displayName
    referenceName
    sourceTableName
    refSql
    primaryKey
    cached
    refreshTime
    description
    fields {
      ...CommonColumn
    }
    calculatedFields {
      ...CommonColumn
    }
    relations {
      ...CommonRelation
    }
    properties
  }
}
    ${r}
${i}`;let c=(0,n.gql)`
    mutation CreateModel($data: CreateModelInput!) {
  createModel(data: $data)
}
    `;function m(e){let t={...s,...e};return n.useMutation(c,t)}let u=(0,n.gql)`
    mutation UpdateModel($where: ModelWhereInput!, $data: UpdateModelInput!) {
  updateModel(where: $where, data: $data)
}
    `;function E(e){let t={...s,...e};return n.useMutation(u,t)}let I=(0,n.gql)`
    mutation DeleteModel($where: ModelWhereInput!) {
  deleteModel(where: $where)
}
    `;function N(e){let t={...s,...e};return n.useMutation(I,t)}let h=(0,n.gql)`
    mutation PreviewModelData($where: WhereIdInput!) {
  previewModelData(where: $where)
}
    `;function p(e){let t={...s,...e};return n.useMutation(h,t)}},81481:(e,t,a)=>{a.d(t,{Z:()=>v});var n=a(20997),s=a(11163),r=a(53800),l=a.n(r),i=a(45417),o=a.n(i),d=a(7374),c=a.n(d),m=a(57518),u=a.n(m),E=a(25675),I=a.n(E);function N(){return n.jsx(I(),{src:"/images/logo-white-with-text.svg",alt:"Wren AI",width:125,height:30})}var h=a(24351);a(16689);var p=a(58168),R=a.n(p),g=a(17369),S=a.n(g),T=a(15382),f=a.n(T),D=a(47263),A=a.n(D),C=a(34869),x=a.n(C),O=a(82103),_=a(1313),b=a(95619);let{Text:y}=R(),L=(e,t)=>{let a=e?O.wR.IN_PROGRESS:t;return({[O.wR.IN_PROGRESS]:(0,n.jsxs)(c(),{size:[4,0],children:[n.jsx(A(),{className:"mr-1 gray-1"}),n.jsx(y,{className:"gray-1",children:"Deploying..."})]}),[O.wR.SYNCRONIZED]:(0,n.jsxs)(c(),{size:[4,0],children:[n.jsx(f(),{className:"mr-1 green-7"}),n.jsx(y,{className:"gray-1",children:"Synced"})]}),[O.wR.UNSYNCRONIZED]:(0,n.jsxs)(c(),{size:[4,0],children:[n.jsx(x(),{className:"mr-1 gold-6"}),n.jsx(y,{className:"gray-1",children:"Undeployed changes"})]})})[a]||""};function j(){let{data:e,loading:t,startPolling:a,stopPolling:s}=(0,b.q)(),[r,{data:i,loading:o}]=(0,_.J$)({onCompleted:e=>{e.deploy?.status==="FAILED"&&(console.error("Failed to deploy - ",e.deploy?.error),S().error("Failed to deploy. Please check the log for more details."))}}),d=e?.modelSync.status,m=()=>{r(),a(1e3)},u=o||t||[O.wR.SYNCRONIZED,O.wR.IN_PROGRESS].includes(d);return(0,n.jsxs)(c(),{size:[8,0],children:[L(o,d),n.jsx(l(),{className:`adm-modeling-header-btn ${u?"":"gray-10"}`,disabled:u,onClick:()=>m(),size:"small","data-guideid":"deploy-model",children:"Deploy"})]})}let{Header:U}=o(),P=u()(l()).withConfig({displayName:"HeaderBar__StyledButton",componentId:"sc-b9d2d314-0"})(["background:",";font-weight:",";border:none;color:var(--gray-1);&:hover,&:focus{background:",";color:var(--gray-1);}"],e=>e.$isHighlight?"rgba(255, 255, 255, 0.20)":"transparent",e=>e.$isHighlight?"700":"normal",e=>e.$isHighlight?"rgba(255, 255, 255, 0.20)":"rgba(255, 255, 255, 0.05)"),M=u()(U).withConfig({displayName:"HeaderBar__StyledHeader",componentId:"sc-b9d2d314-1"})(["height:48px;border-bottom:1px solid var(--gray-5);background:var(--gray-10);padding:10px 16px;"]);function v(){let e=(0,s.useRouter)(),{pathname:t}=e,a=!t.startsWith(h.y$.Onboarding),r=t.startsWith(h.y$.Modeling);return n.jsx(M,{children:(0,n.jsxs)("div",{className:"d-flex justify-space-between align-center",style:{marginTop:-2},children:[(0,n.jsxs)(c(),{size:[48,0],children:[n.jsx(N,{}),a&&(0,n.jsxs)(c(),{size:[16,0],children:[n.jsx(P,{shape:"round",size:"small",$isHighlight:t.startsWith(h.y$.Home),onClick:()=>e.push(h.y$.Home),children:"Home"}),n.jsx(P,{shape:"round",size:"small",$isHighlight:t.startsWith(h.y$.Modeling),onClick:()=>e.push(h.y$.Modeling),children:"Modeling"}),n.jsx(P,{shape:"round",size:"small",$isHighlight:t.startsWith(h.y$.Knowledge),onClick:()=>e.push(h.y$.KnowledgeQuestionSQLPairs),children:"Knowledge"}),n.jsx(P,{shape:"round",size:"small",$isHighlight:t.startsWith(h.y$.APIManagement),onClick:()=>e.push(h.y$.APIManagementHistory),children:"API"})]})]}),r&&n.jsx(c(),{size:[16,0],children:n.jsx(j,{})})]})})}},95619:(e,t,a)=>{a.d(t,{N:()=>s,q:()=>r});var n=a(16689);let s=(0,n.createContext)({});function r(){return(0,n.useContext)(s)}},21626:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>m});var s=a(20997),r=a(35629),l=a(81481),i=a(58287),o=a(5011),d=a(16593),c=e([d]);d=(c.then?(await c)():c)[0];let{Content:u}=r.Layout;function m(e){let{loading:t}=(0,o.V)(),{children:a,loading:n}=e,c=t||n;return(0,s.jsxs)(r.Layout,{className:(0,d.default)("adm-main bg-gray-3",{"overflow-hidden":c}),children:[s.jsx(l.Z,{}),s.jsx(u,{className:"adm-content",children:a}),s.jsx(i.ZP,{visible:c})]})}n()}catch(e){n(e)}})},79130:(e,t,a)=>{a.d(t,{J:()=>T,Z:()=>f});var n=a(20997),s=a(16689),r=a(89699),l=a.n(r),i=a(16190),o=a.n(i),d=a(76418),c=a.n(d),m=a(53526),u=a.n(m),E=a(28303),I=a(30675),N=a.n(I);function h(e){let{modelValue:t,fieldValue:a,value:r={},onModelChange:l,onFieldChange:i,onChange:o,modelOptions:d,fieldOptions:c,modelDisabled:m,fieldDisabled:E}=e,[I,h]=(0,s.useState)({model:t,field:a,...r});return(0,n.jsxs)(N().Group,{className:"d-flex",compact:!0,children:[n.jsx(u(),{style:{width:"35%"},options:d,onChange:e=>{l&&l(e);let t={model:e,field:void 0};h(t),o&&o(t)},placeholder:"Model",value:r?.model||t,disabled:m,showSearch:!0,optionFilterProp:"label","data-testid":"common__models-select"}),n.jsx(u(),{className:"flex-grow-1",options:c,onChange:e=>{i&&i(e),h({...I,field:e})},placeholder:"Field",value:r?.field||a,disabled:E,showSearch:!0,optionFilterProp:"label","data-testid":"common__fields-select"})]})}var p=a(24351),R=a(80284),g=a(99430),S=a(17544);let T={FROM_FIELD:"fromField",TO_FIELD:"toField",TYPE:"type"};function f(e){let{defaultValue:t,loading:a,model:s,onClose:r,onSubmit:i,relations:d,visible:m,formMode:I,isRecommendMode:N}=e,[f]=o().useForm(),D=I===p.SD.EDIT,A=(0,S.ZP)({model:s}),C=A.modelOptions.find(e=>p.bu(e.value).referenceName===s)?.value,x=t?.toField.modelName,O=(0,S.ZP)({model:x,excludeModels:[s]}),_=Object.keys(p.oD).map(e=>({label:(0,R.I)(e),value:p.oD[e]}));return n.jsx(c(),{title:`${l()(t)?"Add":"Update"} relationship`,width:750,visible:m,okText:"Submit",onOk:()=>{f.validateFields().then(async e=>{await i({...t,...e}),r()}).catch(console.error)},onCancel:r,confirmLoading:a,maskClosable:!1,destroyOnClose:!0,afterClose:()=>f.resetFields(),centered:!0,children:(0,n.jsxs)(o(),{form:f,preserve:!1,layout:"vertical",children:[n.jsx(o().Item,{label:"From",name:T.FROM_FIELD,required:!0,rules:[({getFieldValue:e})=>({validator:(0,g.xq)(D||N,d,e)})],children:n.jsx(h,{modelValue:C,modelDisabled:!0,fieldDisabled:D,onModelChange:A.onModelChange,modelOptions:A.modelOptions,fieldOptions:A.fieldOptions})}),n.jsx(o().Item,{label:"To",name:T.TO_FIELD,required:!0,rules:[({getFieldValue:e})=>({validator:(0,g.kq)(D||N,d,e)})],children:n.jsx(h,{onModelChange:O.onModelChange,modelOptions:O.modelOptions,fieldOptions:O.fieldOptions,modelDisabled:D,fieldDisabled:D})}),n.jsx(o().Item,{label:"Type",name:T.TYPE,required:!0,rules:[{required:!0,message:E.q.ADD_RELATION.RELATION_TYPE.REQUIRED}],children:n.jsx(u(),{"data-testid":"relationship-form__type-select",options:_,placeholder:"Select a relationship type"})})]})})}},25027:(e,t,a)=>{a.d(t,{Z:()=>I});var n=a(20997),s=a(25675),r=a.n(s),l=a(46810),i=a(57518),o=a.n(i),d=a(36005);let c=o()(l.z).withConfig({displayName:"ButtonItem__StyledButton",componentId:"sc-184829de-0"})(["border:2px var(--gray-4) solid;background-color:var(--gray-2);border-radius:4px;width:100%;height:auto;&:focus{border:2px var(--gray-4) solid;background-color:var(--gray-2);}&:hover{border-color:var(--geekblue-6);background-color:var(--gray-2);}&.is-active{border-color:var(--geekblue-6) !important;background-color:var(--gray-2) !important;}&:disabled{opacity:0.5;}.ant-btn-loading-icon .anticon{font-size:24px;}"]),m=o()(d.Z).withConfig({displayName:"ButtonItem__StyledIcon",componentId:"sc-184829de-1"})(["width:40px;height:40px;font-size:32px;display:inline-flex;justify-content:center;align-items:center;"]),u=o().div.withConfig({displayName:"ButtonItem__PlainImage",componentId:"sc-184829de-2"})(["border:1px var(--gray-4) solid;background-color:white;width:40px;height:40px;"]),E=o().div.withConfig({displayName:"ButtonItem__ComingSoon",componentId:"sc-184829de-3"})(["border:1px var(--gray-7) solid;color:var(--gray-7);font-size:8px;padding:2px 6px;border-radius:999px;&:before{content:'COMING SOON';}"]);function I(e){let{value:t,disabled:a,submitting:s,logo:l,IconComponent:i,label:o,onSelect:d,selectedTemplate:I}=e,N=I===t,h=N&&s;return(0,n.jsxs)(c,{className:["px-4 py-2 gray-8 d-flex align-center",h?"flex-start":"justify-space-between",N?"is-active":""].join(" "),disabled:a||s,loading:h,onClick:()=>d(t),children:[(0,n.jsxs)("div",{className:"d-flex align-center",style:{width:"100%"},children:[l?n.jsx(r(),{className:"mr-2",src:l,alt:o,width:"40",height:"40"}):i?n.jsx(m,{component:i,className:"mr-2"}):n.jsx(u,{className:"mr-2"}),o]}),a&&n.jsx(E,{})]})}},35188:(e,t,a)=>{a.d(t,{cS:()=>ew,B5:()=>ev,QC:()=>eq,f6:()=>eQ,mt:()=>eF,m4:()=>eB,uR:()=>eG});var n=a(20997),s=a(61831),r=a.n(s),l=a(4943),i=a.n(l),o=a(90661),d=a.n(o),c=a(24351),m=a(41664),u=a.n(m),E=a(16689),I=a(23647),N=a(95015),h=a(25027);let p=e=>n.jsx(I.JX,{span:6,children:n.jsx(h.Z,{...e})},e.label),R=(0,N.x)(p),g=(0,N.x)(p);var S=a(25675),T=a.n(S),f=a(27889),D=a.n(f),A=a(53800),C=a.n(A),x=a(28518),O=a.n(x),_=a(16190),b=a.n(_),y=a(27050),L=a.n(y),j=a(58168),U=a.n(j),P=a(57518),M=a.n(P),v=a(37554);let w=M()(b()).withConfig({displayName:"ConnectDataSource__StyledForm",componentId:"sc-6858b8f4-0"})(["border:1px var(--gray-4) solid;border-radius:4px;"]),q=M().div.withConfig({displayName:"ConnectDataSource__DataSource",componentId:"sc-6858b8f4-1"})(["border:1px var(--gray-4) solid;border-radius:4px;"]);var F=a(28303),Q=a(97026),G=a.n(Q),B=a(77047),k=a.n(B),H=a(30675),W=a.n(H),$=a(74285),Y=a.n($),V=a(69349),K=a.n(V),Z=a(93776);let z=M().div.withConfig({displayName:"MultiSelectBox__StyledBox",componentId:"sc-45baf25f-0"})(["border:1px solid var(--gray-5);border-radius:4px;&.multiSelectBox-input-error{border-color:var(--red-5);}.ant-table{border:0;}.ant-table-body,.ant-table-placeholder{height:195px;}"]),J=M().div.withConfig({displayName:"MultiSelectBox__StyledTotal",componentId:"sc-45baf25f-1"})(["padding:8px 12px;border-bottom:1px var(--gray-3) solid;"]);function X(e){let{columns:t,loading:a,items:s,onChange:r,value:l}=e,[i,o]=(0,E.useState)(new Set(l)),[d,c]=(0,E.useState)(""),{status:m}=(0,E.useContext)(Z.FormItemInputContext),u=(0,E.useMemo)(()=>d?s.filter(e=>t.map(t=>e[t.dataIndex]).some(e=>G()(e)&&e.includes(d))):s,[s,d]),I=e=>{let t=new Set(i);t.has(e)?t.delete(e):t.add(e),o(t),r&&r(Array.from(t))},N=0===i.size?s.length:`${i.size}/${s.length}`;return(0,n.jsxs)(z,{className:m?`multiSelectBox-input-${m}`:void 0,children:[(0,n.jsxs)(J,{children:[N," table(s)"]}),n.jsx("div",{className:"p-2",children:n.jsx(W(),{prefix:n.jsx(K(),{}),onChange:e=>{e.persist();let{value:t}=e.target;c(t)},placeholder:"Search here",allowClear:!0})}),n.jsx(Y(),{rowSelection:{type:"checkbox",selectedRowKeys:Array.from(i),onSelect:e=>I(e.value),onChange(e){if(0===e.length){let e=u.map(e=>e.value),t=new Set(k()([...i.values()],e));o(t),r&&r(Array.from(t));return}if(e.length===u.length){let t=new Set([...i,...e]);o(t),r&&r(Array.from(t))}}},rowKey:e=>e.value,columns:t,dataSource:u,scroll:{y:195},pagination:!1,loading:a})]})}let{Title:ee,Text:et}=U(),ea=[{title:"Table name",dataIndex:"name"}];var en=a(63901),es=a.n(en),er=a(93984),el=a.n(er),ei=a(7374),eo=a.n(ei),ed=a(80261),ec=a.n(ed),em=a(59289),eu=a.n(em),eE=a(97615),eI=a.n(eE),eN=a(79253),eh=a.n(eN),ep=a(59817),eR=a(58633),eg=a.n(eR);let{Panel:eS}=eg(),eT=M()(eg()).withConfig({displayName:"SelectionTable__StyledCollapse",componentId:"sc-bfcb74be-0"})(["&.ant-collapse.adm-error{border-color:var(--red-5);border-bottom:1px solid var(--red-5);}&.ant-collapse{background-color:white;border-color:var(--gray-4);> .ant-collapse-item > .ant-collapse-header{padding:16px 12px;align-items:center;}> .ant-collapse-item,.ant-collapse-content{border-color:var(--gray-4);}.ant-collapse-content-box{padding:0px;}.ant-table{border:none;.ant-table-thead > tr > th{color:var(--gray-7);background-color:white;}&.ant-table-empty{.ant-empty-normal{margin:16px 0;}}}}"]),ef=M()(L()).attrs(e=>({className:`${e.$isRowSelection?"":"ml-1"}`})).withConfig({displayName:"SelectionTable__StyledRow",componentId:"sc-bfcb74be-1"})([""]),eD=(0,E.forwardRef)(function(e,t){let{columns:a,dataSource:s,extra:r,enableRowSelection:l,onChange:i,rowKey:o,tableHeader:d,tableTitle:c}=e,{status:m}=(0,E.useContext)(Z.FormItemInputContext),u=function(e){let[t,a]=(0,E.useState)([e]),n=e=>a(e);return{collapseDefaultActiveKey:t,onChangeCollapsePanelState:n,onCollapseOpen:(e,a)=>{n([a]),t.includes(a)&&e.stopPropagation()}}}(c),I=!!l;return n.jsx(eT,{className:m?`adm-${m}`:"",defaultActiveKey:u.collapseDefaultActiveKey,onChange:u.onChangeCollapsePanelState,children:n.jsx(eS,{extra:r&&r(u.onCollapseOpen),header:n.jsx(ef,{wrap:!1,gutter:8,align:"middle",$isRowSelection:I,children:d}),showArrow:!1,children:n.jsx(Y(),{ref:t,columns:a,dataSource:s,rowKey:o,rowSelection:I?{type:"checkbox",onChange:(e,t)=>{i&&i(t)}}:void 0,pagination:{hideOnSinglePage:!0,pageSize:50,size:"small"}})},c)})});function eA(e){return n.jsx(eD,{...e,tableHeader:(0,n.jsxs)(n.Fragment,{children:[n.jsx(ep.ZA,{className:"pr-2 text-md"}),e.tableTitle]})})}var eC=a(80284),ex=a(47427),eO=a(79130),e_=a(17544);let{Title:eb,Text:ey}=U(),eL=["name","isAutoGenerated"];function ej(e){let{index:t,modelName:a,onSetRelation:s,onDeleteRow:r,relations:l,recommendNameMapping:i}=e,o=[{title:"From",dataIndex:"fromField",key:"fromField",render:e=>`${e.modelName}.${e.fieldName}`,width:"35%"},{title:"To",dataIndex:"toField",key:"toField",render:e=>`${e.modelName}.${e.fieldName}`,width:"35%"},{title:"Type",dataIndex:"type",key:"type",render:(e,t)=>(0,n.jsxs)(n.Fragment,{children:[(0,eC.I)(e),t.isAutoGenerated&&n.jsx(ey,{className:"pl-1",type:"secondary",children:"(auto-generated)"})]}),width:"30%"},{title:"",key:"action",width:48,align:"center",render:(e,t)=>(0,n.jsxs)(eo(),{size:[16,0],children:[n.jsx(eI(),{onClick:()=>s({modelName:a,defaultValue:t})}),n.jsx(el(),{title:"Confirm to delete?",okText:"Delete",okButtonProps:{danger:!0},onConfirm:()=>r(a,t),children:n.jsx(eu(),{})})]})}];return n.jsx("div",{className:"mt-6",children:n.jsx(eA,{columns:o,dataSource:l,tableTitle:i[a],extra:e=>(0,n.jsxs)(C(),{onClick:t=>{s({modelName:a}),e(t,i[a])},size:"small",title:"Add relationship",children:[n.jsx(eh(),{}),"Add"]}),rowKey:e=>`${a}-${e.fromField.fieldName}-${e.toField.modelName}-${e.toField.fieldName}-${t}`})})}var eU=a(82103),eP=a(42698),eM=a(46386);let ev={[c.ye.STARTER]:{step:0,component:function(e){let{onNext:t,submitting:a}=e,[s,r]=(0,E.useState)(),l=eF(),i=eG();return(0,n.jsxs)(n.Fragment,{children:[n.jsx(I.ZT.Title,{level:1,className:"mb-3",children:"Connect a data source"}),(0,n.jsxs)(I.ZT.Text,{children:["Vote for your favorite data sources on"," ",n.jsx(u(),{href:"https://github.com/Canner/WrenAI/discussions/327",target:"_blank",rel:"noopener noreferrer",children:"GitHub"}),"."]}),n.jsx(I.X2,{className:"mt-6",gutter:[16,16],children:n.jsx(R,{data:l,onSelect:e=>{t&&t({dataSource:e})},submitting:a})}),n.jsx("div",{className:"py-8"}),n.jsx(I.ZT.Title,{level:1,className:"mb-3",children:"Play around with sample data"}),n.jsx(I.X2,{className:"mt-6",gutter:[16,16],children:n.jsx(g,{data:i,onSelect:e=>{r(e),t&&t({template:e})},submitting:a,selectedTemplate:s})}),n.jsx("div",{className:"py-12"})]})}},[c.ye.CREATE_DATA_SOURCE]:{step:0,component:function(e){let{connectError:t,dataSource:a,submitting:s,onNext:r,onBack:l}=e,[i]=b().useForm(),o=eQ(a);return(0,n.jsxs)(n.Fragment,{children:[n.jsx(U().Title,{level:1,className:"mb-3",children:"Connect the data source"}),(0,n.jsxs)(U().Text,{children:["Vote for your favorite data sources on"," ",n.jsx(u(),{href:"https://github.com/Canner/WrenAI/discussions/327",target:"_blank",rel:"noopener noreferrer",children:"GitHub"}),"."]}),(0,n.jsxs)(w,{form:i,layout:"vertical",className:"p-6 my-6",children:[(0,n.jsxs)(L(),{align:"middle",className:"mb-6",children:[n.jsx(O(),{span:12,children:(0,n.jsxs)(q,{className:"d-inline-block px-4 py-2 bg-gray-2 gray-8",children:[n.jsx(T(),{className:"mr-2",src:o.logo,alt:a,width:"40",height:"40"}),o.label]})}),(0,n.jsxs)(O(),{className:"text-right",span:12,children:["Learn more information in the ",o.label," ",n.jsx(u(),{href:o.guide,target:"_blank",rel:"noopener noreferrer",children:"setup guide"}),"."]})]}),n.jsx(o.component,{})]}),t&&n.jsx(D(),{message:t.shortMessage,description:a===v.t.POSTGRES?eB(t):t.message,type:"error",showIcon:!0,className:"my-6"}),(0,n.jsxs)(L(),{gutter:16,className:"pt-6",children:[n.jsx(O(),{span:12,children:n.jsx(C(),{onClick:l,size:"large",className:"adm-onboarding-btn",disabled:s,children:"Back"})}),n.jsx(O(),{className:"text-right",span:12,children:n.jsx(C(),{type:"primary",size:"large",onClick:()=>{i.validateFields().then(e=>{r&&r({properties:e})}).catch(e=>{console.error(e)})},loading:s,className:"adm-onboarding-btn",children:"Next"})})]})]})},maxWidth:960},[c.ye.SELECT_MODELS]:{step:1,component:function(e){let{fetching:t,tables:a,onBack:s,onNext:r,submitting:l}=e,[i]=b().useForm(),o=a.map(e=>({...e,value:e.name}));return(0,n.jsxs)("div",{children:[n.jsx(ee,{level:1,className:"mb-3",children:"Select tables to create data models"}),(0,n.jsxs)(et,{children:["We will create data models based on selected tables to help AI better understand your data.",n.jsx("br",{}),n.jsx(u(),{href:"https://docs.getwren.ai/oss/guide/modeling/overview",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})," ","about data models."]}),n.jsx("div",{className:"my-6",children:n.jsx(b(),{form:i,layout:"vertical",style:{marginTop:8},children:n.jsx(b().Item,{name:"tables",rules:[{required:!0,message:F.q.SETUP_MODEL.TABLE.REQUIRED}],children:n.jsx(X,{columns:ea,items:o,loading:t})})})}),(0,n.jsxs)(L(),{gutter:16,className:"pt-6",children:[n.jsx(O(),{span:12,children:n.jsx(C(),{onClick:s,size:"large",className:"adm-onboarding-btn",disabled:l,children:"Back"})}),n.jsx(O(),{className:"text-right",span:12,children:n.jsx(C(),{type:"primary",size:"large",onClick:()=>{i.validateFields().then(e=>{r&&r({selectedTables:e.tables})}).catch(e=>{console.error(e)})},className:"adm-onboarding-btn",loading:l,children:"Next"})})]})]})},maxWidth:960},[c.ye.DEFINE_RELATIONS]:{step:2,component:function(e){let{fetching:t,recommendRelations:a,recommendNameMapping:s,onBack:r,onNext:l,onSkip:i,submitting:o}=e,[d,c]=(0,E.useState)(a),[m,u]=(0,E.useState)(null),[I,N]=(0,E.useState)(!1),h=(0,ex.Z)(),p=(e,t)=>{let n=(a[e]||[]).find(e=>JSON.stringify(es()(e,eL))===JSON.stringify(es()(t,eL)));return n?.isAutoGenerated||!1},R=e=>{let t=(0,e_.T9)(e),a=t.fromField.modelName,n=p(a,t);c({...d,[a]:[...d[a]||[],{...t,isAutoGenerated:n}]})},g=(e,t)=>{c({...d,[e]:d[e].filter(e=>JSON.stringify(e)!==JSON.stringify(t))})},S=e=>{u(e),h.openModal()},T=(e,t,a)=>{let n=(0,e_.T9)(a),s=p(e,n);c({...d,[e]:d[e].map(e=>JSON.stringify(e)===JSON.stringify(t)?{...n,isAutoGenerated:s}:e)})};return(0,n.jsxs)("div",{children:[n.jsx(eb,{level:1,className:"mb-3",children:"Define relationships"}),n.jsx(ey,{children:"You can create relationships between selected tables. We provide suggested relationships based on primary and foreign keys defined in your data source. The relationships are then added to data models."}),I&&n.jsx(D(),{message:"No recommended relationships",description:"No relationships are recommended because no primary or foreign keys were detected.",type:"info",showIcon:!0,className:"my-6"}),(0,n.jsxs)("div",{className:"my-6 text-center",children:[Object.entries(d).map(([e,t=[]],a)=>n.jsx(ej,{index:a,modelName:e,relations:t,onSetRelation:S,onDeleteRow:g,recommendNameMapping:s},`${e}-${t.length}`)),n.jsx(ec(),{spinning:t,tip:"Loading...",className:"my-15"})]}),(0,n.jsxs)(L(),{gutter:16,className:"pt-6",children:[n.jsx(O(),{span:12,children:n.jsx(C(),{onClick:r,size:"large",className:"adm-onboarding-btn",children:"Back"})}),(0,n.jsxs)(O(),{className:"text-right",span:12,children:[n.jsx(C(),{className:"mr-4 gray-7 adm-onboarding-btn",type:"text",size:"large",onClick:i,disabled:o,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_skip_define_relationship",children:"Skip this step"}),n.jsx(C(),{type:"primary",size:"large",onClick:()=>{l&&l({relations:d})},className:"adm-onboarding-btn",loading:o,disabled:t,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_finish_define_relationship",children:"Finish"})]})]}),n.jsx(eO.Z,{...h.state,model:m?.modelName,onSubmit:async e=>{m?.defaultValue?T(m.modelName,m.defaultValue,e):R(e),u(null)},onClose:()=>{u(null),h.closeModal()},defaultValue:m?.defaultValue?es()(m.defaultValue,eL):void 0,relations:d,isRecommendMode:!!m?.defaultValue})]})}}},ew={[c.tW.BIG_QUERY]:{...(0,eM.Mh)(c.tW.BIG_QUERY),guide:"https://docs.getwren.ai/oss/guide/connect/bigquery",disabled:!1},[c.tW.DUCKDB]:{...(0,eM.Mh)(c.tW.DUCKDB),guide:"https://docs.getwren.ai/oss/guide/connect/duckdb",disabled:!1},[c.tW.POSTGRES]:{...(0,eM.Mh)(c.tW.POSTGRES),guide:"https://docs.getwren.ai/oss/guide/connect/postgresql",disabled:!1},[c.tW.MYSQL]:{...(0,eM.Mh)(c.tW.MYSQL),guide:"https://docs.getwren.ai/oss/guide/connect/mysql",disabled:!1},[c.tW.MSSQL]:{...(0,eM.Mh)(c.tW.MSSQL),guide:"https://docs.getwren.ai/oss/guide/connect/sqlserver",disabled:!1},[c.tW.CLICK_HOUSE]:{...(0,eM.Mh)(c.tW.CLICK_HOUSE),guide:"https://docs.getwren.ai/oss/guide/connect/clickhouse",disabled:!1},[c.tW.TRINO]:{...(0,eM.Mh)(c.tW.TRINO),guide:"https://docs.getwren.ai/oss/guide/connect/trino",disabled:!1},[c.tW.SNOWFLAKE]:{...(0,eM.Mh)(c.tW.SNOWFLAKE),guide:"https://docs.getwren.ai/oss/guide/connect/snowflake",disabled:!1}},eq={[eU.Y0.ECOMMERCE]:{label:"E-commerce",IconComponent:i(),guide:"https://docs.getwren.ai/oss/getting_started/sample_data/ecommerce"},[eU.Y0.HR]:{label:"Human Resource",IconComponent:d(),guide:"https://docs.getwren.ai/oss/getting_started/sample_data/hr"}},eF=()=>Object.values(ew),eQ=e=>r()(ew[e],(0,eM.pA)(e)),eG=()=>Object.keys(eq).map(e=>({...eq[e],value:e})),eB=e=>e.code===eP.O1.CONNECTION_REFUSED?(0,n.jsxs)("div",{children:[e.message,". ",n.jsx("br",{}),"If you are having trouble connecting to your PostgreSQL database, please refer to our"," ",n.jsx("a",{href:"https://docs.getwren.ai/oss/guide/connect/postgresql#connect",target:"_blank",rel:"noopener noreferrer",children:"documentation"})," ","for detailed instructions."]}):e.message},5011:(e,t,a)=>{a.d(t,{Z:()=>E,V:()=>u});var n=a(16689),s=a(11163),r=a(29114);let l={},i=(0,r.gql)`
    query OnboardingStatus {
  onboardingStatus {
    status
  }
}
    `;function o(e){let t={...l,...e};return r.useQuery(i,t)}var d=a(82103),c=a(24351);let m={[d.uo.DATASOURCE_SAVED]:c.y$.OnboardingModels,[d.uo.NOT_STARTED]:c.y$.OnboardingConnection,[d.uo.ONBOARDING_FINISHED]:c.y$.Modeling,[d.uo.WITH_SAMPLE_DATASET]:c.y$.Modeling},u=()=>{let e=(0,s.useRouter)(),{data:t,loading:a}=o(),r=t?.onboardingStatus?.status;return(0,n.useEffect)(()=>{if(r){let t=m[r],a=e.pathname;if(t&&t!==c.y$.Modeling){if(t===a||e.pathname.startsWith(c.y$.Onboarding)&&r!==d.uo.ONBOARDING_FINISHED)return;e.push(t);return}if("/"===a||a===c.y$.OnboardingRelationships&&r===d.uo.WITH_SAMPLE_DATASET||[c.y$.OnboardingConnection,c.y$.OnboardingModels].includes(a)){e.push(t);return}}},[r,e.pathname]),{loading:a,onboardingStatus:r}};function E(){let{data:e,loading:t,error:a,refetch:n}=o();return{loading:t,error:a,refetch:n,onboardingStatus:e?.onboardingStatus?.status}}},17544:(e,t,a)=>{a.d(t,{T9:()=>o,ZP:()=>d});var n=a(16689),s=a(57289),r=a(24351);let l=["id","referenceName"],i=["id","referenceName"],o=e=>{let t=(0,r.bu)(e.fromField.model),a=(0,r.bu)(e.fromField.field),n=(0,r.bu)(e.toField.model),s=(0,r.bu)(e.toField.field);return{...e,fromField:{modelId:t.id,modelName:t.referenceName,fieldId:a.id,fieldName:a.referenceName},toField:{modelId:n.id,modelName:n.referenceName,fieldId:s.id,fieldName:s.referenceName}}};function d(e){let{model:t,excludeModels:a}=e,[o,d]=(0,n.useState)(t||""),{data:c}=(0,s.it)({fetchPolicy:"cache-and-network"}),m=(0,n.useMemo)(()=>c?c.listModels.map(e=>({id:e.id,referenceName:e.referenceName,displayName:e.displayName,fields:e.fields})):[],[c]),u=(0,n.useMemo)(()=>m.filter(e=>!(a&&a.includes(e.referenceName))),[a,o,c]),E=(0,n.useMemo)(()=>u.map(e=>({label:e.displayName,value:(0,r.Q_)(e,l),"data-testid":"common__models__select-option"})),[u]),I=(0,n.useMemo)(()=>u.find(e=>e.referenceName===o),[E,o]);return{modelOptions:E,fieldOptions:(0,n.useMemo)(()=>(I?.fields||[]).map(e=>({label:e.displayName,value:(0,r.Q_)(e,i),"data-testid":"common__fields__select-option"})),[I]),onModelChange:e=>{d((0,r.bu)(e).referenceName)}}}},47427:(e,t,a)=>{a.d(t,{Z:()=>r});var n=a(16689),s=a(24351);function r(){let[e,t]=(0,n.useState)(!1),[a,r]=(0,n.useState)(s.SD.CREATE),[l,i]=(0,n.useState)(null),[o,d]=(0,n.useState)(null);return{state:{visible:e,formMode:a,defaultValue:o,payload:l},openModal:(e,a)=>{a&&i(a),e&&d(e),e&&r(s.SD.EDIT),t(!0)},closeModal:()=>{t(!1),i(null),d(null),r(s.SD.CREATE)}}}},36005:(e,t,a)=>{a.d(t,{Z:()=>s});var n=a(5152);let s=a.n(n)()(()=>Promise.resolve().then(a.t.bind(a,50910,23)),{loadableGenerated:{modules:["import/icon.ts -> @ant-design/icons/lib/components/Icon"]},ssr:!1})},11070:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var n=a(20997),s=a(56859),r=a.n(s),l=a(57518);class i extends r(){static async getInitialProps(e){let t=e.renderPage,a=new l.ServerStyleSheet;e.renderPage=()=>t({enhanceApp:e=>t=>a.collectStyles(n.jsx(e,{...t})),enhanceComponent:e=>e});let s=await r().getInitialProps(e),i=a.getStyleElement();return{...s,styles:i}}render(){return(0,n.jsxs)(s.Html,{children:[n.jsx(s.Head,{children:this.props.styles}),(0,n.jsxs)("body",{children:[n.jsx(s.Main,{}),n.jsx(s.NextScript,{})]})]})}}},80284:(e,t,a)=>{a.d(t,{X:()=>i,I:()=>l});var n=a(82103),s=a(24351);let r="Unknown",l=e=>({[s.oD.MANY_TO_ONE]:"Many-to-one",[s.oD.ONE_TO_MANY]:"One-to-many",[s.oD.ONE_TO_ONE]:"One-to-one"})[e]||r,i=e=>({[n.i_.AVG]:{name:"Average",syntax:"avg(column)",description:"Returns the average of the values in the column."},[n.i_.COUNT]:{name:"Count",syntax:"count(column)",description:"Returns the count of non-null rows (also known as records) in the selected data."},[n.i_.MAX]:{name:"Max",syntax:"max(column)",description:"Returns the largest value found in the column."},[n.i_.MIN]:{name:"Min",syntax:"min(column)",description:"Returns the smallest value found in the column."},[n.i_.SUM]:{name:"Sum",syntax:"sum(column)",description:"Adds up all the values of the column."},[n.i_.ABS]:{name:"Absolute",syntax:"abs(column)",description:"Returns the absolute (positive) value of the specified column."},[n.i_.CBRT]:{name:"Cube root",syntax:"cbrt(column)",description:"Returns the cube root of the number."},[n.i_.CEIL]:{name:"Ceil",syntax:"ceil(column)",description:"Rounds a decimal up (ceil as in ceiling)."},[n.i_.EXP]:{name:"Exponential",syntax:"exp(column)",description:"Returns Euler’s number, e, raised to the power of the supplied number."},[n.i_.FLOOR]:{name:"Floor",syntax:"floor(column)",description:"Rounds a decimal number down."},[n.i_.LN]:{name:"Natural logarithm",syntax:"ln(column)",description:"Returns the natural logarithm of the number."},[n.i_.LOG10]:{name:"Log10",syntax:"log10(column)",description:"Returns the base 10 log of the number."},[n.i_.ROUND]:{name:"Round",syntax:"round(column)",description:"Rounds a decimal number either up or down to the nearest integer value."},[n.i_.SIGN]:{name:"Signum",syntax:"sign(column)",description:"Returns the signum function of the number."},[n.i_.LENGTH]:{name:"Length",syntax:"length(column)",description:"Returns the number of characters in string."},[n.i_.REVERSE]:{name:"Reverse",syntax:"reverse(column)",description:"Returns string with the characters in reverse order."}})[e]||{name:r,syntax:r,description:r}},46386:(e,t,a)=>{a.d(t,{Mh:()=>H,pA:()=>W,Ur:()=>G,J9:()=>B});var n=a(24351),s=a(20997),r=a(16689),l=a(53800),i=a.n(l),o=a(16190),d=a.n(o),c=a(30675),m=a.n(c),u=a(29538),E=a.n(u),I=a(65271),N=a.n(I),h=a(28303);let p=e=>{let{onChange:t,value:a}=e,[n,l]=(0,r.useState)([]);(0,r.useEffect)(()=>{a||l([])},[a]);let o=(e,t)=>{let a=new FileReader;a.onloadend=e=>{let n=a.result;n&&t(JSON.parse(String(n)))},a.readAsText(e)};return s.jsx(E(),{accept:".json",fileList:n,onChange:e=>{let{file:a,fileList:n}=e;if(n.length){let e=n[0];o(a.originFileObj,e=>{t&&t(e)}),l([e])}},onRemove:()=>{l([]),t&&t(void 0)},maxCount:1,children:s.jsx(i(),{icon:s.jsx(N(),{}),children:"Click to upload JSON key file"})})};function R(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",required:!0,name:"displayName",rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{placeholder:"Our BigQuery"})}),s.jsx(d().Item,{label:"Project ID",required:!0,name:"projectId",rules:[{required:!a,message:h.q.CONNECTION.PROJECT_ID.REQUIRED}],children:s.jsx(m(),{placeholder:"The GCP project ID",disabled:a})}),s.jsx(d().Item,{label:"Dataset ID",required:!0,name:"datasetId",rules:[{required:!a,message:h.q.CONNECTION.DATASET_ID.REQUIRED}],children:s.jsx(m(),{disabled:a})}),s.jsx(d().Item,{label:"Credentials",required:!a,name:"credentials",rules:[{required:!a,message:h.q.CONNECTION.CREDENTIAL.REQUIRED}],children:s.jsx(p,{})})]})}var g=a(41664),S=a.n(g),T=a(28518),f=a.n(T),D=a(27050),A=a.n(D),C=a(59289),x=a.n(C),O=a(79253),_=a.n(O);let{TextArea:b}=m();function y(){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",name:"displayName",rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{placeholder:"DuckDB"})}),s.jsx(d().Item,{label:"Initial SQL statements",name:"initSql",extra:"These statements are meant to be executed only once during initialization.",rules:[{required:!0,message:h.q.CONNECTION.INIT_SQL.REQUIRED}],children:s.jsx(b,{placeholder:"CREATE TABLE new_tbl AS SELECT * FROM read_csv('input.csv');",rows:4})}),s.jsx(d().Item,{label:"Configuration options",extra:(0,s.jsxs)(s.Fragment,{children:["DuckDB offers various configuration options that can modify the system's behavior."," ",s.jsx(S(),{href:"https://duckdb.org/docs/configuration/overview.html",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})]}),children:s.jsx(d().List,{name:"configurations",initialValue:[{}],children:(e,{add:t,remove:a})=>(0,s.jsxs)(s.Fragment,{children:[e.map(({key:e,name:t,...n})=>(0,s.jsxs)(A(),{wrap:!1,gutter:8,children:[s.jsx(f(),{flex:"1 0",children:s.jsx(d().Item,{...n,name:[t,"key"],style:{width:"100%"},rules:[({getFieldValue:e})=>({validator:(a,n)=>e(["configurations",t,"value"])&&!n?Promise.reject(h.q.CONNECTION.CONFIGURATION.KEY.REQUIRED):Promise.resolve()})],children:s.jsx(m(),{placeholder:"Key"})})}),s.jsx(f(),{flex:"1 0",children:s.jsx(d().Item,{...n,name:[t,"value"],style:{width:"100%"},rules:[({getFieldValue:e})=>({validator:(a,n)=>e(["configurations",t,"key"])&&!n?Promise.reject(h.q.CONNECTION.CONFIGURATION.VALUE.REQUIRED):Promise.resolve()})],children:s.jsx(m(),{placeholder:"Value"})})}),s.jsx(f(),{flex:"none",className:"p-1",children:s.jsx(x(),{onClick:()=>a(t)})})]},e)),s.jsx(d().Item,{noStyle:!0,children:s.jsx(i(),{type:"dashed",onClick:()=>t(),block:!0,icon:s.jsx(_(),{}),children:"Add an option"})})]})})}),s.jsx(d().Item,{label:"Extensions",extra:(0,s.jsxs)(s.Fragment,{children:["DuckDB has an extension mechanism that enables the dynamic loading of extensions."," ",s.jsx(S(),{href:"https://duckdb.org/docs/extensions/overview.html",target:"_blank",rel:"noopener noreferrer",children:"Learn more"})]}),children:s.jsx(d().List,{name:"extensions",initialValue:[""],children:(e,{add:t,remove:a})=>(0,s.jsxs)(s.Fragment,{children:[e.map(({key:e,name:t,...n})=>(0,s.jsxs)(A(),{wrap:!1,gutter:8,className:"my-2",children:[s.jsx(f(),{flex:"1 0",children:s.jsx(d().Item,{...n,name:t,noStyle:!0,style:{width:"100%"},children:s.jsx(m(),{placeholder:"Extension name"})})}),s.jsx(f(),{flex:"none",className:"p-1",children:s.jsx(x(),{onClick:()=>a(t)})})]},e)),s.jsx(d().Item,{noStyle:!0,children:s.jsx(i(),{type:"dashed",onClick:()=>t(),block:!0,icon:s.jsx(_(),{}),children:"Add an extension"})})]})})})]})}var L=a(89688),j=a(99430);function U(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(L.l.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(L.I,{})}),s.jsx(L.l.Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:j.KT}],children:s.jsx(L.I,{placeholder:"********",disabled:a})}),s.jsx(L.l.Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PORT.REQUIRED}],children:s.jsx(L.I,{placeholder:"3306",disabled:a})}),s.jsx(L.l.Item,{label:"Username",name:"user",rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(L.I,{})}),s.jsx(L.l.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(L.I.Password,{placeholder:"input password"})}),s.jsx(L.l.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DATABASE.REQUIRED}],children:s.jsx(L.I,{placeholder:"MySQL database name",disabled:a})})]})}var P=a(32024),M=a.n(P);function v(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:j.KT}],children:s.jsx(m(),{placeholder:"********",disabled:a})}),s.jsx(d().Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PORT.REQUIRED}],children:s.jsx(m(),{placeholder:"5432",disabled:a})}),s.jsx(d().Item,{label:"Username",name:"user",rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(m().Password,{placeholder:"input password"})}),s.jsx(d().Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DATABASE.REQUIRED}],children:s.jsx(m(),{placeholder:"PostgreSQL database name",disabled:a})}),s.jsx(d().Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:s.jsx(M(),{})})]})}function w(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:j.KT}],children:s.jsx(m(),{placeholder:"********",disabled:a})}),s.jsx(d().Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PORT.REQUIRED}],children:s.jsx(m(),{placeholder:"1433",disabled:a})}),s.jsx(d().Item,{label:"Username",name:"user",rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(m().Password,{placeholder:"input password"})}),s.jsx(d().Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DATABASE.REQUIRED}],children:s.jsx(m(),{placeholder:"SQL Server database name",disabled:a})}),s.jsx(d().Item,{extra:"This parameter is used to skip server certificate validation. If you are using a trusted certificate, you can disable it.",label:"Enable Trust Server Certificate",name:"trustServerCertificate",valuePropName:"checked",initialValue:!0,children:s.jsx(M(),{})})]})}function q(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:j.KT}],children:s.jsx(m(),{placeholder:"<your_click_house_account>.clickhouse.cloud",disabled:a})}),s.jsx(d().Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PORT.REQUIRED}],children:s.jsx(m(),{placeholder:"8443",disabled:a})}),s.jsx(d().Item,{label:"Username",name:"user",rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(m().Password,{placeholder:"input password"})}),s.jsx(d().Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DATABASE.REQUIRED}],children:s.jsx(m(),{placeholder:"ClickHouse database name",disabled:a})}),s.jsx(d().Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:s.jsx(M(),{})})]})}function F({mode:e}){let t=e===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d().Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Host",name:"host",required:!0,rules:[{required:!0,validator:j.KT}],children:s.jsx(m(),{placeholder:"********",disabled:t})}),s.jsx(d().Item,{label:"Port",name:"port",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PORT.REQUIRED}],children:s.jsx(m(),{disabled:t})}),s.jsx(d().Item,{label:"Schemas",name:"schemas",required:!0,rules:[{required:!0,message:h.q.CONNECTION.SCHEMAS.REQUIRED}],children:s.jsx(m(),{placeholder:"catalog.schema1, catalog.schema2"})}),s.jsx(d().Item,{label:"Username",name:"username",required:!0,rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(m(),{})}),s.jsx(d().Item,{label:"Password",name:"password",required:!0,rules:[{required:!1,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(m().Password,{placeholder:"Input password"})}),s.jsx(d().Item,{label:"Use SSL",name:"ssl",valuePropName:"checked",children:s.jsx(M(),{})})]})}function Q(e){let{mode:t}=e,a=t===n.SD.EDIT;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(L.l.Item,{label:"Display name",name:"displayName",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DISPLAY_NAME.REQUIRED}],children:s.jsx(L.I,{})}),s.jsx(L.l.Item,{label:"Username",name:"user",rules:[{required:!0,message:h.q.CONNECTION.USERNAME.REQUIRED}],children:s.jsx(L.I,{})}),s.jsx(L.l.Item,{label:"Password",name:"password",required:!0,rules:[{required:!0,message:h.q.CONNECTION.PASSWORD.REQUIRED}],children:s.jsx(L.I.Password,{placeholder:"input password"})}),s.jsx(L.l.Item,{label:"Account",name:"account",required:!0,rules:[{required:!0,message:h.q.CONNECTION.ACCOUNT.REQUIRED}],children:s.jsx(L.I,{placeholder:"<snowflake_org_id>-<snowflake_user_id>",disabled:a})}),s.jsx(L.l.Item,{label:"Database name",name:"database",required:!0,rules:[{required:!0,message:h.q.CONNECTION.DATABASE.REQUIRED}],children:s.jsx(L.I,{placeholder:"Snowflake database name",disabled:a})}),s.jsx(L.l.Item,{label:"Schema",name:"schema",required:!0,rules:[{required:!0,message:h.q.CONNECTION.SCHEMA.REQUIRED}],children:s.jsx(L.I,{})})]})}let G=e=>{switch(e){case n.tW.BIG_QUERY:return"/images/dataSource/bigQuery.svg";case n.tW.POSTGRES:return"/images/dataSource/postgreSql.svg";case n.tW.MYSQL:return"/images/dataSource/mysql.svg";case n.tW.MSSQL:return"/images/dataSource/sqlserver.svg";case n.tW.CLICK_HOUSE:return"/images/dataSource/clickhouse.svg";case n.tW.DUCKDB:return"/images/dataSource/duckDb.svg";case n.tW.TRINO:return"/images/dataSource/trino.svg";case n.tW.SNOWFLAKE:return"/images/dataSource/snowflake.svg";default:return null}},B=e=>{switch(e){case n.tW.BIG_QUERY:return"BigQuery";case n.tW.POSTGRES:return"PostgreSQL";case n.tW.MYSQL:return"MySQL";case n.tW.MSSQL:return"SQL Server";case n.tW.CLICK_HOUSE:return"ClickHouse";case n.tW.DUCKDB:return"DuckDB";case n.tW.TRINO:return"Trino";case n.tW.SNOWFLAKE:return"Snowflake";default:return""}},k=e=>{switch(e){case n.tW.BIG_QUERY:return R;case n.tW.POSTGRES:return v;case n.tW.MYSQL:return U;case n.tW.MSSQL:return w;case n.tW.CLICK_HOUSE:return q;case n.tW.DUCKDB:return y;case n.tW.TRINO:return F;case n.tW.SNOWFLAKE:return Q;default:return null}},H=e=>({label:B(e),logo:G(e),value:n.tW[e]}),W=e=>({component:k(e)||(()=>null)})},37554:(e,t,a)=>{var n;a.d(t,{t:()=>n}),function(e){e.BIG_QUERY="BIG_QUERY",e.DUCKDB="DUCKDB",e.POSTGRES="POSTGRES",e.MYSQL="MYSQL",e.MSSQL="MSSQL",e.CLICK_HOUSE="CLICK_HOUSE",e.TRINO="TRINO",e.SNOWFLAKE="SNOWFLAKE"}(n||(n={}))},24351:(e,t,a)=>{a.d(t,{HE:()=>E,BA:()=>i,tW:()=>p.t,Hk:()=>c,SD:()=>r,oD:()=>R.uT,E6:()=>d,mo:()=>h,dI:()=>N,QZ:()=>R.Jq,F4:()=>u,y$:()=>o,L6:()=>I,ye:()=>l,bu:()=>s,Q_:()=>n}),function(e){e.CREATE="CREATE",e.EDIT="EDIT"}(r||(r={}));let n=(e,t)=>t.map(t=>`${t}:${e[t]||""}`).join("☺"),s=e=>Object.fromEntries(e.split("☺").map(e=>e.split(":")));!function(e){e.STARTER="starter",e.CREATE_DATA_SOURCE="createDataSource",e.SELECT_MODELS="selectModels",e.RECOMMEND_RELATIONS="recommendRelations",e.DEFINE_RELATIONS="defineRelations"}(l||(l={}));var r,l,i,o,d,c,m,u,E,I,N,h,p=a(37554);!function(e){e.BOOLEAN="BOOLEAN",e.TINYINT="TINYINT",e.INT2="INT2",e.SMALLINT="SMALLINT",e.INT4="INT4",e.INTEGER="INTEGER",e.INT8="INT8",e.BIGINT="BIGINT",e.INT64="INT64",e.NUMERIC="NUMERIC",e.DECIMAL="DECIMAL",e.FLOAT4="FLOAT4",e.REAL="REAL",e.FLOAT8="FLOAT8",e.DOUBLE="DOUBLE",e.VARCHAR="VARCHAR",e.CHAR="CHAR",e.BPCHAR="BPCHAR",e.TEXT="TEXT",e.STRING="STRING",e.NAME="NAME",e.TIMESTAMP="TIMESTAMP",e.TIMESTAMPTZ="TIMESTAMP WITH TIME ZONE",e.DATE="DATE",e.INTERVAL="INTERVAL",e.JSON="JSON",e.RECORD="RECORD",e.OID="OID",e.BYTEA="BYTEA",e.VARBINARY="VARBINARY",e.UUID="UUID",e.INET="INET",e.UNKNOWN="UNKNOWN"}(i||(i={}));var R=a(82103);(function(e){e.Home="/home",e.HomeDashboard="/home/<USER>",e.Thread="/home/<USER>",e.Modeling="/modeling",e.Onboarding="/setup",e.OnboardingConnection="/setup/connection",e.OnboardingModels="/setup/models",e.OnboardingRelationships="/setup/relationships",e.Knowledge="/knowledge",e.KnowledgeQuestionSQLPairs="/knowledge/question-sql-pairs",e.KnowledgeInstructions="/knowledge/instructions",e.APIManagement="/api-management",e.APIManagementHistory="/api-management/history"})(o||(o={})),function(e){e.MANY="many",e.ONE="one"}(d||(d={})),function(e){e.STEP="step",e.SMOOTHSTEP="smoothstep",e.BEZIER="bezier",e.MODEL="model",e.METRIC="metric"}(c||(c={})),function(e){e.NONE="none",e.VIEW_SQL="view_sql",e.PREVIEW_DATA="preview_data"}(m||(m={})),function(e){e[e.IDLE=0]="IDLE",e[e.UNDERSTANDING=1]="UNDERSTANDING",e[e.SEARCHING=2]="SEARCHING",e[e.PLANNING=3]="PLANNING",e[e.GENERATING=4]="GENERATING",e[e.CORRECTING=5]="CORRECTING",e[e.FINISHED=6]="FINISHED",e[e.FAILED=7]="FAILED",e[e.STOPPED=8]="STOPPED",e[e.NO_RESULT=9]="NO_RESULT"}(u||(u={})),function(e){e.ANSWER="answer",e.VIEW_SQL="view-sql",e.CHART="chart"}(E||(E={})),function(e){e.DATA_SOURCE="DATA_SOURCE",e.PROJECT="PROJECT"}(I||(I={})),function(e){e.EDIT="edit",e.DELETE="delete",e.UPDATE_COLUMNS="update_columns",e.CACHE_SETTINGS="cache_settings",e.REFRESH="refresh",e.HIDE_CATEGORY="hide_category",e.VIEW_SQL_PAIR="view_sql_pair",e.VIEW_INSTRUCTION="view_instruction",e.ADJUST_SQL="adjust_sql",e.ADJUST_STEPS="adjust_steps"}(N||(N={})),function(e){e.QUESTION_SQL_PAIRS="question-sql-pairs",e.INSTRUCTIONS="instructions",e.API_HISTORY="api-history",e.API_REFERENCE="api-reference"}(h||(h={}))},28303:(e,t,a)=>{a.d(t,{q:()=>n});let n={CONNECTION:{DISPLAY_NAME:{REQUIRED:"Please input display name."},PROJECT_ID:{REQUIRED:"Please input project id."},DATASET_ID:{REQUIRED:"Please input dataset ID."},CREDENTIAL:{REQUIRED:"Please upload credential."},INIT_SQL:{REQUIRED:"Please input initial SQL statements."},CONFIGURATION:{KEY:{REQUIRED:"Please input configuration key."},VALUE:{REQUIRED:"Please input configuration value."}},HOST:{REQUIRED:"Please input host.",INVALID:"Invalid host. Use 'host.docker.internal' on macOS/Windows to connect to the local database."},PORT:{REQUIRED:"Please input port."},USERNAME:{REQUIRED:"Please input username."},PASSWORD:{REQUIRED:"Please input password."},DATABASE:{REQUIRED:"Please input database name."},SCHEMA:{REQUIRED:"Please input schema name."},SCHEMAS:{REQUIRED:"Please input list of catalog.schema separated by comma."},ACCOUNT:{REQUIRED:"Please input account."}},ADD_RELATION:{FROM_FIELD:{REQUIRED:"Please select a field."},TO_FIELD:{REQUIRED:"Please select a field."},RELATION_TYPE:{REQUIRED:"Please select a relationship type."},RELATIONSHIP:{EXIST:"This relationship already exists."}},SETUP_MODEL:{TABLE:{REQUIRED:"Please select at least one table."}},SAVE_AS_VIEW:{NAME:{REQUIRED:"Please input view name."}},MODELING_CREATE_MODEL:{TABLE:{REQUIRED:"Please select a table."},COLUMNS:{REQUIRED:"Please select at least one column."},PRIMARY_KEY:{INVALID:"Please select again, the primary key must be one of the selected columns."}},CALCULATED_FIELD:{NAME:{REQUIRED:"Please input field name."},EXPRESSION:{REQUIRED:"Please select an expression."},LINEAGE:{REQUIRED:"Please select a field.",INVALID_STRING_TYPE:"Please select a string type field.",INVALID_NUMBER_TYPE:"Please select a number type field."}},SQL_PAIR:{SQL:{REQUIRED:"Please input SQL statement."},QUESTION:{REQUIRED:"Please input a matching question.",MAX_LENGTH:"Question must be 300 characters or fewer."}},INSTRUCTION:{DETAILS:{REQUIRED:"Please input an instruction details."},QUESTIONS:{REQUIRED:"Please input a matching question."},IS_DEFAULT_GLOBAL:{REQUIRED:"Please select how to apply this instruction."}},FIX_SQL:{SQL:{REQUIRED:"Please input SQL statement."}},ADJUST_REASONING:{SELECTED_MODELS:{REQUIRED:"Please select at least one model"},STEPS:{REQUIRED:"Please input reasoning steps",MAX_LENGTH:"Reasoning steps must be 3000 characters or fewer."}},IMPORT_DATA_SOURCE_SQL:{SQL:{REQUIRED:"Please input SQL statement."}},CRON:{REQUIRED:"Please input cron expression.",INVALID:"Invalid cron expression."},CACHE_SETTINGS:{DAY:{REQUIRED:"Please select day."},TIME:{REQUIRED:"Please select time."}}}},23613:(e,t,a)=>{a.d(t,{lV:()=>l,pQ:()=>r,yA:()=>s});var n=a(82103);let s=[n.i_.AVG,n.i_.COUNT,n.i_.MAX,n.i_.MIN,n.i_.SUM],r=[n.i_.ABS,n.i_.CBRT,n.i_.CEIL,n.i_.EXP,n.i_.FLOOR,n.i_.LN,n.i_.LOG10,n.i_.ROUND,n.i_.SIGN],l=[n.i_.LENGTH,n.i_.REVERSE]},59817:(e,t,a)=>{a.d(t,{Bk:()=>w,D7:()=>G,Du:()=>M,ET:()=>B,EU:()=>j,F$:()=>Q,NA:()=>P,ON:()=>F,Qu:()=>O,Rh:()=>C,Se:()=>L,T9:()=>k,VL:()=>x,Vu:()=>_,Ye:()=>A,ZA:()=>U,cJ:()=>y,km:()=>v,nX:()=>q,r$:()=>H,r3:()=>D,tT:()=>b});var n=a(57518),s=a.n(n),r=a(20504),l=a(28532),i=a(48074),o=a(45739),d=a(23318),c=a(1042),m=a.n(c),u=a(88839),E=a.n(u),I=a(88842),N=a.n(I),h=a(66946),p=a.n(h),R=a(3665),g=a(51968),S=a.n(g),T=a(27852),f=a(45625);let D=s()(o.SortNumerically).withConfig({displayName:"icons__NumericIcon",componentId:"sc-1b106fcf-0"})(["height:1em;"]),A=s()(o.Tick).withConfig({displayName:"icons__TickIcon",componentId:"sc-1b106fcf-1"})(["height:1em;"]),C=s()(o.SortAlphabetically).withConfig({displayName:"icons__StringIcon",componentId:"sc-1b106fcf-2"})(["height:1em;"]),x=s()(l.Text).withConfig({displayName:"icons__TextIcon",componentId:"sc-1b106fcf-3"})(["height:1em;"]),O=s()(l.Calendar).withConfig({displayName:"icons__CalendarIcon",componentId:"sc-1b106fcf-4"})(["height:1em;"]),_=s()(l.IdCard).withConfig({displayName:"icons__IdIcon",componentId:"sc-1b106fcf-5"})(["height:1em;"]),b=s()(i.Braces).withConfig({displayName:"icons__JsonBracesIcon",componentId:"sc-1b106fcf-6"})(["height:1em;"]),y=s()(m()).withConfig({displayName:"icons__BinaryIcon",componentId:"sc-1b106fcf-7"})(["height:1em;"]),L=s()(r.Columns).withConfig({displayName:"icons__ColumnsIcon",componentId:"sc-1b106fcf-8"})(["height:1em;"]);s()(l.InfoCircle).withConfig({displayName:"icons__InfoIcon",componentId:"sc-1b106fcf-9"})(["height:1em;"]);let j=s()(d.VpnKey).withConfig({displayName:"icons__PrimaryKeyIcon",componentId:"sc-1b106fcf-10"})(["height:1em;"]),U=s()(l.Cube).withConfig({displayName:"icons__ModelIcon",componentId:"sc-1b106fcf-11"})(["height:1em;"]);s()(d.CenterFocusWeak).withConfig({displayName:"icons__FocusIcon",componentId:"sc-1b106fcf-12"})(["height:1em;"]),s()(i.Map2).withConfig({displayName:"icons__MapIcon",componentId:"sc-1b106fcf-13"})(["height:1em;"]);let P=s()(N()).withConfig({displayName:"icons__RelationshipIcon",componentId:"sc-1b106fcf-14"})(["height:1em;"]);s()(E()).withConfig({displayName:"icons__MonitorIcon",componentId:"sc-1b106fcf-15"})(["height:1em;"]);let M=s()(d.Refresh).withConfig({displayName:"icons__RefreshIcon",componentId:"sc-1b106fcf-16"})([""]),v=s()(l.LineChart).withConfig({displayName:"icons__MetricIcon",componentId:"sc-1b106fcf-17"})(["height:1em;"]);s()(p()).withConfig({displayName:"icons__ShareIcon",componentId:"sc-1b106fcf-18"})([""]);let w=s()(R.LightningCharge).withConfig({displayName:"icons__LightningIcon",componentId:"sc-1b106fcf-19"})(["height:1em;"]),q=s()(S()).withConfig({displayName:"icons__MoreIcon",componentId:"sc-1b106fcf-20"})([""]),F=s()(d.Pageview).withConfig({displayName:"icons__ViewIcon",componentId:"sc-1b106fcf-21"})(["height:1em;"]);s()(d.Explore).withConfig({displayName:"icons__ExploreIcon",componentId:"sc-1b106fcf-22"})(["height:1em;"]),s()(T.Sparkles).withConfig({displayName:"icons__SparklesIcon",componentId:"sc-1b106fcf-23"})(["height:1em;"]);let Q=s()(R.Binoculars).withConfig({displayName:"icons__BinocularsIcon",componentId:"sc-1b106fcf-24"})(["height:16px;width:14px;"]),G=s()(f.Discord).withConfig({displayName:"icons__DiscordIcon",componentId:"sc-1b106fcf-25"})(["height:1em;"]),B=s()(f.Github).withConfig({displayName:"icons__GithubIcon",componentId:"sc-1b106fcf-26"})(["height:1em;"]),k=s()(d.Translate).withConfig({displayName:"icons__TranslateIcon",componentId:"sc-1b106fcf-27"})(["height:1em;"]),H=s()(d.OpenInNew).withConfig({displayName:"icons__OpenInNewIcon",componentId:"sc-1b106fcf-28"})(["height:1em;"])},95015:(e,t,a)=>{a.d(t,{x:()=>s});var n=a(20997);let s=e=>t=>{let{data:a,keyIndex:s="key",...r}=t,l=a.map((t,l)=>{let i="function"==typeof s?s(t):t[s];return n.jsx(e,{data:a,index:l,...r,...t},`${l}-${i}`)});return n.jsx(n.Fragment,{children:l})}},99430:(e,t,a)=>{a.d(t,{gZ:()=>d,r7:()=>o,rv:()=>l,kN:()=>c,xq:()=>N,kq:()=>h,bI:()=>R,qd:()=>m,x7:()=>S,KT:()=>p,gY:()=>g});var n=a(24351),s=a(28303),r=a(23613);let l=e=>async(t,a)=>{if(!a)return Promise.reject(s.q.CALCULATED_FIELD.NAME.REQUIRED);let n=await e(a),{valid:r,message:l}=n?.data?.validateCalculatedField;return r?Promise.resolve():Promise.reject(l)},i=(e,t)=>(a,s)=>!([n.QZ.FIELD,n.QZ.CALCULATED_FIELD].includes(s.nodeType)&&e.includes(a))||t.includes(s.type.toLocaleUpperCase()),o=i(r.lV,[n.BA.VARCHAR,n.BA.CHAR,n.BA.BPCHAR,n.BA.TEXT,n.BA.STRING,n.BA.NAME]),d=i(r.pQ,[n.BA.TINYINT,n.BA.INT2,n.BA.SMALLINT,n.BA.INT4,n.BA.INTEGER,n.BA.INT8,n.BA.BIGINT,n.BA.INT64,n.BA.NUMERIC,n.BA.DECIMAL,n.BA.FLOAT4,n.BA.REAL,n.BA.FLOAT8,n.BA.DOUBLE]),c=e=>(t,a)=>{if(!a)return Promise.reject(Error(s.q.CALCULATED_FIELD.LINEAGE.REQUIRED));let r=a[a.length-1];return[n.QZ.FIELD,n.QZ.CALCULATED_FIELD].includes(r.nodeType)?o(e,r)?d(e,r)?Promise.resolve():Promise.reject(Error(s.q.CALCULATED_FIELD.LINEAGE.INVALID_NUMBER_TYPE)):Promise.reject(Error(s.q.CALCULATED_FIELD.LINEAGE.INVALID_STRING_TYPE)):Promise.reject(Error(s.q.CALCULATED_FIELD.LINEAGE.REQUIRED))},m=e=>async(t,a)=>{if(!a)return Promise.reject(s.q.SAVE_AS_VIEW.NAME.REQUIRED);let n=await e({variables:{data:{name:a}}}),{valid:r,message:l}=n?.data?.validateView;return r?Promise.resolve():Promise.reject(l)};var u=a(17544),E=a(79130);let I=(e,t)=>!!e[t.fromField.modelName].find(e=>e.fromField.modelId===t.fromField.modelId&&e.fromField.fieldId===t.fromField.fieldId&&e.toField.modelId===t.toField.modelId&&e.toField.fieldId===t.toField.fieldId)||!!e[t.toField.modelName].find(e=>e.fromField.modelId===t.toField.modelId&&e.fromField.fieldId===t.toField.fieldId&&e.toField.modelId===t.fromField.modelId&&e.toField.fieldId===t.fromField.fieldId),N=(e=!1,t,a)=>async(n,r)=>{if(!r||!r.field)return Promise.reject(s.q.ADD_RELATION.FROM_FIELD.REQUIRED);if(!e){let e=a(E.J.TO_FIELD);if(e&&e.model&&e.field&&I(t,(0,u.T9)({fromField:r,toField:e})))return Promise.reject(s.q.ADD_RELATION.RELATIONSHIP.EXIST)}return Promise.resolve()},h=(e=!1,t,a)=>async(n,r)=>{if(!r||!r.field)return Promise.reject(s.q.ADD_RELATION.TO_FIELD.REQUIRED);if(!e){let e=a(E.J.FROM_FIELD);if(e&&e.model&&e.field&&I(t,(0,u.T9)({fromField:e,toField:r})))return Promise.reject(s.q.ADD_RELATION.RELATIONSHIP.EXIST)}return Promise.resolve()},p=(e,t)=>t?["localhost","127.0.0.1"].includes(t)?Promise.reject(s.q.CONNECTION.HOST.INVALID):Promise.resolve():Promise.reject(s.q.CONNECTION.HOST.REQUIRED),R=e=>async(t,a)=>a&&""!==a.trim()?a.length>300?Promise.reject(e.MAX_LENGTH):Promise.resolve():Promise.reject(e.REQUIRED),g=e=>e?.trim().split(" ").length===5,S=(e,t)=>t?g(t)?Promise.resolve():Promise.reject(s.q.CRON.INVALID):Promise.reject(s.q.CRON.REQUIRED)}};