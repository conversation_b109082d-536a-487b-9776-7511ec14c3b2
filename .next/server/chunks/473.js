"use strict";exports.id=473,exports.ids=[473],exports.modules={94086:(e,t,a)=>{a.d(t,{O:()=>n.a});var s=a(71030),n=a.n(s)},53560:(e,t,a)=>{a.d(t,{i:()=>c});var s=a(29114);let n=(0,s.gql)`
  fragment CommonError on Error {
    code
    shortMessage
    message
    stacktrace
  }
`,r=(0,s.gql)`
  fragment CommonBreakdownDetail on ThreadResponseBreakdownDetail {
    queryId
    status
    description
    steps {
      summary
      sql
      cteName
    }
    error {
      ...CommonError
    }
  }

  ${n}
`,o=(0,s.gql)`
  fragment CommonAnswerDetail on ThreadResponseAnswerDetail {
    queryId
    status
    content
    numRowsUsedInLLM
    error {
      ...CommonError
    }
  }

  ${n}
`,d=(0,s.gql)`
  fragment CommonChartDetail on ThreadResponseChartDetail {
    queryId
    status
    description
    chartType
    chartSchema
    error {
      ...CommonError
    }
    adjustment
  }
`,i=(0,s.gql)`
  fragment CommonAskingTask on AskingTask {
    status
    type
    candidates {
      sql
      type
      view {
        id
        name
        statement
        displayName
      }
      sqlPair {
        id
        question
        sql
        projectId
      }
    }
    error {
      ...CommonError
    }
    rephrasedQuestion
    intentReasoning
    sqlGenerationReasoning
    retrievedTables
    invalidSql
    traceId
    queryId
  }
  ${n}
`,l=(0,s.gql)`
  fragment CommonResponse on ThreadResponse {
    id
    threadId
    question
    sql
    view {
      id
      name
      statement
      displayName
    }
    breakdownDetail {
      ...CommonBreakdownDetail
    }
    answerDetail {
      ...CommonAnswerDetail
    }
    chartDetail {
      ...CommonChartDetail
    }
    askingTask {
      ...CommonAskingTask
    }
    adjustment {
      type
      payload
    }
    adjustmentTask {
      queryId
      status
      error {
        ...CommonError
      }
      sql
      traceId
      invalidSql
    }
  }

  ${r}
  ${o}
  ${d}
  ${i}
  ${n}
`,m=(0,s.gql)`
  fragment CommonRecommendedQuestionsTask on RecommendedQuestionsTask {
    status
    questions {
      question
      category
      sql
    }
    error {
      ...CommonError
    }
  }

  ${n}
`;(0,s.gql)`
  query SuggestedQuestions {
    suggestedQuestions {
      questions {
        label
        question
      }
    }
  }
`,(0,s.gql)`
  query AskingTask($taskId: String!) {
    askingTask(taskId: $taskId) {
      ...CommonAskingTask
    }
  }
  ${i}
`,(0,s.gql)`
  query Threads {
    threads {
      id
      summary
    }
  }
`;let c=(0,s.gql)`
  query Thread($threadId: Int!) {
    thread(threadId: $threadId) {
      id
      responses {
        ...CommonResponse
      }
    }
  }
  ${l}
`;(0,s.gql)`
  query ThreadResponse($responseId: Int!) {
    threadResponse(responseId: $responseId) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  mutation CreateAskingTask($data: AskingTaskInput!) {
    createAskingTask(data: $data) {
      id
    }
  }
`,(0,s.gql)`
  mutation CancelAskingTask($taskId: String!) {
    cancelAskingTask(taskId: $taskId)
  }
`,(0,s.gql)`
  mutation RerunAskingTask($responseId: Int!) {
    rerunAskingTask(responseId: $responseId) {
      id
    }
  }
`,(0,s.gql)`
  mutation CreateThread($data: CreateThreadInput!) {
    createThread(data: $data) {
      id
    }
  }
`,(0,s.gql)`
  mutation CreateThreadResponse(
    $threadId: Int!
    $data: CreateThreadResponseInput!
  ) {
    createThreadResponse(threadId: $threadId, data: $data) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  mutation UpdateThread(
    $where: ThreadUniqueWhereInput!
    $data: UpdateThreadInput!
  ) {
    updateThread(where: $where, data: $data) {
      id
      summary
    }
  }
`,(0,s.gql)`
  mutation UpdateThreadResponse(
    $where: ThreadResponseUniqueWhereInput!
    $data: UpdateThreadResponseInput!
  ) {
    updateThreadResponse(where: $where, data: $data) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  mutation AdjustThreadResponse(
    $responseId: Int!
    $data: AdjustThreadResponseInput!
  ) {
    adjustThreadResponse(responseId: $responseId, data: $data) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  mutation DeleteThread($where: ThreadUniqueWhereInput!) {
    deleteThread(where: $where)
  }
`,(0,s.gql)`
  mutation PreviewData($where: PreviewDataInput!) {
    previewData(where: $where)
  }
`,(0,s.gql)`
  mutation PreviewBreakdownData($where: PreviewDataInput!) {
    previewBreakdownData(where: $where)
  }
`,(0,s.gql)`
  query GetNativeSQL($responseId: Int!) {
    nativeSql(responseId: $responseId)
  }
`,(0,s.gql)`
  mutation CreateInstantRecommendedQuestions(
    $data: InstantRecommendedQuestionsInput!
  ) {
    createInstantRecommendedQuestions(data: $data) {
      id
    }
  }
`,(0,s.gql)`
  query InstantRecommendedQuestions($taskId: String!) {
    instantRecommendedQuestions(taskId: $taskId) {
      ...CommonRecommendedQuestionsTask
    }
  }
  ${m}
`,(0,s.gql)`
  query GetThreadRecommendationQuestions($threadId: Int!) {
    getThreadRecommendationQuestions(threadId: $threadId) {
      ...CommonRecommendedQuestionsTask
    }
  }

  ${m}
`,(0,s.gql)`
  query GetProjectRecommendationQuestions {
    getProjectRecommendationQuestions {
      ...CommonRecommendedQuestionsTask
    }
  }

  ${m}
`,(0,s.gql)`
  mutation GenerateProjectRecommendationQuestions {
    generateProjectRecommendationQuestions
  }
`,(0,s.gql)`
  mutation GenerateThreadRecommendationQuestions($threadId: Int!) {
    generateThreadRecommendationQuestions(threadId: $threadId)
  }
`,(0,s.gql)`
  mutation GenerateThreadResponseAnswer($responseId: Int!) {
    generateThreadResponseAnswer(responseId: $responseId) {
      ...CommonResponse
    }
  }

  ${l}
`,(0,s.gql)`
  mutation GenerateThreadResponseChart($responseId: Int!) {
    generateThreadResponseChart(responseId: $responseId) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  mutation AdjustThreadResponseChart(
    $responseId: Int!
    $data: AdjustThreadResponseChartInput!
  ) {
    adjustThreadResponseChart(responseId: $responseId, data: $data) {
      ...CommonResponse
    }
  }
  ${l}
`,(0,s.gql)`
  query AdjustmentTask($taskId: String!) {
    adjustmentTask(taskId: $taskId) {
      queryId
      status
      error {
        code
        shortMessage
        message
        stacktrace
      }
      sql
      traceId
      invalidSql
    }
  }
`,(0,s.gql)`
  mutation CancelAdjustmentTask($taskId: String!) {
    cancelAdjustmentTask(taskId: $taskId)
  }
`,(0,s.gql)`
  mutation RerunAdjustmentTask($responseId: Int!) {
    rerunAdjustmentTask(responseId: $responseId)
  }
`},1682:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>m});var n=a(20997),r=a(57518),o=a.n(r),d=a(23135),i=a(66809),l=e([d,i]);[d,i]=l.then?(await l)():l;let c=o()(d.default).withConfig({displayName:"MarkdownBlock__ReactMarkdownBlock",componentId:"sc-99fa7ae9-0"})(["h1,h2,h3,h4,h5,h6{color:var(--gray-10);margin-bottom:8px;}h1{font-size:20px;}h2{font-size:18px;}h3{font-size:16px;}h4{font-size:14px;}hr{border-top:1px solid var(--gray-5);border-bottom:none;border-left:none;border-right:none;margin:18px 0;}pre{background-color:var(--gray-2);border:1px var(--gray-4) solid;padding:16px;border-radius:4px;}table td,table th{border:1px solid var(--gray-4);padding:4px 8px;}table th{background-color:var(--gray-2);font-weight:600;}table{border:1px solid var(--gray-4);border-collapse:collapse;margin-bottom:16px;}ol,ul,dl{padding-inline-start:20px;}h1 code,h2 code,h3 code,h4 code,li code,p code{font-size:12px;background:var(--gray-4);color:var(--gray-8);padding:2px 4px;border-radius:4px;}"]);function m(e){return n.jsx(c,{remarkPlugins:[i.default],children:e.content})}s()}catch(e){s(e)}})},86722:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>h,j:()=>N});var n=a(20997),r=a(16593),o=a(57518),d=a.n(o),i=a(16689),l=a(94086),m=a(10889),c=a.n(m),u=a(95015),p=a(82103),I=e([r]);r=(I.then?(await I)():I)[0];let g=d()(l.O).withConfig({displayName:"RecommendedQuestions__StyledSkeleton",componentId:"sc-2ba47ac7-0"})(["padding:4px 0;.ant-skeleton-paragraph{margin-bottom:0;li{height:14px;+ li{margin-top:12px;}}}"]),N=(e,t=!0)=>{if(!e||!t)return{show:!1};let a=(e?.questions||[]).slice(0,3).map(e=>({question:e.question,sql:e.sql})),s=e?.status===p.Tj.GENERATING;return{show:s||a.length>0,state:{items:a,loading:s,error:e?.error}}},x=(0,u.x)(e=>{let{index:t,question:a,sql:s,onSelect:o}=e;return n.jsx("div",{className:(0,r.default)(t>0&&"mt-1"),children:n.jsx("span",{className:"cursor-pointer hover:text",onClick:()=>o({question:a,sql:s}),children:a})})});function h(e){let{items:t,loading:a,className:s,onSelect:o}=e,d=(0,i.useMemo)(()=>t.map(({question:e,sql:t})=>({question:e,sql:t})),[t]);return(0,n.jsxs)("div",{className:(0,r.default)("bg-gray-2 rounded p-3",s),children:[(0,n.jsxs)("div",{className:"mb-2",children:[n.jsx(c(),{className:"mr-1 gray-6"}),n.jsx("b",{className:"text-semi-bold text-sm gray-7",children:"Recommended questions"})]}),n.jsx("div",{className:"pl-1 gray-8",children:n.jsx(g,{active:!0,loading:a,paragraph:{rows:3},title:!1,children:n.jsx(x,{data:d,onSelect:o})})})]})}s()}catch(e){s(e)}})},48233:(e,t,a)=>{a.d(t,{Z:()=>p});var s=a(20997),n=a(16689),r=a(53800),o=a.n(r),d=a(30675),i=a.n(d),l=a(57518),m=a.n(l),c=a(48099);let u=m()(o()).withConfig({displayName:"Input__PromptButton",componentId:"sc-4c88e34d-0"})(["min-width:72px;"]);function p(e){let{onAsk:t,isProcessing:a,question:r,inputProps:o}=e,d=(0,n.useRef)(null),[l,m]=(0,n.useState)(""),[p,I]=(0,n.useState)(!1),h=()=>{let e=l.trim();e&&(0,c.iZ)(t,I)(e)},g=p||a;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(i().TextArea,{ref:d,"data-gramm":"false",size:"large",autoSize:!0,value:l,onInput:e=>{m(e.target.value)},onPressEnter:e=>{e.shiftKey||(e.preventDefault(),h())},disabled:g,...o}),s.jsx(u,{type:"primary",size:"large",className:"ml-3",onClick:h,disabled:g,children:"Ask"})]})}},65998:(e,t,a)=>{a.a(e,async(e,s)=>{try{let C;a.d(t,{Z:()=>Z});var n=a(20997),r=a(16593),o=a(16689),d=a(46810),i=a(57518),l=a.n(i),m=a(24351),c=a(48099),u=a(23221),p=a.n(u),I=a(22553),h=a.n(I),g=a(47263),N=a.n(g),x=a(1207),T=a.n(x),y=a(34869),R=a.n(y),k=a(31159),E=a.n(k),j=a(95308),S=a(62125),q=a.n(S),A=a(86722),$=a(1682),w=a(82103),F=e([r,A,$]);[r,A,$]=F.then?(await F)():F;let v=l().div.withConfig({displayName:"Result__StyledResult",componentId:"sc-a8a5bd3f-0"})(["position:absolute;bottom:calc(100% + 12px);left:0;width:100%;background:white;box-shadow:rgba(0,0,0,0.1) 0px 10px 15px -3px,rgba(0,0,0,0.05) 0px 4px 6px -2px;"]),b=({children:e})=>n.jsx(v,{className:"border border-gray-3 rounded p-4","data-testid":"prompt__result",children:e}),f=e=>t=>{let{onClose:a,onSelectRecommendedQuestion:s,data:r,error:o}=t,{message:i,shortMessage:l,stacktrace:m}=o||{},c=(0,A.j)(r?.recommendedQuestions);return(0,n.jsxs)(b,{children:[(0,n.jsxs)("div",{className:"d-flex justify-space-between text-medium mb-2",children:[(0,n.jsxs)("div",{className:"d-flex align-center",children:[e.icon,e.title||l]}),(0,n.jsxs)(d.z,{className:"adm-btn-no-style gray-7 bg-gray-3 text-sm px-2",type:"text",size:"small",onClick:a,children:[n.jsx(p(),{className:"-mr-1"}),"Close"]})]}),n.jsx("div",{className:"gray-7",children:e.description||r.intentReasoning||i}),!!m&&n.jsx(j.Z,{className:"mt-2",message:m.join("\n")}),c.show&&n.jsx(A.Z,{className:"mt-2",...c.state,onSelect:s})]})},D=f({icon:n.jsx(()=>n.jsx(T(),{className:"mr-2 red-5 text-lg"}),{})}),G=(C="Understanding question",e=>{let{onStop:t}=e,[a,s]=(0,o.useState)(!1);return n.jsx(b,{children:(0,n.jsxs)("div",{className:"d-flex justify-space-between",children:[(0,n.jsxs)("span",{children:[n.jsx(N(),{className:"mr-2 geekblue-6 text-lg",spin:!0}),C]}),(0,n.jsxs)(d.z,{className:(0,r.default)("adm-btn-no-style bg-gray-3 text-sm px-2",a?"gray-6":"gray-7"),type:"text",size:"small",onClick:(0,c.iZ)(t,s),disabled:a,children:[n.jsx(h(),{className:"-mr-1"}),"Stop"]})]})})}),P=e=>{let{data:t,onIntentSQLAnswer:a}=e,{type:s}=t;return(0,o.useEffect)(()=>{s===w.oB.TEXT_TO_SQL&&a&&a()},[s]),n.jsx(G,{...e})},Q=e=>{let{onClose:t,onSelectRecommendedQuestion:a,data:s,loading:r}=e,i=(0,o.useRef)(null),{originalQuestion:l,askingStreamTask:m,recommendedQuestions:c}=s,u=m&&!r,I=()=>{i.current&&i.current.scrollTo({top:i.current.scrollHeight})};(0,o.useEffect)(()=>{I()},[m]),(0,o.useEffect)(()=>{u&&I()},[u]);let h=(0,A.j)(c);return(0,n.jsxs)(b,{children:[(0,n.jsxs)("div",{className:"d-flex justify-space-between",children:[(0,n.jsxs)("div",{className:"d-flex align-start",children:[n.jsx(E(),{className:"mr-2 mt-1 geekblue-6"}),n.jsx("b",{className:"text-semi-bold",children:l})]}),(0,n.jsxs)(d.z,{className:"adm-btn-no-style gray-7 bg-gray-3 text-sm px-2",type:"text",size:"small",onClick:t,children:[n.jsx(p(),{className:"-mr-1"}),"Close"]})]}),n.jsx("div",{className:"py-3",children:(0,n.jsxs)("div",{ref:i,className:"py-2 px-3",style:{maxHeight:"calc(100vh - 420px)",overflowY:"auto"},children:[n.jsx($.Z,{content:m}),u&&(0,n.jsxs)("div",{className:"gray-6",children:[n.jsx(q(),{className:"mr-2"}),"For the most accurate semantics, please visit the modeling page."]})]})}),h.show&&n.jsx(A.Z,{...h.state,onSelect:a})]})},L=f({icon:n.jsx(R(),{className:"mr-2 text-lg gold-6"}),title:"Clarification needed"}),H=e=>({[m.F4.FINISHED]:Q})[e]||null,U=e=>({[m.F4.FINISHED]:L})[e]||null,_=e=>({[m.F4.UNDERSTANDING]:G,[m.F4.SEARCHING]:P,[m.F4.PLANNING]:P,[m.F4.GENERATING]:P,[m.F4.FINISHED]:P,[m.F4.FAILED]:D})[e]||null,O=e=>e===w.oB.GENERAL?H:e===w.oB.MISLEADING_QUERY?U:_,Z=(0,o.memo)(function(e){let{processState:t,data:a}=e,s=O(a?.type)(t);return null===s?null:n.jsx(s,{...e})});s()}catch(e){s(e)}})},67334:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>I});var n=a(20997),r=a(16689),o=a(57518),d=a.n(o),i=a(24351),l=a(48233),m=a(65998),c=a(59027),u=e([m]);m=(u.then?(await u)():u)[0];let p=d().div.withConfig({displayName:"prompt__PromptStyle",componentId:"sc-5b784cfe-0"})(["position:fixed;width:680px;left:50%;margin-left:calc(-340px + 133px);bottom:18px;z-index:999;box-shadow:rgba(0,0,0,0.1) 0px 10px 15px -3px,rgba(0,0,0,0.05) 0px 4px 6px -2px;"]),I=(0,r.forwardRef)(function(e,t){let{data:a,loading:s,onSubmit:o,onStop:d,onCreateResponse:u,onStopStreaming:I,onStopRecommend:h,inputProps:g}=e,N=(0,c.ZP)(),{originalQuestion:x,askingTask:T,askingStreamTask:y,recommendedQuestions:R}=a,k=(0,r.useMemo)(()=>({type:T?.type,originalQuestion:x,askingStreamTask:y,recommendedQuestions:R,intentReasoning:T?.intentReasoning||""}),[a]),E=(0,r.useMemo)(()=>T?.error||null,[T?.error]),[j,S]=(0,r.useState)(!1),[q,A]=(0,r.useState)(""),$=(0,r.useMemo)(()=>N.currentState,[N.currentState]),w=(0,r.useMemo)(()=>(0,c.c1)($),[$]),F=async e=>{u&&await u(e),v()},C=async()=>{u&&await u({question:q,taskId:T?.queryId}),S(!1)},v=()=>{N.resetState(),A(""),I&&I(),h&&h()},b=async()=>{d&&await d(),S(!1),N.resetState()},f=async e=>{A(e),!w&&e&&(N.transitionTo(i.F4.UNDERSTANDING),S(!0),o&&await o(e))};return(0,r.useImperativeHandle)(t,()=>({submit:f,close:v}),[q,w,A]),(0,n.jsxs)(p,{className:"d-flex align-end bg-gray-2 p-3 border border-gray-3 rounded",children:[n.jsx(l.Z,{question:q,isProcessing:w,onAsk:f,inputProps:g}),j&&n.jsx(m.Z,{data:k,error:E,loading:s,processState:$,onSelectRecommendedQuestion:F,onIntentSQLAnswer:C,onClose:v,onStop:b})]})});s()}catch(e){s(e)}})},59027:(e,t,a)=>{a.d(t,{Xq:()=>d,ZI:()=>l,ZP:()=>i,c1:()=>o});var s=a(16689),n=a(24351),r=a(82103);let o=e=>[n.F4.UNDERSTANDING,n.F4.SEARCHING,n.F4.PLANNING,n.F4.GENERATING,n.F4.CORRECTING].includes(e),d=e=>{if(!e)return null;let t={[r.j.UNDERSTANDING]:n.F4.UNDERSTANDING,[r.j.SEARCHING]:n.F4.SEARCHING,[r.j.PLANNING]:n.F4.PLANNING,[r.j.GENERATING]:n.F4.GENERATING,[r.j.CORRECTING]:n.F4.CORRECTING,[r.j.FINISHED]:n.F4.FINISHED,[r.j.STOPPED]:n.F4.STOPPED,[r.j.FAILED]:n.F4.FAILED}[e.status];return e?.type===r.oB.TEXT_TO_SQL&&t===n.F4.FINISHED&&0===e.candidates.length?n.F4.NO_RESULT:t};function i(){let[e,t]=(0,s.useState)(n.F4.IDLE);return{currentState:e,resetState:()=>{t(n.F4.IDLE)},matchedState:t=>{let a=d(t);return a&&a!==e?l.canTransition(e,a)?a:(console.warn(`Invalid transition from ${e} to ${a}.`),e):e},transitionTo:e=>{t(e)},isFinished:()=>e===n.F4.FINISHED,isFailed:()=>e===n.F4.FAILED}}class l{static{this.transitions={[n.F4.IDLE]:{next:[n.F4.UNDERSTANDING],prev:[]},[n.F4.UNDERSTANDING]:{next:[n.F4.SEARCHING,n.F4.PLANNING,n.F4.GENERATING],prev:[n.F4.IDLE]},[n.F4.SEARCHING]:{next:[n.F4.PLANNING],prev:[n.F4.UNDERSTANDING]},[n.F4.PLANNING]:{next:[n.F4.GENERATING],prev:[n.F4.SEARCHING]},[n.F4.GENERATING]:{next:[n.F4.CORRECTING,n.F4.FINISHED,n.F4.FAILED],prev:[n.F4.PLANNING]},[n.F4.CORRECTING]:{next:[n.F4.FINISHED,n.F4.FAILED],prev:[n.F4.GENERATING]},[n.F4.FINISHED]:{next:[],prev:[n.F4.GENERATING,n.F4.CORRECTING]}}}static canTransition(e,t){return e===n.F4.IDLE||t===n.F4.FINISHED||t===n.F4.FAILED||t===n.F4.STOPPED||this.transitions[e]?.next.includes(t)}static getAllNextStates(e,t=!1){let a=new Set(t?[e]:[]),s=e=>{(this.transitions[e]?.next||[]).forEach(e=>{a.has(e)||(a.add(e),s(e))})};return s(e),Array.from(a)}}},25154:(e,t,a)=>{a.d(t,{di:()=>p,ZP:()=>h,eX:()=>u});var s=a(16689),n=a(59591),r=a.n(n),o=a(18459),d=a.n(o),i=a(82103),l=a(38436),m=a(53560),c=a(9401);let u=e=>[i.j.FINISHED,i.j.FAILED,i.j.STOPPED].includes(e),p=e=>null!==e&&e?.status!==i.j.FAILED&&e?.status!==i.j.STOPPED,I=(e,t,a,s)=>{if(!a)return;let n=s.cache.readQuery({query:m.i,variables:{threadId:e}});if(n?.thread){let n=r()(a);n.status===i.j.UNDERSTANDING&&(n.status=i.j.SEARCHING,n.type=i.oB.TEXT_TO_SQL),s.cache.updateQuery({query:m.i,variables:{threadId:e}},e=>({thread:{...e.thread,responses:e.thread.responses.map(e=>e.id===t?{...e,askingTask:n}:e)}}))}};function h(e){let[t,a]=(0,s.useState)(""),[n,r]=(0,s.useState)([]),[o,m]=(0,l.Gf)(),[u]=(0,l.wU)(),[p]=(0,l.vt)(),[h,g]=(0,l.Rh)({pollInterval:1e3}),[N,x]=function(){let e=(0,s.useRef)(null),[t,a]=(0,s.useState)(!1),[n,r]=(0,s.useState)(""),o=()=>{e.current&&(e.current?.close(),e.current=null),r("")};return[t=>{a(!0),o();let s=new EventSource(`/api/ask_task/streaming?queryId=${t}`);s.onmessage=e=>{let t=JSON.parse(e.data);t.done?(s.close(),a(!1)):r(e=>e+(t?.message||""))},s.onerror=e=>{console.error(e),s.close(),a(!1)},e.current=s},{data:n,loading:t,reset:o}]}(),[T]=(0,l.eO)(),[y,R]=(0,l.hC)({pollInterval:1e3}),k=(0,s.useMemo)(()=>g.data?.askingTask||null,[g.data]);(0,s.useMemo)(()=>k?.type,[k?.type]);let E=x.data,j=(0,s.useMemo)(()=>R.data?.instantRecommendedQuestions||null,[R.data]),S=x.loading,q=(0,s.useMemo)(()=>({originalQuestion:t,askingTask:k,askingStreamTask:E,recommendedQuestions:j}),[t,k,E,j]);return(0,s.useCallback)(async()=>{let e=[...d()(n).slice(-5),t];y({variables:{taskId:(await T({variables:{data:{previousQuestions:e}}})).data.createInstantRecommendedQuestions.id}})},[t]),(0,s.useCallback)(e=>{E||e.status!==i.j.PLANNING||N(e.queryId)},[E]),{data:q,loading:S,onStop:async e=>{let t=e||m.data?.createAskingTask.id;t&&(await u({variables:{taskId:t}}).catch(e=>console.error(e)),await (0,c.Y3)(1e3))},onReRun:async t=>{x.reset(),a(t.question);try{let a=await p({variables:{responseId:t.id}}),{data:s}=await h({variables:{taskId:a.data.rerunAskingTask.id}});I(e,t.id,s.askingTask,g.client)}catch(e){console.error(e)}},onSubmit:async t=>{x.reset(),a(t);try{let a=await o({variables:{data:{question:t,threadId:e}}});await h({variables:{taskId:a.data.createAskingTask.id}})}catch(e){console.error(e)}},onFetching:async e=>{await h({variables:{taskId:e}})},onStopPolling:()=>g.stopPolling(),onStopStreaming:()=>x.reset(),onStopRecommend:()=>R.stopPolling(),onStoreThreadQuestions:e=>r(e),inputProps:{placeholder:e?"Ask follow-up questions to explore your data":"Ask to explore your data"}}}},48099:(e,t,a)=>{a.d(t,{Mo:()=>i,iZ:()=>l,yg:()=>d});var s=a(66011),n=a.n(s),r=a(22133),o=a.n(r);let d=e=>n()(e,o()),i=e=>{try{return JSON.parse(e)}catch(t){return e}},l=(e,t)=>async(...a)=>{t(!0);try{await e(...a)}finally{t(!1)}}}};