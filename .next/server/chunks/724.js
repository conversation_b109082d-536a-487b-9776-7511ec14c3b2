"use strict";exports.id=724,exports.ids=[724],exports.modules={9200:(e,t,a)=>{a.d(t,{L:()=>r.a,v:()=>s.a});var n=a(1788),r=a.n(n),o=a(10274),s=a.n(o)},37211:(e,t,a)=>{a.d(t,{u:()=>r.a});var n=a(76418),r=a.n(n)},94488:(e,t,a)=>{a.d(t,{P:()=>r.a});var n=a(53526),r=a.n(n)},49330:(e,t,a)=>{a.d(t,{W:()=>l});var n=a(29114);let r=(0,n.gql)`
  fragment ViewField on DiagramViewField {
    id
    displayName
    referenceName
    type
    nodeType
    description
  }
`,o=(0,n.gql)`
  fragment RelationField on DiagramModelRelationField {
    id
    relationId
    type
    nodeType
    displayName
    referenceName
    fromModelId
    fromModelName
    fromModelDisplayName
    fromColumnId
    fromColumnName
    fromColumnDisplayName
    toModelId
    toModelName
    toModelDisplayName
    toColumnId
    toColumnName
    toColumnDisplayName
    description
  }
`,s=(0,n.gql)`
  fragment NestedField on DiagramModelNestedField {
    id
    nestedColumnId
    columnPath
    type
    displayName
    referenceName
    description
  }
`,i=(0,n.gql)`
  fragment Field on DiagramModelField {
    id
    columnId
    type
    nodeType
    displayName
    referenceName
    description
    isPrimaryKey
    expression
    aggregation
    lineage
    nestedFields {
      ...NestedField
    }
  }
  ${s}
`,l=(0,n.gql)`
  query Diagram {
    diagram {
      models {
        id
        modelId
        nodeType
        displayName
        referenceName
        sourceTableName
        refSql
        cached
        refreshTime
        description
        fields {
          ...Field
        }
        calculatedFields {
          ...Field
        }
        relationFields {
          ...RelationField
        }
      }
      views {
        id
        viewId
        nodeType
        displayName
        description
        referenceName
        statement
        fields {
          ...ViewField
        }
      }
    }
  }
  ${i}
  ${o}
  ${r}
`},50014:(e,t,a)=>{a.d(t,{fV:()=>l,gT:()=>s});var n=a(29114);let r={},o=(0,n.gql)`
    query LearningRecord {
  learningRecord {
    paths
  }
}
    `;function s(e){let t={...r,...e};return n.useQuery(o,t)}let i=(0,n.gql)`
    mutation SaveLearningRecord($data: SaveLearningRecordInput!) {
  saveLearningRecord(data: $data) {
    paths
  }
}
    `;function l(e){let t={...r,...e};return n.useMutation(i,t)}},37119:(e,t,a)=>{a.d(t,{uG:()=>i});var n=a(29114);let r=(0,n.gql)`
  fragment CommonColumn on DetailedColumn {
    displayName
    referenceName
    sourceColumnName
    type
    isCalculated
    notNull
    properties
  }
`,o=(0,n.gql)`
  fragment CommonField on FieldInfo {
    id
    displayName
    referenceName
    sourceColumnName
    type
    isCalculated
    notNull
    expression
    properties
  }
`,s=(0,n.gql)`
  fragment CommonRelation on DetailedRelation {
    fromModelId
    fromColumnId
    toModelId
    toColumnId
    type
    name
  }
`,i=(0,n.gql)`
  query ListModels {
    listModels {
      id
      displayName
      referenceName
      sourceTableName
      refSql
      primaryKey
      cached
      refreshTime
      description
      fields {
        ...CommonField
      }
      calculatedFields {
        ...CommonField
      }
    }
  }
  ${o}
`;(0,n.gql)`
  query GetModel($where: ModelWhereInput!) {
    model(where: $where) {
      displayName
      referenceName
      sourceTableName
      refSql
      primaryKey
      cached
      refreshTime
      description
      fields {
        ...CommonColumn
      }
      calculatedFields {
        ...CommonColumn
      }
      relations {
        ...CommonRelation
      }
      properties
    }
  }
  ${r}
  ${s}
`,(0,n.gql)`
  mutation CreateModel($data: CreateModelInput!) {
    createModel(data: $data)
  }
`,(0,n.gql)`
  mutation UpdateModel($where: ModelWhereInput!, $data: UpdateModelInput!) {
    updateModel(where: $where, data: $data)
  }
`,(0,n.gql)`
  mutation DeleteModel($where: ModelWhereInput!) {
    deleteModel(where: $where)
  }
`,(0,n.gql)`
  mutation PreviewModelData($where: WhereIdInput!) {
    previewModelData(where: $where)
  }
`},59462:(e,t,a)=>{a.d(t,{JO:()=>m,bB:()=>i,in:()=>d,ks:()=>s});var n=a(29114);let r={},o=(0,n.gql)`
    query GetSettings {
  settings {
    productVersion
    dataSource {
      type
      properties
      sampleDataset
    }
    language
  }
}
    `;function s(e){let t={...r,...e};return n.useQuery(o,t)}function i(e){let t={...r,...e};return n.useLazyQuery(o,t)}let l=(0,n.gql)`
    mutation ResetCurrentProject {
  resetCurrentProject
}
    `;function d(e){let t={...r,...e};return n.useMutation(l,t)}let c=(0,n.gql)`
    mutation UpdateCurrentProject($data: UpdateCurrentProjectInput!) {
  updateCurrentProject(data: $data)
}
    `;function m(e){let t={...r,...e};return n.useMutation(c,t)}},71224:(e,t,a)=>{a.d(t,{Z:()=>i});var n=a(20997),r=a(16689),o=a(57518);let s=a.n(o)().div.withConfig({displayName:"EllipsisWrapper__Wrapper",componentId:"sc-d437ba0c-0"})(["overflow:hidden;text-overflow:ellipsis;",""],e=>e.multipleLine?`
  display: -webkit-box;
  -webkit-line-clamp: ${e.multipleLine};
  -webkit-box-orient: vertical;
`:`
  white-space: nowrap;
`);function i(e){let{text:t,multipleLine:a,minHeight:o,children:i,showMoreCount:l}=e,d=(0,r.useRef)(null),c=(0,r.useRef)(null),[m,u]=(0,r.useState)(void 0),g=void 0!==m,[p,h]=(0,r.useState)([]);(0,r.useRef)(!1);let x=Array.isArray(t)?t.join(""):t;return n.jsx(s,{ref:d,title:x,multipleLine:a,style:{width:m,minHeight:o},children:g?(()=>{if(!i)return t||"-";if(l){let e=i.length-p.length;return(0,n.jsxs)("span",{className:"d-inline-block",ref:c,children:[p,e>0&&(0,n.jsxs)("span",{className:"gray-7",children:["...",e," more"]})]})}return i})():null})}},66724:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>g});var r=a(20997),o=a(35629),s=a(57518),i=a.n(s),l=a(21626),d=a(39998),c=a(33878),m=a(47427),u=e([l,d]);[l,d]=u.then?(await u)():u;let{Sider:p}=o.Layout,h=(0,s.css)(["height:calc(100vh - 48px);overflow:auto;"]),x=i()(o.Layout).withConfig({displayName:"SiderLayout__StyledContentLayout",componentId:"sc-3cb7a1b1-0"})(["position:relative;"," ",""],h,e=>e.color&&`background-color: var(--${e.color});`),y=i()(p).withConfig({displayName:"SiderLayout__StyledSider",componentId:"sc-3cb7a1b1-1"})(["",""],h);function g(e){let{sidebar:t,loading:a,color:n}=e,s=(0,m.Z)();return(0,r.jsxs)(l.Z,{loading:a,children:[(0,r.jsxs)(o.Layout,{className:"adm-layout",children:[r.jsx(y,{width:280,children:r.jsx(d.Z,{...t,onOpenSettings:s.openModal})}),r.jsx(x,{color:n,children:e.children})]}),r.jsx(c.Z,{...s.state,onClose:s.closeModal})]})}n()}catch(e){n(e)}})},77134:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>c});var r=a(70222),o=a(11163),s=a(16689),i=a(11699),l=a(59462);a(20655);var d=e([r]);r=(d.then?(await d)():d)[0];let c=(0,s.forwardRef)(function(e,t){let a=(0,o.useRouter)(),n=(0,s.useRef)(null),{data:r}=(0,l.ks)(),d=(0,s.useMemo)(()=>({sampleDataset:r?.settings?.dataSource.sampleDataset,language:r?.settings?.language}),[r?.settings]),c=(e,t)=>{(0,i.N)(n.current,a,d)(e,t)};return(0,s.useImperativeHandle)(t,()=>({play:c}),[n.current,d,a]),null});n()}catch(e){n(e)}})},11699:(e,t,a)=>{a.d(t,{N:()=>w});var n=a(20997),r=a(16689),o=a(7849),s=a(94488),i=a(57518),l=a.n(i),d=a(59817),c=a(56289),m=a(78684),u=a(68330),g=a(24351),p=a(82103),h=a(35188),x=a(45697),y=a(12301),C=a(9401);let f=l()(c.Hx).withConfig({displayName:"stories__RobotIcon",componentId:"sc-f9259573-0"})(["width:24px;height:24px;"]),v={progressText:"{{current}} / {{total}}",nextBtnText:"Next",prevBtnText:"Previous",showButtons:["next"],allowClose:!1},w=(...e)=>(t,a)=>{let n={[u.M.DATA_MODELING_GUIDE]:()=>j(...e,a),[u.M.SWITCH_PROJECT_LANGUAGE]:()=>S(...e,a),[u.M.KNOWLEDGE_GUIDE]:()=>T(...e,a),[u.M.SAVE_TO_KNOWLEDGE]:()=>L(...e,a)}[t]||null;return n&&n()},N=(e,t)=>{let a=e.wrapper;a.style.maxWidth="none",a.style.width=`${t}px`},j=(e,t,a,r)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy();let o=!!a.sampleDataset,s=h.QC[a.sampleDataset];e.setConfig({...v,showProgress:!0}),e.setSteps([{popover:{title:(0,m.renderToString)((0,n.jsxs)("div",{className:"pt-4",children:[n.jsx("div",{className:"-mx-4",style:{minHeight:331},children:n.jsx("img",{className:"mb-4",src:"/images/learning/data-modeling.jpg",alt:"data-modeling-guide"})}),"Data modeling guide"]})),description:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:["Data modeling adds a logical layer over your original data schema, organizing relationships, semantics, and calculations. This helps AI align with business logic, retrieve precise data, and generate meaningful insights."," ",n.jsx("a",{href:"https://docs.getwren.ai/oss/guide/modeling/overview",target:"_blank",rel:"noopener noreferrer",children:"More details"}),n.jsx("br",{}),n.jsx("br",{}),o?(0,n.jsxs)(n.Fragment,{children:["We use ",s.label," Dataset to present the guide. To know more, please visit"," ",(0,n.jsxs)("a",{href:s.guide,target:"_blank",rel:"noopener noreferrer",children:["about the ",s.label," Dataset."]})]}):null]})),showButtons:["next","close"],onPopoverRender:e=>{N(e,720)},onCloseClick:()=>{e.destroy(),window.sessionStorage.setItem("skipDataModelingGuide","1")}}},{element:'[data-guideid="add-model"]',popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"mb-1",children:n.jsx(d.ZA,{style:{fontSize:24}})}),"Create a model"]})),description:(0,m.renderToString)(n.jsx(n.Fragment,{children:"Click the add icon to start create your first model."}))}},{element:'[data-guideid="edit-model-0"]',popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"-mx-4",style:{minHeight:175},children:n.jsx("img",{className:"mb-2",src:"/images/learning/edit-model.gif",alt:"edit-model"})}),"Edit a model"]})),description:(0,m.renderToString)(n.jsx(n.Fragment,{children:"Click the more icon to update the columns of model or delete it."}))}},{element:'[data-guideid="model-0"]',popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"-mx-4",style:{minHeight:214},children:n.jsx("img",{className:"mb-2",src:"/images/learning/edit-metadata.gif",alt:"edit-metadata"})}),"Edit metadata"]})),description:(0,m.renderToString)(n.jsx(n.Fragment,{children:"You could edit alias (alternative name) and descriptions of models and columns."})),onPopoverRender:e=>{N(e,360)}}},{element:'[data-guideid="deploy-model"]',popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"-mx-4",style:{minHeight:102},children:n.jsx("img",{className:"mb-2",src:"/images/learning/deploy-modeling.jpg",alt:"deploy-modeling"})}),"Deploy modeling"]})),description:(0,m.renderToString)(n.jsx(n.Fragment,{children:"After editing the models, remember to deploy the changes."}))}},{popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"-mx-4",style:{minHeight:331},children:n.jsx("img",{className:"mb-2",src:"/images/learning/ask-question.jpg",alt:"ask-question"})}),"Ask questions"]})),description:(0,m.renderToString)(n.jsx(n.Fragment,{children:"When you finish editing your models, you can visit “Home” and start asking questions."})),onPopoverRender:e=>{N(e,720)},doneBtnText:"Go to Home",onNextClick:()=>{t.push(g.y$.Home),e.destroy(),r?.onDone&&r.onDone()}}}]),y.WI(y.W1.GO_TO_FIRST_MODEL),e.drive()},b=e=>{let[t,a]=(0,r.useState)(e.defaultValue),o=Object.keys(p.y9).map(e=>({label:(0,x.p)(e),value:e}));return(0,n.jsxs)(n.Fragment,{children:[n.jsx("label",{className:"d-block mb-2",children:"Project language"}),n.jsx(s.P,{showSearch:!0,style:{width:"100%"},options:o,getPopupContainer:e=>e.parentElement,onChange:e=>{a(e)},value:t}),n.jsx("input",{name:"language",type:"hidden",value:t})]})},S=(e,t,a,r)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...v,showProgress:!1}),e.setSteps([{popover:{title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"mb-1",children:n.jsx(d.T9,{style:{fontSize:24}})}),"Switch the language"]})),description:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:["Choose your preferred language. Once set up, AI will respond in your chosen language.",n.jsx("div",{className:"my-3",children:n.jsx("div",{id:"projectLanguageContainer"})}),"You can go to project settings to change it if you change your mind."]})),onPopoverRender:e=>{N(e,400);let t=document.getElementById("projectLanguageContainer");t&&(0,o.createRoot)(t).render(n.jsx(b,{defaultValue:a.language}))},showButtons:["next","close"],nextBtnText:"Submit",onCloseClick:()=>{e.destroy(),window.sessionStorage.setItem("skipSwitchProjectLanguageGuide","1")},onNextClick:async()=>{let t=document.getElementById("projectLanguageContainer");if(t){let e=t.querySelector('input[name="language"]'),a=document.querySelectorAll(".driver-popover-next-btn")[0],n=document.createElement("span");n.setAttribute("aria-hidden","loading"),n.setAttribute("role","img"),n.className="anticon anticon-loading anticon-spin text-sm gray-6 ml-2",n.innerHTML='<svg viewBox="0 0 1024 1024" focusable="false" data-icon="loading" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg>',a.setAttribute("disabled","true"),a.appendChild(n),await r?.onSaveLanguage(e.value).catch(e=>console.error(e)).finally(()=>{a.removeAttribute("disabled"),a.removeChild(n)})}e.destroy(),r?.onDone()}}}]),e.drive()},T=(e,t,a,r)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...v,showProgress:!0}),e.setSteps([{element:'[data-guideid="question-sql-pairs"]',popover:{title:(0,m.renderToString)((0,n.jsxs)("div",{className:"pt-4",children:[n.jsx("div",{className:"-mx-4",style:{minHeight:317},children:n.jsx("img",{className:"mb-4",src:"/images/learning/save-to-knowledge.gif",alt:"question-sql-pairs-guide"})}),"Build knowledge base: Question-SQL pairs"]})),description:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:["Create and manage ",n.jsx("b",{children:"Question-SQL pairs"})," to refine Wren AI’s SQL generation. You can manually add pairs here or go to Home, ask a question, and save the correct answer to Knowledge. The more you save, the smarter Wren AI becomes!"]})),onPopoverRender:e=>{N(e,640)}}},{element:'[data-guideid="instructions"]',popover:{title:(0,m.renderToString)((0,n.jsxs)("div",{className:"pt-4",children:[n.jsx("div",{className:"-mx-4",style:{minHeight:260},children:n.jsx("img",{className:"mb-4",src:"/images/learning/instructions.png",alt:"instructions-guide"})}),"Build knowledge base: Instructions"]})),description:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:["In addition to Question-SQL pairs, you can create instructions to define ",n.jsx("b",{children:"business rules"})," and ",n.jsx("b",{children:"query logic"}),". These rules guide Wren AI in applying consistent filters, constraints, and best practices to SQL queries."]})),onPopoverRender:e=>{N(e,520)},doneBtnText:"Got it",onNextClick:()=>{e.destroy(),r?.onDone&&r.onDone()}}}]),e.drive()},L=async(e,t,a,r)=>{if(null===e){console.error("Driver object is not initialized.");return}e.isActive()&&e.destroy(),e.setConfig({...v,showProgress:!1});let o={saveToKnowledge:'[data-guideid="last-answer-result"] [data-guideid="save-to-knowledge"]',previewData:'[data-guideid="last-answer-result"] [data-guideid="text-answer-preview-data"]'};e.setSteps([{element:o.saveToKnowledge,popover:{side:"top",align:"start",title:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"mb-1",children:n.jsx(f,{})}),"Save to knowledge"]})),description:(0,m.renderToString)((0,n.jsxs)(n.Fragment,{children:["If the AI-generated answer is correct, save it as a"," ",n.jsx("b",{children:"Question-SQL pair"})," to improve AI learning. If it's incorrect, refine it with follow-ups before saving to ensure accuracy."]})),onPopoverRender:e=>{N(e,360)},doneBtnText:"Got it",onNextClick:()=>{e.destroy(),r?.onDone&&r.onDone()}}}]);let s=null,i=null,l=()=>{s&&(s.disconnect(),s=null)},d=()=>{i&&(i.disconnect(),i=null)},c=()=>{let t=document.querySelector(o.previewData);return!!t&&(l(),(i=new IntersectionObserver(async t=>{for(let a of t)if(a.isIntersecting){d(),await (0,C.Y3)(700),e.drive();return}},{threshold:.5})).observe(t),!0)};c()||((s=new MutationObserver(()=>{c()&&l()})).observe(document.body,{childList:!0,subtree:!0}),await (0,C.Y3)(6e4),l(),d())}},68330:(e,t,a)=>{var n;a.d(t,{M:()=>n}),function(e){e.DATA_MODELING_GUIDE="DATA_MODELING_GUIDE",e.CREATING_MODEL="CREATING_MODEL",e.CREATING_VIEW="CREATING_VIEW",e.WORKING_RELATIONSHIP="WORKING_RELATIONSHIP",e.CONNECT_OTHER_DATA_SOURCES="CONNECT_OTHER_DATA_SOURCES",e.SWITCH_PROJECT_LANGUAGE="SWITCH_PROJECT_LANGUAGE",e.SHARE_RESULTS="SHARE_RESULTS",e.VIEW_FULL_SQL="VIEW_FULL_SQL",e.KNOWLEDGE_GUIDE="KNOWLEDGE_GUIDE",e.SAVE_TO_KNOWLEDGE="SAVE_TO_KNOWLEDGE"}(n||(n={}))},72909:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>N});var r=a(20997),o=a(16689),s=a(23672),i=a.n(s),l=a(57518),d=a.n(l),c=a(36942),m=a.n(c),u=a(37835),g=a.n(u),p=a(95015),h=a(77134),x=a(68330),y=a(11163),C=a(24351),f=a(50014);a(9401);var v=a(59462),w=e([h]);h=(w.then?(await w)():w)[0];let j=d().div.withConfig({displayName:"learning__Progress",componentId:"sc-7a8ec05a-0"})(["display:block;border-radius:999px;height:6px;width:100%;background-color:var(--gray-4);&::before{content:'';display:block;border-radius:999px;width:",";height:100%;background:linear-gradient(to left,#75eaff,#6150e0);transition:width 0.3s;}"],({total:e,current:t})=>`${t/e*100}%`),b=d().div.withConfig({displayName:"learning__CollapseBlock",componentId:"sc-7a8ec05a-1"})(["overflow:hidden;"]),S=d().div.withConfig({displayName:"learning__PlayIcon",componentId:"sc-7a8ec05a-2"})(["position:relative;width:16px;height:16px;border-radius:50%;background-color:var(--gray-5);&::before{content:'';display:block;position:absolute;top:50%;left:50%;margin-top:-4px;margin-left:-2px;border-top:4px solid transparent;border-left:6px solid var(--gray-8);border-bottom:4px solid transparent;}"]),T=d().div.withConfig({displayName:"learning__List",componentId:"sc-7a8ec05a-3"})(["display:flex;align-items:center;justify-content:space-between;cursor:pointer;font-size:12px;color:",";text-decoration:",";padding:2px 16px;&:hover{transition:background-color 0.3s;background-color:var(--gray-4);color:",";text-decoration:",";}"],({finished:e})=>e?"var(--gray-6)":"var(--gray-8)",({finished:e})=>e?"line-through":"none",({finished:e})=>e?"var(--gray-6)":"var(--gray-8)",({finished:e})=>e?"line-through":"none"),L=(0,p.x)(e=>{let{title:t,onClick:a,href:n,finished:o}=e;return(0,r.jsxs)(T,{className:"select-none",finished:o,onClick:a,as:n?"a":"div",...n?{href:n,target:"_blank",rel:"noopener noreferrer"}:{},children:[t,r.jsx(S,{})]})}),I=(e,t,a,n)=>{let r=e=>({onDone:()=>a(e),onSaveLanguage:n}),o=[{id:x.M.DATA_MODELING_GUIDE,title:"Data modeling guide",onClick:()=>e?.current?.play(x.M.DATA_MODELING_GUIDE,r(x.M.DATA_MODELING_GUIDE))},{id:x.M.CREATING_MODEL,title:"Creating a model",href:"https://docs.getwren.ai/oss/guide/modeling/models",onClick:()=>a(x.M.CREATING_MODEL)},{id:x.M.CREATING_VIEW,title:"Creating a view",href:"https://docs.getwren.ai/oss/guide/modeling/views",onClick:()=>a(x.M.CREATING_VIEW)},{id:x.M.WORKING_RELATIONSHIP,title:"Working on relationship",href:"https://docs.getwren.ai/oss/guide/modeling/relationships",onClick:()=>a(x.M.WORKING_RELATIONSHIP)},{id:x.M.CONNECT_OTHER_DATA_SOURCES,title:"Connect to other data sources",href:"https://docs.getwren.ai/oss/guide/connect/overview",onClick:()=>a(x.M.CONNECT_OTHER_DATA_SOURCES)}],s=[{id:x.M.SWITCH_PROJECT_LANGUAGE,title:"Switch the language",onClick:()=>e?.current?.play(x.M.SWITCH_PROJECT_LANGUAGE,r(x.M.SWITCH_PROJECT_LANGUAGE))},{id:x.M.SHARE_RESULTS,title:"Export to Excel/Sheets",href:"https://docs.getwren.ai/oss/guide/integrations/excel-add-in",onClick:()=>a(x.M.SHARE_RESULTS)},{id:x.M.VIEW_FULL_SQL,title:"View full SQL",href:"https://docs.getwren.ai/oss/guide/home/<USER>",onClick:()=>a(x.M.VIEW_FULL_SQL)}];return t.startsWith(C.y$.Modeling)?o:t.startsWith(C.y$.Home)?s:[]},_=e=>e.startsWith(C.y$.Modeling)||e.startsWith(C.y$.Home);function N(e){let t=(0,y.useRouter)(),[a,n]=(0,o.useState)(!0),s=(0,o.useRef)(null),l=(0,o.useRef)(null),{data:d}=(0,f.gT)(),[c]=(0,f.fV)({refetchQueries:["LearningRecord"]}),[u]=(0,v.JO)({refetchQueries:["GetSettings"]}),p=async e=>{await c({variables:{data:{path:e}}})},x=async e=>{await u({variables:{data:{language:e}}})},C=(0,o.useMemo)(()=>{let e=I(s,t.pathname,p,x),a=d?.learningRecord.paths||[];return i()(e.map(e=>({...e,finished:a.includes(e.id)})),"finished")},[d?.learningRecord]),w=(0,o.useMemo)(()=>C.length,[C]),N=(0,o.useMemo)(()=>C.filter(e=>e?.finished).length,[C]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(h.Z,{ref:s}),_(t.pathname)&&(0,r.jsxs)("div",{className:"border-t border-gray-4",children:[(0,r.jsxs)("div",{className:"px-4 py-1 d-flex align-center cursor-pointer select-none",onClick:()=>{n(!a)},children:[(0,r.jsxs)("div",{className:"flex-grow-1",children:[r.jsx(m(),{className:"mr-1"}),"Learning"]}),r.jsx(g(),{className:"text-sm",style:{transform:`rotate(${a?"90deg":"0deg"})`}})]}),(0,r.jsxs)(b,{ref:l,children:[r.jsx(L,{data:C}),(0,r.jsxs)("div",{className:"px-4 py-2 d-flex align-center",children:[r.jsx(j,{total:w,current:N}),(0,r.jsxs)("span",{className:"text-xs gray-6 text-nowrap pl-2",children:[N,"/",w," Finished"]})]})]})]})]})}n()}catch(e){n(e)}})},79802:(e,t,a)=>{a.d(t,{ZQ:()=>u,ck:()=>p,e0:()=>m,mU:()=>h,pn:()=>g,pv:()=>x,py:()=>y});var n=a(20997),r=a(37211),o=a(47142),s=a.n(o),i=a(59289),l=a.n(i);let d=(e,t)=>a=>{let{title:o,content:i,modalProps:l={},onConfirm:d,...c}=a;return n.jsx(e,{icon:t.icon,onClick:()=>r.u.confirm({autoFocusButton:null,cancelText:"Cancel",content:t?.content||"This will be permanently deleted, please confirm you want to delete it.",icon:n.jsx(s(),{}),okText:"Delete",onOk:d,title:`Are you sure you want to delete this ${t?.itemName}?`,width:464,...l,okButtonProps:{...l.okButtonProps,danger:!0}}),...c})},c=e=>{let{icon:t=null,disabled:a,...r}=e;return(0,n.jsxs)("a",{className:a?"":"red-5",...r,children:[t,"Delete"]})};d(c),d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"thread",content:"This will permanently delete all results history in this thread, please confirm you want to delete it."});let m=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"view",content:"This will be permanently deleted, please confirm you want to delete it."}),u=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"model",content:"This will be permanently deleted, please confirm you want to delete it."}),g=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"calculated field",content:"This will be permanently deleted, please confirm you want to delete it."}),p=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"relationship",content:"This will be permanently deleted, please confirm you want to delete it."}),h=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"dashboard item",content:"This will be permanently deleted, please confirm you want to delete it."}),x=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"question-SQL pair",content:"This action is permanent and cannot be undone. Are you sure you want to proceed?"}),y=d(c,{icon:n.jsx(l(),{className:"mr-2"}),itemName:"instruction",content:"This action is permanent and cannot be undone. Are you sure you want to proceed?"})},33878:(e,t,a)=>{a.d(t,{Z:()=>es});var n=a(20997),r=a(16689),o=a(53800),s=a.n(o),i=a(45417),l=a.n(i),d=a(76418),c=a.n(d),m=a(57518),u=a.n(m),g=a(24351),p=a(95015),h=a(21145),x=a.n(h),y=a(62125),C=a.n(y),f=a(11163),v=a(25675),w=a.n(v),N=a(27889),j=a.n(N),b=a(16190),S=a.n(b),T=a(17369),L=a.n(T),I=a(35188),_=a(58287),A=a(25027),E=a(24186),R=a(42698),k=a(31795);let M=(0,p.x)(A.Z),D=e=>{let t=(0,f.useRouter)(),{sampleDataset:a,closeModal:r}=e,o=(0,I.uR)(),[s]=(0,k.xv)({onError:e=>console.error(e),onCompleted:()=>{t.push(g.y$.Home),r()},refetchQueries:"active"});return(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"mb-2",children:"Change sample dataset"}),n.jsx("div",{className:"d-grid grid-columns-3 g-4",children:n.jsx(M,{data:o,selectedTemplate:a,onSelect:e=>{if(a!==e){let t=o.find(t=>t.value===e);c().confirm({title:`Are you sure you want to change to "${t.label}" dataset?`,okButtonProps:{danger:!0},okText:"Change",onOk:async()=>{await s({variables:{data:{name:e}}})}})}}})}),n.jsx("div",{className:"gray-6 mt-1",children:"Please be aware that choosing another sample dataset will delete all thread records in the Home page."})]})},O=e=>{let{type:t,properties:a,refetchSettings:o}=e,i=(0,I.f6)(t),[l]=S().useForm(),[d,{loading:c,error:m}]=(0,k.Lh)({onError:e=>console.error(e),onCompleted:async()=>{o(),L().success("Successfully update data source.")}}),u=(0,r.useMemo)(()=>(0,R.Bx)(m),[m]);(0,r.useEffect)(()=>a&&p(),[a]);let p=()=>{l.setFieldsValue((0,E.yN)(a,t))};return t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"d-flex align-center",children:[n.jsx(w(),{className:"mr-2",src:i.logo,alt:i.label,width:"24",height:"24"}),i.label]}),(0,n.jsxs)(S(),{form:l,layout:"vertical",className:"py-3 px-4",children:[n.jsx(i.component,{mode:g.SD.EDIT}),u&&n.jsx(j(),{message:u.shortMessage,description:u.message,type:"error",showIcon:!0,className:"my-6"}),(0,n.jsxs)("div",{className:"py-2 text-right",children:[n.jsx(s(),{className:"mr-2",style:{width:80},onClick:p,children:"Cancel"}),n.jsx(s(),{type:"primary",style:{width:80},onClick:()=>{l.validateFields().then(e=>{d({variables:{data:{properties:(0,E.TR)(e,t)}}})}).catch(e=>{console.error(e)})},loading:c,children:"Save"})]})]})]}):n.jsx(_.OL,{align:"center",height:150})};function P(e){let{sampleDataset:t}=e;return n.jsx("div",{className:"py-3 px-4",children:n.jsx(t?D:O,{...e})})}var H=a(28518),B=a.n(H),F=a(27050),Z=a.n(F),$=a(53526),G=a.n($),q=a(59462),V=a(45697),W=a(82103);function U(e){let{data:t}=e,a=(0,f.useRouter)(),[r]=S().useForm(),[o,{client:i}]=(0,q.in)(),l=Object.keys(W.y9).map(e=>({label:(0,V.p)(e),value:e})),[d,{loading:m}]=(0,q.JO)({refetchQueries:["GetSettings"],onError:e=>console.error(e),onCompleted:()=>{L().success("Successfully updated project language.")}});return(0,n.jsxs)("div",{className:"py-3 px-4",children:[n.jsx(S(),{form:r,layout:"vertical",initialValues:{language:t.language},children:n.jsx(S().Item,{label:"Project language",extra:"This setting will affect the language in which the AI responds to you.",children:(0,n.jsxs)(Z(),{gutter:16,wrap:!1,children:[n.jsx(B(),{className:"flex-grow-1",children:n.jsx(S().Item,{name:"language",noStyle:!0,children:n.jsx(G(),{placeholder:"Select a language",showSearch:!0,options:l})})}),n.jsx(B(),{children:n.jsx(s(),{type:"primary",style:{width:70},onClick:()=>{r.validateFields().then(e=>{d({variables:{data:e}})}).catch(e=>console.error(e))},loading:m,children:"Save"})})]})})}),n.jsx("div",{className:"gray-8 mb-2",children:"Reset project"}),n.jsx(s(),{type:"primary",style:{width:70},danger:!0,onClick:()=>{c().confirm({title:"Are you sure you want to reset?",okButtonProps:{danger:!0},okText:"Reset",onOk:async()=>{await o(),i.clearStore(),a.push(g.y$.OnboardingConnection)}})},children:"Reset"}),n.jsx("div",{className:"gray-6 mt-1",children:"Please be aware that resetting will delete all current settings and records, including those in the Modeling Page and Home Page threads."})]})}var K=a(83795),Q=a.n(K),z=a(2863),J=a.n(z);let Y=e=>({[g.L6.DATA_SOURCE]:{icon:Q(),label:"Data source settings"},[g.L6.PROJECT]:{icon:J(),label:"Project settings"}})[e]||null,{Sider:X,Content:ee}=l(),et=u()(X).withConfig({displayName:"settings__StyledSider",componentId:"sc-b16e58f-0"})([".ant-layout-sider-children{display:flex;flex-direction:column;height:100%;}"]),ea=u()(c()).withConfig({displayName:"settings__StyledModal",componentId:"sc-b16e58f-1"})([".ant-modal-content{overflow:hidden;}.ant-modal-close-x{width:48px;height:48px;line-height:48px;}"]),en=u()(s()).withConfig({displayName:"settings__StyledButton",componentId:"sc-b16e58f-2"})(["display:flex;align-items:center;padding:12px 8px;margin-bottom:4px;"]),er=({menu:e,data:t,refetch:a,closeModal:r})=>{let{dataSource:o,language:s}=t||{};return({[g.L6.DATA_SOURCE]:n.jsx(P,{type:o?.type,sampleDataset:o?.sampleDataset,properties:o?.properties,refetchSettings:a,closeModal:r}),[g.L6.PROJECT]:n.jsx(U,{data:{language:s}})})[e]||null},eo=(0,p.x)(({currentMenu:e,value:t,onClick:a})=>{let r=Y(t);return n.jsx(en,{className:e===t?"geekblue-6 bg-gray-4":"gray-8",type:"text",block:!0,onClick:()=>a({value:t}),icon:n.jsx(r.icon,{}),children:r.label})});function es(e){let{onClose:t,visible:a}=e,[o,s]=(0,r.useState)(g.L6.DATA_SOURCE),i=Y(o),d=Object.keys(g.L6).map(e=>({key:e,value:g.L6[e]})),[c,{data:m,refetch:u}]=(0,q.bB)({fetchPolicy:"cache-and-network"}),p=(0,r.useMemo)(()=>m?.settings?.productVersion,[m?.settings]);return n.jsx(ea,{width:950,bodyStyle:{padding:0,height:700},visible:a,footer:null,onCancel:t,destroyOnClose:!0,centered:!0,children:(0,n.jsxs)(l(),{style:{height:"100%"},children:[(0,n.jsxs)(et,{width:310,className:"border-r border-gray-4",children:[(0,n.jsxs)("div",{className:"gray-9 text-bold py-3 px-5",children:[n.jsx(x(),{className:"mr-2"}),"Settings"]}),n.jsx("div",{className:"p-3 flex-grow-1",children:n.jsx(eo,{data:d,currentMenu:o,onClick:({value:e})=>s(e)})}),!!p&&(0,n.jsxs)("div",{className:"gray-7 d-flex align-center p-3 px-5",children:[n.jsx(C(),{className:"mr-2 text-sm"}),"Wren AI version: ",p]})]}),(0,n.jsxs)(ee,{className:"d-flex flex-column",children:[(0,n.jsxs)("div",{className:"d-flex align-center gray-9 border-b border-gray-4 text-bold py-3 px-4",children:[n.jsx(i.icon,{className:"mr-2"}),i.label]}),n.jsx("div",{className:"flex-grow-1",style:{overflowY:"auto"},children:n.jsx(er,{menu:o,data:m?.settings,refetch:u,closeModal:t})})]})]})})}},96388:(e,t,a)=>{a.d(t,{Z:()=>f});var n=a(20997),r=a(41664),o=a.n(r),s=a(11163),i=a(57518),l=a.n(i),d=a(24351),c=a(59817),m=a(35729),u=a.n(m),g=a(36942),p=a.n(g),h=a(34476);let x=l().div.withConfig({displayName:"APIManagement__Layout",componentId:"sc-3f38168c-0"})(["padding:16px 0;position:absolute;z-index:1;left:0;top:0;width:100%;background-color:var(--gray-2);overflow:hidden;"]),y={[d.y$.APIManagementHistory]:d.mo.API_HISTORY},C={color:"inherit",transition:"none"};function f(){let e=(0,s.useRouter)(),t=[{"data-guideid":"api-history",label:n.jsx(o(),{style:C,href:d.y$.APIManagementHistory,children:"API history"}),icon:n.jsx(u(),{}),key:d.mo.API_HISTORY,className:"pl-4"},{label:(0,n.jsxs)(o(),{className:"gray-8 d-inline-flex align-center",href:"https://wrenai.readme.io/reference/sql-generation",target:"_blank",rel:"noopener noreferrer",children:["API reference",n.jsx(c.r$,{className:"ml-1"})]}),icon:n.jsx(p(),{}),key:d.mo.API_REFERENCE,className:"pl-4"}];return n.jsx(x,{children:n.jsx(h.Z,{items:t,selectedKeys:y[e.pathname]})})}},31875:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>x});var r=a(20997),o=a(16593);a(16689);var s=a(11163),i=a(39332),l=a(57518),d=a.n(l),c=a(24351),m=a(26205),u=a.n(m),g=a(89013),p=a(88859),h=e([o]);function x(e){let{data:t,onSelect:a,onRename:n,onDelete:l}=e,d=(0,s.useRouter)(),m=(0,i.useParams)(),{threads:h}=t,{treeSelectedKeys:x,setTreeSelectedKeys:y}=(0,g.pM)(),C=async e=>{await l(e),m?.id==e&&d.push(c.y$.Home)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(g.$v,{className:(0,o.default)({"adm-treeNode--selected":d.pathname===c.y$.HomeDashboard}),href:c.y$.HomeDashboard,children:[r.jsx(u(),{className:"mr-2"}),r.jsx("span",{className:"text-medium",children:"Dashboard"})]}),r.jsx(p.Z,{threads:h,selectedKeys:x,onSelect:(e,t)=>{0!==e.length&&(y(e),a(e))},onRename:n,onDeleteThread:C})]})}o=(h.then?(await h)():h)[0],d()(g.ZP).withConfig({displayName:"Home__StyledSidebarTree",componentId:"sc-6442ba22-0"})([".adm-treeNode{&.adm-treeNode__thread{padding:0px 16px 0px 4px !important;.ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}}}"]),n()}catch(e){n(e)}})},89144:(e,t,a)=>{a.d(t,{Z:()=>y});var n=a(20997),r=a(41664),o=a.n(r),s=a(11163),i=a(57518),l=a.n(i),d=a(95933),c=a.n(d),m=a(24351),u=a(56289),g=a(34476);let p=l().div.withConfig({displayName:"Knowledge__Layout",componentId:"sc-55b886c-0"})(["padding:16px 0;position:absolute;z-index:1;left:0;top:0;width:100%;background-color:var(--gray-2);overflow:hidden;"]),h={[m.y$.KnowledgeQuestionSQLPairs]:m.mo.QUESTION_SQL_PAIRS,[m.y$.KnowledgeInstructions]:m.mo.INSTRUCTIONS},x={color:"inherit",transition:"none"};function y(){let e=(0,s.useRouter)(),t=[{"data-guideid":"question-sql-pairs",label:n.jsx(o(),{style:x,href:m.y$.KnowledgeQuestionSQLPairs,children:"Question-SQL pairs"}),icon:n.jsx(c(),{}),key:m.mo.QUESTION_SQL_PAIRS,className:"pl-4"},{"data-guideid":"instructions",label:n.jsx(o(),{style:x,href:m.y$.KnowledgeInstructions,children:"Instructions"}),icon:n.jsx(u.pB,{}),key:m.mo.INSTRUCTIONS,className:"pl-4"}];return n.jsx(p,{children:n.jsx(g.Z,{items:t,selectedKeys:h[e.pathname]})})}},79453:(e,t,a)=>{a(20997)},14215:(e,t,a)=>{a.d(t,{l:()=>eo,Z:()=>es});var n=a(20997),r=a(57518),o=a.n(r),s=a(89013),i=a(16689),l=a(37972);a(18639);var d=a(29345),c=a(47427);a(79453);var m=a(79253),u=a.n(m),g=a(34869),p=a.n(g),h=a(13422),x=a.n(h),y=a(27889),C=a.n(y),f=a(53800),v=a.n(f),w=a(58633),N=a.n(w),j=a(76418),b=a.n(j),S=a(93984),T=a.n(S),L=a(74285),I=a.n(L),_=a(51886),A=a.n(_),E=a(58168),R=a.n(E),k=a(83905),M=a.n(k),D=a(9284),O=a.n(D),P=a(27777),H=a.n(P),B=a(71224),F=a(82103);let Z=o()(N()).withConfig({displayName:"SchemaChangeModal__StyledCollapse",componentId:"sc-8dbeeb02-0"})(["border:none;background-color:white;.ant-collapse-item:last-child,.ant-collapse-item:last-child > .ant-collapse-header{border-radius:0;}.ant-collapse-item,.ant-collapse-content{border-color:var(--gray-4);}.ant-collapse-content-box{padding:0;}"]),$=o()(I()).withConfig({displayName:"SchemaChangeModal__StyledTable",componentId:"sc-8dbeeb02-1"})(["padding-left:36px;.ant-table{border:none;border-radius:0;.non-expandable{.ant-table-row-expand-icon{display:none;}}.ant-table-expanded-row{.ant-table-cell{background-color:white;}}}"]),G=[{title:"Affected Resource",dataIndex:"resourceType",width:200,render:e=>e===F.Jq.CALCULATED_FIELD?n.jsx(A(),{className:"ant-tag--geekblue",children:"Calculated Field"}):e===F.Jq.RELATION?n.jsx(A(),{className:"ant-tag--citrus",children:"Relationship"}):null},{title:"Name",dataIndex:"displayName"}],q=e=>e.calculatedFields.length+e.relationships.length>0?"":"non-expandable",V=e=>{let{title:t,count:a,onResolve:r,isResolving:o}=e;return(0,n.jsxs)("div",{className:"d-flex align-center flex-grow-1",style:{userSelect:"none"},children:[n.jsx("b",{className:"text-medium",children:t}),(0,n.jsxs)("span",{className:"flex-grow-1 text-right d-flex justify-end",children:[(0,n.jsxs)(R().Text,{className:"gray-6",children:[a," table(s) affected"]}),n.jsx("div",{style:{width:150},children:!!r&&n.jsx(T(),{title:"Are you sure?",okText:"Confirm",okButtonProps:{danger:!0},onConfirm:e=>{e.stopPropagation(),r()},onCancel:e=>e.stopPropagation(),children:n.jsx(v(),{type:"text",size:"small",className:"red-5",onClick:e=>e.stopPropagation(),loading:o,icon:n.jsx(H(),{}),children:"Resolve"})})})]})]})},W=({record:e,tipMessage:t})=>0===e.resources.length?null:(0,n.jsxs)("div",{className:"pl-12",children:[n.jsx(C(),{showIcon:!0,icon:n.jsx(p(),{className:"orange-5"}),className:"gray-6 ml-2 bg-gray-1 pl-0",style:{border:"none"},message:t}),n.jsx(I(),{columns:G,dataSource:e.resources||[],pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowKey:"rowKey",size:"small",className:"adm-nested-table"})]});function U(e){let{visible:t,onClose:a,defaultValue:r,payload:o}=e,{onResolveSchemaChange:s,isResolving:l}=o||{},{deletedTables:d,deletedColumns:c,modifiedColumns:m}=(0,i.useMemo)(()=>{let{deletedTables:e,deletedColumns:t,modifiedColumns:a}=r||{};if(!r)return{deletedTables:e,deletedColumns:t,modifiedColumns:a};let n=e=>({...e,resources:[...e.calculatedFields.map((t,a)=>({...t,resourceType:F.Jq.CALCULATED_FIELD,rowKey:`${e.sourceTableName}-${t.referenceName}-${a}`})),...e.relationships.map((t,a)=>({...t,resourceType:F.Jq.RELATION,rowKey:`${e.sourceTableName}-${t.referenceName}-${a}`}))],rowKey:e.sourceTableName});return{deletedTables:e?.map(n),deletedColumns:t?.map(n),modifiedColumns:a?.map(n)}},[r]);return(0,n.jsxs)(b(),{title:(0,n.jsxs)(n.Fragment,{children:[n.jsx(p(),{className:"orange-5 mr-2"}),"Schema Changes"]}),width:750,visible:t,onCancel:a,destroyOnClose:!0,footer:null,children:[n.jsx(R().Paragraph,{className:"gray-6 mb-4",children:"We have detected schema changes from your connected data source. Please review the impacts of these changes."}),n.jsx(C(),{showIcon:!0,type:"warning",className:"gray-8 mb-6",message:'Please note that clicking "Resolve" may automatically delete all affected models, relationships, and calculated fields.'}),(0,n.jsxs)(Z,{expandIcon:e=>e.isActive?n.jsx(O(),{}):n.jsx(M(),{}),children:[d&&n.jsx(N().Panel,{header:n.jsx(V,{title:"Source table deleted",count:d.length,onResolve:()=>s(F.a_.DELETED_TABLES),isResolving:l}),children:n.jsx($,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Source table name",dataIndex:"sourceTableName"}],dataSource:d,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:q,expandable:{expandedRowRender:e=>n.jsx(W,{record:e,tipMessage:"The following table shows resources affected by this model and will be deleted when resolving."})}})},"deleteTables"),c&&n.jsx(N().Panel,{header:n.jsx(V,{title:"Source column deleted",count:c.length,onResolve:()=>s(F.a_.DELETED_COLUMNS),isResolving:l}),children:n.jsx($,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Deleted columns",dataIndex:"columns",render:e=>n.jsx(B.Z,{showMoreCount:!0,children:e.map(e=>n.jsx(A(),{className:"ant-tag--geekblue",children:e.displayName},e.sourceColumnName))})}],dataSource:c,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:q,expandable:{expandedRowRender:e=>n.jsx(W,{record:e,tipMessage:"The following table shows resources affected by this column of the model and will be deleted when resolving."})}})},"deleteColumns"),m&&n.jsx(N().Panel,{header:n.jsx(V,{title:"Source column type changed",count:m.length}),children:n.jsx($,{rowKey:"rowKey",columns:[{title:"Affected model",width:200,dataIndex:"displayName"},{title:"Affected columns",dataIndex:"columns",render:e=>n.jsx(B.Z,{showMoreCount:!0,children:e.map(e=>n.jsx(A(),{className:"ant-tag--geekblue",children:e.displayName},e.sourceColumnName))})}],dataSource:m,size:"small",pagination:{hideOnSinglePage:!0,size:"small",pageSize:10},rowClassName:q,expandable:{expandedRowRender:e=>n.jsx(W,{record:e,tipMessage:"The following table shows the resources utilized by this column of the model. Please review each resource and manually update the relevant ones if any changes are required."})}})},"modifiedColumns")]})]})}var K=a(31795),Q=a(49330),z=a(37119),J=a(9401);let Y=e=>[e?.deletedTables,e?.deletedColumns,e?.modifiedColumns].some(e=>!!e);function X(e){let{onOpenModelDrawer:t,models:a}=e,r=(0,c.Z)(),[o,{loading:s}]=(0,K.t_)({onError:e=>console.error(e),onCompleted:async e=>{e.triggerDataSourceDetection?l.y.warning("Schema change detected."):l.y.success("There is no schema change."),await h()}}),[m,{loading:g}]=(0,K.v5)({onError:e=>console.error(e),onCompleted:async(e,t)=>{let{type:a}=t.variables?.where;a===F.a_.DELETED_TABLES?l.y.success("Source table deleted resolved successfully."):a===F.a_.DELETED_COLUMNS&&l.y.success("Source column deleted resolved successfully.");let{data:n}=await h();Y(n.schemaChange)||r.closeModal()},refetchQueries:[{query:Q.W},{query:z.uG}]}),{data:p,refetch:h}=(0,K.xY)({fetchPolicy:"cache-and-network"});(0,i.useMemo)(()=>Y(p?.schemaChange),[p]);let y=(0,d.Ak)({groupName:"Models",groupKey:"models",actions:[{key:"trigger-schema-detection",disabled:s,icon:()=>n.jsx(x(),{spin:s,title:p?.schemaChange.lastSchemaChangeTime?`Last refresh ${(0,J.ni)(p?.schemaChange.lastSchemaChangeTime)}`:"",onClick:()=>o()})},{key:"add-model",render:()=>n.jsx(d.lJ,{"data-guideid":"add-model","data-testid":"add-model",icon:n.jsx(u(),{}),size:"small",onClick:()=>t(),children:"New"})}]}),[C,f]=(0,i.useState)(y());return(0,n.jsxs)(n.Fragment,{children:[n.jsx(eo,{...e,treeData:C}),n.jsx(U,{...r.state,defaultValue:p?.schemaChange,payload:{onResolveSchemaChange:e=>{m({variables:{where:{type:e}}})},isResolving:g},onClose:r.closeModal})]})}var ee=a(41664),et=a.n(ee),ea=a(37211),en=a(24351);function er(e){let{views:t}=e,a=()=>{ea.u.info({title:"How to create a View?",content:(0,n.jsxs)("div",{children:["Pose your questions at"," ",n.jsx(et(),{href:en.y$.Home,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_add_view_navigate_to_home",children:"homepage"}),", and get some helpful answers to save as views."]}),okButtonProps:{"data-ph-capture":!0,"data-ph-capture-attribute-name":"cta_add_view_ok_btn"}})},r=(0,d.Ak)({groupName:"Views",groupKey:"views",actions:[{key:"add-view-info",render:()=>n.jsx(d.lJ,{icon:n.jsx(u(),{}),size:"small",onClick:a,"data-ph-capture":"true","data-ph-capture-attribute-name":"cta_add_view",children:"New"})}]}),[o,s]=(0,i.useState)(r());return n.jsx(eo,{...e,treeData:o})}let eo=o()(s.ZP).withConfig({displayName:"Modeling__StyledSidebarTree",componentId:"sc-2d34f56a-0"})([""," .adm-treeNode{.ant-tree-title{display:inline-flex;flex-wrap:nowrap;min-width:1px;flex-grow:0;}}"],s.e5);function es(e){let{data:t,onSelect:a,onOpenModelDrawer:r}=e,{models:o=[],views:s=[]}=t||{};return(0,n.jsxs)(n.Fragment,{children:[n.jsx(X,{models:o,onSelect:a,selectedKeys:[],onOpenModelDrawer:r}),n.jsx(er,{views:s,onSelect:a,selectedKeys:[]})]})}},34476:(e,t,a)=>{a.d(t,{Z:()=>d});var n=a(20997);a(16689);var r=a(57518),o=a.n(r),s=a(10274),i=a.n(s);let l=o()(i()).withConfig({displayName:"SidebarMenu__StyledMenu",componentId:"sc-ae3c8b69-0"})(["&.ant-menu{background-color:transparent;border-right:0;color:var(--gray-8);&:not(.ant-menu-horizontal){.ant-menu-item-selected{color:var(--gray-8);background-color:var(--gray-5);}}.ant-menu-item-group{margin-top:20px;&:first-child{margin-top:0;}}.ant-menu-item-group-title{font-size:12px;font-weight:700;padding:5px 16px;}.ant-menu-item{line-height:28px;height:auto;margin:0;font-weight:500;&:not(last-child){margin-bottom:0;}&:not(.ant-menu-item-disabled):hover{color:inherit;background-color:var(--gray-4);}&:not(.ant-menu-item-disabled):active{background-color:var(--gray-6);}&:active{background-color:transparent;}&-selected{color:var(--gray-8);&:after{display:none;}&:hover{color:var(--gray-8);}}}}"]);function d({items:e,selectedKeys:t,onSelect:a}){return n.jsx(l,{mode:"inline",items:e,selectedKeys:t,onSelect:a})}},89013:(e,t,a)=>{a.d(t,{$v:()=>p,ZP:()=>x,e5:()=>g,pM:()=>h});var n=a(20997),r=a(41664),o=a.n(r),s=a(16689),i=a(57518),l=a.n(i),d=a(87421),c=a.n(d);let m=(0,i.css)(["[class^='anticon anticon-']{transition:background-color ease-out 0.12s;border-radius:2px;width:12px;height:12px;font-size:12px;vertical-align:middle;&:hover{background-color:var(--gray-5);}&:active{background-color:var(--gray-6);}&[disabled]{cursor:not-allowed;color:var(--gray-6);&:hover,&:active{background-color:transparent;}}}.anticon + .anticon{margin-left:4px;}"]),u=l()(c()).withConfig({displayName:"SidebarTree__StyledTree",componentId:"sc-6c96e180-0"})(["&.ant-tree{background-color:transparent;color:var(--gray-8);.ant-tree-indent-unit{width:12px;}.ant-tree-node-content-wrapper{display:flex;align-items:center;line-height:18px;min-height:28px;min-width:1px;padding:0;}.ant-tree-node-content-wrapper:hover,.ant-tree-node-content-wrapper.ant-tree-node-selected{background-color:transparent;}.ant-tree-treenode{padding:0 16px;background-color:transparent;transition:background-color ease-out 0.12s;&-selected{color:var(--geekblue-6);background-color:var(--gray-4);}.ant-tree-switcher{width:12px;align-self:center;.ant-tree-switcher-icon{font-size:12px;vertical-align:middle;}","}.ant-tree-iconEle{flex-shrink:0;}}.adm{&-treeTitle__title{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}&-treeNode{&:hover{background-color:var(--gray-4);}&:active{background-color:var(--gray-6);}.ant-tree-title{display:inline-flex;flex-wrap:nowrap;min-width:1px;}&--relation,&--primary{margin-left:4px;}&--group{color:var(--gray-8);margin-top:16px;font-size:14px;font-weight:500;.ant-tree-switcher-noop{display:none;}> *{cursor:inherit;}}&--empty{color:var(--gray-7);font-size:12px;.ant-tree-switcher{display:none;}.ant-tree-node-content-wrapper{min-height:auto;}}&--selectNode{*{cursor:auto;}&:hover,&:active{background-color:transparent;}}&--subtitle{color:var(--gray-7);font-size:12px;font-weight:500;.ant-tree-switcher{display:none;}.ant-tree-node-content-wrapper{min-height:auto;}}&--selectNone{*{cursor:auto;}&:hover,&:active{background-color:transparent;}}}&-actionIcon{font-size:14px;border-radius:2px;margin-right:-3px;&:not(.adm-actionIcon--disabled){cursor:pointer;&:hover{background-color:var(--gray-5);}}.anticon{padding:2px;cursor:inherit;}&--disabled{color:var(--gray-6);cursor:not-allowed;}}}}"],m),g=(0,i.css)([".ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}"]),p=l()(o()).withConfig({displayName:"SidebarTree__StyledTreeNodeLink",componentId:"sc-6c96e180-1"})(["display:block;cursor:pointer;user-select:none;margin-top:16px;padding:0 16px;line-height:28px;color:var(--gray-8);&:hover{background-color:var(--gray-4);}&:active{background-color:var(--gray-6);}&.adm-treeNode--selected{background-color:var(--gray-4);color:var(--geekblue-6);}"]),h=()=>{let[e,t]=(0,s.useState)([]),[a,n]=(0,s.useState)([]),[r,o]=(0,s.useState)([]),[i,l]=(0,s.useState)(!0);return{treeSelectedKeys:e,treeExpandKeys:a,treeLoadedKeys:r,autoExpandParent:i,setTreeSelectedKeys:t,setTreeExpandKeys:n,setTreeLoadedKeys:o,setAutoExpandParent:l}};function x(e){return n.jsx(u,{blockNode:!0,showIcon:!0,motion:null,...e})}},88859:(e,t,a)=>{a.d(t,{Z:()=>h});var n=a(20997),r=a(16689),o=a(57518),s=a.n(o),i=a(24351),l=a(39332),d=a(79253),c=a.n(d),m=a(89013),u=a(29345),g=a(9200);a(97615),a(51968),a(79453),a(30675),a(79802),s()(g.v).withConfig({displayName:"TreeTitle__StyledMenu",componentId:"sc-9f797d5d-0"})(["a:hover{color:white;}"]);let p=s()(m.ZP).withConfig({displayName:"ThreadTree__StyledSidebarTree",componentId:"sc-ed81b4d0-0"})([""," .adm-treeNode{&.adm-treeNode__thread{padding:0px 16px 0px 4px !important;.ant-tree-title{flex-grow:1;display:inline-flex;align-items:center;span:first-child,.adm-treeTitle__title{flex-grow:1;}}}}"],m.e5);function h(e){(0,l.useParams)();let t=(0,l.useRouter)(),{threads:a=[],selectedKeys:o,onSelect:s,onRename:d,onDeleteThread:m}=e,g=(0,u.Ak)({groupName:"Threads",groupKey:"threads",actions:[{key:"new-thread",render:()=>n.jsx(u.lJ,{size:"small",icon:n.jsx(c(),{}),onClick:()=>t.push(i.y$.Home),children:"New"})}]}),[h,x]=(0,r.useState)(g());return n.jsx(p,{treeData:h,selectedKeys:o,onSelect:s})}},39998:(e,t,a)=>{a.a(e,async(e,n)=>{try{a.d(t,{Z:()=>w});var r=a(20997),o=a(41664),s=a.n(o),i=a(11163),l=a(46810),d=a(57518),c=a.n(d),m=a(24351),u=a(59817),g=a(21145),p=a.n(g),h=a(31875),x=a(14215),y=a(89144),C=a(96388),f=a(72909),v=e([h,f]);[h,f]=v.then?(await v)():v;let N=c().div.withConfig({displayName:"sidebar__Layout",componentId:"sc-3f58aa6f-0"})(["position:relative;height:100%;background-color:var(--gray-2);color:var(--gray-8);padding-bottom:12px;overflow-x:hidden;"]),j=c().div.withConfig({displayName:"sidebar__Content",componentId:"sc-3f58aa6f-1"})(["flex-grow:1;overflow-y:auto;"]),b=c()(l.z).withConfig({displayName:"sidebar__StyledButton",componentId:"sc-3f58aa6f-2"})(["cursor:pointer;display:flex;align-items:center;padding-left:16px;padding-right:16px;color:var(--gray-8) !important;border-radius:0;&:hover,&:focus{background-color:var(--gray-4);}"]),S=e=>{let{pathname:t,...a}=e;return r.jsx(j,{children:t.startsWith(m.y$.Home)?r.jsx(h.Z,{...a}):t.startsWith(m.y$.Modeling)?r.jsx(x.Z,{...a}):t.startsWith(m.y$.Knowledge)?r.jsx(y.Z,{}):t.startsWith(m.y$.APIManagement)?r.jsx(C.Z,{}):null})};function w(e){let{onOpenSettings:t}=e,a=(0,i.useRouter)();return(0,r.jsxs)(N,{className:"d-flex flex-column",children:[r.jsx(S,{...e,pathname:a.pathname}),r.jsx(f.Z,{}),(0,r.jsxs)("div",{className:"border-t border-gray-4 pt-2",children:[(0,r.jsxs)(b,{type:"text",block:!0,onClick:e=>{t&&t(),e.target.blur()},children:[r.jsx(p(),{className:"text-md"}),"Settings"]}),r.jsx(b,{type:"text",block:!0,children:(0,r.jsxs)(s(),{className:"d-flex align-center",href:"https://discord.com/invite/5DvshJqG8Z",target:"_blank",rel:"noopener noreferrer","data-ph-capture":"true","data-ph-capture-attribute-name":"cta_go_to_discord",children:[r.jsx(u.D7,{className:"mr-2",style:{width:16}})," Discord"]})}),r.jsx(b,{type:"text",block:!0,children:(0,r.jsxs)(s(),{className:"d-flex align-center",href:"https://github.com/Canner/WrenAI",target:"_blank",rel:"noopener noreferrer","data-ph-capture":"true","data-ph-capture-attribute-name":"cta_go_to_github",children:[r.jsx(u.ET,{className:"mr-2",style:{width:16}})," GitHub"]})})]})]})}n()}catch(e){n(e)}})},29345:(e,t,a)=>{a.d(t,{lJ:()=>C,Ak:()=>y});var n=a(20997),r=a(57518),o=a.n(r),s=a(46810);a(9620),a(59817);var i=a(99226),l=a.n(i),d=a(89699),c=a.n(d);a(81131);var m=a(80496),u=a.n(m),g=a(16689),p=a(36005);let h=({actions:e})=>{let t=(e||[]).map(({key:e,icon:t,render:a,disabled:r=!1,className:o="",...s})=>t?n.jsx(p.Z,{component:t,className:`adm-actionIcon ${o} ${r?"adm-actionIcon--disabled":""}`,...s},e):a?n.jsx(g.Fragment,{children:a({key:e,disabled:r})},e):null);return n.jsx("span",{className:"d-inline-flex align-center flex-shrink-0 g-2",children:t})};function x({title:e,quotaUsage:t=0,appendSlot:a,...r}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("span",{className:"d-inline-flex align-center",children:[e,(0,n.jsxs)("span",{className:"adm-treeNode-group-count ml-1 text-xs flex-grow-0",children:["(",t,")"]}),a]}),n.jsx(h,{...r})]})}a(80284),a(24351),a(18639);let y=e=>t=>{let{groupName:a="",groupKey:r="",quotaUsage:o,actions:s,children:i=[],appendSlot:d}=l()(e,t),m=[{title:`No ${u()(a)}`,key:`${r}-empty`,selectable:!1,className:"adm-treeNode adm-treeNode--empty adm-treeNode--selectNode"}],g=c()(i)?m:i;return[{className:"adm-treeNode--group",title:n.jsx(x,{title:a,quotaUsage:o,appendSlot:d,actions:s}),key:r,selectable:!1,isLeaf:!0},...g]},C=o()(s.z).withConfig({displayName:"utils__GroupActionButton",componentId:"sc-752a4c65-0"})(["font-size:12px;height:auto;background:transparent;color:var(--gray-8);&:hover{background-color:transparent;}&:focus{border-color:var(--gray-5);background:transparent;color:var(--gray-8);}"])},24186:(e,t,a)=>{a.d(t,{ZP:()=>g,TR:()=>m,yN:()=>u});var n=a(16689),r=a(24351),o=a(11163);a(42698);var s=a(31795),i=a(82103),l=a(29114);let d=(0,l.gql)`
  query OnboardingStatus {
    onboardingStatus {
      status
    }
  }
`,c="************",m=(e,t)=>{if(t===i.ri.DUCKDB){let t=e.configurations.reduce((e,t)=>(t.key&&t.value&&(e[t.key]=t.value),e),{});return{...e,configurations:t,extensions:e.extensions.filter(e=>e)}}return{...e,password:e?.password===c?void 0:e?.password}},u=(e,t)=>{if(t===i.ri.BIG_QUERY)return{...e,credentials:void 0};if(t===i.ri.DUCKDB){let t=Object.entries(e?.configurations||{}).map(([e,t])=>({key:e,value:t})),a=e?.extensions||[];return{...e,configurations:t.length?t:[{key:"",value:""}],extensions:a.length?a:[""]}}return{...e,password:e?.password||c}};function g(){let[e,t]=(0,n.useState)(r.ye.STARTER),[a,i]=(0,n.useState)(),[l,c]=(0,n.useState)(null),u=(0,o.useRouter)(),[g,{loading:p,error:h}]=(0,s.tl)({onError:e=>console.error(e),onCompleted:()=>u.push(r.y$.OnboardingModels)}),[x,{loading:y}]=(0,s.xv)({onError:e=>console.error(e),onCompleted:()=>u.push(r.y$.Modeling),refetchQueries:[{query:d}],awaitRefetchQueries:!0}),C=async e=>{await g({variables:{data:{type:a,properties:m(e,a)}}})},f=async e=>{await x({variables:{data:{name:e}}})};return{stepKey:e,dataSource:a,onBack:()=>{e===r.ye.CREATE_DATA_SOURCE&&t(r.ye.STARTER)},onNext:a=>{e===r.ye.STARTER?a.dataSource?(i(a?.dataSource),t(r.ye.CREATE_DATA_SOURCE)):f(a.template):e===r.ye.CREATE_DATA_SOURCE&&C(a.properties)},submitting:p||y,connectError:l}}},9620:(e,t,a)=>{a.d(t,{u:()=>s});var n=a(20997),r=a(59817),o=a(24351);let s=(e,t)=>{let{type:a}=e;switch(a.toUpperCase()){case o.BA.BOOLEAN:return n.jsx(r.Ye,{...t});case o.BA.JSON:case o.BA.RECORD:return n.jsx(r.tT,{...t});case o.BA.TEXT:return n.jsx(r.VL,{...t});case o.BA.BYTEA:case o.BA.VARBINARY:return n.jsx(r.cJ,{...t});case o.BA.UUID:case o.BA.OID:return n.jsx(r.Vu,{...t});case o.BA.TINYINT:case o.BA.INT2:case o.BA.SMALLINT:case o.BA.INT4:case o.BA.INTEGER:case o.BA.INT8:case o.BA.BIGINT:case o.BA.INT64:case o.BA.NUMERIC:case o.BA.DECIMAL:case o.BA.FLOAT4:case o.BA.REAL:case o.BA.FLOAT8:case o.BA.DOUBLE:case o.BA.INET:return n.jsx(r.r3,{...t});case o.BA.VARCHAR:case o.BA.CHAR:case o.BA.BPCHAR:case o.BA.STRING:case o.BA.NAME:return n.jsx(r.Rh,{...t});case o.BA.TIMESTAMP:case o.BA.TIMESTAMPTZ:case o.BA.DATE:case o.BA.INTERVAL:return n.jsx(r.Qu,{...t});default:return n.jsx(r.Se,{...t})}}},12301:(e,t,a)=>{a.d(t,{W1:()=>r,WI:()=>n});let n=(e,t)=>{let a=new CustomEvent(e,{detail:t});document.dispatchEvent(a)},r={GO_TO_FIRST_MODEL:"goToFirstModel"}},45697:(e,t,a)=>{a.d(t,{p:()=>r});var n=a(82103);let r=e=>({[n.y9.EN]:"English",[n.y9.ES]:"Spanish",[n.y9.FR]:"French",[n.y9.ZH_TW]:"Traditional Chinese",[n.y9.ZH_CN]:"Simplified Chinese",[n.y9.DE]:"German",[n.y9.PT]:"Portuguese",[n.y9.RU]:"Russian",[n.y9.JA]:"Japanese",[n.y9.KO]:"Korean"})[e]||e},18639:(e,t,a)=>{a.d(t,{i:()=>i});var n=a(20997),r=a(9620),o=a(24351),s=a(59817);let i=(e,t)=>{let{nodeType:a,type:i}=e;switch(a){case o.QZ.MODEL:return n.jsx(s.ZA,{title:"Model",...t});case o.QZ.METRIC:return n.jsx(s.km,{title:"Metric",...t});case o.QZ.VIEW:return n.jsx(s.ON,{title:"View",...t});case o.QZ.RELATION:return n.jsx(s.NA,{title:"Relationship",...t});case o.QZ.FIELD:return i?(0,r.u)({type:i},t):null;default:return null}}},56289:(e,t,a)=>{a.d(t,{gq:()=>r,Wi:()=>i,pB:()=>s,Hx:()=>o});var n=a(20997);let r=({fillCurrentColor:e=!0,className:t})=>n.jsx("svg",{className:t,width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M18.6562 11.7478L13.9791 10.0247L12.2522 5.34375C12.1201 4.98542 11.8813 4.67621 11.5679 4.45781C11.2546 4.2394 10.8819 4.12231 10.5 4.12231C10.1181 4.12231 9.74535 4.2394 9.43205 4.45781C9.11875 4.67621 8.87993 4.98542 8.74781 5.34375L7.02469 10.0247L2.34375 11.7478C1.98542 11.8799 1.67621 12.1187 1.45781 12.432C1.2394 12.7454 1.12231 13.1181 1.12231 13.5C1.12231 13.8819 1.2394 14.2546 1.45781 14.568C1.67621 14.8813 1.98542 15.1201 2.34375 15.2522L7.02094 16.9753L8.74781 21.6562C8.87993 22.0146 9.11875 22.3238 9.43205 22.5422C9.74535 22.7606 10.1181 22.8777 10.5 22.8777C10.8819 22.8777 11.2546 22.7606 11.5679 22.5422C11.8813 22.3238 12.1201 22.0146 12.2522 21.6562L13.9753 16.9791L18.6562 15.2522C19.0146 15.1201 19.3238 14.8813 19.5422 14.568C19.7606 14.2546 19.8777 13.8819 19.8777 13.5C19.8777 13.1181 19.7606 12.7454 19.5422 12.432C19.3238 12.1187 19.0146 11.8799 18.6562 11.7478ZM13.0312 14.9259C12.7777 15.0192 12.5475 15.1664 12.3565 15.3574C12.1655 15.5484 12.0183 15.7787 11.925 16.0322L10.5 19.9012L9.07406 16.0312C8.98079 15.778 8.83365 15.548 8.64282 15.3572C8.45198 15.1663 8.222 15.0192 7.96875 14.9259L4.09875 13.5L7.96875 12.0741C8.222 11.9808 8.45198 11.8337 8.64282 11.6428C8.83365 11.452 8.98079 11.222 9.07406 10.9688L10.5 7.09875L11.9259 10.9688C12.0192 11.2223 12.1664 11.4525 12.3574 11.6435C12.5484 11.8345 12.7787 11.9817 13.0322 12.075L16.9012 13.5L13.0312 14.9259ZM13.125 3.75C13.125 3.45163 13.2435 3.16548 13.4545 2.9545C13.6655 2.74353 13.9516 2.625 14.25 2.625H15.375V1.5C15.375 1.20163 15.4935 0.915483 15.7045 0.704505C15.9155 0.493526 16.2016 0.375 16.5 0.375C16.7984 0.375 17.0845 0.493526 17.2955 0.704505C17.5065 0.915483 17.625 1.20163 17.625 1.5V2.625H18.75C19.0484 2.625 19.3345 2.74353 19.5455 2.9545C19.7565 3.16548 19.875 3.45163 19.875 3.75C19.875 4.04837 19.7565 4.33452 19.5455 4.5455C19.3345 4.75647 19.0484 4.875 18.75 4.875H17.625V6C17.625 6.29837 17.5065 6.58452 17.2955 6.7955C17.0845 7.00647 16.7984 7.125 16.5 7.125C16.2016 7.125 15.9155 7.00647 15.7045 6.7955C15.4935 6.58452 15.375 6.29837 15.375 6V4.875H14.25C13.9516 4.875 13.6655 4.75647 13.4545 4.5455C13.2435 4.33452 13.125 4.04837 13.125 3.75ZM23.625 8.25C23.625 8.54837 23.5065 8.83452 23.2955 9.0455C23.0845 9.25647 22.7984 9.375 22.5 9.375H22.125V9.75C22.125 10.0484 22.0065 10.3345 21.7955 10.5455C21.5845 10.7565 21.2984 10.875 21 10.875C20.7016 10.875 20.4155 10.7565 20.2045 10.5455C19.9935 10.3345 19.875 10.0484 19.875 9.75V9.375H19.5C19.2016 9.375 18.9155 9.25647 18.7045 9.0455C18.4935 8.83452 18.375 8.54837 18.375 8.25C18.375 7.95163 18.4935 7.66548 18.7045 7.4545C18.9155 7.24353 19.2016 7.125 19.5 7.125H19.875V6.75C19.875 6.45163 19.9935 6.16548 20.2045 5.9545C20.4155 5.74353 20.7016 5.625 21 5.625C21.2984 5.625 21.5845 5.74353 21.7955 5.9545C22.0065 6.16548 22.125 6.45163 22.125 6.75V7.125H22.5C22.7984 7.125 23.0845 7.24353 23.2955 7.4545C23.5065 7.66548 23.625 7.95163 23.625 8.25Z",fill:e?"currentColor":void 0})}),o=({fillCurrentColor:e=!0,className:t})=>(0,n.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 17 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.21025 5.02381H11.0755C11.8038 5.0238 12.4049 5.02379 12.8945 5.06379C13.4031 5.10535 13.8706 5.19452 14.3097 5.41826C14.9906 5.76523 15.5443 6.31888 15.8913 6.99984C16.115 7.43896 16.2042 7.90645 16.2457 8.41501C16.2857 8.90466 16.2857 9.50574 16.2857 10.234V14.2898C16.2857 15.0181 16.2857 15.6191 16.2457 16.1088C16.2042 16.6174 16.115 17.0849 15.8913 17.524C15.5443 18.2049 14.9906 18.7586 14.3097 19.1055C13.8706 19.3293 13.4031 19.4185 12.8945 19.46C12.4049 19.5 11.8038 19.5 11.0754 19.5H5.21027C4.48196 19.5 3.88086 19.5 3.39121 19.46C2.88264 19.4185 2.41515 19.3293 1.97603 19.1055C1.29507 18.7586 0.741424 18.2049 0.394453 17.524C0.170714 17.0849 0.0815369 16.6174 0.0399855 16.1088C-2.06409e-05 15.6191 -1.11504e-05 15.0181 3.90216e-07 14.2897V10.2341C-1.11504e-05 9.50576 -2.06409e-05 8.90467 0.0399855 8.41502C0.0815369 7.90645 0.170714 7.43896 0.394453 6.99984C0.741424 6.31888 1.29507 5.76523 1.97603 5.41826C2.41515 5.19452 2.88264 5.10535 3.39121 5.06379C3.88086 5.02379 4.48195 5.0238 5.21025 5.02381ZM3.53856 6.86731C3.1419 6.89972 2.93905 6.95846 2.79754 7.03056C2.45706 7.20404 2.18024 7.48087 2.00675 7.82135C1.93465 7.96285 1.87591 8.16571 1.8435 8.56237C1.81023 8.96959 1.80952 9.49643 1.80952 10.2714V14.2524C1.80952 15.0274 1.81023 15.5542 1.8435 15.9614C1.87591 16.3581 1.93465 16.561 2.00675 16.7025C2.18024 17.0429 2.45706 17.3198 2.79754 17.4932C2.93905 17.5653 3.1419 17.6241 3.53856 17.6565C3.94578 17.6898 4.47262 17.6905 5.24762 17.6905H11.0381C11.8131 17.6905 12.3399 17.6898 12.7472 17.6565C13.1438 17.6241 13.3467 17.5653 13.4882 17.4932C13.8287 17.3198 14.1055 17.0429 14.279 16.7025C14.3511 16.561 14.4098 16.3581 14.4422 15.9614C14.4755 15.5542 14.4762 15.0274 14.4762 14.2524V10.2714C14.4762 9.49643 14.4755 8.96959 14.4422 8.56237C14.4098 8.16571 14.3511 7.96285 14.279 7.82135C14.1055 7.48087 13.8287 7.20404 13.4882 7.03056C13.3467 6.95846 13.1438 6.89972 12.7472 6.86731C12.3399 6.83404 11.8131 6.83333 11.0381 6.83333H5.24762C4.47262 6.83333 3.94578 6.83404 3.53856 6.86731Z",fill:e?"currentColor":void 0}),n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.33337 2.69857L6.33333 3.21434C6.3333 3.71403 5.9282 4.11908 5.42852 4.11905C4.92883 4.11902 4.52378 3.71392 4.52381 3.21423L4.52387 2.30947C4.52388 2.07611 4.61406 1.85178 4.77557 1.68334C4.99499 1.4545 5.32229 1.12171 5.89443 0.873475C6.44775 0.6334 7.16129 0.5 8.1429 0.5C9.12451 0.5 9.83805 0.63341 10.3914 0.873493C10.9635 1.12173 11.2908 1.45452 11.5102 1.68332C11.6717 1.85177 11.7619 2.07613 11.7619 2.30952V3.21429C11.7619 3.71397 11.3568 4.11905 10.8571 4.11905C10.3575 4.11905 9.95238 3.71397 9.95238 3.21429V2.69856C9.8723 2.63607 9.78449 2.58269 9.67111 2.53349C9.4179 2.42363 8.97077 2.30952 8.1429 2.30952C7.31502 2.30952 6.86788 2.42362 6.61467 2.53348C6.50127 2.58269 6.41345 2.63607 6.33337 2.69857Z",fill:e?"currentColor":void 0}),n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.70476 13.5286C5.00457 13.1288 5.57168 13.0478 5.97143 13.3476C6.70123 13.895 7.4087 14.0718 8.14237 14.0714C8.87603 14.071 9.58508 13.8945 10.3143 13.3476C10.714 13.0478 11.2811 13.1288 11.581 13.5286C11.8808 13.9283 11.7997 14.4954 11.4 14.7952C10.3197 15.6055 9.21764 15.8804 8.14335 15.881C7.06593 15.8815 5.96543 15.605 4.88572 14.7952C4.48597 14.4954 4.40495 13.9283 4.70476 13.5286Z",fill:e?"currentColor":void 0}),n.jsx("path",{d:"M3.61905 10.9048C3.61905 10.1552 4.22666 9.54762 4.97619 9.54762C5.72572 9.54762 6.33333 10.1552 6.33333 10.9048C6.33333 11.6543 5.72572 12.2619 4.97619 12.2619C4.22666 12.2619 3.61905 11.6543 3.61905 10.9048Z",fill:e?"currentColor":void 0}),n.jsx("path",{d:"M9.95238 10.9048C9.95238 10.1552 10.56 9.54762 11.3095 9.54762C12.0591 9.54762 12.6667 10.1552 12.6667 10.9048C12.6667 11.6543 12.0591 12.2619 11.3095 12.2619C10.56 12.2619 9.95238 11.6543 9.95238 10.9048Z",fill:e?"currentColor":void 0})]}),s=({fillCurrentColor:e=!0,className:t})=>(0,n.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.63962 1.97296L3.36755 -0.210815H4.63246L5.36038 1.97296L7.54415 2.70088V3.96579L5.36038 4.69372L4.63246 6.87749H3.36755L2.63962 4.69372L0.455849 3.96579V2.70088L2.63962 1.97296ZM4 2.10819L3.79912 2.71082L3.37749 3.13246L2.77485 3.33334L3.37749 3.53421L3.79912 3.95585L4 4.55849L4.20088 3.95585L4.62252 3.53421L5.22515 3.33334L4.62252 3.13246L4.20088 2.71082L4 2.10819ZM11.3926 4.02504C11.0926 4.00053 10.7044 4.00001 10.1333 4.00001H9.33332V2.66668H10.1609C10.6975 2.66667 11.1404 2.66666 11.5012 2.69614C11.8759 2.72675 12.2204 2.79246 12.544 2.95732C13.0457 3.21299 13.4537 3.62094 13.7093 4.1227C13.8742 4.44626 13.9399 4.79073 13.9705 5.16546C14 5.52625 14 5.96915 14 6.50579V8.82719C14 9.36383 14 9.80673 13.9705 10.1675C13.9399 10.5423 13.8742 10.8867 13.7093 11.2103C13.4537 11.712 13.0457 12.12 12.544 12.3757C12.2204 12.5405 11.8759 12.6062 11.5012 12.6368C11.1404 12.6663 10.6975 12.6663 10.1609 12.6663H10.0209L8.5062 14.4338L7.49378 14.4338L5.97905 12.6663H5.83911C5.30247 12.6663 4.85957 12.6663 4.49877 12.6368C4.12404 12.6062 3.77957 12.5405 3.45602 12.3757C2.95425 12.12 2.5463 11.712 2.29064 11.2103C2.12578 10.8867 2.06007 10.5422 2.02945 10.1675C1.99997 9.80672 1.99998 9.36382 1.99999 8.82717L1.99999 8.33315H3.33332V8.79963C3.33332 9.37069 3.33384 9.75888 3.35836 10.0589C3.38224 10.3512 3.42552 10.5007 3.47865 10.605C3.60648 10.8558 3.81045 11.0598 4.06134 11.1876C4.1656 11.2408 4.31507 11.2841 4.60735 11.3079C4.90741 11.3324 5.2956 11.333 5.86666 11.333H6.59237L7.99999 12.9755L9.40763 11.333H10.1333C10.7044 11.333 11.0926 11.3324 11.3926 11.3079C11.6849 11.2841 11.8344 11.2408 11.9386 11.1876C12.1895 11.0598 12.3935 10.8558 12.5213 10.605C12.5745 10.5007 12.6177 10.3512 12.6416 10.0589C12.6661 9.75888 12.6667 9.37069 12.6667 8.79964V6.53334C12.6667 5.96229 12.6661 5.57409 12.6416 5.27403C12.6177 4.98176 12.5745 4.83229 12.5213 4.72802C12.3935 4.47714 12.1895 4.27316 11.9386 4.14533C11.8344 4.09221 11.6849 4.04892 11.3926 4.02504Z",fill:e?"currentColor":void 0}),n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.18377 7.18378L7.62053 5.87351H8.37947L8.81623 7.18378L10.1265 7.62053V8.37948L8.81623 8.81623L8.37947 10.1265H7.62053L7.18377 8.81623L5.87351 8.37948V7.62053L7.18377 7.18378Z",fill:e?"currentColor":void 0})]}),i=({fillCurrentColor:e=!0,className:t})=>(0,n.jsxs)("svg",{className:t,width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.9929 5.04652L10.3083 5.7311L11.602 7.0247L12.2867 6.34012C12.2652 6.26694 12.2367 6.18523 12.2012 6.0998C12.1016 5.86038 11.968 5.64205 11.8296 5.5036C11.6911 5.36514 11.4727 5.23157 11.2333 5.13201C11.1479 5.09648 11.0661 5.06797 10.9929 5.04652ZM10.6592 7.96748L9.36549 6.67388L5.92291 10.1162C5.50551 10.5335 5.25873 11.2125 5.12676 11.8724C5.09852 12.0136 5.07652 12.1489 5.0594 12.2738C5.18415 12.2566 5.31921 12.2346 5.46023 12.2064C6.11984 12.0744 6.79883 11.8275 7.21669 11.4097L10.6592 7.96748ZM4.33325 13C3.66659 13 3.66659 12.9996 3.66659 12.9996L3.66659 12.9983L3.66659 12.9957L3.66664 12.9882L3.66695 12.9637C3.66728 12.9432 3.66793 12.9146 3.66917 12.8787C3.67164 12.807 3.67649 12.7058 3.68602 12.5818C3.70502 12.3349 3.74295 11.9928 3.81931 11.611C3.96692 10.8728 4.27927 9.87414 4.98014 9.17333L10.2919 3.8619C10.4156 3.73825 10.5829 3.66813 10.7578 3.66667C11.0504 3.66422 11.4208 3.76595 11.7452 3.90087C12.0828 4.04124 12.47 4.25846 12.7723 4.56075C13.0747 4.86305 13.2919 5.25027 13.4323 5.58782C13.5672 5.91229 13.669 6.28262 13.6665 6.57528C13.6651 6.75017 13.5949 6.91748 13.4713 7.04115L8.15946 12.3525C7.45837 13.0536 6.45995 13.3661 5.72188 13.5138C5.34011 13.5902 4.99815 13.6282 4.75126 13.6472C4.62736 13.6567 4.5262 13.6616 4.4545 13.6641C4.41863 13.6653 4.39004 13.6659 4.36957 13.6663L4.34504 13.6666L4.33751 13.6666L4.33497 13.6666L4.33401 13.6666C4.33401 13.6666 4.33325 13.6666 4.33325 13ZM4.33325 13V13.6666C3.96506 13.6666 3.66659 13.3678 3.66659 12.9996L4.33325 13Z",fill:e?"currentColor":void 0}),n.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.99992 0.333313C4.28687 0.333313 4.54163 0.516933 4.63237 0.789161L5.27697 2.72293L7.21074 3.36752C7.48297 3.45827 7.66659 3.71303 7.66659 3.99998C7.66659 4.28693 7.48297 4.54169 7.21074 4.63244L5.27697 5.27703L4.63237 7.2108C4.54163 7.48303 4.28687 7.66665 3.99992 7.66665C3.71297 7.66665 3.45821 7.48303 3.36746 7.2108L2.72287 5.27703L0.7891 4.63244C0.516872 4.54169 0.333252 4.28693 0.333252 3.99998C0.333252 3.71303 0.516872 3.45827 0.7891 3.36752L2.72287 2.72293L3.36746 0.789161C3.45821 0.516933 3.71297 0.333313 3.99992 0.333313ZM3.99992 3.10816L3.88237 3.4608C3.81602 3.65987 3.65981 3.81608 3.46074 3.88244L3.1081 3.99998L3.46074 4.11752C3.65981 4.18388 3.81602 4.34009 3.88237 4.53916L3.99992 4.89179L4.11746 4.53916C4.18382 4.34009 4.34003 4.18388 4.5391 4.11752L4.89173 3.99998L4.5391 3.88244C4.34003 3.81608 4.18382 3.65987 4.11746 3.4608L3.99992 3.10816Z",fill:e?"currentColor":void 0})]})},9401:(e,t,a)=>{a.d(t,{Eq:()=>g,Y3:()=>c,ni:()=>m,tq:()=>d,uy:()=>u});var n=a(1635),r=a.n(n),o=a(36619),s=a.n(o),i=a(14195),l=a.n(i);r().extend(s()),r().extend(l());let d=Intl.DateTimeFormat().resolvedOptions().timeZone,c=(e=1)=>new Promise(t=>setTimeout(t,e)),m=e=>r()(e).fromNow(),u=e=>r()(e).format("YYYY-MM-DD HH:mm:ss"),g=e=>r()(e).format("YYYY-MM-DD HH:mm")}};