"use strict";exports.id=423,exports.ids=[423,353],exports.modules={3230:(e,t,i)=>{i.d(t,{bZ:()=>l.a,u:()=>r.a,zx:()=>s.a});var n=i(27889),l=i.n(n),o=i(53800),s=i.n(o),a=i(69348),r=i.n(a)},65893:(e,t,i)=>{i.d(t,{C9:()=>k,Cf:()=>T,ZP:()=>b});var n,l=i(82103),o=i(86069),s=i.n(o),a=i(59591),r=i.n(a),d=i(18459),c=i.n(d),h=i(23672),f=i.n(h),u=i(63901),p=i.n(u),g=i(27078),m=i.n(g);!function(e){e.ARC="arc",e.AREA="area",e.BAR="bar",e.BOXPLOT="boxplot",e.CIRCLE="circle",e.ERRORBAND="errorband",e.ERRORBAR="errorbar",e.IMAGE="image",e.LINE="line",e.POINT="point",e.RECT="rect",e.RULE="rule",e.SQUARE="square",e.TEXT="text",e.TICK="tick",e.TRAIL="trail"}(n||(n={}));let y={GRAY_10:"#262626",GRAY_9:"#434343",GRAY_8:"#65676c",GRAY_5:"#d9d9d9"},x=["#7763CF","#444CE7","#1570EF","#0086C9","#3E4784","#E31B54","#EC4A0A","#EF8D0C","#EBC405","#5381AD"],C=[x[4],x[5],x[8],x[3],x[0]],v=x[2],R={mark:{tooltip:!0},font:"Roboto, Arial, Noto Sans, sans-serif",padding:{top:30,bottom:20,left:0,right:0},title:{color:y.GRAY_10,fontSize:14},axis:{labelPadding:0,labelOffset:0,labelFontSize:10,gridColor:y.GRAY_5,titleColor:y.GRAY_9,labelColor:y.GRAY_8,labelFont:" Roboto, Arial, Noto Sans, sans-serif"},axisX:{labelAngle:-45},line:{color:v},bar:{color:v},legend:{symbolLimit:15,columns:1,labelFontSize:10,labelColor:y.GRAY_8,titleColor:y.GRAY_9,titleFontSize:14},range:{category:x,ordinal:x,diverging:x,symbol:x,heatmap:x,ramp:x},point:{size:60,color:v}};class b{constructor(e,t){this.config=R,this.data=e.data,this.autosize={type:"fit",contains:"padding"},this.params=[{name:"hover",select:{type:"point",on:"mouseover",clear:"mouseout"}}],this.options={width:s()(t?.width)?"container":t.width,height:s()(t?.height)?"container":t.height,stack:s()(t?.stack)?"zero":t.stack,point:!!s()(t?.point)||t.point,donutInner:s()(t?.donutInner)?60:t.donutInner,categoriesLimit:s()(t?.categoriesLimit)?25:t.categoriesLimit,isShowTopCategories:!s()(t?.isShowTopCategories)&&t?.isShowTopCategories,isHideLegend:!s()(t?.isHideLegend)&&t.isHideLegend,isHideTitle:!s()(t?.isHideTitle)&&t.isHideTitle};let i=r()(e);this.parseSpec(i)}getChartSpec(){let e=this.getAllCategories(this.encoding);return e.length>this.options.categoriesLimit?null:(e.length<=5&&(this.encoding.color={...this.encoding.color,scale:{range:C}}),this.options.isHideLegend&&(this.encoding.color={...this.encoding.color,legend:null}),this.options.isHideTitle&&(this.title=null),this.data=this.transformDataValues(this.data,this.encoding),{$schema:this.$schema,title:this.title,data:this.data,mark:this.mark,width:this.options.width,height:this.options.height,autosize:this.autosize,encoding:this.encoding,params:this.params,transform:this.transform})}parseSpec(e){if(this.$schema=e.$schema,this.title=e.title,this.transform=e.transform,"mark"in e){let t="string"==typeof e.mark?{type:e.mark}:e.mark;this.addMark(t)}if("encoding"in e){if(this.options?.isShowTopCategories){let t=this.filterTopCategories(e.encoding);t&&(this.data=t)}this.addEncoding(e.encoding)}}addMark(e){let t={};"line"===e.type?t={point:this.options.point,tooltip:!0}:"arc"===e.type&&(t={innerRadius:this.options.donutInner}),this.mark={type:e.type,...t}}addEncoding(e){if(this.encoding=e,s()(this.encoding.color)){let t=["x","y"].find(t=>e[t]?.type==="nominal");if(t){let i=e[t];this.encoding.color={field:i.field,type:i.type}}}if("bar"===this.mark.type&&("stack"in this.encoding.y&&(this.encoding.y.stack=this.options.stack),"xOffset"in this.encoding)){let e=this.encoding.xOffset,t=e?.title;t||(t=this.findFieldTitleInEncoding(this.encoding,e?.field)),this.encoding.xOffset={...e,title:t}}this.addHoverHighlight(this.encoding)}addHoverHighlight(e){let t=e.color?.condition?e.color.condition:e.color;if(!t?.field||!t?.type)return;this.params&&t?.field&&(this.params[0].select.fields=[t.field]),this.encoding.opacity={condition:{param:"hover",value:1},value:.3};let i=t?.title;i||(i=this.findFieldTitleInEncoding(this.encoding,t?.field));let n={title:i,field:t?.field,type:t?.type,scale:{range:x}};this.encoding.color={...n,condition:{param:"hover",...p()(n,"scale")}}}filterTopCategories(e){let t=["xOffset","color","x","y"].filter(t=>e[t]?.type==="nominal"),i=["theta","x","y"].filter(t=>e[t]?.type==="quantitative");if(!t.length||!i.length)return;let n=r()(this.data.values),l=e[i[0]],o=f()(n,e=>{let t=e[l.field];return m()(t)?-t:0}),s=[];for(let i of t){let t=e[i];if(s.some(e=>e.field===t.field))continue;let n=o.map(e=>e[t.field]),l=c()(n).slice(0,this.options.categoriesLimit);s.push({field:t.field,values:l})}return{values:n.filter(e=>s.every(t=>t.values.includes(e[t.field])))}}getAllCategories(e){let t=["xOffset","color","x","y"].find(t=>e[t]?.type==="nominal");if(!t)return[];let i=e[t],n=this.data.values.map(e=>e[i.field]);return c()(n)}findFieldTitleInEncoding(e,t){let i=["x","y","xOffset","color"].find(i=>e[i]?.field===t&&e[i]?.title);return e[i]?.title||void 0}transformDataValues(e,t){if(t?.x?.type==="temporal"){let i=e.values.map(e=>({...e,[t.x.field]:this.transformTemporalValue(e[t.x.field])}));return{...e,values:i}}if(t?.y?.type==="temporal"){let i=e.values.map(e=>({...e,[t.y.field]:this.transformTemporalValue(e[t.y.field])}));return{...e,values:i}}return e}transformTemporalValue(e){if(null==e)return e;let t="string"==typeof e?e:String(e);return t.includes("UTC")?t.replace(/\s+UTC([+-][0-9]+)?(:[0-9]+)?/,""):t}}let A=(e,t)=>{if("bar"===e){if(t?.xOffset)return l.oX.GROUPED_BAR;if(!s()(t?.y?.stack)||!s()(t?.x?.stack))return l.oX.STACKED_BAR}else if("arc"===e)return l.oX.PIE;return e?e.toUpperCase():null},T=e=>{let t=e?.chartSchema,i=e?.chartType||null,n=null,l=null,o=null,s=null,a=null;if(t&&"encoding"in t){let e=t.encoding;n=e?.x?.field||null,l=e?.y?.field||null,o=e?.color?.field||null,s=e?.xOffset?.field||null,a=e?.theta?.field||null,null===i&&(i=A("string"==typeof t.mark?t.mark:t.mark.type,e))}return{chartType:i,xAxis:n,yAxis:l,color:o,xOffset:s,theta:a}},k=e=>e?["x","y","xOffset","color"].reduce((t,i)=>{let n=e[i];return n?.field&&n?.title&&(t[n?.field]=n?.title),t},{}):{}},21353:(e,t,i)=>{i.a(e,async(e,n)=>{try{i.r(t),i.d(t,{default:()=>R});var l=i(20997),o=i(16689),s=i(16593),a=i(89699),r=i.n(a),d=i(3230),c=i(8849);i(65097);var h=i(65893),f=i(13422),u=i.n(f),p=i(97615),g=i.n(p),m=i(48683),y=i.n(m),x=i(87680),C=i.n(x),v=e([s]);function R(e){let{className:t,spec:i,values:n,width:a=600,height:f=320,autoFilter:p,hideActions:m,hideTitle:x,hideLegend:v,forceUpdate:R,onReload:b,onEdit:A,onPin:T}=e,[k,E]=(0,o.useState)(null),[S,j]=(0,o.useState)(!1);(0,o.useRef)(null);let w=(0,o.useRef)(null),L=(0,o.useMemo)(()=>{if(!i||!n)return;let e=new h.ZP({...i,data:{values:n}},{donutInner:k,isShowTopCategories:p||S,isHideLegend:v,isHideTitle:x}),t=e.getChartSpec();return r()(t?.data?.values)?null:(0,c.compile)(t,{config:e.config}).spec},[i,n,S,k,R]);return null===L?0===n.length?l.jsx("div",{children:"No available data"}):l.jsx(d.bZ,{className:"mt-6 mb-4 mx-4",message:(0,l.jsxs)("div",{className:"d-flex align-center justify-space-between",children:[l.jsx("div",{children:"There are too many categories to display effectively. Click 'Show top 25' to view the top results, or ask a follow-up question to focus on a specific group or filter results."}),l.jsx(d.zx,{size:"small",icon:l.jsx(y(),{}),onClick:()=>{j(!S)},children:"Show top 25"})]}),type:"warning"}):(0,l.jsxs)("div",{className:(0,s.default)("adm-chart",{"adm-chart--no-actions":m},t),style:{width:a},children:[(!!b||!!A||!!T)&&(0,l.jsxs)("div",{className:"adm-chart-additional d-flex justify-content-between align-center",children:[!!b&&l.jsx(d.u,{title:"Regenerate chart",children:l.jsx("button",{onClick:b,children:l.jsx(u(),{})})}),!!A&&l.jsx(d.u,{title:"Edit chart",children:l.jsx("button",{onClick:A,children:l.jsx(g(),{})})}),!!T&&l.jsx(d.u,{title:"Pin chart to dashboard",children:l.jsx("button",{onClick:T,children:l.jsx(C(),{})})})]}),l.jsx("div",{style:{width:a,height:f},ref:w})]})}s=(v.then?(await v)():v)[0],n()}catch(e){n(e)}})}};