exports.id=459,exports.ids=[459],exports.modules={72535:(e,t,i)=>{"use strict";i.d(t,{H:()=>d,z:()=>c});var a=i(20997),s=i(46810),r=i(83905),l=i.n(r),n=i(59817);let o=e=>t=>{let{onClick:i,onMouseEnter:r,onMouseLeave:l,className:n,marginLeft:o,marginRight:d,...c}=t;return a.jsx(s.z,{className:n,style:{marginLeft:o,marginRight:d},icon:e,onClick:e=>{i&&i(e),e.stopPropagation()},onMouseEnter:e=>{r&&r(e),e.stopPropagation()},onMouseLeave:e=>{l&&l(e),e.stopPropagation()},type:"text",size:"small",...c})},d=o(a.jsx(l(),{})),c=o(a.jsx(n.nX,{}))},10466:(e,t,i)=>{"use strict";i.d(t,{N:()=>a});let a=(0,i(16689).createContext)({onMoreClick:()=>{},onNodeClick:()=>{},onAddClick:()=>{}})},6529:(e,t,i)=>{"use strict";i.d(t,{At:()=>v,Lb:()=>C,aV:()=>M,iI:()=>_,px:()=>w,rt:()=>I,s_:()=>b,uu:()=>D});var a=i(20997);i(16689);var s=i(57518),r=i.n(s),l=i(9200),n=i(24351),o=i(97615),d=i.n(o),c=i(13422),h=i.n(c),m=i(28768),g=i.n(m),x=i(48683),u=i.n(x),p=i(21760),f=i.n(p),j=i(83795),y=i.n(j),E=i(56289),L=i(79802);let k=r()(l.v).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),N=e=>t=>{let{children:i,onMenuEnter:s,onDropdownVisibleChange:r}=t,n=e(t);return a.jsx(l.L,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:a.jsx(k,{onClick:e=>e.domEvent.stopPropagation(),items:n,onMouseEnter:s}),onVisibleChange:r,children:i})},v=N(e=>{let{onMoreClick:t}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(d(),{className:"mr-2"}),"Update Columns"]}),key:n.dI.UPDATE_COLUMNS,onClick:()=>t(n.dI.UPDATE_COLUMNS)},{label:a.jsx(L.ZQ,{onConfirm:()=>t(n.dI.DELETE)}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),C=N(e=>{let{onMoreClick:t}=e;return[{label:a.jsx(L.e0,{onConfirm:()=>t(n.dI.DELETE)}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),b=N(e=>{let{onMoreClick:t,data:i}=e,{nodeType:s}=i,r={[n.QZ.CALCULATED_FIELD]:L.pn,[n.QZ.RELATION]:L.ck}[s]||L.pn;return[{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(d(),{className:"mr-2"}),"Edit"]}),key:n.dI.EDIT,onClick:()=>t(n.dI.EDIT)},{label:a.jsx(r,{onConfirm:()=>t(n.dI.DELETE)}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),w=N(e=>{let{onMoreClick:t,isSupportCached:i}=e;return[i&&{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(y(),{className:"mr-2"}),"Cache settings"]}),key:n.dI.CACHE_SETTINGS,onClick:()=>t(n.dI.CACHE_SETTINGS)},{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(h(),{className:"mr-2"}),i?"Refresh all caches":"Refresh all"]}),key:n.dI.REFRESH,onClick:()=>t(n.dI.REFRESH)}].filter(Boolean)}),I=N(e=>{let{onMoreClick:t,isHideLegend:i,isSupportCached:s}=e;return[{label:i?(0,a.jsxs)(a.Fragment,{children:[a.jsx(u(),{className:"mr-2"}),"Show categories"]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(g(),{className:"mr-2"}),"Hide categories"]}),key:n.dI.HIDE_CATEGORY,onClick:()=>t(n.dI.HIDE_CATEGORY)},{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(h(),{className:"mr-2"}),s?"Refresh cache":"Refresh"]}),key:n.dI.REFRESH,onClick:()=>t(n.dI.REFRESH)},{label:a.jsx(L.mU,{onConfirm:()=>t(n.dI.DELETE)}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),_=N(e=>{let{onMoreClick:t,data:i}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(u(),{className:"mr-2"}),"View"]}),key:n.dI.VIEW_SQL_PAIR,onClick:()=>t({type:n.dI.VIEW_SQL_PAIR,data:i})},{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(d(),{className:"mr-2"}),"Edit"]}),key:n.dI.EDIT,onClick:()=>t({type:n.dI.EDIT,data:i})},{label:a.jsx(L.pv,{onConfirm:()=>t({type:n.dI.DELETE,data:i}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),D=N(e=>{let{onMoreClick:t,data:i}=e;return[{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(u(),{className:"mr-2"}),"View"]}),key:n.dI.VIEW_INSTRUCTION,onClick:()=>t({type:n.dI.VIEW_INSTRUCTION,data:i})},{label:(0,a.jsxs)(a.Fragment,{children:[a.jsx(d(),{className:"mr-2"}),"Edit"]}),key:n.dI.EDIT,onClick:()=>t({type:n.dI.EDIT,data:i})},{label:a.jsx(L.py,{onConfirm:()=>t({type:n.dI.DELETE,data:i}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:n.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),M=N(e=>{let{onMoreClick:t,data:i}=e;return[{label:"Adjust steps",icon:a.jsx(E.Wi,{}),disabled:!i.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>t({type:n.dI.ADJUST_STEPS,data:i})},{label:"Adjust SQL",icon:a.jsx(f(),{className:"text-base"}),disabled:!i.sql,key:"adjust-sql",onClick:()=>t({type:n.dI.ADJUST_SQL,data:i})}]})},93462:(e,t,i)=>{"use strict";i.d(t,{Z:()=>m});var a=i(20997),s=i(28518),r=i.n(s),l=i(55893),n=i.n(l),o=i(27050),d=i.n(o),c=i(58168),h=i.n(c);function m(e){let{children:t}=e;return a.jsx(n(),{...e,mouseLeaveDelay:0,overlayStyle:{maxWidth:520},children:t})}m.Row=d(),m.Col=e=>{let{title:t,children:i,code:s,span:l=24,marginBottom:n=8}=e;return(0,a.jsxs)(r(),{span:l,children:[a.jsx("div",{className:"gray-7 mb-0",children:t}),a.jsx("div",{style:{marginBottom:n},children:a.jsx(h().Text,{code:s,children:i})})]})}},54909:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var a=i(20997);function s(){return a.jsx("svg",{style:{position:"absolute",top:0,left:0,width:0,height:0,zIndex:-1},children:(0,a.jsxs)("defs",{children:[a.jsx("marker",{id:"many_right",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:0,refY:11,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.28866 10L6.49577e-06 2.33206L1.4329e-05 -1.18499e-06L13.5547 11L2.869e-06 22L3.07287e-06 19.668L9.28864 12L5.65057e-06 12L5.82542e-06 10L9.28866 10Z",fill:"#b1b1b7"})}),a.jsx("marker",{id:"many_left",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:14,refY:11,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.26603 12L13.5547 19.6679L13.5547 22L0 11L13.5547 0V2.33204L4.26605 10L13.5547 10V12L4.26603 12Z",fill:"#b1b1b7"})}),(0,a.jsxs)("marker",{id:"one_right",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:-4,refY:11,children:[a.jsx("rect",{width:"1400",height:"993",transform:"translate(-407 -263)",fill:"none"}),a.jsx("rect",{x:"6",width:"2",height:"22",fill:"#b1b1b7"})]}),(0,a.jsxs)("marker",{id:"one_left",viewBox:"0 0 14 22",markerHeight:14,markerWidth:14,refX:18,refY:11,children:[a.jsx("rect",{width:"1400",height:"993",transform:"translate(-407 -263)",fill:"none"}),a.jsx("rect",{x:"6",width:"2",height:"22",fill:"#b1b1b7"})]}),a.jsx("marker",{id:"many_right_selected",viewBox:"0 0 18 32",markerHeight:18,markerWidth:18,refX:0,refY:16,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.4161 4.94444L13.2993 8H14.7007L14.5839 4.94444L17.2993 6.58333L18 5.41667L15.1387 4L18 2.58333L17.2993 1.41667L14.5839 3.05556L14.7007 0H13.2993L13.4161 3.05556L10.7007 1.41667L10 2.58333L12.8613 4L10 5.41667L10.7007 6.58333L13.4161 4.94444ZM3.63475e-06 7.33206L9.28865 15L2.9644e-06 15L2.78955e-06 17L9.28863 17L0 24.668V27L13.5547 16L1.1468e-05 5L3.63475e-06 7.33206Z",fill:"#2F54EB"})}),a.jsx("marker",{id:"many_left_selected",viewBox:"0 0 18 32",markerHeight:18,markerWidth:18,refX:18,refY:16,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.41606 4.94444L3.29927 8H4.70073L4.58394 4.94444L7.29927 6.58333L8 5.41667L5.13869 4L8 2.58333L7.29927 1.41667L4.58394 3.05556L4.70073 0H3.29927L3.41606 3.05556L0.70073 1.41667L0 2.58333L2.86131 4L0 5.41667L0.70073 6.58333L3.41606 4.94444ZM17.8899 24.6679L8.60127 17H17.8899V15H8.60129L17.8899 7.33204V5L4.33524 16L17.8899 27L17.8899 24.6679Z",fill:"#2F54EB"})}),a.jsx("marker",{id:"one_right_selected",viewBox:"0 0 16 32",markerHeight:16,markerWidth:16,refX:0,refY:16,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 8V0H15.0211L13 1.32812V2.3125L14.9737 1.01563H15.0211V8H16ZM8.63351 5H6.63351V27H8.63351V5Z",fill:"#2F54EB"})}),a.jsx("marker",{id:"one_left_selected",viewBox:"0 0 16 32",markerHeight:16,markerWidth:16,refX:18,refY:16,children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 8V0H2.02105L0 1.32812V2.3125L1.97368 1.01563H2.02105V8H3ZM9 5H7V27H9V5Z",fill:"#2F54EB"})})]})})}},15833:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{Z:()=>g});var s=i(20997),r=i(16689),l=i(8898),n=i(57518),o=i.n(n),d=i(93462),c=i(80284),h=e([l]);l=(h.then?(await h)():h)[0];let m=o().div.withConfig({displayName:"ModelEdge__Joint",componentId:"sc-4b47b209-0"})(["position:absolute;width:30px;height:30px;opacity:0;"]),g=(0,r.memo)(({sourceX:e,sourceY:t,targetX:i,targetY:a,sourcePosition:n,targetPosition:o,markerStart:h,markerEnd:g,data:x})=>{let[u,p,f]=(0,l.getSmoothStepPath)({sourceX:e,sourceY:t,sourcePosition:n,targetX:i,targetY:a,targetPosition:o}),j=x.highlight,y=(0,r.useMemo)(()=>{let e=`${x.relation.fromModelName}.${x.relation.fromColumnName}`,t=`${x.relation.toModelName}.${x.relation.toColumnName}`;return{name:x.relation.name,joinType:(0,c.I)(x.relation.type),description:x.relation?.description||"-",fromField:e,toField:t}},[x.relation]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(l.BaseEdge,{path:u,markerStart:h,markerEnd:g,style:j?{stroke:"var(--geekblue-6)",strokeWidth:1.5}:{stroke:"var(--gray-5)"}}),s.jsx(l.EdgeLabelRenderer,{children:s.jsx(d.Z,{visible:j,title:"Relationship",content:(0,s.jsxs)(d.Z.Row,{gutter:16,children:[s.jsx(d.Z.Col,{title:"From",span:12,children:y.fromField}),s.jsx(d.Z.Col,{title:"To",span:12,children:y.toField}),s.jsx(d.Z.Col,{title:"Type",span:12,children:y.joinType}),s.jsx(d.Z.Col,{title:"Description",children:y.description})]}),children:s.jsx(m,{style:{transform:`translate(-50%, -50%) translate(${p}px,${f}px)`}})})})]})});a()}catch(e){a(e)}})},82526:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{B:()=>s.Z});var s=i(15833),r=e([s]);s=(r.then?(await r)():r)[0],a()}catch(e){a(e)}})},82295:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{Z:()=>d});var s=i(20997);i(16689);var r=i(57518),l=i.n(r),n=i(67078),o=e([n]);n=(o.then?(await o)():o)[0];let c=l().div.withConfig({displayName:"Column__NodeColumn",componentId:"sc-b3b66940-0"})(["position:relative;display:flex;align-items:center;justify-content:space-between;padding:4px 8px;color:var(--gray-9);line-height:24px;&:hover{background-color:var(--gray-3);}svg{flex-shrink:0;}.adm-column-title{display:flex;align-items:center;min-width:1px;svg{margin-right:6px;}> span{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}}"]),h=l().div.withConfig({displayName:"Column__Title",componentId:"sc-b3b66940-1"})(["display:flex;justify-content:space-between;align-items:center;color:var(--gray-8);padding:4px 12px;cursor:default;"]);function d(e){let{id:t,type:i,onMouseEnter:a,onMouseLeave:r,displayName:l,style:o={},icon:d,extra:h}=e;return(0,s.jsxs)(c,{style:o,onMouseEnter:a,onMouseLeave:r,children:[(0,s.jsxs)("div",{className:"adm-column-title",children:[s.jsx("span",{className:"d-inline-flex flex-shrink-0",title:i,children:d}),s.jsx("span",{title:l,children:l})]}),h,s.jsx(n.Z,{id:t.toString()})]})}d.Title=e=>{let{show:t,extra:i,children:a}=e;return t?(0,s.jsxs)(h,{children:[a,s.jsx("span",{children:i})]}):null},d.MoreTip=e=>(0,s.jsxs)("div",{className:"text-sm gray-7 px-3 py-1",children:["and ",e.count," more"]}),a()}catch(e){a(e)}})},67078:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{Z:()=>n});var s=i(20997),r=i(8898),l=e([r]);function n({id:e}){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(r.Handle,{type:"source",position:r.Position.Left,id:`${e}_${r.Position.Left}`}),s.jsx(r.Handle,{type:"source",position:r.Position.Right,id:`${e}_${r.Position.Right}`}),s.jsx(r.Handle,{type:"target",position:r.Position.Left,id:`${e}_${r.Position.Left}`}),s.jsx(r.Handle,{type:"target",position:r.Position.Right,id:`${e}_${r.Position.Right}`})]})}r=(l.then?(await l)():l)[0],a()}catch(e){a(e)}})},48508:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{Z:()=>k});var s=i(20997),r=i(16689),l=i(62018),n=i(8898),o=i(26785),d=i(46990),c=i(67078),h=i(10466),m=i(82295),g=i(59817),x=i(9620),u=i(95015),p=i(47826),f=i(24351),j=i(6529),y=i(72535),E=e([n,c,m,p]);[n,c,m,p]=E.then?(await E)():E;let{Text:L}=l.Typography,k=(0,r.memo)(({data:e})=>{let t=(0,r.useContext)(h.N),i=i=>{t?.onAddClick({targetNodeType:i,data:e.originalData})},a=(0,r.useCallback)(t=>v(t,e,{limit:p.De.columnsLimit}),[e.highlight]);return(0,s.jsxs)(d.l$,{onClick:()=>{t?.onNodeClick({data:e.originalData})},"data-testid":`diagram__model-node__${e.originalData.displayName}`,"data-guideid":`model-${e.index}`,children:[(0,s.jsxs)(d.l7,{className:"dragHandle",children:[(0,s.jsxs)("span",{className:"adm-model-header",children:[s.jsx(g.ZA,{}),s.jsx(L,{ellipsis:!0,title:e.originalData.displayName,children:e.originalData.displayName})]}),(0,s.jsxs)("span",{children:[s.jsx(d.Ab,{originalData:e.originalData}),s.jsx(j.At,{data:e.originalData,onMoreClick:i=>{t?.onMoreClick({type:i,data:e.originalData})},children:s.jsx(y.z,{className:"gray-1",marginRight:-4,"data-guideid":`edit-model-${e.index}`})})]}),s.jsx(c.Z,{id:e.originalData.id.toString()})]}),(0,s.jsxs)(d.x_,{draggable:!1,children:[s.jsx(m.Z.Title,{show:!0,children:"Columns"}),a(e.originalData.fields),s.jsx(m.Z.Title,{show:!0,extra:s.jsx(y.H,{className:"gray-8",marginRight:-8,onClick:()=>i(f.QZ.CALCULATED_FIELD)}),children:"Calculated Fields"}),a(e.originalData.calculatedFields),s.jsx(m.Z.Title,{show:!0,extra:s.jsx(y.H,{className:"gray-8",marginRight:-8,onClick:()=>i(f.QZ.RELATION)}),children:"Relationships"}),a(e.originalData.relationFields)]})]})}),N=(0,u.x)(e=>{let{nodeType:t,id:i,type:a,isPrimaryKey:l,highlight:d}=e,c=t===f.QZ.RELATION,u=t===f.QZ.CALCULATED_FIELD,p=(0,n.useReactFlow)(),E=(0,r.useContext)(h.N),L=(0,r.useCallback)(e=>{if(!c)return;let{getEdges:t,setEdges:a,setNodes:s}=p,r=t().find(e=>(0,o.lS)(e.sourceHandle)===i||(0,o.lS)(e.targetHandle)===i);r&&(a((0,o.Bx)([r?.id],!0)),s((0,o.kx)([r.source,r.target],[(0,o.lS)(r.sourceHandle),(0,o.lS)(r.targetHandle)])))},[p]),k=(0,r.useCallback)(e=>{if(!c)return;let{setEdges:t,setNodes:i}=p;t((0,o.Bx)([],!1)),i((0,o.kx)([],[]))},[p]),N=(0,r.useCallback)(e=>{k(e)},[p]),v=(0,r.useCallback)(e=>{L(e)},[p]),C=(0,r.useCallback)(e=>{k(e)},[p]);return(0,r.createElement)(m.Z,{...e,key:i,className:d.includes(i)?"bg-gray-3":void 0,icon:c?s.jsx(g.ZA,{}):(0,x.u)({type:a}),extra:(0,s.jsxs)(s.Fragment,{children:[l&&s.jsx(g.EU,{})," ",(u||c)&&s.jsx(j.s_,{data:e,onMoreClick:t=>{E?.onMoreClick({type:t,data:e})},onMenuEnter:C,children:s.jsx(y.z,{className:"gray-8",marginRight:-4,onMouseEnter:N,onMouseLeave:v})})]}),onMouseLeave:k,onMouseEnter:L})}),v=(e,t,i)=>{let a=i?e.length-i.limit:0,r=i?e.slice(0,i.limit):e;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(N,{data:r,highlight:t.highlight,modelId:t.originalData.modelId}),a>0&&s.jsx(m.Z.MoreTip,{count:a})]})};a()}catch(e){a(e)}})},76880:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{Z:()=>j});var s=i(20997),r=i(16689),l=i(92076),n=i(59817),o=i(9620),d=i(47826),c=i(95015),h=i(10466),m=i(46990),g=i(67078),x=i(82295),u=i(6529),p=e([d,g,x]);[d,g,x]=p.then?(await p)():p;let{Text:f}=l.Typography,j=(0,r.memo)(({data:e})=>{let t=(0,r.useContext)(h.N),i=(0,r.useCallback)(t=>(function(e,t,i){let a=i?e.length-i.limit:0,r=i?e.slice(0,i.limit):e;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(y,{data:r,highlight:t.highlight}),a>0&&s.jsx(x.Z.MoreTip,{count:a})]})})(t,e,{limit:d.De.columnsLimit}),[e.highlight]);return(0,s.jsxs)(m.l$,{onClick:()=>{t?.onNodeClick({data:e.originalData})},"data-testid":`diagram__view-node__${e.originalData.displayName}`,children:[(0,s.jsxs)(m.l7,{className:"dragHandle",color:"var(--green-6)",children:[(0,s.jsxs)("span",{className:"adm-model-header",children:[s.jsx(n.ON,{}),s.jsx(f,{ellipsis:!0,title:e.originalData.displayName,children:e.originalData.displayName})]}),s.jsx("span",{children:s.jsx(u.Lb,{onMoreClick:i=>{t?.onMoreClick({type:i,data:e.originalData})},children:s.jsx(l.z,{className:"gray-1",icon:s.jsx(n.nX,{}),onClick:e=>e.stopPropagation(),type:"text",size:"small"})})}),s.jsx(g.Z,{id:e.originalData.id})]}),s.jsx(m.x_,{draggable:!1,children:i(e.originalData.fields)})]})}),y=(0,c.x)(e=>{let{id:t,type:i}=e;return(0,r.createElement)(x.Z,{...e,key:t,icon:(0,o.u)({type:i})})});a()}catch(e){a(e)}})},35126:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{H:()=>s.Z,R:()=>r.Z});var s=i(48508),r=i(76880),l=e([s,r]);[s,r]=l.then?(await l)():l,a()}catch(e){a(e)}})},46990:(e,t,i)=>{"use strict";i.d(t,{Ab:()=>m,x_:()=>h,l7:()=>c,l$:()=>d});var a=i(20997),s=i(59817),r=i(69348),l=i.n(r),n=i(57518),o=i.n(n);let d=o().div.withConfig({displayName:"utils__StyledNode",componentId:"sc-8b78834f-0"})(["position:relative;width:200px;border-radius:4px;overflow:hidden;box-shadow:0px 3px 6px -4px rgba(0,0,0,0.12),0px 6px 16px rgba(0,0,0,0.08),0px 9px 28px 8px rgba(0,0,0,0.05);cursor:pointer;&:before{content:'';pointer-events:none;position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;border:2px solid transparent;transition:border-color 0.15s ease-in-out;}&:hover,&:focus{&:before{border-color:var(--geekblue-6);}}.react-flow__handle{border:none;opacity:0;&-left{left:0;}&-right{right:0;}}"]),c=o().div.withConfig({displayName:"utils__NodeHeader",componentId:"sc-8b78834f-1"})(["position:relative;background-color:",";font-size:14px;color:white;padding:6px 8px;display:flex;align-items:center;justify-content:space-between;height:36px;&.dragHandle{cursor:move;}.adm-model-header{display:flex;align-items:center;svg{margin-right:6px;}+ svg{cursor:pointer;}.ant-typography{width:140px;color:white;}}"],e=>e.color||"var(--geekblue-6)"),h=o().div.withConfig({displayName:"utils__NodeBody",componentId:"sc-8b78834f-2"})(["background-color:white;padding-bottom:4px;"]),m=({originalData:e})=>e.cached?a.jsx(l(),{title:(0,a.jsxs)(a.Fragment,{children:["Cached",e.refreshTime?`: refresh every ${e.refreshTime}`:null]}),placement:"top",children:a.jsx(s.Bk,{className:"cursor-pointer"})}):null},4459:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.r(t),i.d(t,{default:()=>L});var s=i(20997),r=i(16689),l=i(8898),n=i(35126),o=i(82526),d=i(54909),c=i(10466),h=i(26785),m=i(59817),g=i(24351),x=i(47826),u=i(9401);i(20410);var p=e([l,n,o,x]);[l,n,o,x]=p.then?(await p)():p;let f={[g.QZ.MODEL]:n.H,[g.QZ.VIEW]:n.R},j={[g.Hk.MODEL]:o.B},y={height:120},E=(0,r.forwardRef)(function(e,t){let{data:i,onMoreClick:a,onNodeClick:n,onAddClick:o}=e,[g,p]=(0,r.useState)(!1),E=(0,l.useReactFlow)();(0,r.useImperativeHandle)(t,()=>E,[E]);let L=(0,r.useMemo)(()=>new x.d3(i).toJsonObject(),[i]);(0,r.useEffect)(()=>{N(L.nodes),b(L.edges),(0,u.Y3)(50).then(()=>E.fitView())},[L]);let[k,N,v]=(0,l.useNodesState)(L.nodes),[C,b,w]=(0,l.useEdgesState)(L.edges),I=(0,r.useCallback)((e,t)=>{b((0,h.Bx)([t.id],!0)),N((0,h.kx)([t.source,t.target],[(0,h.lS)(t.sourceHandle),(0,h.lS)(t.targetHandle)]))},[]),_=(0,r.useCallback)((e,t)=>{b((0,h.Bx)([],!1)),N((0,h.kx)([],[]))},[]),D=async()=>{N(L.nodes),b(L.edges)},M=async()=>{await (0,u.Y3)(),E.fitView(),await (0,u.Y3)(100),p(!g)},H=e=>{let t=new MouseEvent("mousedown",{bubbles:!0,cancelable:!0,clientX:e.clientX,clientY:e.clientY,button:0});document.dispatchEvent(t)},T=e=>{let t=document.querySelectorAll(".ant-dropdown");return Array.from(t).some(t=>t.contains(e))},R=e=>{!e.isTrusted||T(e.target)||H(e)};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(c.N.Provider,{value:{onMoreClick:a,onNodeClick:n,onAddClick:o},children:(0,s.jsxs)(l.default,{nodes:k,edges:C,onNodesChange:v,onEdgesChange:w,onEdgeMouseEnter:I,onEdgeMouseLeave:_,onInit:M,nodeTypes:f,edgeTypes:j,maxZoom:1,onPointerDown:e=>R(e),proOptions:{hideAttribution:!0},children:[s.jsx(l.MiniMap,{style:y,zoomable:!0,pannable:!0}),s.jsx(l.Controls,{showInteractive:!1,children:s.jsx(l.ControlButton,{onClick:D,children:s.jsx(m.Du,{style:{maxWidth:24,maxHeight:24}})})}),s.jsx(l.Background,{gap:16})]})}),s.jsx(d.Z,{})]})}),L=e=>s.jsx(l.ReactFlowProvider,{children:s.jsx(E,{ref:e.forwardRef,...e})});a()}catch(e){a(e)}})},26785:(e,t,i)=>{"use strict";i.d(t,{Bx:()=>s,kx:()=>r,lS:()=>a});let a=e=>e.split("_")[0],s=(e,t)=>i=>i.map(i=>{let a="_selected",s=i.markerStart.replace(a,""),r=i.markerEnd.replace(a,"");return e.includes(i.id)?{...i,data:{...i.data,highlight:t},markerStart:s+a,markerEnd:r+a}:{...i,data:{...i.data,highlight:!1},markerStart:s,markerEnd:r}}),r=(e,t)=>i=>i.map(i=>e.includes(i.id)?{...i,data:{...i.data,highlight:t}}:{...i,data:{...i.data,highlight:[]}})},26952:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{d:()=>l});var s=i(79257),r=e([s]);s=(r.then?(await r)():r)[0];class l{constructor(e){this.viewport={x:0,y:0,zoom:1};let t=new s.$(e);this.nodes=t.nodes,this.edges=t.edges}toJsonObject(){return{nodes:this.nodes,edges:this.edges,viewport:this.viewport}}}a()}catch(e){a(e)}})},47826:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{De:()=>r.D,d3:()=>s.d});var s=i(26952),r=i(79257),l=e([s,r]);[s,r]=l.then?(await l)():l,a()}catch(e){a(e)}})},79257:(e,t,i)=>{"use strict";i.a(e,async(e,a)=>{try{i.d(t,{$:()=>c,D:()=>n});var s=i(8898),r=i(24351),l=e([s]);s=(l.then?(await l)():l)[0];let n={nodesInRow:4,width:200,height:void 0,headerHeight:32,columnHeight:32,moreTipHeight:25,columnsLimit:10,bodyOverflow:"auto",marginX:100,marginY:50,modelNodePreservedHeight:96,viewNodePreservedHeight:32},o=e=>e?1:0,d=(e=[])=>{let t=e.length>n.columnsLimit,i=t?n.columnsLimit:e.length;return{isOverLimit:t,limitedLength:i,originalLength:e.length}};class c{constructor(e){this.config=n,this.nodes=[],this.edges=[],this.start={x:0,y:0,floor:0},this.models=e?.models||[],this.views=e?.views||[],this.init()}init(){for(let e of[...this.models,...this.views])this.addOne(e)}addOne(e){let{nodeType:t}=e,i=this.start.x,a=this.start.y,s=this.createNode({nodeType:t,data:e,x:i,y:a});this.nodes.push(s),this.updateNextStartedPoint()}updateNextStartedPoint(){let e=this.getNodeWidth(),t=0,{length:i}=this.nodes,{marginX:a,marginY:s,nodesInRow:r}=this.config,l=i%r==0;if(l){this.start.floor++;let e=r*(this.start.floor-1),i=[...this.models,...this.views].slice(e,e+4),a=i.reduce((e,t)=>{let i=[...e.fields,...e?.calculatedFields||[],...t?.relationFields||[]],a=[...t.fields,...t?.calculatedFields||[],...t?.relationFields||[]];return i.length>a.length?e:t},i[0]);t=this.getNodeHeight(a)+s}this.start.x=this.start.x+e+a,l&&(this.start.x=0),this.start.y=this.start.y+t}createNode(e){let{nodeType:t,data:i,x:a,y:s}=e;return t===r.QZ.MODEL&&this.addModelEdge(i),{id:i.id,type:t,position:{x:a,y:s},dragHandle:".dragHandle",data:{originalData:i,index:this.nodes.length,highlight:[]}}}addModelEdge(e){let{relationFields:t}=e;for(let i of t){if(this.edges.some(e=>e.data?.relation?.relationId===i.relationId))continue;let t=this.models.find(t=>t.id!==e.id&&[i.fromModelName,i.toModelName].includes(t.referenceName));if(!t)continue;let a=t.relationFields.find(e=>[`${e.fromModelName}.${e.fromColumnName}`,`${e.toModelName}.${e.toColumnName}`].toString()===[`${i.fromModelName}.${i.fromColumnName}`,`${i.toModelName}.${i.toColumnName}`].toString()),s=[i.fromModelName,i.toModelName],l=s.findIndex(t=>t===e.referenceName),n=s.findIndex(e=>e===t?.referenceName);t&&this.edges.push(this.createEdge({type:r.Hk.MODEL,joinType:i.type,sourceModel:e,sourceField:i,sourceJoinIndex:l,targetModel:t,targetField:a,targetJoinIndex:n}))}}createEdge(e){let{type:t,sourceModel:i,sourceField:a,sourceJoinIndex:s,targetModel:r,targetField:l,targetJoinIndex:n,joinType:o,animated:d}=e,c=i.id,h=r.id,[m,g]=this.detectEdgePosition(c,h),x=`${a?.id||c}_${m}`,u=`${l?.id||h}_${g}`,p=this.getMarker(o,s,m),f=this.getMarker(o,n,g);return{id:`${x}_${u}`,type:t,source:c,target:h,sourceHandle:x,targetHandle:u,markerStart:p,markerEnd:f,data:{relation:a,highlight:!1},animated:d}}getFloorIndex(e){let{nodesInRow:t}=this.config;return e%t}detectEdgePosition(e,t){let i=[],[a,r]=[...this.models].reduce((i,a,s)=>(a.id===e&&(i[0]=s),a.id===t&&(i[1]=s),i),[-1,-1]),l=this.getFloorIndex(a),n=this.getFloorIndex(r);return l===n?(i[0]=s.Position.Left,i[1]=s.Position.Left):l>n?(i[0]=s.Position.Left,i[1]=s.Position.Right):(i[0]=s.Position.Right,i[1]=s.Position.Left),i}getMarker(e,t,i){return(({[r.oD.ONE_TO_ONE]:[r.E6.ONE,r.E6.ONE],[r.oD.ONE_TO_MANY]:[r.E6.ONE,r.E6.MANY],[r.oD.MANY_TO_ONE]:[r.E6.MANY,r.E6.ONE]})[e]||[])[t]+(i?`_${i}`:"")}getNodeWidth(){return this.config.width}getNodeHeight(e){let{height:t,headerHeight:i,columnHeight:a,moreTipHeight:s,modelNodePreservedHeight:l,viewNodePreservedHeight:n}=this.config,c={[r.QZ.MODEL]:l,[r.QZ.VIEW]:n}[e.nodeType],{limitedLength:h,isOverLimit:m}=d(e.fields),{limitedLength:g,isOverLimit:x}=d(e?.calculatedFields),{limitedLength:u,isOverLimit:p}=d(e?.relationFields),f=o(m)+o(x)+o(p);return i+(t||a*(h+g+u))+s*f+c+4}}a()}catch(e){a(e)}})},20410:()=>{}};