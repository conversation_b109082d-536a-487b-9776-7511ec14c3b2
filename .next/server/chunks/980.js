"use strict";exports.id=980,exports.ids=[980],exports.modules={56249:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},68022:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{HC:()=>f,Y_:()=>n,xZ:()=>a});var a,n,i,o=r(99648),d=r(11774),l=r(24634),u=r(42759),c=r(47104),h=r(83372),p=r(81131),m=r.n(p),E=r(78978),y=e([o,h]);[o,h]=y.then?(await y)():y;let R=(0,d.j)("IbisAdaptor");R.level="debug";let I=(0,u.i)();!function(e){e.POSTGRES="POSTGRES",e.BIG_QUERY="BIG_QUERY",e.SNOWFLAKE="SNOWFLAKE",e.MYSQL="MYSQL",e.MSSQL="MSSQL",e.CLICK_HOUSE="CLICK_HOUSE",e.TRINO="TRINO"}(a||(a={}));let g={POSTGRES:"postgres",BIG_QUERY:"bigquery",SNOWFLAKE:"snowflake",MYSQL:"mysql",MSSQL:"mssql",CLICK_HOUSE:"clickhouse",TRINO:"trino"};(n||(n={})).COLUMN_IS_VALID="COLUMN_IS_VALID",function(e){e.QUERY="QUERY",e.DRY_RUN="DRY_RUN",e.DRY_PLAN="DRY_PLAN",e.METADATA="METADATA",e.VALIDATION="VALIDATION",e.ANALYSIS="ANALYSIS",e.MODEL_SUBSTITUTE="MODEL_SUBSTITUTE"}(i||(i={}));class f{constructor({ibisServerEndpoint:e}){this.ibisServerEndpoint=e}async getNativeSql(e){let{dataSource:t,mdl:r,sql:s}=e,a={sql:s,manifestStr:Buffer.from(JSON.stringify(r)).toString("base64")};try{return(await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("DRY_PLAN")}/connector/${g[t]}/dry-plan`,a)).data}catch(e){R.debug(`Dry plan error: ${e.response?.data||e.message}`),this.throwError(e,"Error during dry plan execution")}}async query(e,t){let{dataSource:r,mdl:s}=t,a=this.updateConnectionInfo(t.connectionInfo),n=(0,E.fU)(r,a),i=this.buildQueryString(t),d={sql:e,connectionInfo:n,manifestStr:Buffer.from(JSON.stringify(s)).toString("base64")};try{let e=await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("QUERY")}/connector/${g[r]}/query${i}`,d,{params:{limit:t.limit||h.sf}});return{...e.data,correlationId:e.headers["x-correlation-id"],processTime:e.headers["x-process-time"],cacheHit:"true"===e.headers["x-cache-hit"],cacheCreatedAt:e.headers["x-cache-create-at"]&&new Date(parseInt(e.headers["x-cache-create-at"])).toISOString(),cacheOverrodeAt:e.headers["x-cache-override-at"]&&new Date(parseInt(e.headers["x-cache-override-at"])).toISOString(),override:"true"===e.headers["x-cache-override"]}}catch(e){R.debug(`Query error: ${e.response?.data||e.message}`),this.throwError(e,"Error querying ibis server")}}async dryRun(e,t){let{dataSource:r,mdl:s}=t,a=this.updateConnectionInfo(t.connectionInfo),n=(0,E.fU)(r,a),i={sql:e,connectionInfo:n,manifestStr:Buffer.from(JSON.stringify(s)).toString("base64")};R.debug("Dry run sql from ibis with body:");try{let e=await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("DRY_RUN")}/connector/${g[r]}/query?dryRun=true`,i);return R.debug("Ibis server Dry run success"),{correlationId:e.headers["x-correlation-id"],processTime:e.headers["x-process-time"]}}catch(e){R.debug(`Dry run error: ${e.response?.data||e.message}`),this.throwError(e,"Error during dry run execution")}}async getTables(e,t){try{let r=async t=>{R.debug("Getting tables from ibis");let r=await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("METADATA")}/connector/${g[e]}/metadata/tables`,{connectionInfo:t});return this.transformDescriptionToProperties(r.data)};t=this.updateConnectionInfo(t);let s=(0,E.JW)(e,t);if(s)return(await Promise.all(s.map(r))).flat();let a=(0,E.fU)(e,t);return await r(a)}catch(e){R.debug(`Get tables error: ${e.response?.data||e.message}`),this.throwError(e,"Error getting table from ibis server")}}async getConstraints(e,t){t=this.updateConnectionInfo(t);let r=(0,E.fU)(e,t);try{return R.debug("Getting constraint from ibis"),(await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("METADATA")}/connector/${g[e]}/metadata/constraints`,{connectionInfo:r})).data}catch(e){R.debug(`Get constraints error: ${e.response?.data||e.message}`),this.throwError(e,"Error getting constraint from ibis server")}}async validate(e,t,r,s,a){r=this.updateConnectionInfo(r);let n={connectionInfo:(0,E.fU)(e,r),manifestStr:Buffer.from(JSON.stringify(s)).toString("base64"),parameters:a};try{return R.debug(`Run validation rule "${t}" with ibis`),await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("VALIDATION")}/connector/${g[e]}/validate/${m()(t)}`,n),{valid:!0,message:null}}catch(e){return R.debug(`Validation error: ${e.response?.data||e.message}`),{valid:!1,message:e.response?.data||e.message}}}async modelSubstitute(e,t){let{dataSource:r,mdl:s,catalog:a,schema:n}=t,i=t.connectionInfo;i=this.updateConnectionInfo(i);let d=(0,E.fU)(r,i),l={sql:e,connectionInfo:d,manifestStr:Buffer.from(JSON.stringify(s)).toString("base64")};try{return R.debug("Running model substitution with ibis"),(await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("MODEL_SUBSTITUTE")}/connector/${g[r]}/model-substitute`,l,{headers:{"X-User-CATALOG":a,"X-User-SCHEMA":n}})).data}catch(e){R.debug(`Model substitution error: ${e.response?.data||e.message}`),this.throwError(e,"Error running model substitution with ibis server",this.modelSubstituteErrorMessageBuilder)}}async getVersion(e,t){t=this.updateConnectionInfo(t);let r=(0,E.fU)(e,t);try{return R.debug("Getting version from ibis"),(await o.default.post(`${this.ibisServerEndpoint}/${this.getIbisApiVersion("METADATA")}/connector/${g[e]}/metadata/version`,{connectionInfo:r})).data}catch(e){R.debug(`Get version error: ${e.response?.data||e.message}`),this.throwError(e,"Error getting version from ibis server")}}updateConnectionInfo(e){return I.otherServiceUsingDocker&&Object.hasOwnProperty.call(e,"host")&&(e.host=(0,c.P5)(e.host),R.debug("Host replaced with docker host")),e}transformDescriptionToProperties(e){let t=e=>{let r=e?.properties||{};e.description&&(r.description=e.description);let s=e.nestedColumns?.map(e=>t(e));return{...e,properties:r,nestedColumns:s}};return e.map(e=>{try{let r=e?.properties||{};if(e.description&&(r.description=e.description),e.columns){let r=e.columns.map(e=>t(e));e.columns=r}return{...e,properties:r}}catch(e){console.log("e",e)}})}getIbisApiVersion(e){if(!I.experimentalEngineRustVersion)return"v2";let t=["QUERY","DRY_RUN","DRY_PLAN","VALIDATION","MODEL_SUBSTITUTE"].includes(e);return t&&R.debug("Using ibis v3 api"),t?"v3":"v2"}throwError(e,t,r){let s=e.response?.data?.message||e.response?.data||e.message||t;throw l.Ue(l.GL.IBIS_SERVER_ERROR,{customMessage:r?r(s):s,originalError:e,other:{correlationId:e.response?.headers["x-correlation-id"],processTime:e.response?.headers["x-process-time"]}})}modelSubstituteErrorMessageBuilder(e){let t={MODEL_NOT_FOUND:()=>e.includes("Model not found"),PARSING_EXCEPTION:()=>e.includes("sql.parser.ParsingException")};if(t.MODEL_NOT_FOUND()){let t=e.split(": ")[1];switch(t.split(".").length-1){case 0:return e+`. Try adding both catalog and schema before your table name. e.g. my_database.public.${t}`;case 1:return e+`. Try adding the catalog before the schema in your table name. e.g. my_database.${t}`;default:return e+". It may be missing from models, misnamed, or have a case mismatch."}}else if(t.PARSING_EXCEPTION())return e+". Please check your selected column and make sure its quoted for columns with non-alphanumeric characters.";return e}buildQueryString(e){if(!e.cacheEnabled)return"";let t=[];return t.push("cacheEnable=true"),e.refresh&&t.push("overrideCache=true"),`?${t.join("&")}`}}s()}catch(e){s(e)}})},39698:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{HC:()=>a.HC,YC:()=>i.Y,o7:()=>n.o});var a=r(68022),n=r(11604),i=r(24720),o=e([a,n,i]);[a,n,i]=o.then?(await o)():o,s()}catch(e){s(e)}})},11604:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{o:()=>c});var a=r(99648),n=r(75145),i=r(47104),o=r(24634),d=e([a]);a=(d.then?(await d)():d)[0];let l=(0,i.jl)("WrenAIAdaptor");l.level="debug";let u=e=>{let{data:t}=e.response||{};return t?.detail?`${e.message}, detail: ${t.detail}`:e.message};class c{constructor({wrenAIBaseEndpoint:e}){this.wrenAIBaseEndpoint=e}async delete(e){try{if(!e)throw Error("Project ID is required");let t=`${this.wrenAIBaseEndpoint}/v1/semantics`,r=await a.default.delete(t,{params:{project_id:e.toString()}});if(200===r.status)l.info(`Wren AI: Deleted semantics for project ${e}`);else throw Error(`Failed to delete semantics. ${r.data?.error}`)}catch(e){throw Error(`Wren AI: Failed to delete semantics: ${u(e)}`)}}async deploySqlPair(e,t){try{let r={sql_pairs:[{id:`${t.id}`,sql:t.sql,question:t.question}],project_id:e.toString()};return a.default.post(`${this.wrenAIBaseEndpoint}/v1/sql-pairs`,r).then(e=>({queryId:e.data.event_id}))}catch(e){throw l.debug(`Got error when deploying SQL pair: ${u(e)}`),e}}async getSqlPairResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/sql-pairs/${e}`),{status:r,error:s}=this.transformStatusAndError(t.data);return{status:r,error:s}}catch(e){throw l.debug(`Got error when getting SQL pair result: ${u(e)}`),e}}async deleteSqlPairs(e,t){try{await a.default.delete(`${this.wrenAIBaseEndpoint}/v1/sql-pairs`,{data:{sql_pair_ids:t.map(e=>e.toString()),project_id:e.toString()}});return}catch(e){throw l.debug(`Got error when deleting SQL pair: ${u(e)}`),e}}async ask(e){try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/asks`,{query:e.query,id:e.deployId,histories:this.transformHistoryInput(e.histories),configurations:e.configurations})).data.query_id}}catch(e){throw l.debug(`Got error when asking wren AI: ${u(e)}`),e}}async cancelAsk(e){try{await a.default.patch(`${this.wrenAIBaseEndpoint}/v1/asks/${e}`,{status:"stopped"})}catch(e){throw l.debug(`Got error when canceling ask: ${u(e)}`),e}}async getAskResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/asks/${e}/result`);return this.transformAskResult(t.data)}catch(e){throw l.debug(`Got error when getting ask result: ${u(e)}`),o.Ue(o.GL.INTERNAL_SERVER_ERROR,{originalError:e})}}async getAskStreamingResult(e){try{return(await a.default.get(`${this.wrenAIBaseEndpoint}/v1/asks/${e}/streaming-result`,{responseType:"stream"})).data}catch(e){throw l.debug(`Got error when getting ask streaming result: ${u(e)}`),o.Ue(o.GL.INTERNAL_SERVER_ERROR,{originalError:e})}}async generateAskDetail(e){try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/ask-details`,e)).data.query_id}}catch(e){throw l.debug(`Got error when generating ask detail: ${u(e)}`),e}}async getAskDetailResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/ask-details/${e}/result`);return this.transformAskDetailResult(t.data)}catch(e){throw l.debug(`Got error when getting ask detail result: ${u(e)}`),e}}async deploy(e){let{manifest:t,hash:r}=e;try{let e=(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/semantics-preparations`,{mdl:JSON.stringify(t),id:r})).data.id;if(l.debug(`Wren AI: Deploying wren AI, hash: ${r}, deployId: ${e}`),await this.waitDeployFinished(e))return l.debug(`Wren AI: Deploy wren AI success, hash: ${r}`),{status:n.T_.SUCCESS};return{status:n.T_.FAILED,error:`Wren AI: Deploy wren AI failed or timeout, hash: ${r}`}}catch(e){return l.debug(`Got error when deploying to wren AI, hash: ${r}. Error: ${e.message}`),{status:n.T_.FAILED,error:`Wren AI Error: deployment hash:${r}, ${e.message}`}}}async generateRecommendationQuestions(e){let t={mdl:JSON.stringify(e.manifest),previous_questions:e.previousQuestions,max_questions:e.maxQuestions,max_categories:e.maxCategories,configuration:e.configuration};l.info("Wren AI: Generating recommendation questions");try{let e=await a.default.post(`${this.wrenAIBaseEndpoint}/v1/question-recommendations`,t);return l.info(`Wren AI: Generating recommendation questions, queryId: ${e.data.id}`),{queryId:e.data.id}}catch(e){throw l.debug(`Got error when generating recommendation questions: ${u(e)}`),e}}async getRecommendationQuestionsResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/question-recommendations/${e}`);return this.transformRecommendationQuestionsResult(t.data)}catch(e){throw l.debug(`Got error when getting recommendation questions result: ${u(e)}`),e}}async createTextBasedAnswer(e){let t={query:e.query,sql:e.sql,sql_data:e.sqlData,thread_id:e.threadId,user_id:e.userId,configurations:e.configurations};try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/sql-answers`,t)).data.query_id}}catch(e){throw l.debug(`Got error when creating text-based answer: ${u(e)}`),e}}async getTextBasedAnswerResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/sql-answers/${e}`);return this.transformTextBasedAnswerResult(t.data)}catch(e){throw l.debug(`Got error when getting text-based answer result: ${u(e)}`),e}}async streamTextBasedAnswer(e){try{return(await a.default.get(`${this.wrenAIBaseEndpoint}/v1/sql-answers/${e}/streaming`,{responseType:"stream"})).data}catch(e){throw l.debug(`Got error when getting text-based answer streaming result: ${u(e)}`),e}}async generateChart(e){try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/charts`,e)).data.query_id}}catch(e){throw l.debug(`Got error when creating chart: ${u(e)}`),e}}async getChartResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/charts/${e}`);return this.transformChartResult(t.data)}catch(e){throw l.debug(`Got error when getting chart result: ${u(e)}`),e}}async cancelChart(e){try{await a.default.patch(`${this.wrenAIBaseEndpoint}/v1/charts/${e}`,{status:"stopped"})}catch(e){throw l.debug(`Got error when canceling chart: ${u(e)}`),e}}async adjustChart(e){try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/chart-adjustments`,this.transformChartAdjustmentInput(e))).data.query_id}}catch(e){throw l.debug(`Got error when adjusting chart: ${u(e)}`),e}}async getChartAdjustmentResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/chart-adjustments/${e}`);return this.transformChartResult(t.data)}catch(e){throw l.debug(`Got error when getting chart adjustment result: ${u(e)}`),e}}async cancelChartAdjustment(e){try{await a.default.patch(`${this.wrenAIBaseEndpoint}/v1/chart-adjustments/${e}`,{status:"stopped"})}catch(e){throw l.debug(`Got error when canceling chart adjustment: ${u(e)}`),e}}async generateQuestions(e){try{let t={sqls:e.sqls,project_id:e.projectId.toString(),configurations:e.configurations};return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/sql-questions`,t)).data.query_id}}catch(e){throw l.debug(`Got error when generating questions: ${u(e)}`),e}}async generateInstruction(e){let t={instructions:e.map(e=>({id:e.id.toString(),instruction:e.instruction,questions:e.questions,is_default:e.isDefault})),project_id:e[0]?.projectId.toString()};try{return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/instructions`,t)).data.event_id}}catch(e){throw l.debug(`Got error when generating instruction: ${u(e)}`),e}}async getQuestionsResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/sql-questions/${e}`),{status:r,error:s}=this.transformStatusAndError(t.data);return{status:r,error:s,questions:t.data.questions||[]}}catch(e){throw l.debug(`Got error when getting questions result: ${u(e)}`),e}}async getInstructionResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/instructions/${e}`);return this.transformStatusAndError(t.data)}catch(e){throw l.debug(`Got error when getting instruction result: ${u(e)}`),e}}async deleteInstructions(e,t){try{await a.default.delete(`${this.wrenAIBaseEndpoint}/v1/instructions`,{data:{instruction_ids:e.map(e=>e.toString()),project_id:t.toString()}})}catch(e){throw l.debug(`Got error when deleting instruction: ${u(e)}`),e}}async createAskFeedback(e){try{let t={question:e.question,tables:e.tables,sql_generation_reasoning:e.sqlGenerationReasoning,sql:e.sql,project_id:e.projectId.toString(),configurations:e.configurations};return{queryId:(await a.default.post(`${this.wrenAIBaseEndpoint}/v1/ask-feedbacks`,t)).data.query_id}}catch(e){throw l.debug(`Got error when creating ask feedback: ${u(e)}`),e}}async getAskFeedbackResult(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/ask-feedbacks/${e}`);return this.transformAskFeedbackResult(t.data)}catch(e){throw l.debug(`Got error when getting ask feedback result: ${u(e)}`),e}}async cancelAskFeedback(e){try{await a.default.patch(`${this.wrenAIBaseEndpoint}/v1/ask-feedbacks/${e}`,{status:"stopped"})}catch(e){throw l.debug(`Got error when canceling ask feedback: ${u(e)}`),e}}transformAskFeedbackResult(e){let{status:t,error:r}=this.transformStatusAndError(e);return{status:t,error:r,response:e.response?.map(e=>({sql:e.sql,type:e.type?.toUpperCase()}))||[],traceId:e.trace_id,invalidSql:e.invalid_sql}}transformChartAdjustmentInput(e){let{query:t,sql:r,adjustmentOption:s,chartSchema:a,configurations:n}=e;return{query:t,sql:r,adjustment_option:{chart_type:s.chartType.toLowerCase(),x_axis:s.xAxis,y_axis:s.yAxis,x_offset:s.xOffset,color:s.color,theta:s.theta},chart_schema:a,configurations:n}}transformChartResult(e){let{status:t,error:r}=this.transformStatusAndError(e);return{status:t,error:r,response:{reasoning:e.response?.reasoning,chartType:e.response?.chart_type,chartSchema:e.response?.chart_schema}}}transformTextBasedAnswerResult(e){let{status:t,error:r}=this.transformStatusAndError(e);return{status:t,numRowsUsedInLLM:e.num_rows_used_in_llm,error:r}}async waitDeployFinished(e){let t=!1;for(let r=1;r<=7;r++){try{let r=await this.getDeployStatus(e);if(l.debug(`Wren AI: Deploy status: ${r}`),r===n.QO.FINISHED){t=!0;break}if(r===n.QO.FAILED)break;if(r===n.QO.INDEXING);else{l.debug(`Wren AI: Unknown Wren AI deploy status: ${r}`);return}}catch(e){throw e}await new Promise(e=>setTimeout(e,1e3*r))}return t}async getDeployStatus(e){try{let t=await a.default.get(`${this.wrenAIBaseEndpoint}/v1/semantics-preparations/${e}/status`);if(t.data.error)throw Error(t.data.error);return t.data?.status.toUpperCase()}catch(t){throw l.debug(`Got error in API /v1/semantics-preparations/${e}/status: ${t.message}`),t}}transformAskResult(e){let{status:t,error:r}=this.transformStatusAndError(e),s=(e?.response||[]).map(e=>({type:e?.type?.toUpperCase(),sql:e.sql,viewId:e?.viewId?Number(e.viewId):null,sqlpairId:e?.sqlpairId?Number(e.sqlpairId):null}));return{type:e?.type,status:t,error:r,response:s,rephrasedQuestion:e?.rephrased_question,intentReasoning:e?.intent_reasoning,sqlGenerationReasoning:e?.sql_generation_reasoning,retrievedTables:e?.retrieved_tables,invalidSql:e?.invalid_sql,traceId:e?.trace_id}}transformRecommendationQuestionsResult(e){let{status:t,error:r}=this.transformStatusAndError(e);return{...e,status:t,error:r}}transformAskDetailResult(e){let{type:t}=e,{status:r,error:s}=this.transformStatusAndError(e),a=(e?.response?.steps||[]).map(e=>({summary:e.summary,sql:e.sql,cteName:e.cte_name}));return{type:t,status:r,error:s,response:{description:e?.response?.description,steps:a}}}transformStatusAndError(e){let t=e?.status?.toUpperCase();if(!t)throw Error(`Unknown ask status: ${e?.status}`);let r=e?.error?.code,s=r===o.GL.NO_RELEVANT_SQL||r===o.GL.AI_SERVICE_UNDEFINED_ERROR,a=r?o.Ue(r,s?{customMessage:e?.error?.message}:void 0):null,n=a?{code:a.extensions.code,message:a.message,shortMessage:a.extensions.shortMessage}:null;return{status:t,error:n}}transformHistoryInput(e){return e?e.map(e=>({sql:e.sql,question:e.question})):[]}}s()}catch(e){s(e)}})},24720:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{Y:()=>c});var a,n=r(99648),i=r(47104),o=r(24634),d=r(83372),l=e([n,d]);[n,d]=l.then?(await l)():l;let u=(0,i.jl)("WrenEngineAdaptor");u.level="debug",function(e){e.PASS="PASS",e.ERROR="ERROR",e.FAIL="FAIL",e.WARN="WARN",e.SKIP="SKIP"}(a||(a={}));class c{constructor({wrenEngineEndpoint:e}){this.sessionPropsUrlPath="/v1/data-source/duckdb/settings/session-sql",this.queryDuckdbUrlPath="/v1/data-source/duckdb/query",this.initSqlUrlPath="/v1/data-source/duckdb/settings/init-sql",this.previewUrlPath="/v1/mdl/preview",this.dryPlanUrlPath="/v1/mdl/dry-plan",this.dryRunUrlPath="/v1/mdl/dry-run",this.validateUrlPath="/v1/mdl/validate",this.wrenEngineBaseEndpoint=e}async validateColumnIsValid(e,t,r){let s=e.models.find(e=>e.name===t);if(!s)return{valid:!1,message:`Model ${t} not found in the manifest`};if(!s.columns.find(e=>e.name===r))return{valid:!1,message:`Column ${r} not found in model ${t} in the manifest`};try{let s=(await n.default.post(`${this.wrenEngineBaseEndpoint}${this.validateUrlPath}/column_is_valid`,{manifest:e,parameters:{modelName:t,columnName:r}})).data[0];if("PASS"===s.status)return{valid:!0};return{valid:!1,message:JSON.stringify(s)}}catch(e){return u.debug(`Got error when validating column: ${e.message}`),{valid:!1,message:e.message}}}async prepareDuckDB(e){let{initSql:t,sessionProps:r}=e;await this.initDatabase(t),await this.putSessionProps(r)}async listTables(){let e=await this.queryDuckdb("SELECT       table_catalog, table_schema, table_name, column_name, ordinal_position, is_nullable, data_type      FROM INFORMATION_SCHEMA.COLUMNS;");return this.formatToCompactTable(e)}async putSessionProps(e){let t=Object.entries(e).map(([e,t])=>`SET ${e} = '${t}';`).join("\n");try{let e=new URL(this.sessionPropsUrlPath,this.wrenEngineBaseEndpoint);await n.default.put(e.href,t,{headers:{"Content-Type":"text/plain; charset=utf-8"}})}catch(e){throw u.debug(`Got error when put session props: ${e.message}`),o.Ue(o.GL.SESSION_PROPS_ERROR,{customMessage:o.Qj[o.GL.SESSION_PROPS_ERROR],originalError:e})}}async queryDuckdb(e){try{let t=new URL(this.queryDuckdbUrlPath,this.wrenEngineBaseEndpoint);return(await n.default.post(t.href,e,{headers:{"Content-Type":"text/plain; charset=utf-8"}})).data}catch(e){throw u.debug(`Got error when querying duckdb: ${e.message}`),o.Ue(o.GL.WREN_ENGINE_ERROR,{customMessage:e.response?.data?.message||e.message,originalError:e})}}async patchConfig(e){try{let t=Object.entries(e).map(([e,t])=>({name:e,value:t})),r=new URL("/v1/config",this.wrenEngineBaseEndpoint);await n.default.patch(r.href,t,{headers:{"Content-Type":"application/json"}})}catch(e){throw u.debug(`Got error when patching config: ${e.message}`),o.Ue(o.GL.WREN_ENGINE_ERROR,{customMessage:e.response?.data?.message||e.message,originalError:e})}}async previewData(e,t,r=d.sf){try{let s=new URL(this.previewUrlPath,this.wrenEngineBaseEndpoint);return(await (0,n.default)({method:"get",url:s.href,headers:{"Content-Type":"application/json"},data:{sql:e,limit:r,manifest:t}})).data}catch(e){throw u.debug(`Got error when previewing data: ${e.message}`),o.Ue(o.GL.WREN_ENGINE_ERROR,{customMessage:e.response?.data?.message||e.message,originalError:e})}}async getNativeSQL(e,t){try{let r={modelingOnly:!!t?.modelingOnly,manifest:t?.manifest},s=new URL(this.dryPlanUrlPath,this.wrenEngineBaseEndpoint);return(await (0,n.default)({method:"get",url:s.href,headers:{"Content-Type":"application/json"},data:{sql:e,...r}})).data}catch(e){u.debug(`Got error when getting native SQL: ${e.message}`),o.Ue(o.GL.DRY_PLAN_ERROR,{customMessage:e.message,originalError:e})}}async dryRun(e,t){try{let{manifest:r}=t;u.debug(`Dry run wren engine with body: ${JSON.stringify(e,null,2)}`);let s=new URL(this.dryRunUrlPath,this.wrenEngineBaseEndpoint),a=await (0,n.default)({method:"get",url:s.href,data:{sql:e,manifest:r}});return u.debug("Wren Engine Dry run success"),a.data}catch(e){throw u.info("Got error when dry running"),o.Ue(o.GL.DRY_RUN_ERROR,{customMessage:e.response.data.message,originalError:e})}}async getDeployStatus(){try{return(await n.default.get(`${this.wrenEngineBaseEndpoint}/v1/mdl/status`)).data}catch(e){throw u.debug(`WrenEngine: Got error when getting deploy status: ${e.message}`),e}}async initDatabase(e){try{let t=new URL(this.initSqlUrlPath,this.wrenEngineBaseEndpoint);await n.default.put(t.href,e,{headers:{"Content-Type":"text/plain; charset=utf-8"}})}catch(e){throw u.debug(`Got error when init database: ${e}`),o.Ue(o.GL.INIT_SQL_ERROR,{customMessage:o.Qj[o.GL.INIT_SQL_ERROR],originalError:e})}}formatToCompactTable(e){return e.data.reduce((e,t)=>{let[r,s,a,n,i,o,d]=t,l=e.find(e=>e.name===a&&e.properties.schema===s);return l||(l={name:a,description:"",columns:[],properties:{schema:s,catalog:r,table:a},primaryKey:null},e.push(l)),l.columns.push({name:n,type:d,notNull:"yes"!==o.toLocaleLowerCase(),description:"",properties:{}}),e},[])}}s()}catch(e){s(e)}})},54169:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{v:()=>u});var a=r(47104),n=r(75145),i=r(12332),o=r(10827),d=e([o]);o=(d.then?(await d)():d)[0];let l=(0,a.jl)("AdjustmentTaskTracker");l.level="debug";class u{constructor({telemetry:e,wrenAIAdaptor:t,askingTaskRepository:r,threadResponseRepository:s,pollingInterval:a=1e3,memoryRetentionTime:n=3e5}){this.trackedTasks=new Map,this.trackedTasksById=new Map,this.runningJobs=new Set,this.telemetry=e,this.wrenAIAdaptor=t,this.askingTaskRepository=r,this.threadResponseRepository=s,this.pollingInterval=a,this.memoryRetentionTime=n,this.startPolling()}async createAdjustmentTask(e){try{let t=(await this.wrenAIAdaptor.createAskFeedback(e)).queryId,r=await this.askingTaskRepository.createOne({queryId:t,question:e.question,threadId:e.threadId,detail:{adjustment:!0,status:n.z8.UNDERSTANDING,response:[],error:null}}),s=await this.threadResponseRepository.createOne({question:e.question,threadId:e.threadId,askingTaskId:r.id,adjustment:{type:i.fz.REASONING,payload:{originalThreadResponseId:e.originalThreadResponseId,retrievedTables:e.tables,sqlGenerationReasoning:e.sqlGenerationReasoning}}});await this.askingTaskRepository.updateOne(r.id,{threadResponseId:s.id});let a={queryId:t,lastPolled:Date.now(),isFinalized:!1,originalThreadResponseId:e.originalThreadResponseId,threadResponseId:s.id,question:e.question,adjustmentPayload:{originalThreadResponseId:e.originalThreadResponseId,retrievedTables:e.tables,sqlGenerationReasoning:e.sqlGenerationReasoning}};return this.trackedTasks.set(t,a),this.trackedTasksById.set(s.id,a),l.info(`Created adjustment task with queryId: ${t}`),{queryId:t,createdThreadResponse:s}}catch(e){throw l.error(`Failed to create adjustment task: ${e}`),e}}async rerunAdjustmentTask(e){let t=await this.threadResponseRepository.findOneBy({id:e.threadResponseId});if(!t)throw Error(`Thread response ${e.threadResponseId} not found`);let r=t.adjustment;if(!r)throw Error(`Thread response ${e.threadResponseId} has no adjustment`);let s=await this.threadResponseRepository.findOneBy({id:r.payload?.originalThreadResponseId});if(!s)throw Error(`Original thread response ${r.payload?.originalThreadResponseId} not found`);let a=(await this.wrenAIAdaptor.createAskFeedback({...e,tables:r.payload?.retrievedTables,sqlGenerationReasoning:r.payload?.sqlGenerationReasoning,sql:s.sql,question:s.question})).queryId;await this.askingTaskRepository.updateOne(t.askingTaskId,{queryId:a,detail:{adjustment:!0,status:n.z8.UNDERSTANDING,response:[],error:null}});let i={queryId:a,lastPolled:Date.now(),isFinalized:!1,originalThreadResponseId:s.id,threadResponseId:t.id,question:s.question,rerun:!0,adjustmentPayload:{originalThreadResponseId:s.id,retrievedTables:r.payload?.retrievedTables,sqlGenerationReasoning:r.payload?.sqlGenerationReasoning}};return this.trackedTasks.set(a,i),this.trackedTasksById.set(t.id,i),l.info(`Rerun adjustment task with queryId: ${a}`),{queryId:a}}async getAdjustmentResult(e){let t=this.trackedTasks.get(e);return t&&t.result?{...t.result,queryId:e,taskId:t.taskId}:this.getAdjustmentResultFromDB({queryId:e})}async getAdjustmentResultById(e){let t=this.trackedTasksById.get(e);return t?this.getAdjustmentResult(t.queryId):this.getAdjustmentResultFromDB({taskId:e})}async cancelAdjustmentTask(e){await this.wrenAIAdaptor.cancelAskFeedback(e);let t=o.MW.HOME_ADJUST_THREAD_RESPONSE_CANCEL;this.telemetry.sendEvent(t,{queryId:e})}stopPolling(){this.pollingIntervalId&&clearInterval(this.pollingIntervalId)}startPolling(){this.pollingIntervalId=setInterval(()=>{this.pollTasks()},this.pollingInterval)}async pollTasks(){let e=Date.now(),t=[],r=Array.from(this.trackedTasks.entries()).map(([r,s])=>async()=>{try{if(this.runningJobs.has(r))return;if(s.isFinalized&&e-s.lastPolled>this.memoryRetentionTime){t.push(r);return}if(s.isFinalized)return;this.runningJobs.add(r),l.info(`Polling for updates for task ${r}`);let a=await this.wrenAIAdaptor.getAskFeedbackResult(r);if(s.lastPolled=e,!this.isResultChanged(s.result,a)){this.runningJobs.delete(r);return}if(this.isTaskFinalized(a.status)){s.isFinalized=!0,s.threadResponseId&&await this.updateThreadResponseWhenTaskFinalized(s.threadResponseId,a);let e=s.rerun?o.MW.HOME_ADJUST_THREAD_RESPONSE_RERUN:o.MW.HOME_ADJUST_THREAD_RESPONSE,t={taskId:s.taskId,queryId:s.queryId,status:a.status,error:a.error,adjustmentPayload:s.adjustmentPayload};a.status===n.z8.FINISHED?this.telemetry.sendEvent(e,t):this.telemetry.sendEvent(e,t,o.ic.AI,!1),l.info(`Task ${r} is finalized with status: ${a.status}`)}s.result=a,l.info(`Updating task ${r} in database`),await this.updateTaskInDatabase({queryId:r},a),this.runningJobs.delete(r)}catch(e){throw this.runningJobs.delete(r),l.error(e.stack),e}});Promise.allSettled(r.map(e=>e())).then(e=>{for(let r of(e.forEach((e,t)=>{"rejected"===e.status&&l.error(`Job ${t} failed: ${e.reason}`)}),t.length>0&&l.info(`Cleaning up tasks that have been in memory too long. Tasks: ${t.join(", ")}`),t))this.trackedTasks.delete(r)})}async updateThreadResponseWhenTaskFinalized(e,t){let r=t?.response?.[0];r&&await this.threadResponseRepository.updateOne(e,{sql:r?.sql})}async getAdjustmentResultFromDB({queryId:e,taskId:t}){let r=null;return(e?r=await this.askingTaskRepository.findByQueryId(e):t&&(r=await this.askingTaskRepository.findOneBy({id:t})),r)?{...r?.detail,queryId:e||r?.queryId,taskId:r?.id}:null}async updateTaskInDatabase(e,t){let{queryId:r,taskId:s}=e,a=null;if(r?a=await this.askingTaskRepository.findByQueryId(r):s&&(a=await this.askingTaskRepository.findOneBy({id:s})),!a)throw Error("Asking task not found");await this.askingTaskRepository.updateOne(a.id,{detail:{adjustment:!0,...t}})}isTaskFinalized(e){return[n.z8.FINISHED,n.z8.FAILED,n.z8.STOPPED].includes(e)}isResultChanged(e,t){return e?.status!==t.status}}s()}catch(e){s(e)}})},20372:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{F:()=>u,O:()=>c});var a=r(75145),n=r(11774),i=r(10827),o=e([i]);i=(o.then?(await o)():o)[0];let d=(0,n.j)("ChartBackgroundTracker");d.level="debug";let l=e=>e===a.XP.FINISHED||e===a.XP.FAILED||e===a.XP.STOPPED;class u{constructor({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:r}){this.tasks={},this.runningJobs=new Set,this.telemetry=e,this.wrenAIAdaptor=t,this.threadResponseRepository=r,this.intervalTime=1e3,this.start()}start(){d.info("Chart background tracker started"),setInterval(()=>{let e=Object.values(this.tasks).map(e=>async()=>{if(this.runningJobs.has(e.id))return;this.runningJobs.add(e.id);let t=e.chartDetail,r=await this.wrenAIAdaptor.getChartResult(t.queryId);if(t.status===r.status){d.debug(`Job ${e.id} chart status not changed, finished`),this.runningJobs.delete(e.id);return}let s={queryId:t.queryId,status:r?.status,error:r?.error,description:r?.response?.reasoning,chartType:r?.response?.chartType?.toUpperCase()||null,chartSchema:r?.response?.chartSchema};if(d.debug(`Job ${e.id} chart status changed, updating`),await this.threadResponseRepository.updateOne(e.id,{chartDetail:s}),l(r.status)){let t={question:e.question,error:r.error};r.status===a.XP.FINISHED?this.telemetry.sendEvent(i.MW.HOME_ANSWER_CHART,t):this.telemetry.sendEvent(i.MW.HOME_ANSWER_CHART,t,i.ic.AI,!1),d.debug(`Job ${e.id} chart is finalized, removing`),delete this.tasks[e.id]}this.runningJobs.delete(e.id)});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&d.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[e.id]=e}getTasks(){return this.tasks}}class c{constructor({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:r}){this.tasks={},this.runningJobs=new Set,this.telemetry=e,this.wrenAIAdaptor=t,this.threadResponseRepository=r,this.intervalTime=1e3,this.start()}start(){d.info("Chart adjustment background tracker started"),setInterval(()=>{let e=Object.values(this.tasks).map(e=>async()=>{if(this.runningJobs.has(e.id))return;this.runningJobs.add(e.id);let t=e.chartDetail,r=await this.wrenAIAdaptor.getChartAdjustmentResult(t.queryId);if(t.status===r.status){d.debug(`Job ${e.id} chart status not changed, finished`),this.runningJobs.delete(e.id);return}let s={queryId:t.queryId,status:r?.status,error:r?.error,description:r?.response?.reasoning,chartType:r?.response?.chartType?.toUpperCase()||null,chartSchema:r?.response?.chartSchema,adjustment:!0};if(d.debug(`Job ${e.id} chart status changed, updating`),await this.threadResponseRepository.updateOne(e.id,{chartDetail:s}),l(r.status)){let t={question:e.question,error:r.error};r.status===a.XP.FINISHED?this.telemetry.sendEvent(i.MW.HOME_ANSWER_ADJUST_CHART,t):this.telemetry.sendEvent(i.MW.HOME_ANSWER_ADJUST_CHART,t,i.ic.AI,!1),d.debug(`Job ${e.id} chart is finalized, removing`),delete this.tasks[e.id]}this.runningJobs.delete(e.id)});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&d.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[e.id]=e}getTasks(){return this.tasks}}s()}catch(e){s(e)}})},69737:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{$:()=>u});var a=r(47104),n=r(12332),i=r(82754),o=r(46555),d=e([o]);o=(d.then?(await d)():d)[0];let l=(0,a.jl)("DashboardCacheBackgroundTracker");l.level="debug";class u{constructor({dashboardRepository:e,dashboardItemRepository:t,dashboardItemRefreshJobRepository:r,projectService:s,deployService:a,queryService:n}){this.runningJobs=new Set,this.dashboardRepository=e,this.dashboardItemRepository=t,this.dashboardItemRefreshJobRepository=r,this.projectService=s,this.deployService=a,this.queryService=n,this.intervalTime=6e4,this.start()}start(){l.info("Dashboard cache background tracker started"),setInterval(()=>{this.checkAndRefreshCaches()},this.intervalTime)}async checkAndRefreshCaches(){try{for(let e of(await this.dashboardRepository.findAllBy({cacheEnabled:!0}))){if(!e.scheduleCron||!e.nextScheduledAt)continue;let t=new Date,r=new Date(e.nextScheduledAt);t>=r&&(l.info(`Start Refreshing cache for dashboard ${e.id}`),await this.refreshDashboardCache(e),l.info(`Finished Refreshing cache for dashboard ${e.id}`))}}catch(e){l.error(`Error checking dashboard caches: ${e.message}`)}}async refreshDashboardCache(e){if(this.runningJobs.has(e.id)){l.debug(`Dashboard ${e.id} refresh already in progress`);return}this.runningJobs.add(e.id);try{let t=await this.dashboardItemRepository.findAllBy({dashboardId:e.id}),r=await this.projectService.getCurrentProject(),s=(await this.deployService.getLastDeployment(r.id)).manifest,a=(0,o.v4)();await Promise.all(t.map(async t=>{try{let i=await this.dashboardItemRefreshJobRepository.createOne({hash:a,dashboardId:e.id,dashboardItemId:t.id,startedAt:new Date,finishedAt:null,status:n.xJ.IN_PROGRESS,errorMessage:null});try{await this.queryService.preview(t.detail.sql,{project:r,manifest:s,cacheEnabled:!0,refresh:!0}),await this.dashboardItemRefreshJobRepository.updateOne(i.id,{finishedAt:new Date,status:n.xJ.SUCCESS})}catch(e){await this.dashboardItemRefreshJobRepository.updateOne(i.id,{finishedAt:new Date,status:n.xJ.FAILED,errorMessage:e.message}),l.debug(`Error refreshing cache for item ${t.id}: ${e.message}`)}}catch(e){l.debug(`Error creating refresh job record for item ${t.id}: ${e.message}`)}}));let i=this.calculateNextRunTime(e.scheduleCron);await this.dashboardRepository.updateOne(e.id,{nextScheduledAt:i}),l.info(`Next scheduled time for dashboard ${e.id}: ${i}`),l.info(`Successfully refreshed cache for dashboard ${e.id}`)}catch(t){l.error(`Error refreshing dashboard ${e.id}: ${t.message}`)}finally{this.runningJobs.delete(e.id)}}calculateNextRunTime(e){try{return i.CronExpressionParser.parse(e,{currentDate:new Date}).next().toDate()}catch(e){return l.error(`Failed to parse cron expression: ${e.message}`),null}}}s()}catch(e){s(e)}})},89097:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{$Y:()=>o.$,$m:()=>d.$,Ef:()=>o.E,Fw:()=>i.F,O9:()=>i.O,vs:()=>a.v});var a=r(54169),n=r(19691),i=r(20372),o=r(74479),d=r(69737),l=e([a,n,i,o,d]);[a,n,i,o,d]=l.then?(await l)():l,s()}catch(e){s(e)}})},74479:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{$:()=>u,E:()=>c});var a=r(75145),n=r(10827),i=r(11774),o=e([n]);n=(o.then?(await o)():o)[0];let d="PRQBT:",l=e=>[a.MU.FINISHED,a.MU.FAILED].includes(e);class u{constructor({telemetry:e,wrenAIAdaptor:t,projectRepository:r}){this.tasks={},this.runningJobs=new Set,this.logger=(0,i.j)("PRQ Background Tracker"),this.logger.level="debug",this.telemetry=e,this.wrenAIAdaptor=t,this.projectRepository=r,this.intervalTime=1e3,this.start()}start(){this.logger.info("Recommend question background tracker started"),setInterval(()=>{let e=Object.values(this.tasks).map(e=>async()=>{if(this.runningJobs.has(this.taskKey(e)))return;this.runningJobs.add(this.taskKey(e));let t=await this.wrenAIAdaptor.getRecommendationQuestionsResult(e.queryId);if(e.questionsStatus===t.status&&t.response?.questions.length===(e.questions||[]).length){this.logger.debug(`${d}job ${this.taskKey(e)} status not changed, returning question count: ${t.response?.questions.length||0}`),this.runningJobs.delete(this.taskKey(e));return}if((t.status!==e.questionsStatus||t.response?.questions.length!==(e.questions||[]).length)&&(this.logger.debug(`${d}job ${this.taskKey(e)} have changes, returning question count: ${t.response?.questions.length||0}, updating`),await this.projectRepository.updateOne(e.id,{questionsStatus:t.status.toUpperCase(),questions:t.response?.questions,questionsError:t.error}),e.questionsStatus=t.status,e.questions=t.response?.questions),l(t.status)){let r={projectId:e.id,projectType:e.type,status:t.status,questions:e.questions,error:t.error};t.status===a.MU.FINISHED?this.telemetry.sendEvent(n.MW.HOME_GENERATE_PROJECT_RECOMMENDATION_QUESTIONS,r):this.telemetry.sendEvent(n.MW.HOME_GENERATE_PROJECT_RECOMMENDATION_QUESTIONS,r,n.ic.AI,!1),this.logger.debug(`${d}job ${this.taskKey(e)} is finalized, removing`),delete this.tasks[this.taskKey(e)]}this.runningJobs.delete(this.taskKey(e))});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&this.logger.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[this.taskKey(e)]=e}getTasks(){return this.tasks}async initialize(){for(let e of(await this.projectRepository.findAll()))this.taskKey(e)&&!l(e.questionsStatus)&&this.addTask(e)}taskKey(e){return e.id}isExist(e){return this.tasks[this.taskKey(e)]}}class c{constructor({telemetry:e,wrenAIAdaptor:t,threadRepository:r}){this.tasks={},this.runningJobs=new Set,this.logger=(0,i.j)("TRQ Background Tracker"),this.logger.level="debug",this.telemetry=e,this.wrenAIAdaptor=t,this.threadRepository=r,this.intervalTime=1e3,this.start()}start(){this.logger.info("Recommend question background tracker started"),setInterval(()=>{let e=Object.values(this.tasks).map(e=>async()=>{if(this.runningJobs.has(this.taskKey(e)))return;this.runningJobs.add(this.taskKey(e));let t=await this.wrenAIAdaptor.getRecommendationQuestionsResult(e.queryId);if(e.questionsStatus===t.status&&t.response?.questions.length===(e.questions||[]).length){this.logger.debug(`${d}job ${this.taskKey(e)} status not changed, returning question count: ${t.response?.questions.length||0}`),this.runningJobs.delete(this.taskKey(e));return}if((t.status!==e.questionsStatus||t.response?.questions.length!==(e.questions||[]).length)&&(this.logger.debug(`${d}job ${this.taskKey(e)} have changes, returning question count: ${t.response?.questions.length||0}, updating`),await this.threadRepository.updateOne(e.id,{questionsStatus:t.status.toUpperCase(),questions:t.response?.questions,questionsError:t.error}),e.questionsStatus=t.status,e.questions=t.response?.questions),l(t.status)){let r={thread_id:e.id,status:t.status,questions:e.questions,error:t.error};t.status===a.MU.FINISHED?this.telemetry.sendEvent(n.MW.HOME_GENERATE_THREAD_RECOMMENDATION_QUESTIONS,r):this.telemetry.sendEvent(n.MW.HOME_GENERATE_THREAD_RECOMMENDATION_QUESTIONS,r,n.ic.AI,!1),this.logger.debug(`${d}job ${this.taskKey(e)} is finalized, removing`),delete this.tasks[this.taskKey(e)]}this.runningJobs.delete(this.taskKey(e))});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&this.logger.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[this.taskKey(e)]=e}getTasks(){return this.tasks}async initialize(){for(let e of(await this.threadRepository.findAll()))this.tasks[this.taskKey(e)]||!e.queryId||l(e.questionsStatus)||this.addTask(e)}taskKey(e){return e.id}isExist(e){return this.tasks[this.taskKey(e)]}}s()}catch(e){s(e)}})},19691:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{i:()=>l});var a=r(75145),n=r(83372),i=r(47104),o=e([n]);n=(o.then?(await o)():o)[0];let d=(0,i.jl)("TextBasedAnswerBackgroundTracker");d.level="debug";class l{constructor({wrenAIAdaptor:e,threadResponseRepository:t,projectService:r,deployService:s,queryService:a}){this.tasks={},this.runningJobs=new Set,this.wrenAIAdaptor=e,this.threadResponseRepository=t,this.projectService=r,this.deployService=s,this.queryService=a,this.intervalTime=1e3,this.start()}start(){setInterval(async()=>{let e=Object.values(this.tasks).map(e=>async()=>{let t,r;if(this.runningJobs.has(e.id)||!e.answerDetail)return;this.runningJobs.add(e.id),await this.threadResponseRepository.updateOne(e.id,{answerDetail:{...e.answerDetail,status:n.Vr.FETCHING_DATA}});let s=await this.projectService.getCurrentProject(),i=(await this.deployService.getLastDeployment(s.id)).manifest;try{t=await this.queryService.preview(e.sql,{project:s,manifest:i,modelingOnly:!1,limit:500})}catch(t){throw d.error(`Error when query sql data: ${t}`),await this.threadResponseRepository.updateOne(e.id,{answerDetail:{...e.answerDetail,status:n.Vr.FAILED,error:t?.extensions||t}}),t}let o=await this.wrenAIAdaptor.createTextBasedAnswer({query:e.question,sql:e.sql,sqlData:t,threadId:e.threadId.toString(),configurations:{language:a.aS[s.language]||a.aS.EN}});await this.threadResponseRepository.updateOne(e.id,{answerDetail:{...e.answerDetail,status:n.Vr.PREPROCESSING}});do(r=await this.wrenAIAdaptor.getTextBasedAnswerResult(o.queryId)).status===a.Tw.PREPROCESSING&&await new Promise(e=>setTimeout(e,500));while(r.status===a.Tw.PREPROCESSING);let l={queryId:o.queryId,status:r.status===a.Tw.SUCCEEDED?n.Vr.STREAMING:n.Vr.FAILED,numRowsUsedInLLM:r.numRowsUsedInLLM,error:r.error};await this.threadResponseRepository.updateOne(e.id,{answerDetail:l}),delete this.tasks[e.id],this.runningJobs.delete(e.id)});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&d.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[e.id]=e}getTasks(){return this.tasks}}s()}catch(e){s(e)}})},42759:(e,t,r)=>{r.d(t,{i:()=>d});var s=r(20808),a=r.n(s);let n="192.168.101.147",i={otherServiceUsingDocker:!1,dbType:"sqlite",pgUrl:"***************************************/admin_ui",debug:!1,sqliteFile:"./db.sqlite3",persistCredentialDir:`${process.cwd()}/.tmp`,wrenEngineEndpoint:"http://"+n+":8080",wrenAIEndpoint:"http://"+n+":4555",experimentalEngineRustVersion:!0,ibisServerEndpoint:"http://"+n+":8000",encryptionPassword:"sementic",encryptionSalt:"layer"},o={otherServiceUsingDocker:"true"===process.env.OTHER_SERVICE_USING_DOCKER,dbType:process.env.DB_TYPE,pgUrl:process.env.PG_URL,debug:"true"===process.env.DEBUG,sqliteFile:process.env.SQLITE_FILE,persistCredentialDir:(()=>{if(process.env.PERSIST_CREDENTIAL_DIR&&process.env.PERSIST_CREDENTIAL_DIR.length>0)return process.env.PERSIST_CREDENTIAL_DIR})(),wrenEngineEndpoint:process.env.WREN_ENGINE_ENDPOINT,wrenAIEndpoint:process.env.WREN_AI_ENDPOINT,generationModel:process.env.GENERATION_MODEL,experimentalEngineRustVersion:"true"===process.env.EXPERIMENTAL_ENGINE_RUST_VERSION,ibisServerEndpoint:process.env.IBIS_SERVER_ENDPOINT,encryptionPassword:process.env.ENCRYPTION_PASSWORD,encryptionSalt:process.env.ENCRYPTION_SALT,telemetryEnabled:process.env.TELEMETRY_ENABLED&&"true"===process.env.TELEMETRY_ENABLED.toLocaleLowerCase(),posthogApiKey:process.env.POSTHOG_API_KEY,posthogHost:process.env.POSTHOG_HOST,userUUID:process.env.USER_UUID,wrenUIVersion:process.env.WREN_UI_VERSION,wrenEngineVersion:process.env.WREN_ENGINE_VERSION,wrenAIVersion:process.env.WREN_AI_SERVICE_VERSION,wrenProductVersion:process.env.WREN_PRODUCT_VERSION,projectRecommendationQuestionMaxCategories:process.env.PROJECT_RECOMMENDATION_QUESTION_MAX_CATEGORIES?parseInt(process.env.PROJECT_RECOMMENDATION_QUESTION_MAX_CATEGORIES):3,projectRecommendationQuestionsMaxQuestions:process.env.PROJECT_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS?parseInt(process.env.PROJECT_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS):3,threadRecommendationQuestionMaxCategories:process.env.THREAD_RECOMMENDATION_QUESTION_MAX_CATEGORIES?parseInt(process.env.THREAD_RECOMMENDATION_QUESTION_MAX_CATEGORIES):3,threadRecommendationQuestionsMaxQuestions:process.env.THREAD_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS?parseInt(process.env.THREAD_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS):1};function d(){return{...i,...a()(o)}}},78978:(e,t,r)=>{r.d(t,{JW:()=>u,fU:()=>l,uZ:()=>d});var s=r(3547),a=r(42759),n=r(47104);let i=(0,a.i)(),o=new n.i6(i);function d(e,t){return c[e].sensitiveProps.reduce((e,r)=>{let s=t[r];if(s){let t=o.encrypt(JSON.parse(JSON.stringify({[r]:s})));return{...e,[r]:t}}return e},t)}function l(e,t){return c[e].toIbisConnectionInfo(t)}function u(e,t){return c[e].toMultipleIbisConnectionInfos?c[e].toMultipleIbisConnectionInfos(t):null}let c={[s.ri.BIG_QUERY]:{sensitiveProps:["credentials"],toIbisConnectionInfo(e){let{projectId:t,datasetId:r,credentials:a}=h(s.ri.BIG_QUERY,e);return{project_id:t,dataset_id:r,credentials:Buffer.from(JSON.stringify(a)).toString("base64")}}},[s.ri.POSTGRES]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{host:t,port:r,database:a,user:n,password:i,ssl:o}=h(s.ri.POSTGRES,e),d=encodeURIComponent(i),l=`postgresql://${n}:${d}@${t}:${r}/${a}?`;return o&&(l+="sslmode=require"),{connectionUrl:l}}},[s.ri.MYSQL]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{host:t,port:r,database:a,user:n,password:i}=h(s.ri.MYSQL,e);return{host:t,port:r,database:a,user:n,password:i}}},[s.ri.MSSQL]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{host:t,port:r,database:a,user:n,password:i,trustServerCertificate:o}=h(s.ri.MSSQL,e);return{host:t,port:r,database:a,user:n,password:i,...o&&{kwargs:{trustServerCertificate:"YES"}}}}},[s.ri.CLICK_HOUSE]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{host:t,port:r,database:a,user:n,password:i,ssl:o}=h(s.ri.CLICK_HOUSE,e),d=encodeURIComponent(i),l=`clickhouse://${n}:${d}@${t}:${r}/${a}?`;return o&&(l+="secure=1"),{connectionUrl:l}}},[s.ri.TRINO]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{host:t,password:r,port:a,schemas:n,username:i,ssl:o}=h(s.ri.TRINO,e),[d,l]=n.split(",")?.[0]?.split(".")??[];if(!d||!l)throw Error("Invalid schema format, expected catalog.schema");return{host:o?`https://${t}`:`http://${t}`,port:a,catalog:d,schema:l,user:i,password:r}},toMultipleIbisConnectionInfos(e){let{host:t,port:r,schemas:a,username:n,password:i,ssl:o}=h(s.ri.TRINO,e),d=e=>{let t=e.trim(),[r,s]=t.split(".");if(!r||!s)throw Error(`Invalid schema format: "${t}". Expected format: catalog.schema`);return{catalog:r,schema:s}},l=a.split(",").filter(Boolean);if(0===l.length)throw Error("No valid schemas provided. Expected format: catalog.schema[, catalog.schema, ...]");return l.map(e=>{let{catalog:s,schema:a}=d(e);return{host:o?`https://${t}`:`http://${t}`,port:r,catalog:s,schema:a,user:n,password:i}})}},[s.ri.SNOWFLAKE]:{sensitiveProps:["password"],toIbisConnectionInfo(e){let{user:t,password:r,account:a,database:n,schema:i}=h(s.ri.SNOWFLAKE,e);return{user:t,password:r,account:a,database:n,schema:i}}},[s.ri.DUCKDB]:{sensitiveProps:[],toIbisConnectionInfo(e){throw Error("Not implemented")}}};function h(e,t){return c[e].sensitiveProps.reduce((e,r)=>{let s=t[r];if(s){let t=JSON.parse(o.decrypt(s))[r];return{...e,[r]:t}}return e},t)}},45716:(e,t,r)=>{r.d(t,{S:()=>E});var s,a=r(89699),n=r.n(a),i=r(86069),o=r.n(i),d=r(20808),l=r.n(d);!function(e){e.BIGQUERY="BIGQUERY",e.CANNER="CANNER",e.CLICKHOUSE="CLICKHOUSE",e.MSSQL="MSSQL",e.MYSQL="MYSQL",e.POSTGRES="POSTGRES",e.SNOWFLAKE="SNOWFLAKE",e.TRINO="TRINO",e.DUCKDB="DUCKDB",e.DATAFUSION="DATAFUSION"}(s||(s={}));var u=r(47104),c=r(42759),h=r(3547);let p=(0,u.jl)("MDLBuilder");p.level="debug";let m=(0,c.i)();class E{constructor(e){let{project:t,models:r,columns:s,nestedColumns:a,relations:n,views:i,relatedModels:o,relatedColumns:d,relatedRelations:l}=e;this.project=t,this.models=r.sort((e,t)=>e.id-t.id),this.columns=s.sort((e,t)=>e.id-t.id),this.nestedColumns=a,this.relations=n.sort((e,t)=>e.id-t.id),this.views=i||[],this.relatedModels=o,this.relatedColumns=d,this.relatedRelations=l,this.manifest={}}build(){return this.addProject(),this.addModel(),this.addNormalField(),this.addRelation(),this.addCalculatedField(),this.addView(),this.postProcessManifest(),this.getManifest()}getManifest(){return this.manifest}addModel(){n()(this.manifest.models)&&(this.manifest.models=this.models.map(e=>{let t=e.properties?JSON.parse(e.properties):{};e.displayName&&(t.displayName=e.displayName);let r=this.buildTableReference(e);return{name:e.referenceName,columns:[],tableReference:r,refSql:this.useRustWrenEngine()?null:r?null:e.refSql,cached:!!e.cached,refreshTime:e.refreshTime,properties:{displayName:e.displayName,description:t.description},primaryKey:""}}))}addView(){n()(this.manifest.views)&&(this.manifest.views=this.views.map(e=>{let t=JSON.parse(e.properties)||{},r=l()(t,(e,t)=>!o()(e)&&["displayName","description","question","summary"].includes(t));return{name:e.name,statement:e.statement,properties:{...r,viewId:e.id.toString()}}}))}addNormalField(){if(n()(this.manifest.models)){p.debug("No model in manifest, should build model first");return}this.columns.filter(({isCalculated:e})=>!e).forEach(e=>{let t=this.models.find(t=>t.id===e.modelId)?.referenceName;if(!t){p.debug(`Build MDL Column Error: can not find model, modelId ${e.modelId}, columnId: ${e.id}`);return}let r=this.manifest.models.find(e=>e.name===t);e.isPk&&(r.primaryKey=e.referenceName),r.columns||(r.columns=[]);let s=e.properties?JSON.parse(e.properties):{};e.displayName&&(s.displayName=e.displayName),e.type.includes("STRUCT")&&this.nestedColumns.filter(t=>t.columnId===e.id).forEach(e=>{e.displayName&&(s[`nestedDisplayName.${e.sourceColumnName}`]=e.displayName),e.properties?.description&&(s[`nestedDescription.${e.sourceColumnName}`]=e.properties.description)},{});let a=this.getColumnExpression(e,r);r.columns.push({name:e.referenceName,type:e.type,isCalculated:!!e.isCalculated,notNull:!!e.notNull,expression:a,properties:s})})}addCalculatedField(){if(n()(this.manifest.models)){p.debug("No model in manifest, should build model first");return}this.columns.filter(({isCalculated:e})=>e).forEach(e=>{let t=this.relatedModels.find(t=>t.id===e.modelId),r=this.manifest.models.find(e=>e.name===t.referenceName);if(!r){p.debug(`Build MDL Column Error: can not find model, modelId "${e.modelId}", columnId: "${e.id}"`);return}let s=this.getColumnExpression(e,r),a={name:e.referenceName,type:e.type,isCalculated:!0,expression:s,notNull:!!e.notNull,properties:JSON.parse(e.properties)};r.columns.push(a)})}insertCalculatedField(e,t){let r=this.manifest.models.find(t=>t.name===e);if(!r){p.debug(`Can not find model "${e}" to add calculated field`);return}if(r.columns.find(e=>e.name===t.referenceName))return;let s=this.getColumnExpression(t,r),a={name:t.referenceName,type:t.type,isCalculated:!0,expression:s,notNull:!!t.notNull,properties:JSON.parse(t.properties)};r.columns.push(a)}addRelation(){this.manifest.relationships=this.relations.map(e=>{let{name:t,joinType:r,fromModelName:s,fromColumnName:a,toModelName:n,toColumnName:i}=e,o=this.getRelationCondition(e);return this.addRelationColumn(s,{modelReferenceName:n,columnReferenceName:i,relation:t}),this.addRelationColumn(n,{modelReferenceName:s,columnReferenceName:a,relation:t}),{name:t,models:[s,n],joinType:r,condition:o,properties:e.properties?JSON.parse(e.properties):{}}})}addProject(){this.manifest.schema=this.project.schema,this.manifest.catalog=this.project.catalog;let e=this.buildDataSource();e&&(this.manifest.dataSource=e)}addRelationColumn(e,t){let r=this.manifest.models.find(t=>t.name===e);if(!r){p.debug(`Can not find model "${e}" to add relation column`);return}r.columns||(r.columns=[]);let s={name:r.columns.find(e=>e.name===t.modelReferenceName)?`${t.modelReferenceName}_${t.columnReferenceName}`:t.modelReferenceName,type:t.modelReferenceName,properties:null,relationship:t.relation,isCalculated:!1,notNull:!1};r.columns.push(s)}getColumnExpression(e,t){if(!e.isCalculated)return e.sourceColumnName!==e.referenceName?`"${e.sourceColumnName}"`:"";let r=JSON.parse(e.lineage),s=Object.entries(r).reduce((e,[s,a])=>{if(parseInt(s)==r.length-1){let t=this.relatedColumns.find(e=>e.id===a)?.referenceName;return e.push(`"${t}"`),e}let n=this.relatedRelations.find(e=>e.id===a),i=t.columns.find(e=>e.relationship===n.name).name,o=t.name===n.fromModelName?n.toModelName:n.fromModelName;return t=this.manifest.models.find(e=>e.name===o),e.push(i),e},[]);return`${e.aggregation}(${s.join(".")})`}getRelationCondition(e){let{fromColumnName:t,toColumnName:r,fromModelName:s,toModelName:a}=e;return`"${s}".${t} = "${a}".${r}`}buildTableReference(e){let t=e.properties&&"string"==typeof e.properties?JSON.parse(e.properties):{};return t.table?{catalog:t.catalog||null,schema:t.schema||null,table:t.table}:null}postProcessManifest(){this.useRustWrenEngine()&&(this.manifest.models=this.manifest.models?.map(e=>(e.columns.map(e=>(e.properties=l()(e.properties,e=>null!==e),e)),l()(e,e=>null!==e))),this.manifest.views=this.manifest.views?.map(e=>l()(e,e=>null!==e)),this.manifest.relationships=this.manifest.relationships?.map(e=>l()(e,e=>null!==e)),this.manifest.enumDefinitions=this.manifest.enumDefinitions?.map(e=>l()(e,e=>null!==e)),this.manifest.models?.forEach(e=>{e.columns?.forEach(e=>{""===e.expression&&delete e.expression})}))}useRustWrenEngine(){return!!m.experimentalEngineRustVersion}buildDataSource(){let e=this.project.type;if(e)switch(e){case h.ri.BIG_QUERY:return s.BIGQUERY;case h.ri.DUCKDB:return s.DUCKDB;case h.ri.POSTGRES:return s.POSTGRES;case h.ri.MYSQL:return s.MYSQL;case h.ri.MSSQL:return s.MSSQL;case h.ri.CLICK_HOUSE:return s.CLICKHOUSE;case h.ri.TRINO:return s.TRINO;case h.ri.SNOWFLAKE:return s.SNOWFLAKE;default:throw Error(`Unsupported data source type: ${e} found when building manifest`)}}}},75145:(e,t,r)=>{var s,a,n,i,o,d,l,u,c,h,p,m,E,y;r.d(t,{Fs:()=>p,HA:()=>m,MU:()=>l,QO:()=>a,T_:()=>s,Tw:()=>u,XP:()=>c,aS:()=>n,dd:()=>o,i3:()=>i,oX:()=>h,qj:()=>E,z8:()=>y}),function(e){e.SUCCESS="SUCCESS",e.FAILED="FAILED"}(s||(s={})),function(e){e.INDEXING="INDEXING",e.FINISHED="FINISHED",e.FAILED="FAILED"}(a||(a={})),function(e){e.EN="English",e.ES="Spanish",e.FR="French",e.ZH_TW="Traditional Chinese",e.ZH_CN="Simplified Chinese",e.DE="German",e.PT="Portuguese",e.RU="Russian",e.JA="Japanese",e.KO="Korean"}(n||(n={})),function(e){e.UNDERSTANDING="UNDERSTANDING",e.SEARCHING="SEARCHING",e.PLANNING="PLANNING",e.GENERATING="GENERATING",e.CORRECTING="CORRECTING",e.FINISHED="FINISHED",e.FAILED="FAILED",e.STOPPED="STOPPED"}(i||(i={})),function(e){e.GENERAL="GENERAL",e.TEXT_TO_SQL="TEXT_TO_SQL",e.MISLEADING_QUERY="MISLEADING_QUERY"}(o||(o={})),function(e){e.VIEW="VIEW",e.LLM="LLM",e.SQL_PAIR="SQL_PAIR"}(d||(d={})),function(e){e.GENERATING="GENERATING",e.FINISHED="FINISHED",e.FAILED="FAILED"}(l||(l={})),function(e){e.PREPROCESSING="PREPROCESSING",e.SUCCEEDED="SUCCEEDED",e.FAILED="FAILED"}(u||(u={})),function(e){e.FETCHING="FETCHING",e.GENERATING="GENERATING",e.FINISHED="FINISHED",e.FAILED="FAILED",e.STOPPED="STOPPED"}(c||(c={})),function(e){e.BAR="bar",e.GROUPED_BAR="grouped_bar",e.STACKED_BAR="stacked_bar",e.LINE="line",e.MULTI_LINE="multi_line",e.PIE="pie",e.AREA="area"}(h||(h={})),function(e){e.INDEXING="INDEXING",e.FINISHED="FINISHED",e.FAILED="FAILED"}(p||(p={})),function(e){e.GENERATING="GENERATING",e.SUCCEEDED="SUCCEEDED",e.FAILED="FAILED"}(m||(m={})),function(e){e.INDEXING="INDEXING",e.FINISHED="FINISHED",e.FAILED="FAILED"}(E||(E={})),function(e){e.UNDERSTANDING="UNDERSTANDING",e.GENERATING="GENERATING",e.CORRECTING="CORRECTING",e.FINISHED="FINISHED",e.FAILED="FAILED",e.STOPPED="STOPPED"}(y||(y={}))},3379:(e,t,r)=>{var s,a;r.d(t,{M_:()=>s,z1:()=>n}),function(e){e.WEEKLY="WEEKLY",e.DAILY="DAILY",e.CUSTOM="CUSTOM",e.NEVER="NEVER"}(s||(s={})),function(e){e.SUN="SUN",e.MON="MON",e.TUE="TUE",e.WED="WED",e.THU="THU",e.FRI="FRI",e.SAT="SAT"}(a||(a={}));let n=["SUN","MON","TUE","WED","THU","FRI","SAT"]},39458:(e,t,r)=>{var s;r.d(t,{i_:()=>s}),function(e){e.ABS="ABS",e.AVG="AVG",e.COUNT="COUNT",e.MAX="MAX",e.MIN="MIN",e.SUM="SUM",e.CBRT="CBRT",e.CEIL="CEIL",e.EXP="EXP",e.FLOOR="FLOOR",e.LN="LN",e.LOG10="LOG10",e.ROUND="ROUND",e.SIGN="SIGN",e.LENGTH="LENGTH",e.REVERSE="REVERSE"}(s||(s={})),r(75145),r(3379)},45305:(e,t,r)=>{r.d(t,{I:()=>s,l:()=>p});var s,a=r(86897),n=r.n(a),i=r(15452),o=r.n(i),d=r(87413),l=r.n(d),u=r(9941),c=r.n(u),h=r(58658);!function(e){e.GENERATE_SQL="GENERATE_SQL",e.RUN_SQL="RUN_SQL",e.GENERATE_VEGA_CHART="GENERATE_VEGA_CHART"}(s||(s={}));class p extends h.R{constructor(e){super({knexPg:e,tableName:"api_history"}),this.jsonbColumns=["headers","requestPayload","responsePayload"],this.transformFromDBData=e=>{if(!o()(e))throw Error("Unexpected dbdata");let t=l()(e,(e,t)=>n()(t));return c()(t,(e,t)=>{if(this.jsonbColumns.includes(t)&&"string"==typeof e){if(!e)return e;try{return JSON.parse(e)}catch(e){console.error(`Failed to parse JSON for ${t}:`,e)}}return e})}}async count(e,t){let r=this.knex(this.tableName).count("id as count");return e&&(r=r.where(this.transformToDBData(e))),t&&(t.startDate&&(r=r.where("created_at",">=",t.startDate)),t.endDate&&(r=r.where("created_at","<=",t.endDate))),parseInt((await r)[0].count,10)}async findAllWithPagination(e,t,r){let s=this.knex(this.tableName).select("*");return e&&(s=s.where(this.transformToDBData(e))),t&&(t.startDate&&(s=s.where("created_at",">=",t.startDate)),t.endDate&&(s=s.where("created_at","<=",t.endDate))),r&&(r.orderBy?Object.entries(r.orderBy).forEach(([e,t])=>{s=s.orderBy(this.camelToSnakeCase(e),t)}):s=s.orderBy("created_at","desc"),s=s.offset(r.offset).limit(r.limit)),(await s).map(this.transformFromDBData)}camelToSnakeCase(e){return e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}}},58658:(e,t,r)=>{r.d(t,{R:()=>c});var s=r(86897),a=r.n(s),n=r(15452),i=r.n(n),o=r(87413),d=r.n(o),l=r(81131),u=r.n(l);class c{constructor({knexPg:e,tableName:t}){this.deleteAllBy=async(e,t)=>{let r=(t?.tx?t.tx:this.knex)(this.tableName).where(this.transformToDBData(e)).delete();return await r},this.transformToDBData=e=>{if(!i()(e))throw Error("Unexpected dbdata");return d()(e,(e,t)=>u()(t))},this.transformFromDBData=e=>{if(!i()(e))throw Error("Unexpected dbdata");return d()(e,(e,t)=>a()(t))},this.knex=e,this.tableName=t}async transaction(){return await this.knex.transaction()}async commit(e){await e.commit()}async rollback(e){await e.rollback()}async findOneBy(e,t){let r=(t?.tx?t.tx:this.knex)(this.tableName).where(this.transformToDBData(e));t?.limit&&r.limit(t.limit);let s=await r;return s&&s.length>0?this.transformFromDBData(s[0]):null}async findAllBy(e,t){let r=(t?.tx?t.tx:this.knex)(this.tableName).where(this.transformToDBData(e));return t?.order&&r.orderBy(t.order),(await r).map(this.transformFromDBData)}async findAll(e){let t=(e?.tx?e.tx:this.knex)(this.tableName);return e?.order&&t.orderBy(e.order),e?.limit&&t.limit(e.limit),(await t).map(this.transformFromDBData)}async createOne(e,t){let r=t?.tx?t.tx:this.knex,[s]=await r(this.tableName).insert(this.transformToDBData(e)).returning("*");return this.transformFromDBData(s)}async createMany(e,t){let r=t?.tx?t.tx:this.knex,s=Math.ceil(e.length/100),a=[];for(let t=0;t<s;t++){let s=100*t,n=Math.min((t+1)*100,e.length),i=e.slice(s,n),o=await r(this.tableName).insert(i.map(this.transformToDBData)).returning("*");a.push(...o)}return a.map(e=>this.transformFromDBData(e))}async updateOne(e,t,r){let s=r?.tx?r.tx:this.knex,[a]=await s(this.tableName).where({id:e}).update(this.transformToDBData(t)).returning("*");return this.transformFromDBData(a)}async deleteOne(e,t){let r=(t?.tx?t.tx:this.knex).from(this.tableName).where({id:e}).delete();return await r}async deleteMany(e,t){let r=(t?.tx?t.tx:this.knex).from(this.tableName).whereIn("id",e).delete();return await r}}},29056:(e,t,r)=>{r.d(t,{Q:()=>s,h:()=>p});var s,a=r(58658),n=r(86897),i=r.n(n),o=r(15452),d=r.n(o),l=r(87413),u=r.n(l),c=r(9941),h=r.n(c);!function(e){e.IN_PROGRESS="IN_PROGRESS",e.SUCCESS="SUCCESS",e.FAILED="FAILED"}(s||(s={}));class p extends a.R{constructor(e){super({knexPg:e,tableName:"deploy_log"}),this.transformFromDBData=e=>{if(!d()(e))throw Error("Unexpected dbdata");let t=u()(e,(e,t)=>i()(t));return h()(t,(e,t)=>["manifest"].includes(t)&&"string"==typeof e?JSON.parse(e):e)}}async findLastProjectDeployLog(e){let t=await this.knex.select("*").from(this.tableName).where(this.transformToDBData({projectId:e,status:"SUCCESS"})).orderBy("created_at","desc").first();return t&&this.transformFromDBData(t)||null}async findInProgressProjectDeployLog(e){let t=await this.knex.select("*").from(this.tableName).where(this.transformToDBData({projectId:e,status:"IN_PROGRESS"})).orderBy("created_at","desc").first();return t&&this.transformFromDBData(t)||null}}},12332:(e,t,r)=>{r.d(t,{lg:()=>P.l,Bq:()=>L,xJ:()=>a,Wq:()=>M,Fk:()=>k,Tp:()=>C,hz:()=>A.h,W:()=>q,vq:()=>y,H0:()=>_,N3:()=>N,Sl:()=>R,ZC:()=>w,wp:()=>S,V$:()=>b,R:()=>v,fs:()=>D,fz:()=>O.f,Fu:()=>O.F,vE:()=>T});var s,a,n=r(58658),i=r(86897),o=r.n(i),d=r(15452),l=r.n(d),u=r(87413),c=r.n(u),h=r(9941),p=r.n(h),m=r(81131),E=r.n(m);class y extends n.R{constructor(e){super({knexPg:e,tableName:"learning"}),this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>["paths"].includes(t)?e?JSON.stringify(e):null:e);return c()(t,(e,t)=>E()(t))},this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>["paths"].includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)}}}class R extends n.R{constructor(e){super({knexPg:e,tableName:"model"})}async findAllByIds(e){return(await this.knex(this.tableName).whereIn("id",e)).map(e=>this.transformFromDBData(e))}async deleteAllBySourceTableNames(e,t){let r=(t?.tx?t.tx:this.knex)(this.tableName).whereIn("source_table_name",e).delete();return await r}}var I=r(89699),g=r.n(I),f=r(3547);class w extends n.R{constructor(e){super({knexPg:e,tableName:"project"}),this.jsonTypeColumns=["questions","questions_error","connection_info"],this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected db data");let t=p()(e,(e,t)=>this.jsonTypeColumns.includes(t)&&"string"==typeof e?g()(e)?{}:JSON.parse(e):"type"===t?f.ri[e]:e);return c()(t,(e,t)=>o()(t))},this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected db data");let t=c()(e,(e,t)=>E()(t));return p()(t,(e,t)=>this.jsonTypeColumns.includes(t)&&"string"!=typeof e?JSON.stringify(e):e)}}async getCurrentProject(){let e=await this.findAll({order:"id",limit:1});if(!e.length)throw Error("No project found");return e[0]}}class _ extends n.R{constructor(e){super({knexPg:e,tableName:"model_column"})}async findColumnsByModelIds(e,t){if(t&&t.tx){let{tx:r}=t;return(await r(this.tableName).whereIn("model_id",e).select("*")).map(e=>this.transformFromDBData(e))}return(await this.knex("model_column").whereIn("model_id",e).select("*")).map(e=>this.transformFromDBData(e))}async findColumnsByIds(e,t){if(t&&t.tx){let{tx:r}=t;return(await r(this.tableName).whereIn("id",e).select("*")).map(e=>this.transformFromDBData(e))}return(await this.knex("model_column").whereIn("id",e).select("*")).map(e=>this.transformFromDBData(e))}async deleteByModelIds(e,t){if(t&&t.tx){let{tx:r}=t;await r(this.tableName).whereIn("model_id",e).delete();return}await this.knex("model_column").whereIn("model_id",e).delete()}async resetModelPrimaryKey(e){await this.knex("model_column").where(this.transformToDBData({modelId:e})).update(this.transformToDBData({isPk:!1}))}async setModelPrimaryKey(e,t){await this.knex("model_column").where(this.transformToDBData({modelId:e,sourceColumnName:t})).update(this.transformToDBData({isPk:!0}))}async deleteAllBySourceColumnNames(e,t,r){let s=(r?.tx?r.tx:this.knex)(this.tableName).where(this.transformToDBData({modelId:e})).whereIn("source_column_name",t).delete();return await s}async deleteAllByColumnIds(e,t){let r=t?.tx?t.tx:this.knex;await r(this.tableName).whereIn("id",e).delete()}}class N extends n.R{constructor(e){super({knexPg:e,tableName:"model_nested_column"}),this.findNestedColumnsByModelIds=async e=>(await this.knex(this.tableName).select("*").whereIn("model_id",e)).map(e=>this.transformFromDBData(e)),this.findNestedColumnsByIds=async e=>(await this.knex(this.tableName).select("*").whereIn("id",e)).map(e=>this.transformFromDBData(e)),this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>["columnPath","properties"].includes(t)?e?JSON.stringify(e):null:e);return c()(t,(e,t)=>E()(t))},this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>["columnPath","properties"].includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)}}}class S extends n.R{constructor(e){super({knexPg:e,tableName:"relation"})}async findRelationsBy({columnIds:e,modelIds:t},r){let s=this.knex;if(r&&r.tx){let{tx:e}=r;s=e}let a=s(this.tableName).join("model_column AS fmc",`${this.tableName}.from_column_id`,"=","fmc.id").join("model_column AS tmc",`${this.tableName}.to_column_id`,"=","tmc.id");return e&&e.length>0&&a.whereIn(`${this.tableName}.from_column_id`,e).orWhereIn(`${this.tableName}.to_column_id`,e),t&&t.length>0&&a.whereIn("fmc.model_id",t).orWhereIn("tmc.model_id",t),(await a.select(`${this.tableName}.*`,"fmc.model_id AS fromModelId","tmc.model_id AS toModelId")).map(e=>this.transformFromDBData(e))}async findRelationsByIds(e,t){let r=this.knex;if(t&&t.tx){let{tx:e}=t;r=e}return(await r(this.tableName).whereIn("id",e).select("*")).map(e=>this.transformFromDBData(e))}async deleteRelationsByColumnIds(e,t){if(t&&t.tx){let{tx:r}=t;await r(this.tableName).whereIn("from_column_id",e).orWhereIn("to_column_id",e).delete();return}await this.knex(this.tableName).whereIn("from_column_id",e).orWhereIn("to_column_id",e).delete()}async findRelationInfoBy(e,t){let{projectId:r,columnIds:s,modelIds:a}=e,n=this.knex;if(t&&t.tx){let{tx:e}=t;n=e}let i=n(this.tableName).join("model_column AS fmc",`${this.tableName}.from_column_id`,"=","fmc.id").join("model_column AS tmc",`${this.tableName}.to_column_id`,"=","tmc.id").join("model AS fm","fmc.model_id","=","fm.id").join("model AS tm","tmc.model_id","=","tm.id");return r?i.where(`${this.tableName}.project_id`,r):s&&s.length>0?i.whereIn(`${this.tableName}.from_column_id`,s).orWhereIn(`${this.tableName}.to_column_id`,s):a&&a.length>0&&i.whereIn("fmc.model_id",a).orWhereIn("tmc.model_id",a),(await i.select(`${this.tableName}.*`,"fm.id AS fromModelId","fm.reference_name AS fromModelName","fm.display_name AS fromModelDisplayName","tm.id AS toModelId","tm.reference_name AS toModelName","tm.display_name AS toModelDisplayName","fmc.reference_name AS fromColumnName","fmc.display_name AS fromColumnDisplayName","tmc.reference_name AS toColumnName","tmc.display_name AS toColumnDisplayName")).map(e=>this.transformFromDBData(e))}async findExistedRelationBetweenModels(e){let{fromModelId:t,fromColumnId:r,toModelId:s,toColumnId:a}=e,n=this.knex(this.tableName).join("model_column AS fmc",`${this.tableName}.from_column_id`,"=","fmc.id").join("model_column AS tmc",`${this.tableName}.to_column_id`,"=","tmc.id").whereRaw(`fmc.model_id = ? And ${this.tableName}.from_column_id = ? And tmc.model_id = ? And ${this.tableName}.to_column_id = ?`,[t,r,s,a]).orWhereRaw(`fmc.model_id = ? And ${this.tableName}.from_column_id = ? And tmc.model_id = ? And ${this.tableName}.to_column_id = ?`,[s,a,t,r]).select(`${this.tableName}.*`);return(await n).map(e=>this.transformFromDBData(e))}}var A=r(29056);class T extends n.R{constructor(e){super({knexPg:e,tableName:"view"})}}class D extends n.R{constructor(e){super({knexPg:e,tableName:"thread"}),this.jsonbColumns=["questions","questionsError"],this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>this.jsonbColumns.includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)},this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>this.jsonbColumns.includes(t)?JSON.stringify(e):e);return c()(t,(e,t)=>E()(t))}}async listAllTimeDescOrder(e){return(await this.knex(this.tableName).where(this.transformToDBData({projectId:e})).orderBy("created_at","desc")).map(e=>this.transformFromDBData(e))}}var O=r(75991);class b extends n.R{constructor(e){super({knexPg:e,tableName:"schema_change"}),this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>["change","resolve"].includes(t)?e?JSON.stringify(e):null:e);return c()(t,(e,t)=>E()(t))},this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>["change","resolve"].includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)}}async findLastSchemaChange(e){let t=await this.knex.select("*").from(this.tableName).where(this.transformToDBData({projectId:e})).orderBy("created_at","desc").first();return t&&this.transformFromDBData(t)||null}}class C extends n.R{constructor(e){super({knexPg:e,tableName:"dashboard"})}}!function(e){e.AREA="AREA",e.BAR="BAR",e.GROUPED_BAR="GROUPED_BAR",e.LINE="LINE",e.PIE="PIE",e.STACKED_BAR="STACKED_BAR",e.TABLE="TABLE",e.NUMBER="NUMBER"}(s||(s={}));class k extends n.R{constructor(e){super({knexPg:e,tableName:"dashboard_item"}),this.jsonbColumns=["layout","detail"],this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>this.jsonbColumns.includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)},this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>this.jsonbColumns.includes(t)?JSON.stringify(e):e);return c()(t,(e,t)=>E()(t))}}}class v extends n.R{constructor(e){super({knexPg:e,tableName:"sql_pair"})}}class L extends n.R{constructor(e){super({knexPg:e,tableName:"asking_task"}),this.jsonbColumns=["detail"],this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>this.jsonbColumns.includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)},this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>this.jsonbColumns.includes(t)?JSON.stringify(e):e);return c()(t,(e,t)=>E()(t))}}async findByQueryId(e){return this.findOneBy({queryId:e})}}class q extends n.R{constructor(e){super({knexPg:e,tableName:"instruction"}),this.jsonbColumns=["questions"],this.transformFromDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=c()(e,(e,t)=>o()(t));return p()(t,(e,t)=>this.jsonbColumns.includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)},this.transformToDBData=e=>{if(!l()(e))throw Error("Unexpected dbdata");let t=p()(e,(e,t)=>this.jsonbColumns.includes(t)?JSON.stringify(e):e);return c()(t,(e,t)=>E()(t))}}}var P=r(45305);!function(e){e.IN_PROGRESS="in_progress",e.SUCCESS="success",e.FAILED="failed"}(a||(a={}));class M extends n.R{constructor(e){super({knexPg:e,tableName:"dashboard_item_refresh_job"})}}},75991:(e,t,r)=>{r.d(t,{F:()=>p,f:()=>s});var s,a=r(58658),n=r(86897),i=r.n(n),o=r(15452),d=r.n(o),l=r(87413),u=r.n(l),c=r(9941),h=r.n(c);!function(e){e.REASONING="REASONING",e.APPLY_SQL="APPLY_SQL"}(s||(s={}));class p extends a.R{constructor(e){super({knexPg:e,tableName:"thread_response"}),this.jsonbColumns=["answerDetail","breakdownDetail","chartDetail","adjustment"],this.transformFromDBData=e=>{if(!d()(e))throw Error("Unexpected dbdata");let t=u()(e,(e,t)=>i()(t));return h()(t,(e,t)=>this.jsonbColumns.includes(t)&&"string"==typeof e&&e?JSON.parse(e):e)}}async getResponsesWithThread(e,t){let r=this.knex(this.tableName).select("thread_response.*").where({thread_id:e}).leftJoin("thread","thread.id","thread_response.thread_id");return t&&r.orderBy("created_at","desc").limit(t),(await r).map(e=>u()(e,(e,t)=>i()(t))).map(e=>{let t=e.answerDetail&&"string"==typeof e.answerDetail?JSON.parse(e.answerDetail):e.answerDetail,r=e.breakdownDetail&&"string"==typeof e.breakdownDetail?JSON.parse(e.breakdownDetail):e.breakdownDetail,s=e.chartDetail&&"string"==typeof e.chartDetail?JSON.parse(e.chartDetail):e.chartDetail,a=e.adjustment&&"string"==typeof e.adjustment?JSON.parse(e.adjustment):e.adjustment;return{...e,answerDetail:t||null,breakdownDetail:r||null,chartDetail:s||null,adjustment:a||null}})}async updateOne(e,t,r){let s={status:t.status?t.status:void 0,sql:t.sql?t.sql:void 0,viewId:t.viewId?t.viewId:void 0,answerDetail:t.answerDetail?JSON.stringify(t.answerDetail):void 0,breakdownDetail:t.breakdownDetail?JSON.stringify(t.breakdownDetail):void 0,chartDetail:t.chartDetail?JSON.stringify(t.chartDetail):void 0,adjustment:t.adjustment?JSON.stringify(t.adjustment):void 0},a=r?.tx?r.tx:this.knex,[n]=await a(this.tableName).where({id:e}).update(this.transformToDBData(s)).returning("*");return this.transformFromDBData(n)}}},89179:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{V_:()=>a,Vr:()=>n,o5:()=>_,xH:()=>S});var a,n,i=r(75145),o=r(75991),d=r(47104),l=r(89699),u=r.n(l),c=r(86069),h=r.n(c),p=r(21274),m=r(10827),E=r(89097),y=r(42759),R=r(19691),I=e([p,m,E,R]);[p,m,E,R]=I.then?(await I)():I;let g=(0,y.i)(),f=(0,d.jl)("AskingService");f.level="debug",function(e){e.NOT_STARTED="NOT_STARTED",e.GENERATING="GENERATING",e.FINISHED="FINISHED",e.FAILED="FAILED"}(a||(a={})),function(e){e.NOT_STARTED="NOT_STARTED",e.FETCHING_DATA="FETCHING_DATA",e.PREPROCESSING="PREPROCESSING",e.STREAMING="STREAMING",e.FINISHED="FINISHED",e.FAILED="FAILED",e.INTERRUPTED="INTERRUPTED"}(n||(n={}));let w=e=>e===i.i3.FAILED||e===i.i3.FINISHED||e===i.i3.STOPPED,_=(e,t)=>{if(!h()(t)&&(t<0||t>=e.length))throw Error(`Invalid stepIndex: ${t}`);let r=h()(t)?e:e.slice(0,t+1);if(1===r.length)return`-- ${r[0].summary}
${r[0].sql}`;let s="WITH ";return r.forEach((e,t)=>{t===r.length-1?s+=`
-- ${e.summary}
${e.sql}`:t===r.length-2?s+=`${e.cteName} AS
-- ${e.summary}
(${e.sql})`:s+=`${e.cteName} AS
-- ${e.summary}
(${e.sql}),`}),s};class N{constructor({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:r}){this.tasks={},this.runningJobs=new Set,this.telemetry=e,this.wrenAIAdaptor=t,this.threadResponseRepository=r,this.intervalTime=1e3,this.start()}start(){f.info("Background tracker started"),setInterval(()=>{let e=Object.values(this.tasks).map(e=>async()=>{if(this.runningJobs.has(e.id))return;this.runningJobs.add(e.id);let t=e.breakdownDetail,r=await this.wrenAIAdaptor.getAskDetailResult(t.queryId);if(t.status===r.status){f.debug(`Job ${e.id} status not changed, finished`),this.runningJobs.delete(e.id);return}let s={queryId:t.queryId,status:r?.status,error:r?.error,description:r?.response?.description,steps:r?.response?.steps};if(f.debug(`Job ${e.id} status changed, updating`),await this.threadResponseRepository.updateOne(e.id,{breakdownDetail:s}),w(r.status)){let t={question:e.question,error:r.error};r.status===i.i3.FINISHED?this.telemetry.sendEvent(m.MW.HOME_ANSWER_BREAKDOWN,t):this.telemetry.sendEvent(m.MW.HOME_ANSWER_BREAKDOWN,t,m.ic.AI,!1),f.debug(`Job ${e.id} is finalized, removing`),delete this.tasks[e.id]}this.runningJobs.delete(e.id)});Promise.allSettled(e.map(e=>e())).then(e=>{e.forEach((e,t)=>{"rejected"===e.status&&f.error(`Job ${t} failed: ${e.reason}`)})})},this.intervalTime)}addTask(e){this.tasks[e.id]=e}getTasks(){return this.tasks}}class S{constructor({telemetry:e,wrenAIAdaptor:t,deployService:r,projectService:s,viewRepository:a,threadRepository:n,threadResponseRepository:i,askingTaskRepository:o,queryService:d,mdlService:l,askingTaskTracker:u}){this.wrenAIAdaptor=t,this.deployService=r,this.projectService=s,this.viewRepository=a,this.threadRepository=n,this.threadResponseRepository=i,this.telemetry=e,this.queryService=d,this.breakdownBackgroundTracker=new N({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:i}),this.textBasedAnswerBackgroundTracker=new R.i({wrenAIAdaptor:t,threadResponseRepository:i,projectService:s,deployService:r,queryService:d}),this.chartBackgroundTracker=new E.Fw({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:i}),this.chartAdjustmentBackgroundTracker=new E.O9({telemetry:e,wrenAIAdaptor:t,threadResponseRepository:i}),this.threadRecommendQuestionBackgroundTracker=new E.Ef({telemetry:e,wrenAIAdaptor:t,threadRepository:n}),this.adjustmentBackgroundTracker=new E.vs({telemetry:e,wrenAIAdaptor:t,askingTaskRepository:o,threadResponseRepository:i}),this.askingTaskRepository=o,this.mdlService=l,this.askingTaskTracker=u}async getThreadRecommendationQuestions(e){let t=await this.threadRepository.findOneBy({id:e});if(!t)throw Error(`Thread ${e} not found`);let r={status:"NOT_STARTED",questions:[],error:null};return t.queryId&&t.questionsStatus&&(r.status=a[t.questionsStatus]?a[t.questionsStatus]:r.status,r.questions=t.questions||[],r.error=t.questionsError),r}async generateThreadRecommendationQuestions(e){let t=await this.threadRepository.findOneBy({id:e});if(!t)throw Error(`Thread ${e} not found`);if(this.threadRecommendQuestionBackgroundTracker.isExist(t)){f.debug(`thread "${e}" recommended questions are generating, skip the current request`);return}let r=await this.projectService.getCurrentProject(),{manifest:s}=await this.mdlService.makeCurrentModelMDL(),a=(await this.threadResponseRepository.findAllBy({threadId:e})).sort((e,t)=>t.id-e.id).slice(0,5).map(({question:e})=>e),n={manifest:s,previousQuestions:a,...this.getThreadRecommendationQuestionsConfig(r)},o=await this.wrenAIAdaptor.generateRecommendationQuestions(n),d=await this.threadRepository.updateOne(e,{queryId:o.queryId,questionsStatus:i.MU.GENERATING,questions:[],questionsError:null});this.threadRecommendQuestionBackgroundTracker.addTask(d)}async initialize(){let e=(await this.threadResponseRepository.findAll()).filter(e=>e?.breakdownDetail?.status&&!w(e?.breakdownDetail?.status));for(let t of(f.info(`Initialization: adding unfininshed breakdown thread responses (total: ${e.length}) to background tracker`),e))this.breakdownBackgroundTracker.addTask(t)}async createAskingTask(e,t,r,s,a){let{threadId:n,language:i}=t,o=await this.getDeployId(),d=n?await this.getAskingHistory(n,a):null;return{id:(await this.askingTaskTracker.createAskingTask({query:e.question,histories:d,deployId:o,configurations:{language:i},rerunFromCancelled:r,previousTaskId:s,threadResponseId:a})).queryId}}async rerunAskingTask(e,t){let r=await this.threadResponseRepository.findOneBy({id:e});if(!r)throw Error(`Thread response ${e} not found`);let s=r.question,a={...t,threadId:r.threadId};return await this.createAskingTask({question:s},a,!0,r.askingTaskId,e)}async cancelAskingTask(e){let t=m.MW.HOME_CANCEL_ASK;try{await this.askingTaskTracker.cancelAskingTask(e),this.telemetry.sendEvent(t,{})}catch(e){throw this.telemetry.sendEvent(t,{},e.extensions?.service,!1),e}}async getAskingTask(e){return this.askingTaskTracker.getAskingResult(e)}async getAskingTaskById(e){return this.askingTaskTracker.getAskingResultById(e)}async createThread(e){let{id:t}=await this.projectService.getCurrentProject(),r=await this.threadRepository.createOne({projectId:t,summary:e.question}),s=await this.threadResponseRepository.createOne({threadId:r.id,question:e.question,sql:e.sql,askingTaskId:e.trackedAskingResult?.taskId});return e.trackedAskingResult?.taskId&&await this.askingTaskTracker.bindThreadResponse(e.trackedAskingResult.taskId,e.trackedAskingResult.queryId,r.id,s.id),r}async listThreads(){let{id:e}=await this.projectService.getCurrentProject();return await this.threadRepository.listAllTimeDescOrder(e)}async updateThread(e,t){if(u()(t))throw Error("Update thread input is empty");return this.threadRepository.updateOne(e,{summary:t.summary})}async deleteThread(e){await this.threadRepository.deleteOne(e)}async createThreadResponse(e,t){let r=await this.threadRepository.findOneBy({id:t});if(!r)throw Error(`Thread ${t} not found`);let s=await this.threadResponseRepository.createOne({threadId:r.id,question:e.question,sql:e.sql,askingTaskId:e.trackedAskingResult?.taskId});return e.trackedAskingResult?.taskId&&await this.askingTaskTracker.bindThreadResponse(e.trackedAskingResult.taskId,e.trackedAskingResult.queryId,r.id,s.id),s}async updateThreadResponse(e,t){if(!await this.threadResponseRepository.findOneBy({id:e}))throw Error(`Thread response ${e} not found`);return await this.threadResponseRepository.updateOne(e,{sql:t.sql})}async generateThreadResponseBreakdown(e,t){let{language:r}=t,s=await this.threadResponseRepository.findOneBy({id:e});if(!s)throw Error(`Thread response ${e} not found`);let a=await this.wrenAIAdaptor.generateAskDetail({query:s.question,sql:s.sql,configurations:{language:r}}),n=await this.threadResponseRepository.updateOne(s.id,{breakdownDetail:{queryId:a.queryId,status:i.i3.UNDERSTANDING}});return this.breakdownBackgroundTracker.addTask(n),n}async generateThreadResponseAnswer(e){let t=await this.threadResponseRepository.findOneBy({id:e});if(!t)throw Error(`Thread response ${e} not found`);let r=await this.threadResponseRepository.updateOne(t.id,{answerDetail:{status:"NOT_STARTED"}});return this.textBasedAnswerBackgroundTracker.addTask(r),r}async generateThreadResponseChart(e,t){let r=await this.threadResponseRepository.findOneBy({id:e});if(!r)throw Error(`Thread response ${e} not found`);let s=await this.wrenAIAdaptor.generateChart({query:r.question,sql:r.sql,configurations:t}),a=await this.threadResponseRepository.updateOne(r.id,{chartDetail:{queryId:s.queryId,status:i.XP.FETCHING}});return this.chartBackgroundTracker.addTask(a),a}async adjustThreadResponseChart(e,t,r){let s=await this.threadResponseRepository.findOneBy({id:e});if(!s)throw Error(`Thread response ${e} not found`);let a=await this.wrenAIAdaptor.adjustChart({query:s.question,sql:s.sql,adjustmentOption:t,chartSchema:s.chartDetail?.chartSchema,configurations:r}),n=await this.threadResponseRepository.updateOne(s.id,{chartDetail:{queryId:a.queryId,status:i.XP.FETCHING,adjustment:!0}});return this.chartAdjustmentBackgroundTracker.addTask(n),n}async getResponsesWithThread(e){return this.threadResponseRepository.getResponsesWithThread(e)}async getResponse(e){return this.threadResponseRepository.findOneBy({id:e})}async previewData(e,t){let r=await this.getResponse(e);if(!r)throw Error(`Thread response ${e} not found`);let s=await this.projectService.getCurrentProject(),a=(await this.deployService.getLastDeployment(s.id)).manifest,n=m.MW.HOME_PREVIEW_ANSWER;try{let e=await this.queryService.preview(r.sql,{project:s,manifest:a,limit:t});return this.telemetry.sendEvent(n,{sql:r.sql}),e}catch(e){throw this.telemetry.sendEvent(n,{sql:r.sql,error:e.message},e.extensions?.service,!1),e}}async previewBreakdownData(e,t,r){let s=await this.getResponse(e);if(!s)throw Error(`Thread response ${e} not found`);let a=await this.projectService.getCurrentProject(),n=(await this.deployService.getLastDeployment(a.id)).manifest,i=s?.breakdownDetail?.steps,o=(0,p.format)(_(i,t)),d=m.MW.HOME_PREVIEW_ANSWER;try{let e=await this.queryService.preview(o,{project:a,manifest:n,limit:r});return this.telemetry.sendEvent(d,{sql:o}),e}catch(e){throw this.telemetry.sendEvent(d,{sql:o,error:e.message},e.extensions?.service,!1),e}}async createInstantRecommendedQuestions(e){let t=await this.projectService.getCurrentProject(),{manifest:r}=await this.deployService.getLastDeployment(t.id);return{id:(await this.wrenAIAdaptor.generateRecommendationQuestions({manifest:r,previousQuestions:e.previousQuestions,...this.getThreadRecommendationQuestionsConfig(t)})).queryId}}async getInstantRecommendedQuestions(e){return await this.wrenAIAdaptor.getRecommendationQuestionsResult(e)}async deleteAllByProjectId(e){await this.threadRepository.deleteAllBy({projectId:e})}async changeThreadResponseAnswerDetailStatus(e,t,r){let s=await this.threadResponseRepository.findOneBy({id:e});if(!s)throw Error(`Thread response ${e} not found`);if(s.answerDetail?.status!==t)return await this.threadResponseRepository.updateOne(e,{answerDetail:{...s.answerDetail,status:t,content:r}})}async getDeployId(){let{id:e}=await this.projectService.getCurrentProject();return(await this.deployService.getLastDeployment(e)).hash}async adjustThreadResponseWithSQL(e,t){let r=await this.threadResponseRepository.findOneBy({id:e});if(!r)throw Error(`Thread response ${e} not found`);return await this.threadResponseRepository.createOne({sql:t.sql,threadId:r.threadId,question:r.question,adjustment:{type:o.f.APPLY_SQL,payload:{originalThreadResponseId:r.id,sql:t.sql}}})}async adjustThreadResponseAnswer(e,t,r){let s=await this.threadResponseRepository.findOneBy({id:e});if(!s)throw Error(`Thread response ${e} not found`);let{createdThreadResponse:a}=await this.adjustmentBackgroundTracker.createAdjustmentTask({threadId:s.threadId,tables:t.tables,sqlGenerationReasoning:t.sqlGenerationReasoning,sql:s.sql,projectId:t.projectId,configurations:r,question:s.question,originalThreadResponseId:s.id});return a}async cancelAdjustThreadResponseAnswer(e){await this.adjustmentBackgroundTracker.cancelAdjustmentTask(e)}async rerunAdjustThreadResponseAnswer(e,t,r){let s=await this.threadResponseRepository.findOneBy({id:e});if(!s)throw Error(`Thread response ${e} not found`);let{queryId:a}=await this.adjustmentBackgroundTracker.rerunAdjustmentTask({threadId:s.threadId,threadResponseId:e,projectId:t,configurations:r});return{queryId:a}}async getAdjustmentTask(e){return this.adjustmentBackgroundTracker.getAdjustmentResult(e)}async getAdjustmentTaskById(e){return this.adjustmentBackgroundTracker.getAdjustmentResultById(e)}async getAskingHistory(e,t){if(!e)return[];let r=await this.threadResponseRepository.getResponsesWithThread(e,10);return t&&(r=r.filter(e=>e.id!==t)),r.filter(e=>e.sql)}getThreadRecommendationQuestionsConfig(e){return{maxCategories:g.threadRecommendationQuestionMaxCategories,maxQuestions:g.threadRecommendationQuestionsMaxQuestions,configuration:{language:i.aS[e.language]||i.aS.EN}}}}s()}catch(e){s(e)}})},97880:(e,t,r)=>{r.d(t,{X:()=>o});var s=r(47104),a=r(75145),n=r(24634);let i=(0,s.jl)("AskingTaskTracker");i.level="debug";class o{constructor({wrenAIAdaptor:e,askingTaskRepository:t,threadResponseRepository:r,viewRepository:s,pollingInterval:a=1e3,memoryRetentionTime:n=3e5}){this.trackedTasks=new Map,this.trackedTasksById=new Map,this.runningJobs=new Set,this.wrenAIAdaptor=e,this.askingTaskRepository=t,this.threadResponseRepository=r,this.viewRepository=s,this.pollingInterval=a,this.memoryRetentionTime=n,this.startPolling()}async createAskingTask(e){try{let t=(await this.wrenAIAdaptor.ask(e)).queryId;if(e.rerunFromCancelled&&(!e.previousTaskId||!e.threadResponseId))throw Error("Previous task id and thread response id are required if rerun from cancelled");let r={queryId:t,lastPolled:Date.now(),question:e.query,isFinalized:!1,rerunFromCancelled:e.rerunFromCancelled};if(this.trackedTasks.set(t,r),e.rerunFromCancelled&&e.previousTaskId&&e.threadResponseId){r.threadResponseId=e.threadResponseId,this.trackedTasksById.set(e.previousTaskId,r);let s=await this.wrenAIAdaptor.getAskResult(t);r.result=s,await this.askingTaskRepository.updateOne(e.previousTaskId,{queryId:t})}return i.info(`Created asking task with queryId: ${t}`),{queryId:t}}catch(e){throw i.error(`Failed to create asking task: ${e}`),e}}async getAskingResult(e){let t=this.trackedTasks.get(e);return t&&t.result?{...t.result,queryId:e,question:t.question,taskId:t.taskId}:this.getAskingResultFromDB({queryId:e})}async getAskingResultById(e){let t=this.trackedTasksById.get(e);return t?this.getAskingResult(t.queryId):this.getAskingResultFromDB({taskId:e})}async cancelAskingTask(e){await this.wrenAIAdaptor.cancelAsk(e)}stopPolling(){this.pollingIntervalId&&clearInterval(this.pollingIntervalId)}async bindThreadResponse(e,t,r,s){let a=this.trackedTasks.get(t);if(!a)throw Error(`Task ${t} not found`);a.threadResponseId=s,this.trackedTasksById.set(e,a),await this.askingTaskRepository.updateOne(e,{threadId:r,threadResponseId:s}),a.isFinalized&&await this.updateThreadResponseWhenTaskFinalized(a)}startPolling(){this.pollingIntervalId=setInterval(()=>{this.pollTasks()},this.pollingInterval)}async pollTasks(){let e=Date.now(),t=[];Promise.allSettled(Array.from(this.trackedTasks.entries()).map(([r,s])=>async()=>{try{if(this.runningJobs.has(r))return;if(s.isFinalized&&e-s.lastPolled>this.memoryRetentionTime){t.push(r);return}if(s.isFinalized)return;this.runningJobs.add(r),i.info(`Polling for updates for task ${r}`);let o=await this.wrenAIAdaptor.getAskResult(r);if(s.lastPolled=e,!this.isResultChanged(s.result,o)||(s.result=o,o.status===a.i3.UNDERSTANDING)){this.runningJobs.delete(r);return}if(o.type===a.dd.GENERAL||o.type===a.dd.MISLEADING_QUERY){if(s.isFinalized=!0,s.rerunFromCancelled){let e=o.type===a.dd.GENERAL?n.GL.IDENTIED_AS_GENERAL:n.GL.IDENTIED_AS_MISLEADING_QUERY,t={code:e,message:n.Qj[e],shortMessage:n.j5[e]};await this.updateTaskInDatabase({queryId:r},{...s,result:{...s.result,status:a.i3.FAILED,error:t}})}this.runningJobs.delete(r);return}i.info(`Updating task ${r} in database`),await this.updateTaskInDatabase({queryId:r},s),this.isTaskFinalized(o.status)&&(s.isFinalized=!0,s.threadResponseId&&await this.updateThreadResponseWhenTaskFinalized(s),i.info(`Task ${r} is finalized with status: ${o.status}`)),this.runningJobs.delete(r)}catch(e){throw this.runningJobs.delete(r),i.error(e.stack),e}}).map(e=>e())).then(e=>{for(let r of(e.forEach((e,t)=>{"rejected"===e.status&&i.error(`Job ${t} failed: ${e.reason}`)}),t.length>0&&i.info(`Cleaning up tasks that have been in memory too long. Tasks: ${t.join(", ")}`),t))this.trackedTasks.delete(r)})}async updateThreadResponseWhenTaskFinalized(e){let t=e?.result?.response?.[0];if(t){if(t.viewId){let r=await this.viewRepository.findOneBy({id:t.viewId});await this.threadResponseRepository.updateOne(e.threadResponseId,{sql:r.statement,viewId:t.viewId})}else await this.threadResponseRepository.updateOne(e.threadResponseId,{sql:t?.sql})}}async getAskingResultFromDB({queryId:e,taskId:t}){let r=null;return(e?r=await this.askingTaskRepository.findByQueryId(e):t&&(r=await this.askingTaskRepository.findOneBy({id:t})),r)?{...r?.detail,queryId:e||r?.queryId,question:r?.question,taskId:r?.id}:null}async updateTaskInDatabase(e,t){let{queryId:r,taskId:s}=e,a=null;if(r?a=await this.askingTaskRepository.findByQueryId(r):s&&(a=await this.askingTaskRepository.findOneBy({id:s})),!a){let e;let a=await this.askingTaskRepository.createOne({queryId:r,question:t.question,detail:t.result});r?e=this.trackedTasks.get(r):s&&(e=this.trackedTasksById.get(s)),e&&(e.taskId=a.id);return}await this.askingTaskRepository.updateOne(a.id,{detail:t.result})}isTaskFinalized(e){return[a.i3.FINISHED,a.i3.FAILED,a.i3.STOPPED].includes(e)}isResultChanged(e,t){return e?.status!==t.status}}},57339:(e,t,r)=>{r.d(t,{s:()=>d});var s=r(47104);function a(e){let t=new Date,r=new Date(t.toLocaleString("en-US",{timeZone:"UTC"}));return(new Date(t.toLocaleString("en-US",{timeZone:e}))-r)/6e4}var n=r(3379),i=r(82754);let o=(0,s.jl)("DashboardService");o.level="debug";class d{constructor({projectService:e,dashboardItemRepository:t,dashboardRepository:r}){this.projectService=e,this.dashboardItemRepository=t,this.dashboardRepository=r}async setDashboardSchedule(e,t){try{let{cacheEnabled:r,schedule:s}=t;if(this.validateScheduleInput(t),!await this.dashboardRepository.findOneBy({id:e}))throw Error(`Dashboard with id ${e} not found`);if(!r)return await this.dashboardRepository.updateOne(e,{cacheEnabled:!1,scheduleFrequency:null,scheduleTimezone:null,scheduleCron:null,nextScheduledAt:null});let a=null,i=null;return r&&s.frequency!==n.M_.NEVER&&(a=this.generateCronExpression(s),i=this.calculateNextRunTime(a)),await this.dashboardRepository.updateOne(e,{cacheEnabled:r,scheduleFrequency:s.frequency,scheduleTimezone:s.timezone,scheduleCron:a,nextScheduledAt:i})}catch(e){throw o.error(`Failed to set dashboard schedule: ${e.message}`),e}}async initDashboard(){let e=await this.projectService.getCurrentProject();return await this.dashboardRepository.findOneBy({projectId:e.id})||await this.dashboardRepository.createOne({name:"Dashboard",projectId:e.id})}async getCurrentDashboard(){let e=await this.projectService.getCurrentProject();return{...await this.dashboardRepository.findOneBy({projectId:e.id})}}async getDashboardItem(e){let t=await this.dashboardItemRepository.findOneBy({id:e});if(!t)throw Error("Dashboard item not found.");return t}async getDashboardItems(e){return await this.dashboardItemRepository.findAllBy({dashboardId:e})}async createDashboardItem(e){let t=await this.calculateNewLayout(e.dashboardId);return await this.dashboardItemRepository.createOne({dashboardId:e.dashboardId,type:e.type,detail:{sql:e.sql,chartSchema:e.chartSchema},layout:t})}async updateDashboardItem(e,t){return await this.dashboardItemRepository.updateOne(e,{displayName:t.displayName})}async updateDashboardItemLayouts(e){let t=[];if(!e.every(e=>e.itemId&&e.x>=0&&e.y>=0&&e.w>0&&e.h>0))throw Error("Invalid layouts boundaries.");return await Promise.all(e.map(async e=>{let r=await this.dashboardItemRepository.updateOne(e.itemId,{layout:{x:e.x,y:e.y,w:e.w,h:e.h}});t.push(r)})),t}async deleteDashboardItem(e){return await this.dashboardItemRepository.deleteOne(e),!0}async calculateNewLayout(e){let t=(await this.dashboardItemRepository.findAllBy({dashboardId:e})).map(e=>e.layout);if(0===t.length)return{x:0,y:0,w:3,h:2};let r=Math.max(...t.map(e=>e.y)),s=t.filter(e=>e.y===r).reduce((e,t)=>e+t.x+t.w,0)>3;return{x:s?0:3,y:s?r+2:r,w:3,h:2}}toUTC(e){if(!e.timezone||e.frequency===n.M_.CUSTOM)return e;let t=a(e.timezone),r=e.minute-t,s=0;r<0?(s=Math.ceil(Math.abs(r)/60),r=(r+60*s)%60,s=-s):r>=60&&(s=Math.floor(r/60),r%=60);let i=e.hour+s,o=0;if(i<0?(o=Math.ceil(Math.abs(i)/24),i=(i+24*o)%24,o=-o):i>=24&&(o=Math.floor(i/24),i%=24),e.frequency===n.M_.WEEKLY&&0!==o){let t=(n.z1.indexOf(e.day)+o+7)%7;return{...e,hour:i,minute:r,day:n.z1[t]}}return{...e,hour:i,minute:r}}toTimezone(e){let{timezone:t}=e;if([n.M_.CUSTOM,n.M_.NEVER].includes(e.frequency)||!t)return e;let r=a(t),s=e.minute+r,i=0;s<0?(i=Math.ceil(Math.abs(s)/60),s=(s+60*i)%60,i=-i):s>=60&&(i=Math.floor(s/60),s%=60);let o=e.hour+i,d=0;if(o<0?(d=Math.ceil(Math.abs(o)/24),o=(o+24*d)%24,d=-d):o>=24&&(d=Math.floor(o/24),o%=24),e.frequency===n.M_.WEEKLY&&0!==d){let r=(n.z1.indexOf(e.day)+d+7)%7;return{...e,hour:o,minute:s,day:n.z1[r],timezone:t}}return{...e,hour:o,minute:s,timezone:t}}generateCronExpression(e){let{frequency:t,day:r,hour:s,minute:a}=this.toUTC(e);switch(t){case n.M_.DAILY:return`${a} ${s} * * *`;case n.M_.WEEKLY:return`${a} ${s} * * ${r}`;case n.M_.CUSTOM:return e.cron;case n.M_.NEVER:return null;default:return o.warn(`Unsupported schedule frequency: ${t}`),null}}calculateNextRunTime(e){try{return i.CronExpressionParser.parse(e,{currentDate:new Date}).next().toDate()}catch(e){return o.error(`Failed to parse cron expression: ${e.message}`),null}}validateScheduleInput(e){let{schedule:t}=e;if(t){if(t.frequency===n.M_.WEEKLY&&!t.day)throw Error("Day of week is required for weekly schedule");if(t.frequency===n.M_.CUSTOM&&!t.cron)throw Error("Cron expression is required for custom schedule");if(t.hour<0||t.hour>23)throw Error("Hour must be between 0 and 23");if(t.minute<0||t.minute>59)throw Error("Minute must be between 0 and 59");if(t.timezone)try{new Date().toLocaleString("en-US",{timeZone:t.timezone})}catch(e){throw Error(`Invalid timezone: ${t.timezone}`)}if(t.frequency===n.M_.CUSTOM){let e=i.CronExpressionParser.parse(t.cron,{currentDate:new Date}).next().toDate();if(i.CronExpressionParser.parse(t.cron,{currentDate:e}).next().toDate().getTime()-e.getTime()<6e5)throw Error("Custom cron expression must be at least 10 minutes apart")}}}parseCronExpression(e){if(!e.scheduleCron)return{frequency:e.scheduleFrequency,hour:0,minute:0,day:null,timezone:e.scheduleTimezone||"",cron:""};switch(e.scheduleFrequency){case n.M_.CUSTOM:return{frequency:n.M_.CUSTOM,hour:0,minute:0,day:null,timezone:e.scheduleTimezone||"",cron:e.scheduleCron};case n.M_.DAILY:case n.M_.WEEKLY:{let t=e.scheduleCron.split(" ");if(5!==t.length)throw Error("Invalid cron expression format");let[r,s,,,a]=t;return this.toTimezone({frequency:e.scheduleFrequency,hour:parseInt(s,10),minute:parseInt(r,10),day:e.scheduleFrequency===n.M_.WEEKLY?a:null,timezone:e.scheduleTimezone||"",cron:null})}case n.M_.NEVER:return{frequency:n.M_.NEVER,hour:null,minute:null,day:null,timezone:e.scheduleTimezone||"",cron:null};default:throw Error("Invalid schedule frequency")}}}},84164:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{v:()=>c});var a=r(75145),n=r(29056),i=r(6005),o=r(47104),d=r(10827),l=e([d]);d=(l.then?(await l)():l)[0];let u=(0,o.jl)("DeployService");u.level="debug";class c{constructor({wrenAIAdaptor:e,deployLogRepository:t,telemetry:r}){this.wrenAIAdaptor=e,this.deployLogRepository=t,this.telemetry=r}async getLastDeployment(e){return await this.deployLogRepository.findLastProjectDeployLog(e)||null}async getInProgressDeployment(e){return await this.deployLogRepository.findInProgressProjectDeployLog(e)}async deploy(e,t,r=!1){let s=d.MW.MODELING_DEPLOY_MDL;try{let i=this.createMDLHash(e,t);if(u.debug(`Deploying model, hash: ${i}`),!r){let e=await this.deployLogRepository.findLastProjectDeployLog(t);if(e&&e.hash===i)return u.log(`Model has been deployed, hash: ${i}`),{status:n.Q.SUCCESS}}let o={manifest:e,hash:i,projectId:t,status:n.Q.IN_PROGRESS},l=await this.deployLogRepository.createOne(o),{status:c,error:h}=await this.wrenAIAdaptor.deploy({manifest:e,hash:i}),p=c===a.T_.SUCCESS?n.Q.SUCCESS:n.Q.FAILED;return await this.deployLogRepository.updateOne(l.id,{status:p,error:h}),p===n.Q.SUCCESS?this.telemetry.sendEvent(s):this.telemetry.sendEvent(s,{mdl:e,error:h},d.ic.AI,!1),{status:p,error:h}}catch(t){return u.error(`Error deploying model: ${t.message}`),this.telemetry.sendEvent(s,{mdl:e,error:t.message},t.extensions?.service,!1),{status:n.Q.FAILED,error:t.message}}}createMDLHash(e,t){let r=JSON.stringify(e),s=`${t} ${r}`;return(0,i.createHash)("sha1").update(s).digest("hex")}async getMDLByHash(e){let t=await this.deployLogRepository.findOneBy({hash:e});return t?Buffer.from(JSON.stringify(t.manifest)).toString("base64"):null}async deleteAllByProjectId(e){await this.deployLogRepository.deleteAllBy({projectId:e})}}s()}catch(e){s(e)}})},83372:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{Tw:()=>i.T,Vr:()=>a.Vr,Xi:()=>h.X,Y4:()=>d.Y,_7:()=>l._,od:()=>p.X,sf:()=>l.s,sp:()=>c.s,tj:()=>u.t,v2:()=>n.v,xH:()=>a.xH});var a=r(89179),n=r(84164),i=r(53197),o=r(51458),d=r(58862),l=r(52834),u=r(62202),c=r(57339),h=r(97880),p=r(24673),m=e([a,n,o,d,l]);[a,n,o,d,l]=m.then?(await m)():m,s()}catch(e){s(e)}})},24673:(e,t,r)=>{r.d(t,{X:()=>o});var s=r(84159),a=r.n(s),n=r(75145),i=r(24634);class o{constructor({instructionRepository:e,wrenAIAdaptor:t}){this.instructionRepository=e,this.wrenAIAdaptor=t}async getInstructions(e){return this.instructionRepository.findAllBy({projectId:e})}async createInstruction(e){let t=await this.instructionRepository.transaction();try{this.validateInstructionInput(e);let r=await this.instructionRepository.createOne({...e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{tx:t}),{queryId:s}=await this.wrenAIAdaptor.generateInstruction([this.pickGenerateInstructionInput(r)]),a=await this.waitDeployInstruction(s);if(a.error)throw await t.rollback(),i.Ue(a.error.code,{customMessage:a.error.message});return await t.commit(),r}catch(e){throw await t.rollback(),Error(`Failed to create instruction: ${e}`)}}async createInstructions(e){let t=await this.instructionRepository.transaction();try{e.forEach(e=>this.validateInstructionInput(e));let r=await this.instructionRepository.createMany(e.map(e=>({...e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})),{tx:t}),{queryId:s}=await this.wrenAIAdaptor.generateInstruction(r.map(this.pickGenerateInstructionInput)),a=await this.waitDeployInstruction(s);if(a.error)throw await t.rollback(),i.Ue(a.error.code,{customMessage:a.error.message});return await t.commit(),r}catch(e){throw await t.rollback(),Error(`Failed to create instruction: ${e}`)}}async updateInstruction(e){let t=await this.instructionRepository.transaction();try{this.validateInstructionInput(e);let r=await this.instructionRepository.findOneBy({id:e.id,projectId:e.projectId},{tx:t});if(!r)throw Error("Instruction not found");let s={...r,...e,updatedAt:new Date().toISOString()},a=await this.instructionRepository.updateOne(e.id,s,{tx:t}),{queryId:n}=await this.wrenAIAdaptor.generateInstruction([this.pickGenerateInstructionInput(a)]),o=await this.waitDeployInstruction(n);if(o.error)throw await t.rollback(),i.Ue(o.error.code,{customMessage:o.error.message});return await t.commit(),a}catch(e){throw await t.rollback(),Error(`Failed to update instruction: ${e}`)}}async deleteInstruction(e,t){let r=await this.instructionRepository.transaction();try{let s=await this.instructionRepository.findOneBy({id:e,projectId:t},{tx:r});if(!s)throw Error("Instruction not found");await this.instructionRepository.deleteOne(e,{tx:r}),await this.wrenAIAdaptor.deleteInstructions([e],s.projectId),await r.commit()}catch(e){throw await r.rollback(),Error(`Failed to delete instruction: ${e}`)}}async waitDeployInstruction(e,t=30){let r=e=>e===n.qj.FINISHED||e===n.qj.FAILED,s=await this.wrenAIAdaptor.getInstructionResult(e),a=0;for(;!r(s.status)&&a<t;)await new Promise(e=>setTimeout(e,1e3)),s=await this.wrenAIAdaptor.getInstructionResult(e),a++;if(!r(s.status))throw i.Ue(i.GL.DEPLOY_TIMEOUT_ERROR,{customMessage:`Instruction deployment timed out after ${t} seconds`});return s}pickGenerateInstructionInput(e){return a()(e,["id","projectId","instruction","questions","isDefault"])}validateInstructionInput(e){if(!e.instruction)throw Error("Instruction is required");if(e.instruction.length>1e3)throw Error("Instruction is too long")}}},53197:(e,t,r)=>{r.d(t,{T:()=>a});var s=r(45716);class a{constructor({projectRepository:e,modelRepository:t,modelColumnRepository:r,modelNestedColumnRepository:s,relationRepository:a,viewRepository:n}){this.projectRepository=e,this.modelRepository=t,this.modelColumnRepository=r,this.modelNestedColumnRepository=s,this.relationRepository=a,this.viewRepository=n}async makeCurrentModelMDL(){let e=await this.projectRepository.getCurrentProject(),t=e.id,r=await this.modelRepository.findAllBy({projectId:t}),a=r.map(e=>e.id),n=await this.modelColumnRepository.findColumnsByModelIds(a),i=await this.modelNestedColumnRepository.findNestedColumnsByModelIds(a),o=await this.relationRepository.findRelationInfoBy({projectId:t}),d=await this.viewRepository.findAllBy({projectId:t}),l=new s.S({project:e,models:r,columns:n,nestedColumns:i,relations:o,views:d,relatedModels:r,relatedColumns:n,relatedRelations:o});return{manifest:l.build(),mdlBuilder:l}}}},62202:(e,t,r)=>{r.d(t,{t:()=>n});var s,a=r(3547);(0,r(47104).jl)("MetadataService").level="debug",function(e){e.PRIMARY_KEY="PRIMARY KEY",e.FOREIGN_KEY="FOREIGN KEY",e.UNIQUE="UNIQUE"}(s||(s={}));class n{constructor({ibisAdaptor:e,wrenEngineAdaptor:t}){this.ibisAdaptor=e,this.wrenEngineAdaptor=t}async listTables(e){let{type:t,connectionInfo:r}=e;return t===a.ri.DUCKDB?await this.wrenEngineAdaptor.listTables():await this.ibisAdaptor.getTables(t,r)}async listConstraints(e){let{type:t,connectionInfo:r}=e;return t===a.ri.DUCKDB?[]:await this.ibisAdaptor.getConstraints(t,r)}async getVersion(e){let{type:t,connectionInfo:r}=e;return await this.ibisAdaptor.getVersion(t,r)}}},51458:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{b:()=>E});var a=r(47104),n=r(39458),i=r(68022),o=r(89699),d=r.n(o),l=r(59969),u=r.n(l),c=r(24634),h=r(3547),p=e([i]);i=(p.then?(await p)():p)[0];let m=(0,a.jl)("ModelService");m.level="debug";class E{constructor({projectService:e,modelRepository:t,modelColumnRepository:r,relationRepository:s,viewRepository:a,mdlService:n,wrenEngineAdaptor:i,queryService:o}){this.projectService=e,this.modelRepository=t,this.modelColumnRepository=r,this.relationRepository=s,this.viewRepository=a,this.mdlService=n,this.wrenEngineAdaptor=i,this.queryService=o}async createCalculatedField(e){let{modelId:t,name:r,expression:s,lineage:n}=e,i=`Create Calculated Field ${r}`,o=await this.modelRepository.findOneBy({id:t});if(!o)throw Error("Model not found");let{valid:d,message:l}=await this.validateCalculatedFieldNaming(r,t);if(m.debug(`${i} : validateCalculatedFieldNaming: ${d}, ${l}`),!d)throw Error(l);let u=this.generateReferenceNameFromDisplayName(r);m.debug(`${i} : generated referenceName: "${u}"`);let{valid:h,message:p}=await this.checkCalculatedFieldCanQuery(t,o.referenceName,{referenceName:u,expression:s,lineage:n});if(m.debug(`${i} : checkCalculatedFieldCanQuery: ${h}`),!h){let e=(0,a.as)(p);throw c.Ue(c.GL.INVALID_CALCULATED_FIELD,{customMessage:e?.message||p,originalError:e||null})}let E=n[n.length-1],y=await this.inferCalculatedFieldDataType(s,E);return m.debug(`${i} : inferCalculatedFieldDataType: ${y}`),await this.modelColumnRepository.createOne({modelId:t,displayName:r,sourceColumnName:u,referenceName:u,type:y,isCalculated:!0,isPk:!1,notNull:!1,aggregation:s,lineage:JSON.stringify(n),properties:JSON.stringify({description:""})})}async updateCalculatedField(e,t){let{name:r,expression:s,lineage:a}=e,n=`Update Calculated Field ${t}`,i=await this.modelColumnRepository.findOneBy({id:t});if(!i)throw Error("Column not found");let o=await this.modelRepository.findOneBy({id:i.modelId}),{valid:d,message:l}=await this.validateCalculatedFieldNaming(r,i.modelId,t);if(m.debug(`${n}: validateCalculatedFieldNaming: ${d}, ${l}`),!d)throw Error(l);let u=this.generateReferenceNameFromDisplayName(r);m.debug(`${n}: generated referenceName: "${u}"`);let{valid:h,message:p}=await this.checkCalculatedFieldCanQuery(o.id,o.referenceName,{referenceName:u,expression:s,lineage:a});if(m.debug(`${n}: checkCalculatedFieldCanQuery: ${h}`),!h){let e=JSON.parse(p);throw c.Ue(c.GL.INVALID_CALCULATED_FIELD,{customMessage:e?.message,originalError:e})}let E=a[a.length-1],y=await this.inferCalculatedFieldDataType(s,E);return m.debug(`${n}: inferCalculatedFieldDataType: ${y}`),await this.modelColumnRepository.updateOne(t,{displayName:r,sourceColumnName:u,referenceName:u,type:y,aggregation:s,lineage:JSON.stringify(a)})}async updatePrimaryKeys(e){m.debug("start update primary keys");let{id:t}=await this.projectService.getCurrentProject(),r=await this.modelRepository.findAllBy({projectId:t});for(let t of e.filter(e=>e.primaryKey)){let e=r.find(e=>e.sourceTableName===t.tableName);e||m.debug(`Model not found, table name: ${t.tableName}`),await this.modelColumnRepository.setModelPrimaryKey(e.id,t.primaryKey)}}async batchUpdateModelProperties(e){m.debug("start batch update model description");let{id:t}=await this.projectService.getCurrentProject(),r=await this.modelRepository.findAllBy({projectId:t});await Promise.all([e.map(async e=>{let t=r.find(t=>t.sourceTableName===e.tableName);if(!t){m.debug(`Model not found, table name: ${e.tableName}`);return}let s=t.properties?{...JSON.parse(t.properties),...e.properties}:{...e.properties};await this.modelRepository.updateOne(t.id,{displayName:e.properties?.displayName||t.displayName,properties:JSON.stringify(s)})})])}async batchUpdateColumnProperties(e){m.debug("start batch update column description");let{id:t}=await this.projectService.getCurrentProject(),r=await this.modelRepository.findAllBy({projectId:t}),s=await this.modelColumnRepository.findColumnsByModelIds(r.map(e=>e.id)),a=e.reduce((e,t)=>{let r=t.columns?.map(e=>({...e,tableName:t.tableName}));return r&&e.push(...r),e},[]);await Promise.all([a.map(async e=>{if(!e.properties)return;let t=r.find(t=>t.sourceTableName===e.tableName),a=s.find(r=>r.modelId===t.id&&r.sourceColumnName===e.name);if(!a){m.debug(`Column not found, table name: ${e.tableName}, column name: ${e.name}`);return}let n=a.properties?{...JSON.parse(a.properties),...e.properties}:{description:e.description};await this.modelColumnRepository.updateOne(a.id,{properties:JSON.stringify(n)})})])}generateReferenceName(e){let{sourceTableName:t,existedReferenceNames:r}=e;return r.includes(t)?`${t}_${r.length+1}`:t}async saveRelations(e){if(d()(e))return[];let{id:t}=await this.projectService.getCurrentProject(),r=await this.modelRepository.findAllBy({projectId:t}),s=e.map(({fromColumnId:e,toColumnId:t})=>[e,t]).flat(),a=await this.modelColumnRepository.findColumnsByIds(s),n=e.map(e=>{if(!a.find(t=>t.id===e.fromColumnId))throw Error(`Column not found, column Id ${e.fromColumnId}`);if(!a.find(t=>t.id===e.toColumnId))throw Error(`Column not found, column Id  ${e.toColumnId}`);let s=this.generateRelationName(e,r,a);return{projectId:t,name:s,fromColumnId:e.fromColumnId,toColumnId:e.toColumnId,joinType:e.type,properties:e.description?JSON.stringify({description:e.description}):null}});return await this.relationRepository.createMany(n)}async createRelation(e){let{id:t}=await this.projectService.getCurrentProject(),r=[e.fromModelId,e.toModelId],s=await this.modelRepository.findAllByIds(r),a=[e.fromColumnId,e.toColumnId],n=await this.modelColumnRepository.findColumnsByIds(a),{valid:i,message:o}=await this.validateCreateRelation(s,n,e);if(!i)throw Error(o);let d=this.generateRelationName(e,s,n);return await this.relationRepository.createOne({projectId:t,name:d,fromColumnId:e.fromColumnId,toColumnId:e.toColumnId,joinType:e.type})}async updateRelation(e,t){return await this.relationRepository.updateOne(t,{joinType:e.type})}async deleteRelation(e){if(!await this.relationRepository.findOneBy({id:e}))throw Error("Relation not found");let t=await this.getCalculatedFieldByRelation(e);t.length>0&&await this.modelColumnRepository.deleteMany(t.map(e=>e.id)),await this.relationRepository.deleteOne(e)}async getCalculatedFieldByRelation(e){return(await this.modelColumnRepository.findAllBy({isCalculated:!0})).reduce((t,r)=>{let s=JSON.parse(r.lineage);return s.slice(0,s.length-1).includes(e)&&t.push(r),t},[])}async validateCalculatedFieldNaming(e,t,r){let s=(0,a.tZ)(e);if(!s.valid)return{valid:!1,message:s.message||"Invalid Calculated field name"};let n=this.generateReferenceNameFromDisplayName(e),i=await this.modelColumnRepository.findColumnsByModelIds([t]);return(r&&(i=i.filter(e=>e.id!==r)),i.find(e=>e.referenceName===n))?{valid:!1,message:`The generated calculated field name "${n}" is duplicated with existed column, please change the name and try again`}:{valid:!0}}async deleteAllViewsByProjectId(e){await this.viewRepository.deleteAllBy({projectId:e})}async deleteAllModelsByProjectId(e){await this.relationRepository.deleteAllBy({projectId:e}),await this.modelRepository.deleteAllBy({projectId:e})}generateReferenceNameFromDisplayName(e){return(0,a.Z1)(e)}generateRelationName(e,t,r){let s=t.find(t=>t.id===e.fromModelId),a=t.find(t=>t.id===e.toModelId);if(!s||!a)throw Error("Model not found");let n=r.find(t=>t.id===e.fromColumnId),i=r.find(t=>t.id===e.toColumnId);return u()(s.sourceTableName)+u()(n.referenceName)+u()(a.sourceTableName)+u()(i.referenceName)}async inferCalculatedFieldDataType(e,t){let r=null;switch(e){case n.i_.CEIL:case n.i_.FLOOR:case n.i_.ROUND:case n.i_.SIGN:case n.i_.SUM:case n.i_.MAX:case n.i_.MIN:case n.i_.ABS:r=await this.getFieldDataType(t);break;case n.i_.CBRT:case n.i_.EXP:case n.i_.AVG:case n.i_.LN:case n.i_.LOG10:r="DOUBLE";break;case n.i_.COUNT:case n.i_.LENGTH:r="BIGINT";break;case n.i_.REVERSE:r="VARBINARY";break;default:throw Error("Unsupported expression")}return r}async getFieldDataType(e){let t=await this.modelColumnRepository.findOneBy({id:e});if(!t)throw Error("Field not found");return t.type}async checkCalculatedFieldCanQuery(e,t,r){let s=await this.projectService.getCurrentProject(),{mdlBuilder:a}=await this.mdlService.makeCurrentModelMDL(),{referenceName:n,expression:o,lineage:d}=r,l=d[d.length-1],u=await this.inferCalculatedFieldDataType(o,l),c={id:99999999,modelId:e,displayName:n,sourceColumnName:n,referenceName:n,type:u,isCalculated:!0,isPk:!1,notNull:!1,aggregation:o,lineage:JSON.stringify(d),properties:JSON.stringify({description:""})};a.insertCalculatedField(t,c);let p=a.getManifest(),E=p.models.find(e=>e.name===t)?.columns.find(e=>e.name===n);return(m.debug(`Calculated field MDL: ${JSON.stringify(E)}`),s.type===h.ri.DUCKDB)?await this.wrenEngineAdaptor.validateColumnIsValid(p,t,n):await this.queryService.validate(s,i.Y_.COLUMN_IS_VALID,p,{modelName:t,columnName:n})}async validateCreateRelation(e,t,r){let{fromModelId:s,fromColumnId:a,toModelId:n,toColumnId:i}=r,o=e.find(e=>e.id===s),d=e.find(e=>e.id===n);if(!o)return{valid:!1,message:`Model not found: fromModelId ${s}`};if(!d)return{valid:!1,message:`Model not found: toModelId ${n}`};let l=t.find(e=>e.id===a),u=t.find(e=>e.id===i);return l?u?u.modelId!=n?{valid:!1,message:`Column not belong to the model, column Id ${i}`}:l.modelId!=s?{valid:!1,message:`Column not belong to the model, column Id ${a}`}:(await this.relationRepository.findExistedRelationBetweenModels(r)).length>0?{valid:!1,message:"This relationship already exists."}:{valid:!0}:{valid:!1,message:`Column not found, column Id ${i}`}:{valid:!1,message:`Column not found, column Id ${a}`}}}s()}catch(e){s(e)}})},58862:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{Y:()=>g});var a=r(84770),n=r.n(a),i=r(92048),o=r(55315),d=r.n(o),l=r(47104),u=r(75145),c=r(78978),h=r(89179),p=r(89097),m=r(42759),E=e([h,p]);[h,p]=E.then?(await E)():E;let y=(0,m.i)(),R=(0,l.jl)("ProjectService");R.level="debug";let I=new Set(["credentials","password"]);class g{constructor({projectRepository:e,metadataService:t,mdlService:r,wrenAIAdaptor:s,telemetry:a}){this.projectRepository=e,this.metadataService=t,this.mdlService=r,this.wrenAIAdaptor=s,this.projectRecommendQuestionBackgroundTracker=new p.$Y({projectRepository:e,telemetry:a,wrenAIAdaptor:s})}async updateProject(e,t){return await this.projectRepository.updateOne(e,t)}async getProjectDataSourceVersion(e,t){let r=e||(t?await this.getProjectById(t):await this.getCurrentProject());return await this.metadataService.getVersion(r)}async generateProjectRecommendationQuestions(){let e=await this.getCurrentProject();if(!e)throw Error("Project not found");let{manifest:t}=await this.mdlService.makeCurrentModelMDL(),r=await this.wrenAIAdaptor.generateRecommendationQuestions({manifest:t,...this.getProjectRecommendationQuestionsConfig(e)}),s=await this.projectRepository.updateOne(e.id,{queryId:r.queryId,questionsStatus:u.MU.GENERATING,questions:[],questionsError:null});this.projectRecommendQuestionBackgroundTracker.isExist(s)?R.debug(`Generate Project Recommendation Questions Task ${s.id} already exists, skip adding`):this.projectRecommendQuestionBackgroundTracker.addTask(s)}async getProjectRecommendationQuestions(){let e=await this.projectRepository.getCurrentProject();if(!e)throw Error("Project not found");let t={status:h.V_.NOT_STARTED,questions:[],error:null};return e.queryId&&(t.status=e.questionsStatus?h.V_[e.questionsStatus]:t.status,t.questions=e.questions||[],t.error=e.questionsError),t}async getCurrentProject(){return await this.projectRepository.getCurrentProject()}async getProjectById(e){return await this.projectRepository.findOneBy({id:e})}async getProjectDataSourceTables(e,t){let r=e||(t?await this.getProjectById(t):await this.getCurrentProject());return await this.metadataService.listTables(r)}async getProjectSuggestedConstraint(e,t){let r=e||(t?await this.getProjectById(t):await this.getCurrentProject());return await this.metadataService.listConstraints(r)}async createProject(e){let t={displayName:e.displayName,type:e.type,catalog:"wrenai",schema:"public",connectionInfo:(0,c.uZ)(e.type,e.connectionInfo)};return R.debug("Creating project..."),await this.projectRepository.createOne(t)}writeCredentialFile(e,t){i.existsSync(t)||i.mkdirSync(t,{recursive:!0});let r=JSON.stringify(e),s=n().createHash("md5").update(r).digest("hex"),a=d().join(t,`${s}.json`);return i.existsSync(a)?R.debug(`File ${a} already exists`):(i.writeFileSync(a,r),R.debug("Wrote credentials to file")),a}async deleteProject(e){await this.projectRepository.deleteOne(e)}getGeneralConnectionInfo(e){return Object.entries(e.connectionInfo).reduce((e,[t,r])=>(I.has(t)||(e[t]=r),e),{})}getProjectRecommendationQuestionsConfig(e){return{maxCategories:y.projectRecommendationQuestionMaxCategories,maxQuestions:y.projectRecommendationQuestionsMaxQuestions,regenerate:!0,configuration:{language:u.aS[e.language]||u.aS.EN}}}}s()}catch(e){s(e)}})},52834:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{_:()=>c,s:()=>u});var a=r(3547),n=r(68022),i=r(47104),o=r(10827),d=e([n,o]);[n,o]=d.then?(await d)():d;let l=(0,i.jl)("QueryService");l.level="debug";let u=500;class c{constructor({ibisAdaptor:e,wrenEngineAdaptor:t,telemetry:r}){this.ibisAdaptor=e,this.wrenEngineAdaptor=t,this.telemetry=r}async preview(e,t){let{project:r,manifest:s,limit:a,dryRun:n,refresh:i,cacheEnabled:o}=t,{type:d,connectionInfo:u}=r;return this.useEngine(d)?n?(l.debug("Using wren engine to dry run"),await this.wrenEngineAdaptor.dryRun(e,{manifest:s,limit:a}),!0):(l.debug("Using wren engine to preview"),await this.wrenEngineAdaptor.previewData(e,s,a)):(this.checkDataSourceIsSupported(d),l.debug("Use ibis adaptor to preview"),n)?await this.ibisDryRun(e,d,u,s):await this.ibisQuery(e,d,u,s,a,i,o)}async describeStatement(e,t){try{return t.limit=1,{columns:(await this.preview(e,t)).columns}}catch(e){throw l.debug(`Got error when describing statement: ${e.message}`),e}}async validate(e,t,r,s){let{type:a,connectionInfo:n}=e;return await this.ibisAdaptor.validate(a,t,n,r,s)}useEngine(e){return e===a.ri.DUCKDB}checkDataSourceIsSupported(e){if(!Object.prototype.hasOwnProperty.call(n.xZ,e))throw Error(`Unsupported datasource for ibis: "${e}"`)}async ibisDryRun(e,t,r,s){let a=o.MW.IBIS_DRY_RUN;try{let n=await this.ibisAdaptor.dryRun(e,{dataSource:t,connectionInfo:r,mdl:s});return this.sendIbisEvent(a,n,{dataSource:t,sql:e}),{correlationId:n.correlationId}}catch(r){throw this.sendIbisFailedEvent(a,r,{dataSource:t,sql:e}),r}}async ibisQuery(e,t,r,s,a,n,i){let d=o.MW.IBIS_QUERY;try{let o=await this.ibisAdaptor.query(e,{dataSource:t,connectionInfo:r,mdl:s,limit:a,refresh:n,cacheEnabled:i});this.sendIbisEvent(d,o,{dataSource:t,sql:e});let l=this.transformDataType(o);return{correlationId:o.correlationId,cacheHit:o.cacheHit,cacheCreatedAt:o.cacheCreatedAt,cacheOverrodeAt:o.cacheOverrodeAt,override:o.override,...l}}catch(r){throw this.sendIbisFailedEvent(d,r,{dataSource:t,sql:e}),r}}transformDataType(e){let t=e.columns,r=e.dtypes;return{columns:t.map(e=>{let t="unknown";return r&&r[e]&&(t="object"===r[e]?"string":r[e]),"unknown"===t&&(l.debug(`Did not find type mapping for "${e}"`),l.debug(`dtypes mapping: ${r?JSON.stringify(r,null,2):"undefined"} `)),{name:e,type:t}}),data:e.data}}sendIbisEvent(e,t,r){this.telemetry.sendEvent(e,{correlationId:t.correlationId,processTime:t.processTime,...r})}sendIbisFailedEvent(e,t,r){this.telemetry.sendEvent(e,{correlationId:t.extensions?.other?.correlationId,processTime:t.extensions?.other?.processTime,error:t.message,...r},t.extensions?.service,!1)}}s()}catch(e){s(e)}})},30309:(e,t,r)=>{r.d(t,{o:()=>u});var s=r(47104),a=r(90221),n=r.n(a),i=r(24634),o=r(75145),d=r(3547);let l=(0,s.jl)("SqlPairService");class u{constructor({sqlPairRepository:e,wrenAIAdaptor:t,ibisAdaptor:r}){this.sqlPairRepository=e,this.wrenAIAdaptor=t,this.ibisAdaptor=r}async modelSubstitute(e,t){let{manifest:r,project:s}=t,{type:a,connectionInfo:n}=s;if(a===d.ri.DUCKDB)throw i.Ue(i.GL.IBIS_SERVER_ERROR,{customMessage:"DuckDB data source does not support model substitute."});let o=r.models?.[0],l=o?.tableReference?.catalog,u=o?.tableReference?.schema;return await this.ibisAdaptor.modelSubstitute(e,{dataSource:a,connectionInfo:n,mdl:r,catalog:l,schema:u})}async generateQuestions(e,t){try{let r={language:o.aS[e.language]||o.aS.EN},{queryId:s}=await this.wrenAIAdaptor.generateQuestions({projectId:e.id,configurations:r,sqls:t}),a=await this.waitQuestionGenerateResult(s);if(a.error)throw i.Ue(i.GL.GENERATE_QUESTIONS_ERROR,{customMessage:a.error.message});return a.questions}catch(e){throw i.Ue(i.GL.GENERATE_QUESTIONS_ERROR,{customMessage:e.message})}}async getProjectSqlPairs(e){return this.sqlPairRepository.findAllBy({projectId:e})}async createSqlPair(e,t){let r=await this.sqlPairRepository.transaction();try{let s=await this.sqlPairRepository.createOne({...t,projectId:e},{tx:r}),{queryId:a}=await this.wrenAIAdaptor.deploySqlPair(e,s),n=await this.waitUntilSqlPairResult(a);if(n.error)throw i.Ue(n.error.code,{customMessage:n.error.message});return await r.commit(),s}catch(e){throw await r.rollback(),e}}async createSqlPairs(e,t){let r=await this.sqlPairRepository.transaction(),s=await this.sqlPairRepository.createMany(t.map(t=>({...t,projectId:e})),{tx:r}),a=[],o=[];for(let t of n()(s,10))await Promise.allSettled(t.map(async t=>{let{queryId:r}=await this.wrenAIAdaptor.deploySqlPair(e,t),s=await this.waitUntilSqlPairResult(r);s.error&&o.push(s.error),a.push(s)})).then(async t=>{if(o.length>0)throw l.debug(`deploy sql pair failed. ${o.map(e=>e.question).join(", ")}`),await r.rollback(),await this.wrenAIAdaptor.deleteSqlPairs(e,a.map(e=>e.id)),i.Ue(i.GL.DEPLOY_SQL_PAIR_ERROR,{customMessage:o.map(e=>e.message).join(", ")})});return await r.commit(),s}async editSqlPair(e,t,r){let s=await this.sqlPairRepository.findOneBy({id:t,projectId:e});if(!s)throw Error(`SQL pair with ID ${t} not found in project ${e}`);let a={sql:s.sql,question:s.question,updatedAt:new Date().toISOString()};void 0!==r.sql&&(a.sql=r.sql),void 0!==r.question&&(a.question=r.question);let n=await this.sqlPairRepository.transaction();try{let r=await this.sqlPairRepository.updateOne(t,a,{tx:n}),{queryId:s}=await this.wrenAIAdaptor.deploySqlPair(e,r),o=await this.waitUntilSqlPairResult(s);if(o.error)throw i.Ue(i.GL.DEPLOY_SQL_PAIR_ERROR,{customMessage:o.error.message});return await n.commit(),r}catch(e){throw l.error(`edit sql pair failed. ${e}`),await n.rollback(),i.Ue(i.GL.DEPLOY_SQL_PAIR_ERROR,{customMessage:e.message})}}async deleteSqlPair(e,t){if(!await this.sqlPairRepository.findOneBy({id:t,projectId:e}))throw Error(`SQL pair with ID ${t} not found in project ${e}`);let r=await this.sqlPairRepository.transaction();try{return await this.sqlPairRepository.deleteOne(t,{tx:r}),await this.wrenAIAdaptor.deleteSqlPairs(e,[t]),await r.commit(),!0}catch(e){throw l.error(`delete sql pair failed. ${e}`),await r.rollback(),i.Ue(i.GL.DEPLOY_SQL_PAIR_ERROR,{customMessage:e.message})}}async waitUntilSqlPairResult(e){let t=await this.wrenAIAdaptor.getSqlPairResult(e);for(;!this.isFinishedState(t.status);)await new Promise(e=>setTimeout(e,500)),t=await this.wrenAIAdaptor.getSqlPairResult(e);return t}async waitQuestionGenerateResult(e){let t=await this.wrenAIAdaptor.getQuestionsResult(e);for(;![o.HA.SUCCEEDED,o.HA.FAILED].includes(t.status);)await new Promise(e=>setTimeout(e,500)),t=await this.wrenAIAdaptor.getQuestionsResult(e);return t}isFinishedState(e){return[o.Fs.FINISHED,o.Fs.FAILED].includes(e)}}},10827:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{MF:()=>c,MW:()=>a,Mk:()=>w,ic:()=>n});var a,n,i=r(42759),o=r(68122),d=r(46555),l=r(47104),u=e([o,d]);[o,d]=u.then?(await u)():u;let h=(0,l.jl)("TELEMETRY");h.level="debug";let{userUUID:p,telemetryEnabled:m,wrenAIVersion:E,generationModel:y,wrenEngineVersion:R,wrenUIVersion:I,posthogApiKey:g,posthogHost:f}=(0,i.i)();(function(e){e.CONNECTION_START_SAMPLE_DATASET="connection_start_sample_dataset",e.CONNECTION_SAVE_DATA_SOURCE="connection_save_data_source",e.CONNECTION_SAVE_TABLES="connection_save_tables",e.CONNECTION_SAVE_RELATION="connection_save_relation",e.MODELING_DEPLOY_MDL="modeling_deploy_mdl",e.MODELING_CREATE_MODEL="modeling_create_model",e.MODELING_UPDATE_MODEL="modeling_update_model",e.MODELING_CREATE_CF="modeling_create_cf",e.MODELING_UPDATE_CF="modeling_update_cf",e.MODELING_UPDATE_MODEL_METADATA="modeling_update_model_metadata",e.MODELING_UPDATE_VIEW_METADATA="modeling_update_view_metadata",e.MODELING_CREATE_RELATION="modeling_create_relation",e.MODELING_UPDATE_RELATION="modeling_update_relation",e.MODELING_DETECT_SCHEMA_CHANGE="modeling_detect_schema_change",e.MODELING_RESOLVE_SCHEMA_CHANGE="modeling_resolve_schema_change",e.HOME_UPDATE_THREAD_SUMMARY="update_thread_summary",e.HOME_ASK_CANDIDATE="home_ask_candidate",e.HOME_CREATE_THREAD="home_create_thread",e.HOME_ANSWER_QUESTION="home_answer_question",e.HOME_ANSWER_QUESTION_INTERRUPTED="home_answer_question_interrupted",e.HOME_ANSWER_BREAKDOWN="home_answer_breakdown",e.HOME_ANSWER_CHART="home_answer_chart",e.HOME_ANSWER_ADJUST_CHART="home_answer_adjust_chart",e.HOME_ASK_FOLLOWUP_QUESTION="home_ask_followup_question",e.HOME_CANCEL_ASK="home_cancel_ask",e.HOME_RERUN_ASKING_TASK="home_rerun_asking_task",e.HOME_GENERATE_PROJECT_RECOMMENDATION_QUESTIONS="home_generate_project_recommendation_questions",e.HOME_GENERATE_THREAD_RECOMMENDATION_QUESTIONS="home_generate_thread_recommendation_questions",e.HOME_ADJUST_THREAD_RESPONSE="home_adjust_thread_response",e.HOME_ADJUST_THREAD_RESPONSE_CANCEL="home_adjust_thread_response_cancel",e.HOME_ADJUST_THREAD_RESPONSE_RERUN="home_adjust_thread_response_rerun",e.HOME_ADJUST_THREAD_RESPONSE_WITH_SQL="home_adjust_thread_response_with_sql",e.HOME_CREATE_VIEW="home_create_view",e.HOME_PREVIEW_ANSWER="home_preview_answer",e.SETTING_RESET_PROJECT="setting_reset_project",e.IBIS_DRY_RUN="ibis_dry_run",e.IBIS_QUERY="ibis_query",e.GRAPHQL_ERROR="graphql_error",e.KNOWLEDGE_CREATE_INSTRUCTION="knowledge_create_instruction",e.KNOWLEDGE_UPDATE_INSTRUCTION="knowledge_update_instruction",e.KNOWLEDGE_DELETE_INSTRUCTION="knowledge_delete_instruction",e.KNOWLEDGE_CREATE_SQL_PAIR="knowledge_create_sql_pair",e.KNOWLEDGE_UPDATE_SQL_PAIR="knowledge_update_sql_pair",e.KNOWLEDGE_DELETE_SQL_PAIR="knowledge_delete_sql_pair"})(a||(a={})),function(e){e.BE="BE",e.AI="AI",e.ENGINE="ENGINE",e.UNKNOWN="UNKNOWN"}(n||(n={}));class w{constructor(){if(m){if(!g){h.debug("Telemetry enabled but posthogApiKey not provided.");return}let e=new o.PostHog(g,{host:f||"https://us.posthog.com"});this.posthog=e,this.userId=p||(0,d.v4)(),h.info(`Telemetry initialized: ${this.userId}`);return}h.info("Telemetry not enabled.")}async sendEvent(e,t={},r="UNKNOWN",s=!0){if(!this.posthog)return;let a=s?`${e}_success`:`${e}_failed`;try{console.log("sendEvent",a,t,r,s);let e=this.collectSystemInfo();this.posthog.capture({distinctId:this.userId,event:a,properties:{...e,...t,wren_service:r}})}catch(e){h.error(e)}}collectSystemInfo(){return{"wren-ui-version":I||null,"wren-engine-version":R||null,"wren-ai-service-version":E||null,"generation-model":y||null,node_version:process.version,node_platform:process.platform,node_arch:process.arch,memory_usage:process.memoryUsage(),cpu_usage:process.cpuUsage()}}stop(){this.posthog&&this.posthog.shutdown()}}function c(e){return function(t,r,s){let a=s.value;return s.value=async function(...t){let[,,r]=t,s=t[1]?.data||t[1];try{let n=await a.apply(this,t);return r.telemetry.sendEvent(e,{data:s}),n}catch(t){throw r.telemetry.sendEvent(e,{data:s,error:t.message},t.extensions?.service,!1),t}},s}}s()}catch(e){s(e)}})},3547:(e,t,r)=>{var s,a,n,i;r.d(t,{ri:()=>s,Jq:()=>n,uT:()=>a}),function(e){e.BIG_QUERY="BIG_QUERY",e.DUCKDB="DUCKDB",e.POSTGRES="POSTGRES",e.MYSQL="MYSQL",e.MSSQL="MSSQL",e.CLICK_HOUSE="CLICK_HOUSE",e.TRINO="TRINO",e.SNOWFLAKE="SNOWFLAKE"}(s||(s={})),function(e){e.ONE_TO_ONE="ONE_TO_ONE",e.ONE_TO_MANY="ONE_TO_MANY",e.MANY_TO_ONE="MANY_TO_ONE"}(a||(a={})),function(e){e.MODEL="MODEL",e.VIEW="VIEW",e.RELATION="RELATION",e.FIELD="FIELD",e.CALCULATED_FIELD="CALCULATED_FIELD"}(n||(n={})),function(e){e.TABLE="TABLE",e.METRIC="METRIC"}(i||(i={}))},24634:(e,t,r)=>{r.d(t,{EJ:()=>d,GL:()=>s,Qj:()=>n,Ue:()=>o,j5:()=>i});var s,a=r(57343);!function(e){e.INTERNAL_SERVER_ERROR="INTERNAL_SERVER_ERROR",e.NO_RELEVANT_DATA="NO_RELEVANT_DATA",e.NO_RELEVANT_SQL="NO_RELEVANT_SQL",e.RESOURCE_NOT_FOUND="RESOURCE_NOT_FOUND",e.MDL_PARSE_ERROR="MDL_PARSE_ERROR",e.NO_CHART="NO_CHART",e.AI_SERVICE_UNDEFINED_ERROR="OTHERS",e.IBIS_SERVER_ERROR="IBIS_SERVER_ERROR",e.CONNECTION_ERROR="CONNECTION_ERROR",e.INIT_SQL_ERROR="INIT_SQL_ERROR",e.SESSION_PROPS_ERROR="SESSION_PROPS_ERROR",e.CONNECTION_REFUSED="CONNECTION_REFUSED",e.DUPLICATED_FIELD_NAME="DUPLICATED_FIELD_NAME",e.INVALID_EXPRESSION="INVALID_EXPRESSION",e.INVALID_CALCULATED_FIELD="INVALID_CALCULATED_FIELD",e.INVALID_VIEW_CREATION="INVALID_VIEW_CREATION",e.DRY_RUN_ERROR="DRY_RUN_ERROR",e.DRY_PLAN_ERROR="DRY_PLAN_ERROR",e.DEPLOY_SQL_PAIR_ERROR="DEPLOY_SQL_PAIR_ERROR",e.GENERATE_QUESTIONS_ERROR="GENERATE_QUESTIONS_ERROR",e.INVALID_SQL_ERROR="INVALID_SQL_ERROR",e.WREN_ENGINE_ERROR="WREN_ENGINE_ERROR",e.IDENTIED_AS_GENERAL="IDENTIED_AS_GENERAL",e.IDENTIED_AS_MISLEADING_QUERY="IDENTIED_AS_MISLEADING_QUERY",e.DEPLOY_TIMEOUT_ERROR="DEPLOY_TIMEOUT_ERROR",e.NON_SQL_QUERY="NON_SQL_QUERY",e.NO_DEPLOYMENT_FOUND="NO_DEPLOYMENT_FOUND",e.FAILED_TO_GENERATE_VEGA_SCHEMA="FAILED_TO_GENERATE_VEGA_SCHEMA",e.POLLING_TIMEOUT="POLLING_TIMEOUT"}(s||(s={}));let n={INTERNAL_SERVER_ERROR:"Internal server error",NO_RELEVANT_DATA:"I can't find the exact data you're looking for, but feel free to ask about other available topics.",NO_RELEVANT_SQL:"Could you please provide more details or specify the information you're seeking?",NO_CHART:"The chart couldn't be generated this time. Please try regenerating the chart or rephrasing your question for better results.",CONNECTION_ERROR:"Can not connect to data source",INIT_SQL_ERROR:"The initializing SQL seems to be invalid, Please check your SQL and try again.",SESSION_PROPS_ERROR:"The session properties seem to be invalid, Please check your session properties and try again.",CONNECTION_REFUSED:"Connection refused by the server, Please check your connection settings and try again.",IBIS_SERVER_ERROR:"Error occurred while querying ibis server, please try again later.",DUPLICATED_FIELD_NAME:"This field name already exists",INVALID_EXPRESSION:"Invalid expression, please check your expression and try again.",INVALID_CALCULATED_FIELD:"Can not execute a query when using this calculated field",INVALID_VIEW_CREATION:"Invalid view creation",DRY_RUN_ERROR:"Dry run sql statement error",DRY_PLAN_ERROR:"Dry plan error",DEPLOY_SQL_PAIR_ERROR:"Deploy sql pair error",GENERATE_QUESTIONS_ERROR:"Generate questions error",INVALID_SQL_ERROR:"Invalid SQL, please check your SQL syntax",IDENTIED_AS_GENERAL:"The question is identified as a general question, please follow-up ask with more specific questions.",IDENTIED_AS_MISLEADING_QUERY:"The question is identified as a misleading query, please follow-up ask with more specific questions.",DEPLOY_TIMEOUT_ERROR:"LLM deployment timed out after 30 seconds",NON_SQL_QUERY:"Cannot generate SQL from this question.",NO_DEPLOYMENT_FOUND:"No deployment found, please deploy your project first",FAILED_TO_GENERATE_VEGA_SCHEMA:"Failed to generate Vega spec",POLLING_TIMEOUT:"Polling timeout"},i={INTERNAL_SERVER_ERROR:"Internal server error",NO_RELEVANT_DATA:"Try a different query",NO_RELEVANT_SQL:"Clarification needed",NO_CHART:"Chart not available",CONNECTION_ERROR:"Failed to connect",IBIS_SERVER_ERROR:"Data connection error",INIT_SQL_ERROR:"Invalid initializing SQL",SESSION_PROPS_ERROR:"Invalid session properties",CONNECTION_REFUSED:"Connection refused",DUPLICATED_FIELD_NAME:"Duplicated field name",INVALID_EXPRESSION:"Invalid expression",INVALID_CALCULATED_FIELD:"Invalid calculated field",INVALID_VIEW_CREATION:"Invalid view creation",DRY_RUN_ERROR:"Dry run sql statement error",DRY_PLAN_ERROR:"Dry plan error",DEPLOY_SQL_PAIR_ERROR:"Deploy sql pair error",GENERATE_QUESTIONS_ERROR:"Generate questions error",INVALID_SQL_ERROR:"Invalid SQL, please check your SQL syntax",IDENTIED_AS_GENERAL:"Identified as general question",IDENTIED_AS_MISLEADING_QUERY:"Identified as misleading query",DEPLOY_TIMEOUT_ERROR:"LLM deployment timed out",NON_SQL_QUERY:"Cannot generate SQL from this question.",NO_DEPLOYMENT_FOUND:"No deployment found, please deploy your project first",FAILED_TO_GENERATE_VEGA_SCHEMA:"Failed to generate Vega spec",POLLING_TIMEOUT:"Polling timeout"},o=(e,t)=>{let{customMessage:r,originalError:s,service:o}=t||{};e=e||"INTERNAL_SERVER_ERROR";let d=r||s?.message||n[e]||n.INTERNAL_SERVER_ERROR;return new a.GraphQLError(d,{extensions:{originalError:s,code:e,message:d,service:o,shortMessage:i[e]||i.INTERNAL_SERVER_ERROR,other:t?.other}})},d=e=>{if(e instanceof a.GraphQLError){let t=e.extensions?.code||"INTERNAL_SERVER_ERROR";return{locations:e.locations,path:e.path,message:e.message,extensions:{code:t,message:e.message,shortMessage:i[t],stacktrace:e.extensions?.exception?.stacktrace}}}return e}},47104:(e,t,r)=>{r.d(t,{i6:()=>i,pK:()=>c,jl:()=>s.j,jC:()=>l.jC,Z1:()=>h.Z,Ub:()=>l.Ub,as:()=>u,P5:()=>d,e3:()=>l.e3,fy:()=>o,tZ:()=>h.t});var s=r(11774),a=r(84770),n=r.n(a);class i{constructor({encryptionPassword:e,encryptionSalt:t}){this.ENCRYPTION_ITERATION=1e3,this.ENCRYPTION_KEY_LENGTH=32,this.ENCRYPTION_ALGORITHM="aes-256-cbc",this.ENCRYPTION_SEPARATOR=":",this.ENCRYPTION_PASSWORD=e,this.ENCRYPTION_SALT=t}encrypt(e){let t=JSON.stringify(e),r=this.createSecretKey(),s=n().randomBytes(16),a=n().createCipheriv(this.ENCRYPTION_ALGORITHM,r,s),i=Buffer.concat([a.update(t,"utf8"),a.final()]);return s.toString("base64")+this.ENCRYPTION_SEPARATOR+i.toString("base64")}decrypt(e){let[t,r]=e.split(this.ENCRYPTION_SEPARATOR),s=Buffer.from(t,"base64"),a=Buffer.from(r,"base64"),i=this.createSecretKey(),o=n().createDecipheriv(this.ENCRYPTION_ALGORITHM,i,s);return Buffer.concat([o.update(a),o.final()]).toString("utf8")}createSecretKey(){return n().pbkdf2Sync(this.ENCRYPTION_PASSWORD,this.ENCRYPTION_SALT,this.ENCRYPTION_ITERATION,this.ENCRYPTION_KEY_LENGTH,"sha512")}}let o=e=>e.replace(/^\s+|\s+$/g,""),d=e=>{if("localhost"===e||"127.0.0.1"===e)switch(process.platform){case"darwin":return"docker.for.mac.localhost";case"linux":return"docker.for.linux.localhost";default:return"host.docker.internal"}return e};var l=r(9228);let u=e=>{try{return JSON.parse(e)}catch(e){return!1}},c=e=>e.type.includes("STRUCT")?"RECORD":e.type;var h=r(68467)},2270:(e,t,r)=>{r.d(t,{K:()=>s});let s=e=>{if("pg"!==e.dbType)return console.log("using sqlite"),r(40514)({client:"better-sqlite3",connection:{filename:e.sqliteFile},useNullAsDefault:!0});{let{pgUrl:t,debug:s}=e;return console.log("using pg"),r(40514)({client:"pg",connection:t,debug:s,pool:{min:2,max:10}})}}},11774:(e,t,r)=>{r.d(t,{j:()=>s.getLogger});var s=r(98094)},9228:(e,t,r)=>{r.d(t,{ND:()=>d,Ub:()=>i,Yo:()=>a,e3:()=>n,jC:()=>function e(t,r){if(!t.nestedColumns)return[];let s=[];for(let a of t.nestedColumns){let t=[...r.columnPath||[r.sourceColumnName],a.name.split(`${r.sourceColumnName}.`)[1]],i={modelId:r.modelId,columnId:r.columnId,columnPath:t,displayName:a.name,sourceColumnName:a.name,referenceName:t.map(n).join("."),type:a.type||"string",properties:a.properties};s.push(i),s.push(...e(a,{modelId:r.modelId,columnId:r.columnId,sourceColumnName:a.name,columnPath:t}))}return s},t8:()=>o});var s=r(68467);function a(e){return 0===e.length?"*":e.map(e=>`"${e.referenceName}"`).join(",")}function n(e){let t=(0,s.Z)(e);return/^[A-Za-z]/.test(t)||(t=`col_${t}`),t}function i(e){return e.replace(/\./g,"_")}function o(e,t,r){let s=t.map(({id:t,sourceColumnName:r})=>e.includes(r)?void 0:t).filter(e=>e),a=t.map(({sourceColumnName:e})=>e);return{toDeleteColumnIds:s,toCreateColumns:e.filter(e=>!a.includes(e)),toUpdateColumns:r.reduce((r,s)=>{let a=t.find(e=>e.sourceColumnName===s.name);return a&&e.find(e=>e===s.name)&&s.type!==a.type?[...r,{id:a.id,sourceColumnName:s.name,type:s.type||"string"}]:r},[])}}async function d(e,t,r){await e.resetModelPrimaryKey(t),r&&await e.setModelPrimaryKey(t,r)}},68467:(e,t,r)=>{function s(e){let t=null,r=!0;return/^[A-Za-z0-9 !@#$%^&*()_+{}[\],.'"-]*$/.test(e)||(r=!1,t="Only space & [  a-z, A-Z, 0-9, _, -, !@#$%^&*()-+{}[]'\".,  ] are allowed."),/^[A-Za-z]/.test(e)||(r=!1,t="Must start with a letter."),{valid:r,message:t}}function a(e){return e.replace(/[!@#$%^&*()+{}[\]'",. -]/g,"_")}r.d(t,{Z:()=>a,t:()=>s})},85913:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{components:()=>m});var a=r(42759),n=r(2270),i=r(12332),o=r(39698),d=r(83372),l=r(10827),u=r(89097),c=r(30309),h=e([o,d,l,u]);[o,d,l,u]=h.then?(await h)():h;let p=(0,a.i)(),m=(()=>{let e=new l.Mk,t=(0,n.K)({dbType:p.dbType,pgUrl:p.pgUrl,debug:p.debug,sqliteFile:p.sqliteFile}),r=new i.ZC(t),s=new i.hz(t),a=new i.fs(t),h=new i.Fu(t),m=new i.vE(t),E=new i.Sl(t),y=new i.H0(t),R=new i.N3(t),I=new i.wp(t),g=new i.V$(t),f=new i.vq(t),w=new i.Tp(t),_=new i.Fk(t),N=new i.R(t),S=new i.Bq(t),A=new i.W(t),T=new i.lg(t),D=new i.Wq(t),O=new o.YC({wrenEngineEndpoint:p.wrenEngineEndpoint}),b=new o.o7({wrenAIBaseEndpoint:p.wrenAIEndpoint}),C=new o.HC({ibisServerEndpoint:p.ibisServerEndpoint}),k=new d.tj({ibisAdaptor:C,wrenEngineAdaptor:O}),v=new d._7({ibisAdaptor:C,wrenEngineAdaptor:O,telemetry:e}),L=new d.v2({wrenAIAdaptor:b,deployLogRepository:s,telemetry:e}),q=new d.Tw({projectRepository:r,modelRepository:E,modelColumnRepository:y,modelNestedColumnRepository:R,relationRepository:I,viewRepository:m}),P=new d.Y4({projectRepository:r,metadataService:k,mdlService:q,wrenAIAdaptor:b,telemetry:e}),M=new d.Xi({wrenAIAdaptor:b,askingTaskRepository:S,threadResponseRepository:h,viewRepository:m}),$=new d.xH({telemetry:e,wrenAIAdaptor:b,deployService:L,projectService:P,viewRepository:m,threadRepository:a,threadResponseRepository:h,queryService:v,mdlService:q,askingTaskTracker:M,askingTaskRepository:S}),U=new d.sp({projectService:P,dashboardItemRepository:_,dashboardRepository:w}),B=new c.o({sqlPairRepository:N,wrenAIAdaptor:b,ibisAdaptor:C}),j=new d.od({instructionRepository:A,wrenAIAdaptor:b}),G=new u.$Y({telemetry:e,wrenAIAdaptor:b,projectRepository:r}),F=new u.Ef({telemetry:e,wrenAIAdaptor:b,threadRepository:a}),x=new u.$m({dashboardRepository:w,dashboardItemRepository:_,dashboardItemRefreshJobRepository:D,projectService:P,deployService:L,queryService:v});return{knex:t,telemetry:e,projectRepository:r,deployLogRepository:s,threadRepository:a,threadResponseRepository:h,viewRepository:m,modelRepository:E,modelColumnRepository:y,relationRepository:I,schemaChangeRepository:g,learningRepository:f,modelNestedColumnRepository:R,dashboardRepository:w,dashboardItemRepository:_,sqlPairRepository:N,askingTaskRepository:S,apiHistoryRepository:T,instructionRepository:A,dashboardItemRefreshJobRepository:D,wrenEngineAdaptor:O,wrenAIAdaptor:b,ibisAdaptor:C,metadataService:k,projectService:P,queryService:v,deployService:L,askingService:$,mdlService:q,dashboardService:U,sqlPairService:B,instructionService:j,askingTaskTracker:M,projectRecommendQuestionBackgroundTracker:G,threadRecommendQuestionBackgroundTracker:F,dashboardCacheBackgroundTracker:x}})();s()}catch(e){s(e)}})},47153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},71802:(e,t,r)=>{e.exports=r(20145)}};