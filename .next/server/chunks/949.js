"use strict";exports.id=949,exports.ids=[949],exports.modules={42658:(e,a,t)=>{t.d(a,{II:()=>c.a,ZT:()=>x.a,bZ:()=>l.a,l0:()=>i.a,u_:()=>p.a,zx:()=>s.a});var r=t(27889),l=t.n(r),n=t(53800),s=t.n(n),o=t(16190),i=t.n(o),d=t(30675),c=t.n(d),m=t(76418),p=t.n(m),u=t(58168),x=t.n(u)},68117:(e,a,t)=>{t.d(a,{bZ:()=>l.a,l0:()=>s.a,u_:()=>i.a});var r=t(27889),l=t.n(r),n=t(16190),s=t.n(n),o=t(76418),i=t.n(o)},92076:(e,a,t)=>{t.d(a,{Typography:()=>s.a,z:()=>l.a});var r=t(53800),l=t.n(r),n=t(58168),s=t.n(n)},17209:(e,a,t)=>{t.d(a,{i:()=>l.a});var r=t(74285),l=t.n(r)},62018:(e,a,t)=>{t.d(a,{Typography:()=>l.a});var r=t(58168),l=t.n(r)},98666:(e,a,t)=>{t.d(a,{st:()=>c});var r=t(29114);let l={},n=(0,r.gql)`
    fragment ViewField on DiagramViewField {
  id
  displayName
  referenceName
  type
  nodeType
  description
}
    `,s=(0,r.gql)`
    fragment RelationField on DiagramModelRelationField {
  id
  relationId
  type
  nodeType
  displayName
  referenceName
  fromModelId
  fromModelName
  fromModelDisplayName
  fromColumnId
  fromColumnName
  fromColumnDisplayName
  toModelId
  toModelName
  toModelDisplayName
  toColumnId
  toColumnName
  toColumnDisplayName
  description
}
    `,o=(0,r.gql)`
    fragment NestedField on DiagramModelNestedField {
  id
  nestedColumnId
  columnPath
  type
  displayName
  referenceName
  description
}
    `,i=(0,r.gql)`
    fragment Field on DiagramModelField {
  id
  columnId
  type
  nodeType
  displayName
  referenceName
  description
  isPrimaryKey
  expression
  aggregation
  lineage
  nestedFields {
    ...NestedField
  }
}
    ${o}`,d=(0,r.gql)`
    query Diagram {
  diagram {
    models {
      id
      modelId
      nodeType
      displayName
      referenceName
      sourceTableName
      refSql
      cached
      refreshTime
      description
      fields {
        ...Field
      }
      calculatedFields {
        ...Field
      }
      relationFields {
        ...RelationField
      }
    }
    views {
      id
      viewId
      nodeType
      displayName
      description
      referenceName
      statement
      fields {
        ...ViewField
      }
    }
  }
}
    ${i}
${s}
${n}`;function c(e){let a={...l,...e};return r.useQuery(d,a)}},19978:(e,a,t)=>{t.d(a,{QC:()=>i,ZZ:()=>c,Zg:()=>s});var r=t(29114);let l={},n=(0,r.gql)`
    mutation PreviewSQL($data: PreviewSQLDataInput!) {
  previewSql(data: $data)
}
    `;function s(e){let a={...l,...e};return r.useMutation(n,a)}let o=(0,r.gql)`
    mutation GenerateQuestion($data: GenerateQuestionInput!) {
  generateQuestion(data: $data)
}
    `;function i(e){let a={...l,...e};return r.useMutation(o,a)}let d=(0,r.gql)`
    mutation ModelSubstitute($data: ModelSubstituteInput!) {
  modelSubstitute(data: $data)
}
    `;function c(e){let a={...l,...e};return r.useMutation(d,a)}},58855:(e,a,t)=>{t.d(a,{$i:()=>m,Gd:()=>o,Nz:()=>d,jn:()=>u});var r=t(29114);let l={},n=(0,r.gql)`
    fragment SqlPair on SqlPair {
  id
  projectId
  sql
  question
  createdAt
  updatedAt
}
    `,s=(0,r.gql)`
    query SqlPairs {
  sqlPairs {
    ...SqlPair
  }
}
    ${n}`;function o(e){let a={...l,...e};return r.useQuery(s,a)}let i=(0,r.gql)`
    mutation CreateSqlPair($data: CreateSqlPairInput!) {
  createSqlPair(data: $data) {
    ...SqlPair
  }
}
    ${n}`;function d(e){let a={...l,...e};return r.useMutation(i,a)}let c=(0,r.gql)`
    mutation UpdateSqlPair($where: SqlPairWhereUniqueInput!, $data: UpdateSqlPairInput!) {
  updateSqlPair(where: $where, data: $data) {
    ...SqlPair
  }
}
    ${n}`;function m(e){let a={...l,...e};return r.useMutation(c,a)}let p=(0,r.gql)`
    mutation DeleteSqlPair($where: SqlPairWhereUniqueInput!) {
  deleteSqlPair(where: $where)
}
    `;function u(e){let a={...l,...e};return r.useMutation(p,a)}},95308:(e,a,t)=>{t.d(a,{Z:()=>p});var r=t(20997),l=t(16689),n=t(58633),s=t.n(n),o=t(57518),i=t.n(o),d=t(74989),c=t.n(d);let m=i()(s()).withConfig({displayName:"ErrorCollapse__StyledCollapse",componentId:"sc-96b0dd67-0"})([".ant-collapse-item{> .ant-collapse-header{user-select:none;color:var(--gray-7);padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;.ant-collapse-arrow{margin-right:8px;}}> .ant-collapse-content .ant-collapse-content-box{color:var(--gray-7);padding:4px 0 0 0;}}"]);function p(e){let{message:a,className:t,defaultActive:n}=e,[o,i]=(0,l.useState)(n?["1"]:[]);return r.jsx(m,{className:t,ghost:!0,activeKey:o,onChange:e=>i(e),expandIcon:({isActive:e})=>r.jsx(c(),{rotate:e?90:0}),children:r.jsx(s().Panel,{header:"Show error messages",children:r.jsx("pre",{className:"text-sm mb-0 pl-5",style:{whiteSpace:"pre-wrap"},children:a})},"1")})}},87208:(e,a,t)=>{t.d(a,{iD:()=>y});var r=t(20997),l=t(16689),n=t(57518),s=t.n(n),o=t(92076),i=t(88290),d=t.n(i),c=t(19350),m=t.n(c),p=t(58287);t(49051);let u=s().div.withConfig({displayName:"BaseCodeBlock__Block",componentId:"sc-b28653c8-0"})(["position:relative;white-space:pre;font-size:13px;border:1px var(--gray-4) solid;border-radius:4px;font-family:'Source Code Pro',monospace;user-select:text;cursor:text;&:focus{outline:none;}"," .adm-code-wrap{"," "," user-select:text;}.adm-code-line{display:block;user-select:text;&-number{user-select:none;display:inline-block;min-width:14px;text-align:right;margin-right:1em;color:var(--gray-6);font-weight:700;font-size:12px;}}"],e=>e.inline?`
      display: inline;
      border: none;
      background: transparent !important;
      padding: 0;
      * { display: inline !important; }
    `:`
    background: ${e.backgroundColor||"var(--gray-1)"} !important;
    padding: 8px;
  `,e=>e.inline?"":"overflow: auto;",e=>e.maxHeight?`max-height: ${e.maxHeight}px;`:""),x=s()(o.Typography.Text).withConfig({displayName:"BaseCodeBlock__CopyText",componentId:"sc-b28653c8-1"})(["position:absolute;top:0;right:",";font-size:0;button{background:var(--gray-1) !important;}.ant-typography-copy{font-size:12px;}.ant-btn:not(:hover){color:var(--gray-8);}"],e=>e.$hasVScrollbar?"20px":"0"),g=e=>{let a="ace-tomorrow";if(!document.getElementById(a)){let t=document.createElement("style");t.id=a,document.head.appendChild(t),t.appendChild(document.createTextNode(e))}},y=e=>function(a){let{code:t,copyable:n,maxHeight:s,inline:i,loading:c,showLineNumbers:y,backgroundColor:h,onCopy:b}=a,{ace:f}=window,{Tokenizer:j}=f.require("ace/tokenizer"),I=new j(new e().getRules()),S=(0,l.useRef)(null),[N,v]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let{cssText:e}=f.require("ace/theme/tomorrow");g(e)},[]),(0,l.useEffect)(()=>{let e=S.current;e&&v(e.scrollHeight>e.clientHeight)},[t]);let w=(t||"").split("\n").map((e,a)=>{let t=I.getLineTokens(e).tokens.map((e,a)=>{let t=e.type.split(".").map(e=>`ace_${e}`);return r.jsx("span",{className:t.join(" "),children:e.value},a)});return(0,r.jsxs)("span",{className:"adm-code-line ace_line",children:[y&&r.jsx("span",{className:"adm-code-line-number",children:a+1}),t]},`${e}-${a}`)});return r.jsx(u,{className:"ace_editor ace-tomorrow adm_code-block",maxHeight:s,inline:i,backgroundColor:h,tabIndex:0,onKeyDown:e=>{if((e.metaKey||e.ctrlKey)&&"a"===e.key){e.preventDefault();let a=window.getSelection(),t=document.createRange();t.selectNodeContents(e.currentTarget.querySelector(".adm-code-wrap")||e.currentTarget),a?.removeAllRanges(),a?.addRange(t)}},children:r.jsx(p.gb,{spinning:c,children:(0,r.jsxs)("div",{className:"adm-code-wrap",ref:S,children:[w,n&&r.jsx(x,{$hasVScrollbar:N,copyable:{onCopy:b,icon:[r.jsx(o.z,{icon:r.jsx(m(),{}),size:"small",style:{backgroundColor:"transparent"}},"copy-icon"),r.jsx(o.z,{icon:r.jsx(d(),{className:"green-6"}),size:"small"},"copied-icon")],text:t},children:t})]})})})}},87519:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var r=t(20997),l=t(87208);let n=e=>{let{ace:a}=window,{SqlHighlightRules:t}=a.require("ace/mode/sql_highlight_rules"),n=(0,l.iD)(t);return r.jsx(n,{...e})}},32039:(e,a,t)=>{t.d(a,{Z:()=>C});var r=t(20997),l=t(16689),n=t(27889),s=t.n(n),o=t(53800),i=t.n(o),d=t(58168),c=t.n(d),m=t(57518),p=t.n(m),u=t(9620),x=t(17209),g=t(97026),y=t.n(g);let h=e=>["boolean","object"].includes(typeof e)?JSON.stringify(e):e,b=(e,a)=>e.map((e,t)=>{let r={};return r.key=t,e.forEach((e,t)=>{r[a[t].dataIndex]=h(e)}),r});function f(e){let{columns:a=[],data:t=[],loading:n,locale:s}=e,o=!!a.length,i=(0,l.useMemo)(()=>a.reduce((e,a)=>e+(y()(a.titleText||a.title)?16*(a.titleText||a.title).length:100),0),[a]),d=(0,l.useMemo)(()=>a.map(e=>({...e,ellipsis:!0})),[a]),c=(0,l.useMemo)(()=>b(t,a),[t]);return r.jsx(x.i,{className:`ph-no-capture ${o?"ant-table-has-header":""}`,showHeader:o,dataSource:c,columns:d,pagination:!1,size:"small",scroll:{y:280,x:i},loading:n,locale:s})}var j=t(42698);let{Text:I}=c(),S=p().div.withConfig({displayName:"PreviewData__StyledCell",componentId:"sc-81799b7d-0"})(["position:relative;.copy-icon{position:absolute;top:50%;right:0;transform:translateY(-50%);opacity:0;transition:opacity 0.3s;}.ant-typography-copy{margin:-4px;}&:hover .copy-icon{opacity:1;}"]),N=(0,l.memo)(e=>{let{name:a,type:t}=e,l=(0,u.u)({type:t},{title:t});return(0,r.jsxs)(r.Fragment,{children:[l,r.jsx(I,{title:a,className:"ml-1",children:a})]})}),v=(0,l.memo)(e=>{let{text:a,copyable:t}=e;return(0,r.jsxs)(S,{className:"text-truncate",children:[r.jsx("span",{title:a,className:"text text-container",children:a}),t&&r.jsx(i(),{size:"small",className:"copy-icon",children:r.jsx(I,{copyable:{text:a,tooltips:!1},className:"gray-8"})})]})}),w=(e,{copyable:a})=>e.map(({name:e,type:t})=>({dataIndex:e,titleText:e,key:e,ellipsis:!0,title:r.jsx(N,{name:e,type:t}),render:e=>r.jsx(v,{text:e,copyable:a}),onCell:()=>({style:{lineHeight:"24px"}})}));function C(e){let{previewData:a,loading:t,error:n,locale:o,copyable:i=!0}=e,d=(0,l.useMemo)(()=>a?.columns&&w(a.columns,{copyable:i}),[a?.columns,i]),c=n&&n.message;if(!t&&c){let{message:e,shortMessage:a}=(0,j.Bx)(n);return r.jsx(s(),{message:a,description:e,type:"error",showIcon:!0})}return r.jsx(f,{columns:d,data:a?.data||[],loading:t,locale:o})}},6529:(e,a,t)=>{t.d(a,{At:()=>v,Lb:()=>w,aV:()=>L,iI:()=>T,px:()=>E,rt:()=>k,s_:()=>C,uu:()=>q});var r=t(20997);t(16689);var l=t(57518),n=t.n(l),s=t(9200),o=t(24351),i=t(97615),d=t.n(i),c=t(13422),m=t.n(c),p=t(28768),u=t.n(p),x=t(48683),g=t.n(x),y=t(21760),h=t.n(y),b=t(83795),f=t.n(b),j=t(56289),I=t(79802);let S=n()(s.v).withConfig({displayName:"CustomDropdown__StyledMenu",componentId:"sc-1e033603-0"})([".ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled){color:var(--gray-8);}"]),N=e=>a=>{let{children:t,onMenuEnter:l,onDropdownVisibleChange:n}=a,o=e(a);return r.jsx(s.L,{trigger:["click"],overlayStyle:{minWidth:100,userSelect:"none"},overlay:r.jsx(S,{onClick:e=>e.domEvent.stopPropagation(),items:o,onMouseEnter:l}),onVisibleChange:n,children:t})},v=N(e=>{let{onMoreClick:a}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(d(),{className:"mr-2"}),"Update Columns"]}),key:o.dI.UPDATE_COLUMNS,onClick:()=>a(o.dI.UPDATE_COLUMNS)},{label:r.jsx(I.ZQ,{onConfirm:()=>a(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),w=N(e=>{let{onMoreClick:a}=e;return[{label:r.jsx(I.e0,{onConfirm:()=>a(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),C=N(e=>{let{onMoreClick:a,data:t}=e,{nodeType:l}=t,n={[o.QZ.CALCULATED_FIELD]:I.pn,[o.QZ.RELATION]:I.ck}[l]||I.pn;return[{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>a(o.dI.EDIT)},{label:r.jsx(n,{onConfirm:()=>a(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),E=N(e=>{let{onMoreClick:a,isSupportCached:t}=e;return[t&&{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(f(),{className:"mr-2"}),"Cache settings"]}),key:o.dI.CACHE_SETTINGS,onClick:()=>a(o.dI.CACHE_SETTINGS)},{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(m(),{className:"mr-2"}),t?"Refresh all caches":"Refresh all"]}),key:o.dI.REFRESH,onClick:()=>a(o.dI.REFRESH)}].filter(Boolean)}),k=N(e=>{let{onMoreClick:a,isHideLegend:t,isSupportCached:l}=e;return[{label:t?(0,r.jsxs)(r.Fragment,{children:[r.jsx(g(),{className:"mr-2"}),"Show categories"]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(u(),{className:"mr-2"}),"Hide categories"]}),key:o.dI.HIDE_CATEGORY,onClick:()=>a(o.dI.HIDE_CATEGORY)},{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(m(),{className:"mr-2"}),l?"Refresh cache":"Refresh"]}),key:o.dI.REFRESH,onClick:()=>a(o.dI.REFRESH)},{label:r.jsx(I.mU,{onConfirm:()=>a(o.dI.DELETE)}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),T=N(e=>{let{onMoreClick:a,data:t}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(g(),{className:"mr-2"}),"View"]}),key:o.dI.VIEW_SQL_PAIR,onClick:()=>a({type:o.dI.VIEW_SQL_PAIR,data:t})},{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>a({type:o.dI.EDIT,data:t})},{label:r.jsx(I.pv,{onConfirm:()=>a({type:o.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),q=N(e=>{let{onMoreClick:a,data:t}=e;return[{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(g(),{className:"mr-2"}),"View"]}),key:o.dI.VIEW_INSTRUCTION,onClick:()=>a({type:o.dI.VIEW_INSTRUCTION,data:t})},{label:(0,r.jsxs)(r.Fragment,{children:[r.jsx(d(),{className:"mr-2"}),"Edit"]}),key:o.dI.EDIT,onClick:()=>a({type:o.dI.EDIT,data:t})},{label:r.jsx(I.py,{onConfirm:()=>a({type:o.dI.DELETE,data:t}),modalProps:{cancelButtonProps:{autoFocus:!0}}}),className:"red-5",key:o.dI.DELETE,onClick:({domEvent:e})=>e.stopPropagation()}]}),L=N(e=>{let{onMoreClick:a,data:t}=e;return[{label:"Adjust steps",icon:r.jsx(j.Wi,{}),disabled:!t.sqlGenerationReasoning,key:"adjust-steps",onClick:()=>a({type:o.dI.ADJUST_STEPS,data:t})},{label:"Adjust SQL",icon:r.jsx(h(),{className:"text-base"}),disabled:!t.sql,key:"adjust-sql",onClick:()=>a({type:o.dI.ADJUST_SQL,data:t})}]})},49051:(e,a,t)=>{t.d(a,{Z:()=>n});var r=t(78692),l=t.n(r);t(82286),t(817),t(37774),t(52144);let n=l()},62441:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>u});var l=t(20997),n=t(16593),s=t(16689),o=t(57518),i=t.n(o),d=t(49051),c=t(93776),m=t(58659),p=e([n]);n=(p.then?(await p)():p)[0];let x=i().div.withConfig({displayName:"SQLEditor__Wrapper",componentId:"sc-c8b626da-0"})(["transition:all 0.3s cubic-bezier(0.645,0.045,0.355,1);&:hover{border-color:var(--geekblue-5) !important;}&.adm-markdown-editor-error{border-color:var(--red-5) !important;.adm-markdown-editor-length{color:var(--red-5) !important;}}&:not(.adm-markdown-editor-error).adm-markdown-editor-focused{border-color:var(--geekblue-5) !important;box-shadow:0 0 0 2px rgba(47,84,235,0.2);}&.adm-markdown-editor-focused.adm-markdown-editor-error{borer-color:var(--red-4) !important;box-shadow:0 0 0 2px rgba(255,77,79,0.2);}"]),g=i().div.withConfig({displayName:"SQLEditor__Toolbar",componentId:"sc-c8b626da-1"})(["color:var(--gray-8);background-color:var(--gray-3);border-bottom:1px solid var(--gray-5);height:32px;padding:4px 8px;border-radius:4px 4px 0px 0px;"]);function u(e){let{value:a,onChange:t,autoFocus:r,autoComplete:o,toolbar:i}=e,p=(0,s.useRef)(null),[u,y]=(0,s.useState)(!1),{status:h}=(0,s.useContext)(c.FormItemInputContext);(0,m.ZP)({includeColumns:!0,skip:!o});let[b,f]=(0,s.useState)(a||"");return(0,l.jsxs)(x,{ref:p,className:(0,n.default)("border border-gray-5 rounded overflow-hidden",h?`adm-markdown-editor-${h}`:"",u?"adm-markdown-editor-focused":""),tabIndex:-1,children:[!!i&&l.jsx(g,{children:i}),l.jsx(d.Z,{mode:"sql",width:"100%",height:"300px",fontSize:14,theme:"tomorrow",value:a||b,onChange:e=>{f(e),t?.(e)},onFocus:()=>y(!0),onBlur:()=>y(!1),name:"sql_editor",editorProps:{$blockScrolling:!0},enableLiveAutocompletion:!0,enableBasicAutocompletion:!0,showPrintMargin:!1,focus:r})]})}r()}catch(e){r(e)}})},26427:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>g,f:()=>h});var l=t(20997),n=t(16689),s=t(68117),o=t(46386),i=t(28303),d=t(42698),c=t(62441),m=t(95308),p=t(19978),u=t(82103),x=e([c]);c=(x.then?(await x)():x)[0];let y=e=>{let{dataSource:a}=e;if(!a)return null;let t=(0,o.Ur)(a),r=(0,o.J9)(a);return l.jsx(l.Fragment,{children:(0,l.jsxs)("span",{className:"d-flex align-center gx-2",children:[l.jsx("img",{src:t,alt:"logo",width:"20",height:"20"}),r]})})},h=e=>!e?.sampleDataset&&e?.type!==u.ri.DUCKDB;function g(e){let{visible:a,defaultValue:t,onSubmit:r,onClose:u}=e,x=(0,o.J9)(t?.dataSource)||"data source",[g,h]=(0,p.ZZ)(),b=(0,n.useMemo)(()=>h.error?{...(0,d.Bx)(h.error),shortMessage:`Invalid ${x} SQL syntax`}:null,[h.error]),[f]=s.l0.useForm(),j=()=>{f.resetFields(),h.reset()},I=async()=>{f.validateFields().then(async e=>{let a=await g({variables:{data:{sql:e.dialectSql}}});await r(a.data?.modelSubstitute),u()}).catch(console.error)},S=h.loading;return(0,l.jsxs)(s.u_,{title:`Import from ${x} SQL`,centered:!0,closable:!0,confirmLoading:S,destroyOnClose:!0,maskClosable:!1,onCancel:u,onOk:I,okText:"Convert",visible:a,width:600,cancelButtonProps:{disabled:S},afterClose:()=>j(),children:[l.jsx(s.l0,{form:f,layout:"vertical",children:l.jsx(s.l0.Item,{name:"dialectSql",label:"SQL statement",rules:[{required:!0,message:i.q.IMPORT_DATA_SOURCE_SQL.SQL.REQUIRED}],children:l.jsx(c.Z,{toolbar:l.jsx(y,{dataSource:t?.dataSource}),autoFocus:!0})})}),!!b&&l.jsx(s.bZ,{showIcon:!0,type:"error",message:b.shortMessage,description:l.jsx(m.Z,{message:b.message})})]})}r()}catch(e){r(e)}})},37480:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{Z:()=>E});var l=t(20997),n=t(16689),s=t(57518),o=t.n(s),i=t(42658),d=t(37338),c=t(62125),m=t.n(c),p=t(44958),u=t.n(p),x=t(28303),g=t(24351),y=t(46386),h=t(47427),b=t(62441),f=t(42698),j=t(99430),I=t(95308),S=t(32039),N=t(26427),v=t(19978),w=t(59462),C=e([b,N]);[b,N]=C.then?(await C)():C;let k=o()(i.l0).withConfig({displayName:"QuestionSQLPairModal__StyledForm",componentId:"sc-fbd18db5-0"})([".adm-question-form-item > div > label{width:100%;}"]),T=e=>{let{dataSource:a,onClick:t}=e,r=(0,y.J9)(a);return(0,l.jsxs)("div",{className:"d-flex justify-space-between align-center px-1",children:[(0,l.jsxs)("span",{className:"d-flex align-center gx-2",children:[l.jsx(d.T,{size:16}),"Wren SQL"]}),(0,l.jsxs)(i.zx,{className:"px-0",type:"link",size:"small",onClick:t,children:[l.jsx(u(),{}),"Import from ",r," SQL"]})]})};function E(e){let{defaultValue:a,formMode:t,loading:r,onClose:s,onSubmit:o,visible:d,payload:c}=e,p=t===g.SD.CREATE||c?.isCreateMode,u=(0,h.Z)(),{data:y}=(0,w.ks)(),C=y?.settings,E=(0,n.useMemo)(()=>({isSupportSubstitute:(0,N.f)(C?.dataSource),type:C?.dataSource?.type}),[C?.dataSource]),[q]=i.l0.useForm(),[L,D]=(0,n.useState)(null),[P,_]=(0,n.useState)(!1),[F,$]=(0,n.useState)(!1),[Q,M]=(0,n.useState)(!1),[R,Z]=(0,n.useState)(!1),[A,z]=(0,v.Zg)(),[U]=(0,v.QC)(),B=i.l0.useWatch("sql",q),H=()=>{z.reset(),Z(!1),D(null),q.resetFields()},V=async()=>{await A({variables:{data:{sql:B,limit:1,dryRun:!0}}})},O=e=>{let a=(0,f.Bx)(e);D({...a,shortMessage:"Invalid SQL syntax"}),console.error(a)},W=async()=>{D(null),_(!0);try{await V(),Z(!0),await A({variables:{data:{sql:B,limit:50}}})}catch(e){O(e)}finally{_(!1)}},G=async()=>{M(!0);let{data:e}=await U({variables:{data:{sql:B}}});q.setFieldsValue({question:e?.generateQuestion||""}),M(!1)},J=r||F,K=!B;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(i.u_,{title:`${p?"Add":"Update"} question-SQL pair`,centered:!0,closable:!0,confirmLoading:J,destroyOnClose:!0,maskClosable:!1,onCancel:s,visible:d,width:640,cancelButtonProps:{disabled:J},okButtonProps:{disabled:z.loading},afterClose:()=>H(),footer:(0,l.jsxs)("div",{className:"d-flex justify-space-between align-center",children:[(0,l.jsxs)("div",{className:"text-sm ml-2 d-flex justify-space-between align-center",style:{width:300},children:[l.jsx(m(),{className:"mr-2 text-sm gray-7"}),(0,l.jsxs)(i.ZT.Text,{type:"secondary",className:"text-sm gray-7 text-left",children:["The SQL statement used here follows ",l.jsx("b",{children:"Wren SQL"}),", which is based on ANSI SQL and optimized for Wren AI."," ",l.jsx(i.ZT.Link,{type:"secondary",href:"https://docs.getwren.ai/oss/guide/home/<USER>",target:"_blank",rel:"noopener noreferrer",children:"Learn more about the syntax."})]})]}),(0,l.jsxs)("div",{children:[l.jsx(i.zx,{onClick:s,children:"Cancel"}),l.jsx(i.zx,{type:"primary",onClick:()=>{D(null),$(!0),Z(!1),q.validateFields().then(async e=>{try{await V(),await o({data:e,id:a?.id}),s()}catch(e){O(e)}finally{$(!1)}}).catch(e=>{$(!1),console.error(e)})},loading:J,children:"Submit"})]})]}),children:[(0,l.jsxs)(k,{form:q,preserve:!1,layout:"vertical",children:[l.jsx(i.l0.Item,{className:"adm-question-form-item",label:(0,l.jsxs)("div",{className:"d-flex justify-space-between",style:{width:"100%"},children:[l.jsx("span",{children:"Question"}),(0,l.jsxs)("div",{className:"gray-8 text-sm",children:["Let AI create a matching question for this SQL statement.",l.jsx(i.zx,{className:"ml-2",size:"small",loading:Q,onClick:G,disabled:K,children:l.jsx("span",{className:"text-sm",children:"Generate question"})})]})]}),name:"question",required:!0,rules:[{validator:(0,j.bI)(x.q.SQL_PAIR.QUESTION)}],children:l.jsx(i.II,{})}),l.jsx(i.l0.Item,{label:"SQL statement",name:"sql",required:!0,rules:[{required:!0,message:x.q.SQL_PAIR.SQL.REQUIRED}],children:l.jsx(b.Z,{toolbar:E.isSupportSubstitute&&l.jsx(T,{dataSource:E.type,onClick:()=>u.openModal({dataSource:E.type})}),autoComplete:!0,autoFocus:!0})})]}),(0,l.jsxs)("div",{className:"my-3",children:[l.jsx(i.ZT.Text,{className:"d-block gray-7 mb-2",children:"Data preview (50 rows)"}),l.jsx(i.zx,{onClick:W,loading:P,disabled:K,children:"Preview data"}),R&&l.jsx("div",{className:"my-3",children:l.jsx(S.Z,{loading:P,previewData:z?.data?.previewSql,copyable:!1})})]}),!!L&&l.jsx(i.bZ,{showIcon:!0,type:"error",message:L.shortMessage,description:l.jsx(I.Z,{message:L.message})})]}),E.isSupportSubstitute&&l.jsx(N.Z,{...u.state,onClose:u.closeModal,onSubmit:async e=>{q.setFieldsValue({sql:e})}})]})}r()}catch(e){r(e)}})},58659:(e,a,t)=>{t.d(a,{ZP:()=>p,fV:()=>c});var r=t(16689),l=t(59969),n=t.n(l),s=t(98666),o=t(18639);let i=e=>['<div style="max-width: 380px;">',`<b style="display: block;color: var(--gray-8); padding: 0 4px 4px;">${e.referenceName}</b>`,e.description?`<div style="color: var(--gray-7); padding: 4px 4px 0; border-top: 1px var(--gray-4) solid;">${e.description}</div>`:null,"</div>"].filter(Boolean).join(""),d=e=>/[^a-z0-9_]/.test(e)||/^\d/.test(e),c=e=>({id:`${e.id}-${e.referenceName}`,label:e.displayName,value:e.referenceName,nodeType:n()(e.nodeType),meta:e.parent?`${e.displayName}.${e.displayName}`:void 0,icon:(0,o.i)({nodeType:e.nodeType,type:e.type},{className:"gray-8 mr-2"})}),m=e=>({caption:e.parent?`${e.parent.displayName}.${e.displayName}`:e.displayName,value:d(e.referenceName)?`"${e.referenceName}"`:e.referenceName,meta:e.nodeType.toLowerCase(),score:e.parent?1:10,docHTML:i(e)});function p(e){let{includeColumns:a,skip:t}=e,{data:l}=(0,s.st)({skip:t}),n=e.convertor||m;return(0,r.useMemo)(()=>[...l?.diagram.models||[],...l?.diagram.views||[]].reduce((e,t)=>(e.push(n(t)),a&&t.fields.forEach(a=>{e.push(n({...a,parent:t}))}),e),[]),[l?.diagram,a])}}};