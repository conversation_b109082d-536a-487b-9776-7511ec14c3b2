"use strict";exports.id=514,exports.ids=[514],exports.modules={38436:(e,t,n)=>{n.d(t,{$4:()=>h,DC:()=>eu,Gf:()=>w,HT:()=>B,MG:()=>J,Ok:()=>D,Qk:()=>g,Rf:()=>X,Rh:()=>c,T_:()=>eC,WL:()=>es,Xy:()=>T,Y:()=>P,Z0:()=>z,Zy:()=>en,dP:()=>em,e8:()=>q,eO:()=>Y,hC:()=>F,j8:()=>eo,jv:()=>G,kY:()=>R,tT:()=>L,vi:()=>ee,vt:()=>v,wK:()=>E,wU:()=>Q,xF:()=>H});var a=n(29114);let s={},r=(0,a.gql)`
    fragment CommonError on Error {
  code
  shortMessage
  message
  stacktrace
}
    `,o=(0,a.gql)`
    fragment CommonBreakdownDetail on ThreadResponseBreakdownDetail {
  queryId
  status
  description
  steps {
    summary
    sql
    cteName
  }
  error {
    ...CommonError
  }
}
    ${r}`,d=(0,a.gql)`
    fragment CommonAnswerDetail on ThreadResponseAnswerDetail {
  queryId
  status
  content
  numRowsUsedInLLM
  error {
    ...CommonError
  }
}
    ${r}`,u=(0,a.gql)`
    fragment CommonChartDetail on ThreadResponseChartDetail {
  queryId
  status
  description
  chartType
  chartSchema
  error {
    ...CommonError
  }
  adjustment
}
    ${r}`,i=(0,a.gql)`
    fragment CommonAskingTask on AskingTask {
  status
  type
  candidates {
    sql
    type
    view {
      id
      name
      statement
      displayName
    }
    sqlPair {
      id
      question
      sql
      projectId
    }
  }
  error {
    ...CommonError
  }
  rephrasedQuestion
  intentReasoning
  sqlGenerationReasoning
  retrievedTables
  invalidSql
  traceId
  queryId
}
    ${r}`,m=(0,a.gql)`
    fragment CommonResponse on ThreadResponse {
  id
  threadId
  question
  sql
  view {
    id
    name
    statement
    displayName
  }
  breakdownDetail {
    ...CommonBreakdownDetail
  }
  answerDetail {
    ...CommonAnswerDetail
  }
  chartDetail {
    ...CommonChartDetail
  }
  askingTask {
    ...CommonAskingTask
  }
  adjustment {
    type
    payload
  }
  adjustmentTask {
    queryId
    status
    error {
      ...CommonError
    }
    sql
    traceId
    invalidSql
  }
}
    ${o}
${d}
${u}
${i}
${r}`,l=(0,a.gql)`
    fragment CommonRecommendedQuestionsTask on RecommendedQuestionsTask {
  status
  questions {
    question
    category
    sql
  }
  error {
    ...CommonError
  }
}
    ${r}`,C=(0,a.gql)`
    query SuggestedQuestions {
  suggestedQuestions {
    questions {
      label
      question
    }
  }
}
    `;function h(e){let t={...s,...e};return a.useQuery(C,t)}let p=(0,a.gql)`
    query AskingTask($taskId: String!) {
  askingTask(taskId: $taskId) {
    ...CommonAskingTask
  }
}
    ${i}`;function c(e){let t={...s,...e};return a.useLazyQuery(p,t)}let I=(0,a.gql)`
    query Threads {
  threads {
    id
    summary
  }
}
    `;function g(e){let t={...s,...e};return a.useQuery(I,t)}let $=(0,a.gql)`
    query Thread($threadId: Int!) {
  thread(threadId: $threadId) {
    id
    responses {
      ...CommonResponse
    }
  }
}
    ${m}`;function q(e){let t={...s,...e};return a.useQuery($,t)}function T(e){let t={...s,...e};return a.useLazyQuery($,t)}let k=(0,a.gql)`
    query ThreadResponse($responseId: Int!) {
  threadResponse(responseId: $responseId) {
    ...CommonResponse
  }
}
    ${m}`;function R(e){let t={...s,...e};return a.useLazyQuery(k,t)}let y=(0,a.gql)`
    mutation CreateAskingTask($data: AskingTaskInput!) {
  createAskingTask(data: $data) {
    id
  }
}
    `;function w(e){let t={...s,...e};return a.useMutation(y,t)}let f=(0,a.gql)`
    mutation CancelAskingTask($taskId: String!) {
  cancelAskingTask(taskId: $taskId)
}
    `;function Q(e){let t={...s,...e};return a.useMutation(f,t)}let j=(0,a.gql)`
    mutation RerunAskingTask($responseId: Int!) {
  rerunAskingTask(responseId: $responseId) {
    id
  }
}
    `;function v(e){let t={...s,...e};return a.useMutation(j,t)}let A=(0,a.gql)`
    mutation CreateThread($data: CreateThreadInput!) {
  createThread(data: $data) {
    id
  }
}
    `;function L(e){let t={...s,...e};return a.useMutation(A,t)}let M=(0,a.gql)`
    mutation CreateThreadResponse($threadId: Int!, $data: CreateThreadResponseInput!) {
  createThreadResponse(threadId: $threadId, data: $data) {
    ...CommonResponse
  }
}
    ${m}`;function D(e){let t={...s,...e};return a.useMutation(M,t)}let S=(0,a.gql)`
    mutation UpdateThread($where: ThreadUniqueWhereInput!, $data: UpdateThreadInput!) {
  updateThread(where: $where, data: $data) {
    id
    summary
  }
}
    `;function P(e){let t={...s,...e};return a.useMutation(S,t)}let x=(0,a.gql)`
    mutation UpdateThreadResponse($where: ThreadResponseUniqueWhereInput!, $data: UpdateThreadResponseInput!) {
  updateThreadResponse(where: $where, data: $data) {
    ...CommonResponse
  }
}
    ${m}`;function G(e){let t={...s,...e};return a.useMutation(x,t)}let U=(0,a.gql)`
    mutation AdjustThreadResponse($responseId: Int!, $data: AdjustThreadResponseInput!) {
  adjustThreadResponse(responseId: $responseId, data: $data) {
    ...CommonResponse
  }
}
    ${m}`;function E(e){let t={...s,...e};return a.useMutation(U,t)}let b=(0,a.gql)`
    mutation DeleteThread($where: ThreadUniqueWhereInput!) {
  deleteThread(where: $where)
}
    `;function z(e){let t={...s,...e};return a.useMutation(b,t)}let Z=(0,a.gql)`
    mutation PreviewData($where: PreviewDataInput!) {
  previewData(where: $where)
}
    `;function B(e){let t={...s,...e};return a.useMutation(Z,t)}(0,a.gql)`
    mutation PreviewBreakdownData($where: PreviewDataInput!) {
  previewBreakdownData(where: $where)
}
    `;let N=(0,a.gql)`
    query GetNativeSQL($responseId: Int!) {
  nativeSql(responseId: $responseId)
}
    `;function H(e){let t={...s,...e};return a.useLazyQuery(N,t)}let W=(0,a.gql)`
    mutation CreateInstantRecommendedQuestions($data: InstantRecommendedQuestionsInput!) {
  createInstantRecommendedQuestions(data: $data) {
    id
  }
}
    `;function Y(e){let t={...s,...e};return a.useMutation(W,t)}let O=(0,a.gql)`
    query InstantRecommendedQuestions($taskId: String!) {
  instantRecommendedQuestions(taskId: $taskId) {
    ...CommonRecommendedQuestionsTask
  }
}
    ${l}`;function F(e){let t={...s,...e};return a.useLazyQuery(O,t)}let K=(0,a.gql)`
    query GetThreadRecommendationQuestions($threadId: Int!) {
  getThreadRecommendationQuestions(threadId: $threadId) {
    ...CommonRecommendedQuestionsTask
  }
}
    ${l}`;function X(e){let t={...s,...e};return a.useLazyQuery(K,t)}let _=(0,a.gql)`
    query GetProjectRecommendationQuestions {
  getProjectRecommendationQuestions {
    ...CommonRecommendedQuestionsTask
  }
}
    ${l}`;function J(e){let t={...s,...e};return a.useLazyQuery(_,t)}let V=(0,a.gql)`
    mutation GenerateProjectRecommendationQuestions {
  generateProjectRecommendationQuestions
}
    `;function ee(e){let t={...s,...e};return a.useMutation(V,t)}let et=(0,a.gql)`
    mutation GenerateThreadRecommendationQuestions($threadId: Int!) {
  generateThreadRecommendationQuestions(threadId: $threadId)
}
    `;function en(e){let t={...s,...e};return a.useMutation(et,t)}let ea=(0,a.gql)`
    mutation GenerateThreadResponseAnswer($responseId: Int!) {
  generateThreadResponseAnswer(responseId: $responseId) {
    ...CommonResponse
  }
}
    ${m}`;function es(e){let t={...s,...e};return a.useMutation(ea,t)}let er=(0,a.gql)`
    mutation GenerateThreadResponseChart($responseId: Int!) {
  generateThreadResponseChart(responseId: $responseId) {
    ...CommonResponse
  }
}
    ${m}`;function eo(e){let t={...s,...e};return a.useMutation(er,t)}let ed=(0,a.gql)`
    mutation AdjustThreadResponseChart($responseId: Int!, $data: AdjustThreadResponseChartInput!) {
  adjustThreadResponseChart(responseId: $responseId, data: $data) {
    ...CommonResponse
  }
}
    ${m}`;function eu(e){let t={...s,...e};return a.useMutation(ed,t)}(0,a.gql)`
    query AdjustmentTask($taskId: String!) {
  adjustmentTask(taskId: $taskId) {
    queryId
    status
    error {
      code
      shortMessage
      message
      stacktrace
    }
    sql
    traceId
    invalidSql
  }
}
    `;let ei=(0,a.gql)`
    mutation CancelAdjustmentTask($taskId: String!) {
  cancelAdjustmentTask(taskId: $taskId)
}
    `;function em(e){let t={...s,...e};return a.useMutation(ei,t)}let el=(0,a.gql)`
    mutation RerunAdjustmentTask($responseId: Int!) {
  rerunAdjustmentTask(responseId: $responseId)
}
    `;function eC(e){let t={...s,...e};return a.useMutation(el,t)}},37338:(e,t,n)=>{n.d(t,{T:()=>s});var a=n(20997);let s=e=>{let{color:t="var(--gray-9)",size:n=30}=e;return(0,a.jsxs)("svg",{style:{width:n,height:"auto"},viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",shapeRendering:"geometricPrecision",children:[a.jsx("path",{d:"M15.8023 7.82981C16.2779 8.98701 16.0102 10.1784 15.2043 10.491C14.3983 10.8035 13.3594 10.1187 12.8838 8.96153C12.4082 7.80433 12.676 6.61289 13.4819 6.30038C14.2878 5.98786 15.3267 6.67261 15.8023 7.82981Z",fill:t}),a.jsx("path",{d:"M29.2498 21.952C29.6972 22.4662 30.06 23.0215 29.9917 23.685C29.9234 24.3486 29.3086 24.614 28.8987 24.6804C28.4888 24.7467 20.4276 25.8084 19.5396 25.8748C18.7537 25.9335 18.6097 25.8363 18.303 25.6293C18.1647 25.5398 15.0158 22.5446 13.4586 21.0582C12.69 20.502 11.9941 20.0605 11.5031 19.8762C11.0511 19.7065 10.8708 19.0927 11.2918 18.7366L12.3395 17.8503C12.6148 17.6175 13.0379 17.6319 13.2922 17.9027C13.5897 18.1624 14.5664 19.2307 15.0176 19.7324C16.4453 21.0988 18.1849 22.7674 19.1297 23.685C19.8811 23.9504 21.6801 23.6187 22.0672 23.486C22.0672 23.486 19.4312 20.2141 18.8919 19.8065C18.3525 19.3989 17.8676 19.0849 17.4905 18.9339C16.7022 18.5185 17.059 17.9764 17.336 17.7573L18.4758 16.9463C18.7522 16.7496 19.1457 16.7781 19.3852 17.0359C20.803 18.8099 23.7888 22.4906 24.3899 23.0215C25.1414 23.2869 26.8948 22.9994 27.3274 22.8224L21.8995 16.0762C21.0284 15.3386 20.2227 14.7346 19.6677 14.5037C19.2263 14.3201 19.0551 13.6941 19.5023 13.3497L20.6563 12.4606C20.8517 12.3422 21.3174 12.1979 21.6163 12.5686L22.4088 13.5326L29.2498 21.952Z",fill:t}),a.jsx("path",{d:"M11.8478 1.99067C10.5707 1.99067 9.16387 2.29138 7.65774 3.03904C4.50493 4.60413 2.85603 7.82981 2.33475 10.202C1.60286 13.5326 2.02008 17.0359 3.22442 19.2504C4.07463 20.8137 4.76646 21.6232 6.14326 22.773C8.22894 24.5149 9.81294 25.2866 12.5342 25.6205C13.3997 25.7267 14.7668 25.6858 14.7668 25.6858C15.0555 25.6943 15.3305 25.807 15.5383 26.0018L16.946 27.322L18.5323 28.7087C18.8115 28.9528 18.6338 29.4028 18.2581 29.4028H16.4788C16.1959 29.4028 15.9415 29.1595 15.8496 29.0378C15.2849 28.4856 14.3617 27.6654 14.3617 27.6654L12.5949 27.6134L12.0702 29.601C12.0082 29.8358 11.7904 30 11.5408 30H10.8359C10.4826 30 10.2222 29.6793 10.3044 29.3456L10.7754 27.4341C10.4111 27.3614 10.0218 27.2603 9.62389 27.1204L8.89011 29.615C8.82307 29.8429 8.60863 30 8.36462 30H7.57716C7.21754 30 6.95595 29.6684 7.04944 29.3312L7.88561 26.3144C6.21075 25.5469 4.73704 24.2212 4.73704 24.2212C3.61206 23.3518 2.40208 21.6625 1.93772 20.9265C0.758197 18.9403 0.394862 17.7438 0.0906286 15.4688C-0.12877 13.8281 0.0906362 11.06 0.319969 9.83744C1.19531 6.30038 2.88436 3.2418 6.58906 1.34044C8.46832 0.375947 10.2771 0 11.8478 0C13.447 0 15.5446 0.508003 17.1081 1.30947C18.6578 2.10386 19.4247 2.82829 20.4677 3.84627C20.795 4.1666 21.6271 4.79496 22.3369 4.74577C22.7866 4.80486 24.0471 4.75887 25.4919 4.10215C26.2394 3.83912 26.7594 4.35638 26.9213 4.63196C27.103 4.91761 27.3006 5.6137 26.6372 6.11287L21.7843 9.76476C21.5609 10.1265 21.3962 10.4625 21.3325 10.7478C21.1055 11.6585 20.3487 11.25 20.057 11.06C19.7653 10.87 19.0996 9.90218 19.0996 9.90218C18.8562 9.60785 18.9128 9.17176 19.2335 8.94832C19.5878 8.70146 20.7613 7.88917 21.6518 7.34629L22.4617 6.73683C21.868 6.78175 20.3598 6.55944 19.0768 5.3108C19.0768 5.3108 17.441 3.72084 16.1516 3.07001C14.6097 2.29178 13.0964 1.99067 11.8478 1.99067Z",fill:t})]})}},21793:(e,t,n)=>{n.d(t,{Z:()=>d});var a=n(16689),s=n(11163),r=n(24351),o=n(38436);function d(){let e=(0,s.useRouter)(),{data:t,refetch:n}=(0,o.Qk)({fetchPolicy:"cache-and-network"}),[d]=(0,o.Y)(),[u]=(0,o.Z0)();return{data:{threads:(0,a.useMemo)(()=>(t?.threads||[]).map(e=>({id:e.id.toString(),name:e.summary})),[t])},onSelect:t=>{e.push(`${r.y$.Home}/${t[0]}`)},onRename:async(e,t)=>{await d({variables:{where:{id:Number(e)},data:{summary:t}}}),n()},onDelete:async e=>{await u({variables:{where:{id:Number(e)}}}),n()},refetch:n}}}};