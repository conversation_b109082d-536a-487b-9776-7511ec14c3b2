"use strict";exports.id=353,exports.ids=[353],exports.modules={3230:(e,t,s)=>{s.d(t,{bZ:()=>i.a,u:()=>c.a,zx:()=>l.a});var a=s(27889),i=s.n(a),n=s(53800),l=s.n(n),r=s(69348),c=s.n(r)},21353:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>w});var i=s(20997),n=s(16689),l=s(16593),r=s(89699),c=s.n(r),o=s(3230),d=s(8849);s(65097);var u=s(65893),h=s(13422),x=s.n(h),j=s(97615),f=s.n(j),m=s(48683),p=s.n(m),g=s(87680),v=s.n(g),b=e([l]);function w(e){let{className:t,spec:s,values:a,width:r=600,height:h=320,autoFilter:j,hideActions:m,hideTitle:g,hideLegend:b,forceUpdate:w,onReload:y,onEdit:C,onPin:k}=e,[S,N]=(0,n.useState)(null),[z,R]=(0,n.useState)(!1);(0,n.useRef)(null);let T=(0,n.useRef)(null),Z=(0,n.useMemo)(()=>{if(!s||!a)return;let e=new u.ZP({...s,data:{values:a}},{donutInner:S,isShowTopCategories:j||z,isHideLegend:b,isHideTitle:g}),t=e.getChartSpec();return c()(t?.data?.values)?null:(0,d.compile)(t,{config:e.config}).spec},[s,a,z,S,w]);return null===Z?0===a.length?i.jsx("div",{children:"No available data"}):i.jsx(o.bZ,{className:"mt-6 mb-4 mx-4",message:(0,i.jsxs)("div",{className:"d-flex align-center justify-space-between",children:[i.jsx("div",{children:"There are too many categories to display effectively. Click 'Show top 25' to view the top results, or ask a follow-up question to focus on a specific group or filter results."}),i.jsx(o.zx,{size:"small",icon:i.jsx(p(),{}),onClick:()=>{R(!z)},children:"Show top 25"})]}),type:"warning"}):(0,i.jsxs)("div",{className:(0,l.default)("adm-chart",{"adm-chart--no-actions":m},t),style:{width:r},children:[(!!y||!!C||!!k)&&(0,i.jsxs)("div",{className:"adm-chart-additional d-flex justify-content-between align-center",children:[!!y&&i.jsx(o.u,{title:"Regenerate chart",children:i.jsx("button",{onClick:y,children:i.jsx(x(),{})})}),!!C&&i.jsx(o.u,{title:"Edit chart",children:i.jsx("button",{onClick:C,children:i.jsx(f(),{})})}),!!k&&i.jsx(o.u,{title:"Pin chart to dashboard",children:i.jsx("button",{onClick:k,children:i.jsx(v(),{})})})]}),i.jsx("div",{style:{width:r,height:h},ref:T})]})}l=(b.then?(await b)():b)[0],a()}catch(e){a(e)}})}};