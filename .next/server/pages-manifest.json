{"/_app": "pages/_app.js", "/_document": "pages/_document.js", "/api-management/history": "pages/api-management/history.html", "/_error": "pages/_error.js", "/api/ask_task/streaming": "pages/api/ask_task/streaming.js", "/api/ask_task/streaming_answer": "pages/api/ask_task/streaming_answer.js", "/api/config": "pages/api/config.js", "/api/v1/generate_sql": "pages/api/v1/generate_sql.js", "/api/v1/generate_vega_chart": "pages/api/v1/generate_vega_chart.js", "/api/v1/run_sql": "pages/api/v1/run_sql.js", "/api/v1/stream_explanation": "pages/api/v1/stream_explanation.js", "/home/<USER>": "pages/home/<USER>", "/home": "pages/home.html", "/": "pages/index.html", "/knowledge/instructions": "pages/knowledge/instructions.html", "/knowledge/question-sql-pairs": "pages/knowledge/question-sql-pairs.html", "/setup/connection": "pages/setup/connection.html", "/modeling": "pages/modeling.html", "/setup/models": "pages/setup/models.html", "/setup/relationships": "pages/setup/relationships.html", "/api/graphql": "pages/api/graphql.js", "/404": "pages/404.html"}