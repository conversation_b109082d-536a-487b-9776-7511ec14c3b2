"use strict";(()=>{var e={};e.id=589,e.ids=[589],e.modules={20808:e=>{e.exports=require("lodash/pickBy")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},56249:(e,n)=>{Object.defineProperty(n,"l",{enumerable:!0,get:function(){return function e(n,r){return r in n?n[r]:"then"in n&&"function"==typeof n.then?n.then(n=>e(n,r)):"function"==typeof n&&"default"===r?n:void 0}}})},95985:(e,n,r)=>{r.r(n),r.d(n,{config:()=>I,default:()=>_,routeModule:()=>c});var s={};r.r(s),r.d(s,{default:()=>p});var t=r(71802),o=r(47153),E=r(56249),i=r(42759);function p(e,n){let r=(0,i.i)(),s=r.posthogApiKey?Buffer.from(r.posthogApiKey).toString("base64"):"";n.status(200).json({isTelemetryEnabled:r.telemetryEnabled||!1,telemetryKey:s,telemetryHost:r.posthogHost||"",userUUID:r.userUUID||""})}let _=(0,E.l)(s,"default"),I=(0,E.l)(s,"config"),c=new t.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/config",pathname:"/api/config",bundlePath:"",filename:""},userland:s})},42759:(e,n,r)=>{r.d(n,{i:()=>p});var s=r(20808),t=r.n(s);let o="***************",E={otherServiceUsingDocker:!1,dbType:"sqlite",pgUrl:"***************************************/admin_ui",debug:!1,sqliteFile:"./db.sqlite3",persistCredentialDir:`${process.cwd()}/.tmp`,wrenEngineEndpoint:"http://"+o+":8080",wrenAIEndpoint:"http://"+o+":4555",experimentalEngineRustVersion:!0,ibisServerEndpoint:"http://"+o+":8000",encryptionPassword:"sementic",encryptionSalt:"layer"},i={otherServiceUsingDocker:"true"===process.env.OTHER_SERVICE_USING_DOCKER,dbType:process.env.DB_TYPE,pgUrl:process.env.PG_URL,debug:"true"===process.env.DEBUG,sqliteFile:process.env.SQLITE_FILE,persistCredentialDir:(()=>{if(process.env.PERSIST_CREDENTIAL_DIR&&process.env.PERSIST_CREDENTIAL_DIR.length>0)return process.env.PERSIST_CREDENTIAL_DIR})(),wrenEngineEndpoint:process.env.WREN_ENGINE_ENDPOINT,wrenAIEndpoint:process.env.WREN_AI_ENDPOINT,generationModel:process.env.GENERATION_MODEL,experimentalEngineRustVersion:"true"===process.env.EXPERIMENTAL_ENGINE_RUST_VERSION,ibisServerEndpoint:process.env.IBIS_SERVER_ENDPOINT,encryptionPassword:process.env.ENCRYPTION_PASSWORD,encryptionSalt:process.env.ENCRYPTION_SALT,telemetryEnabled:process.env.TELEMETRY_ENABLED&&"true"===process.env.TELEMETRY_ENABLED.toLocaleLowerCase(),posthogApiKey:process.env.POSTHOG_API_KEY,posthogHost:process.env.POSTHOG_HOST,userUUID:process.env.USER_UUID,wrenUIVersion:process.env.WREN_UI_VERSION,wrenEngineVersion:process.env.WREN_ENGINE_VERSION,wrenAIVersion:process.env.WREN_AI_SERVICE_VERSION,wrenProductVersion:process.env.WREN_PRODUCT_VERSION,projectRecommendationQuestionMaxCategories:process.env.PROJECT_RECOMMENDATION_QUESTION_MAX_CATEGORIES?parseInt(process.env.PROJECT_RECOMMENDATION_QUESTION_MAX_CATEGORIES):3,projectRecommendationQuestionsMaxQuestions:process.env.PROJECT_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS?parseInt(process.env.PROJECT_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS):3,threadRecommendationQuestionMaxCategories:process.env.THREAD_RECOMMENDATION_QUESTION_MAX_CATEGORIES?parseInt(process.env.THREAD_RECOMMENDATION_QUESTION_MAX_CATEGORIES):3,threadRecommendationQuestionsMaxQuestions:process.env.THREAD_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS?parseInt(process.env.THREAD_RECOMMENDATION_QUESTIONS_MAX_QUESTIONS):1};function p(){return{...E,...t()(i)}}},47153:(e,n)=>{var r;Object.defineProperty(n,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},71802:(e,n,r)=>{e.exports=r(20145)}};var n=require("../../webpack-api-runtime.js");n.C(e);var r=n(n.s=95985);module.exports=r})();