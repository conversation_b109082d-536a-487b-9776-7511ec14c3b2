"use strict";(()=>{var e={};e.id=945,e.ids=[945],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},58980:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>d,default:()=>u,routeModule:()=>l});var a=t(71802),o=t(47153),i=t(56249),p=t(68839),n=e([p]);p=(n.then?(await n)():n)[0];let u=(0,i.l)(p,"default"),d=(0,i.l)(p,"config"),l=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/ask_task/streaming",pathname:"/api/ask_task/streaming",bundlePath:"",filename:""},userland:p});s()}catch(e){s(e)}})},68839:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>i});var a=t(85913),o=e([a]);let{wrenAIAdaptor:p}=(a=(o.then?(await o)():o)[0]).components;async function i(e,r){r.setHeader("Content-Type","text/event-stream"),r.setHeader("Cache-Control","no-cache, no-transform"),r.setHeader("Connection","keep-alive"),r.flushHeaders();let{queryId:t}=e.query;try{let s=await p.getAskStreamingResult(t);s.on("data",e=>{r.write(e)}),s.on("end",()=>{r.write(`data: ${JSON.stringify({done:!0})}

`),r.end()}),e.on("close",()=>{s.destroy()})}catch(e){console.error(e),r.status(500).end()}}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[980],()=>t(58980));module.exports=s})();