"use strict";(()=>{var e={};e.id=453,e.ids=[453],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},35323:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>d,default:()=>u,routeModule:()=>l});var o=t(71802),a=t(47153),i=t(56249),n=t(51060),p=e([n]);n=(p.then?(await p)():p)[0];let u=(0,i.l)(n,"default"),d=(0,i.l)(n,"config"),l=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/v1/stream_explanation",pathname:"/api/v1/stream_explanation",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},51060:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>i});var o=t(85913),a=e([o]);let{wrenAIAdaptor:n}=(o=(a.then?(await a)():a)[0]).components;async function i(e,r){r.setHeader("Content-Type","text/event-stream"),r.setHeader("Cache-Control","no-cache, no-transform"),r.setHeader("Connection","keep-alive"),r.flushHeaders();let{queryId:t}=e.query;if(!t){r.status(400).json({error:"queryId is required"});return}try{let s=await n.getAskStreamingResult(t);s.on("data",e=>{r.write(e)}),s.on("end",()=>{r.write(`data: ${JSON.stringify({done:!0})}

`),r.end()}),e.on("close",()=>{s.destroy()})}catch(e){console.error(e),r.status(500).json({error:e.message})}}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[980],()=>t(35323));module.exports=s})();