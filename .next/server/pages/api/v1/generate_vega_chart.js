"use strict";(()=>{var e={};e.id=808,e.ids=[808],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},34404:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{config:()=>c,default:()=>d,routeModule:()=>h});var a=i(71802),o=i(47153),s=i(56249),n=i(22471),l=e([n]);n=(l.then?(await l)():l)[0];let d=(0,s.l)(n,"default"),c=(0,s.l)(n,"config"),h=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/v1/generate_vega_chart",pathname:"/api/v1/generate_vega_chart",bundlePath:"",filename:""},userland:n});r()}catch(e){r(e)}})},85014:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.d(t,{MS:()=>l,Zv:()=>d,zG:()=>c});var a=i(46555),o=i(85913),s=e([a,o]);[a,o]=s.then?(await s)():s;let{apiHistoryRepository:n}=o.components;class l extends Error{constructor(e,t,i,r){super(e),this.statusCode=t,this.code=i,this.additionalData=r}}let d=async({res:e,statusCode:t,responsePayload:i,projectId:r,apiType:o,threadId:s,headers:l,requestPayload:d,startTime:c})=>{let h=c?Date.now()-c:void 0,p=(0,a.v4)();return await n.createOne({id:p,projectId:r,apiType:o,threadId:s,headers:l,requestPayload:d,responsePayload:i,statusCode:t,durationMs:h}),e.status(t).json({id:p,...i})},c=async({error:e,res:t,projectId:i,apiType:r,requestPayload:a,threadId:o,headers:s,startTime:n,logger:c})=>{let h;c&&c.error(`Error in ${r} API:`,e);let p=e instanceof l?e.statusCode:500;e instanceof l&&e.code?(h={code:e.code,error:e.message},e.additionalData&&Object.assign(h,e.additionalData)):h={error:e.message},await d({res:t,statusCode:p,responsePayload:h,projectId:i||0,apiType:r,startTime:n,requestPayload:a,threadId:o,headers:s})};r()}catch(e){r(e)}})},42171:(e,t,i)=>{i.d(t,{N:()=>r});let r=(e,t)=>{if(!t||!e||0===t.length||0===e.length)return[];if(t[0].length!==e.length)throw Error("Number of columns in the rows does not match the number of columns in the columns array");return t.map(t=>{let i={};return e.forEach((e,r)=>{i[e.name]=t[r]}),i})}},22471:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>g});var a=i(85913),o=i(45305),s=i(24634),n=i(46555),l=i(85014),d=i(75145),c=i(42171),h=i(95656),p=e([a,n,l]);[a,n,l]=p.then?(await p)():p;let{projectService:u,wrenAIAdaptor:m,deployService:f,queryService:y}=a.components,E=e=>{if(e.status===d.XP.FAILED||e.error)throw new l.MS(e.error?.message||"Failed to generate Vega spec",400,s.GL.FAILED_TO_GENERATE_VEGA_SCHEMA);if(!e?.response?.chartSchema)throw new l.MS("Failed to generate Vega spec",500)};async function g(e,t){let i;let{question:r,sql:a,threadId:p,sampleSize:g=1e4}=e.body,w=Date.now();try{let x,A;if(i=await u.getCurrentProject(),"POST"!==e.method)throw new l.MS("Method not allowed",405);if(!r)throw new l.MS("Question is required",400);if(!a)throw new l.MS("SQL is required",400);if(!Number.isInteger(g)||g<=0||g>1e6)throw new l.MS("Invalid sampleSize",400);let R=await f.getLastDeployment(i.id);if(!R)throw new l.MS("No deployment found, please deploy your project first",400,s.GL.NO_DEPLOYMENT_FOUND);try{x=await y.preview(a,{project:i,limit:g,manifest:R.manifest,modelingOnly:!1})}catch(e){throw new l.MS(e.message||"Error executing SQL query",400,s.GL.INVALID_SQL_ERROR)}let C=(0,c.N)(x.columns,x.data),I=await m.generateChart({query:r,sql:a,projectId:i.id.toString()});if(!I||!I.queryId)throw new l.MS("Failed to start Vega spec generation task",500);let S=Date.now()+18e4;for(;(A=await m.getChartResult(I.queryId)).status!==d.XP.FINISHED&&A.status!==d.XP.FAILED;){if(Date.now()>S)throw new l.MS("Timeout waiting for Vega spec generation",500,s.GL.POLLING_TIMEOUT);await new Promise(e=>setTimeout(e,1e3))}E(A);let q=p||(0,n.v4)(),v=A?.response?.chartSchema,b=(0,h.Js)(v,C);await (0,l.Zv)({res:t,statusCode:200,responsePayload:{vegaSpec:b,threadId:q},projectId:i.id,apiType:o.I.GENERATE_VEGA_CHART,startTime:w,requestPayload:e.body,threadId:q,headers:e.headers})}catch(r){await (0,l.zG)({error:r,res:t,projectId:i?.id,apiType:o.I.GENERATE_VEGA_CHART,requestPayload:e.body,threadId:p,headers:e.headers,startTime:w})}}r()}catch(e){r(e)}})},95656:(e,t,i)=>{var r;i.d(t,{Js:()=>d}),function(e){e.ARC="arc",e.AREA="area",e.BAR="bar",e.BOXPLOT="boxplot",e.CIRCLE="circle",e.ERRORBAND="errorband",e.ERRORBAR="errorbar",e.IMAGE="image",e.LINE="line",e.POINT="point",e.RECT="rect",e.RULE="rule",e.SQUARE="square",e.TEXT="text",e.TICK="tick",e.TRAIL="trail"}(r||(r={}));let a={GRAY_10:"#262626",GRAY_9:"#434343",GRAY_8:"#65676c",GRAY_5:"#d9d9d9"},o=["#7763CF","#444CE7","#1570EF","#0086C9","#3E4784","#E31B54","#EC4A0A","#EF8D0C","#EBC405","#5381AD"],s="#1570EF",n={mark:{tooltip:!0},font:"Roboto, Arial, Noto Sans, sans-serif",padding:{top:30,bottom:20,left:0,right:0},title:{color:a.GRAY_10,fontSize:14},axis:{labelPadding:0,labelOffset:0,labelFontSize:10,gridColor:a.GRAY_5,titleColor:a.GRAY_9,labelColor:a.GRAY_8,labelFont:" Roboto, Arial, Noto Sans, sans-serif"},axisX:{labelAngle:-45},line:{color:s},bar:{color:s},legend:{symbolLimit:15,columns:1,labelFontSize:10,labelColor:a.GRAY_8,titleColor:a.GRAY_9,titleFontSize:14},range:{category:o,ordinal:o,diverging:o,symbol:o,heatmap:o,ramp:o},point:{size:60,color:s}};class l{constructor(e,t){this.config=n,this.$schema="https://vega.github.io/schema/vega-lite/v5.json",this.title=e.title,this.width="container",this.height="container",this.autosize={type:"fit",contains:"padding"},this.data={values:t},this.params=[{name:"hover",select:{type:"point",on:"mouseover",clear:"mouseout"}}];let i={...e};this.parseSpec(i)}getChartSpec(){return{$schema:this.$schema,config:this.config,title:this.title,data:this.data,mark:this.mark,width:this.width,height:this.height,autosize:this.autosize,encoding:this.encoding,params:this.params}}parseSpec(e){if("mark"in e){let t="string"==typeof e.mark?{type:e.mark}:e.mark;this.addMark(t)}"encoding"in e&&this.addEncoding(e.encoding)}addMark(e){this.mark={type:e.type},"line"===e.type?this.mark.point=!0:"arc"===e.type&&(this.mark.innerRadius=60)}addEncoding(e){this.encoding={...e},this.addColorEncoding(),this.handleBarChartEncoding(),this.addOpacityForInteractivity()}handleBarChartEncoding(){if("bar"===this.mark.type&&(this.encoding.y&&"stack"in this.encoding.y&&(this.encoding.y.stack="zero"),this.encoding.xOffset)){let e=this.encoding.xOffset,t=e.title;!t&&e.field&&(t=this.findFieldTitleInEncoding(e.field)),t&&(this.encoding.xOffset.title=t)}}findFieldTitleInEncoding(e){for(let t of["x","y","xOffset","color"])if(this.encoding[t]?.field===e&&this.encoding[t]?.title)return this.encoding[t].title}addColorEncoding(){if(this.encoding.color)this.encoding.color&&!this.encoding.color.scale&&(this.encoding.color.scale={range:o});else{let e=["x","y"].find(e=>this.encoding[e]?.type==="nominal");if(e){let t=this.encoding[e];this.encoding.color={field:t.field,type:t.type,title:t.title||t.field,scale:{range:o}}}}this.params&&this.encoding.color?.field&&(this.params[0].select.fields=[this.encoding.color.field])}addOpacityForInteractivity(){this.encoding.opacity||(this.encoding.opacity={condition:{param:"hover",value:1},value:.3})}}function d(e,t){return new l(e,t).getChartSpec()}}};var t=require("../../../webpack-api-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[980],()=>i(34404));module.exports=r})();