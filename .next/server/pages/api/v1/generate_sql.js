"use strict";(()=>{var e={};e.id=97,e.ids=[97],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},44815:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>p,default:()=>l,routeModule:()=>u});var o=r(71802),s=r(47153),i=r(56249),n=r(2310),d=e([n]);n=(d.then?(await d)():d)[0];let l=(0,i.l)(n,"default"),p=(0,i.l)(n,"config"),u=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/v1/generate_sql",pathname:"/api/v1/generate_sql",bundlePath:"",filename:""},userland:n});a()}catch(e){a(e)}})},85014:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{MS:()=>d,Zv:()=>l,zG:()=>p});var o=r(46555),s=r(85913),i=e([o,s]);[o,s]=i.then?(await i)():i;let{apiHistoryRepository:n}=s.components;class d extends Error{constructor(e,t,r,a){super(e),this.statusCode=t,this.code=r,this.additionalData=a}}let l=async({res:e,statusCode:t,responsePayload:r,projectId:a,apiType:s,threadId:i,headers:d,requestPayload:l,startTime:p})=>{let u=p?Date.now()-p:void 0,c=(0,o.v4)();return await n.createOne({id:c,projectId:a,apiType:s,threadId:i,headers:d,requestPayload:l,responsePayload:r,statusCode:t,durationMs:u}),e.status(t).json({id:c,...r})},p=async({error:e,res:t,projectId:r,apiType:a,requestPayload:o,threadId:s,headers:i,startTime:n,logger:p})=>{let u;p&&p.error(`Error in ${a} API:`,e);let c=e instanceof d?e.statusCode:500;e instanceof d&&e.code?(u={code:e.code,error:e.message},e.additionalData&&Object.assign(u,e.additionalData)):u={error:e.message},await l({res:t,statusCode:c,responsePayload:u,projectId:r||0,apiType:a,startTime:n,requestPayload:o,threadId:s,headers:i})};a()}catch(e){a(e)}})},2310:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>h});var o=r(85913),s=r(45305),i=r(75145),n=r(24634),d=r(47104),l=r(46555),p=r(85014),u=r(3547),c=e([o,l,p]);[o,l,p]=c.then?(await c)():c;let q=(0,d.jl)("API_GENERATE_SQL");q.level="debug";let{apiHistoryRepository:y,projectService:w,deployService:m,wrenAIAdaptor:x,wrenEngineAdaptor:g,ibisAdaptor:f}=o.components,S=e=>e.status===i.i3.FINISHED||e.status===i.i3.FAILED||e.status===i.i3.STOPPED||e.error,E=(e,t)=>{if(e.error){let t=e.error.message||"Unknown error",r={};throw e.invalidSql&&(r.invalidSql=e.invalidSql),new p.MS(t,400,e.error.code,r)}if(e.type===i.dd.MISLEADING_QUERY)throw new p.MS(e.intentReasoning||n.Qj[n.GL.NON_SQL_QUERY],400,n.GL.NON_SQL_QUERY);if(e.type===i.dd.GENERAL)throw new p.MS(e.intentReasoning||n.Qj[n.GL.NON_SQL_QUERY],400,n.GL.NON_SQL_QUERY,{explanationQueryId:t})},N=e=>e?e.filter(e=>e.responsePayload?.sql&&e.requestPayload?.question).map(e=>({question:e.requestPayload?.question,sql:e.responsePayload?.sql})):[];async function h(e,t){let r;let{question:a,threadId:o,language:d,returnSqlDialect:c=!1}=e.body,h=Date.now();try{let q;if(r=await w.getCurrentProject(),"POST"!==e.method)throw new p.MS("Method not allowed",405);if(!a)throw new p.MS("Question is required",400);let I=await m.getLastDeployment(r.id);if(!I)throw new p.MS("No deployment found, please deploy a model first",400,n.GL.NO_DEPLOYMENT_FOUND);let L=o?await y.findAllBy({threadId:o}):void 0,P=await x.ask({query:a,deployId:I.hash,histories:N(L),configurations:{language:d||i.aS[r.language]||i.aS.EN}}),v=Date.now()+18e4;for(;q=await x.getAskResult(P.queryId),!S(q);){if(Date.now()>v)throw new p.MS("Timeout waiting for SQL generation",500,n.GL.POLLING_TIMEOUT);await new Promise(e=>setTimeout(e,1e3))}E(q,P.queryId);let _=q.response?.[0]?.sql,Q=o||(0,l.v4)();c&&_&&(_=(r.type===u.ri.DUCKDB?await g.getNativeSQL(_,{manifest:I.manifest,modelingOnly:!1}):await f.getNativeSql({dataSource:r.type,sql:_,mdl:I.manifest}))||_),await (0,p.Zv)({res:t,statusCode:200,responsePayload:{sql:_,threadId:Q},projectId:r.id,apiType:s.I.GENERATE_SQL,startTime:h,requestPayload:e.body,threadId:Q,headers:e.headers})}catch(a){await (0,p.zG)({error:a,res:t,projectId:r?.id,apiType:s.I.GENERATE_SQL,requestPayload:e.body,threadId:o,headers:e.headers,startTime:h,logger:q})}}a()}catch(e){a(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[980],()=>r(44815));module.exports=a})();