"use strict";(()=>{var e={};e.id=12,e.ids=[12],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},73228:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>u,default:()=>l,routeModule:()=>p});var a=t(71802),s=t(47153),i=t(56249),n=t(84673),d=e([n]);n=(d.then?(await d)():d)[0];let l=(0,i.l)(n,"default"),u=(0,i.l)(n,"config"),p=new a.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/v1/run_sql",pathname:"/api/v1/run_sql",bundlePath:"",filename:""},userland:n});o()}catch(e){o(e)}})},85014:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.d(r,{MS:()=>d,Zv:()=>l,zG:()=>u});var a=t(46555),s=t(85913),i=e([a,s]);[a,s]=i.then?(await i)():i;let{apiHistoryRepository:n}=s.components;class d extends Error{constructor(e,r,t,o){super(e),this.statusCode=r,this.code=t,this.additionalData=o}}let l=async({res:e,statusCode:r,responsePayload:t,projectId:o,apiType:s,threadId:i,headers:d,requestPayload:l,startTime:u})=>{let p=u?Date.now()-u:void 0,c=(0,a.v4)();return await n.createOne({id:c,projectId:o,apiType:s,threadId:i,headers:d,requestPayload:l,responsePayload:t,statusCode:r,durationMs:p}),e.status(r).json({id:c,...t})},u=async({error:e,res:r,projectId:t,apiType:o,requestPayload:a,threadId:s,headers:i,startTime:n,logger:u})=>{let p;u&&u.error(`Error in ${o} API:`,e);let c=e instanceof d?e.statusCode:500;e instanceof d&&e.code?(p={code:e.code,error:e.message},e.additionalData&&Object.assign(p,e.additionalData)):p={error:e.message},await l({res:r,statusCode:c,responsePayload:p,projectId:t||0,apiType:o,startTime:n,requestPayload:a,threadId:s,headers:i})};o()}catch(e){o(e)}})},42171:(e,r,t)=>{t.d(r,{N:()=>o});let o=(e,r)=>{if(!r||!e||0===r.length||0===e.length)return[];if(r[0].length!==e.length)throw Error("Number of columns in the rows does not match the number of columns in the columns array");return r.map(r=>{let t={};return e.forEach((e,o)=>{t[e.name]=r[o]}),t})}},84673:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>c});var a=t(85913),s=t(45305),i=t(24634),n=t(47104),d=t(46555),l=t(85014),u=t(42171),p=e([a,d,l]);[a,d,l]=p.then?(await p)():p;let h=(0,n.jl)("API_RUN_SQL");h.level="debug";let{projectService:m,queryService:x,deployService:y}=a.components,q=e=>{if("boolean"==typeof e)throw new l.MS("Unexpected query result format",500);return e};async function c(e,r){let t;let{sql:o,threadId:a,limit:n=1e3}=e.body,p=Date.now();try{if("POST"!==e.method)throw new l.MS("Method not allowed",405);if(!o)throw new l.MS("SQL is required",400);t=await m.getCurrentProject();let c=await y.getLastDeployment(t.id);if(!c)throw new l.MS("No deployment found, please deploy your project first",400,i.GL.NO_DEPLOYMENT_FOUND);let w=c.manifest;try{let i=await x.preview(o,{project:t,limit:n,manifest:w,modelingOnly:!1}),c=q(i),h=(0,u.N)(c.columns,c.data),m=a||(0,d.v4)();await (0,l.Zv)({res:r,statusCode:200,responsePayload:{records:h,columns:c.columns,threadId:m,totalRows:c.data?.length||0},projectId:t.id,apiType:s.I.RUN_SQL,startTime:p,requestPayload:e.body,threadId:m,headers:e.headers})}catch(e){throw h.error("Error executing SQL:",e),new l.MS(e.message||"Error executing SQL query",400,i.GL.INVALID_SQL_ERROR)}}catch(o){await (0,l.zG)({error:o,res:r,projectId:t?.id,apiType:s.I.RUN_SQL,requestPayload:e.body,threadId:a,headers:e.headers,startTime:p,logger:h})}}o()}catch(e){o(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[980],()=>t(73228));module.exports=o})();