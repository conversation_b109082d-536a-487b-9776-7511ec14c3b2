"use strict";(()=>{var e={};e.id=888,e.ids=[888],e.modules={47263:e=>{e.exports=require("@ant-design/icons/LoadingOutlined")},29114:e=>{e.exports=require("@apollo/client")},79114:e=>{e.exports=require("@apollo/client/link/error")},17369:e=>{e.exports=require("antd/lib/message")},80261:e=>{e.exports=require("antd/lib/spin")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},40968:e=>{e.exports=require("next/head")},98315:e=>{e.exports=require("posthog-js")},35487:e=>{e.exports=require("posthog-js/react")},16689:e=>{e.exports=require("react")},66405:e=>{e.exports=require("react-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},57518:e=>{e.exports=require("styled-components")},92048:e=>{e.exports=require("fs")},76162:e=>{e.exports=require("stream")},71568:e=>{e.exports=require("zlib")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[567,163,378],()=>s(68378));module.exports=t})();