"use strict";(()=>{var e={};e.id=18,e.ids=[18],e.modules={82754:e=>{e.exports=require("cron-parser")},57343:e=>{e.exports=require("graphql")},40514:e=>{e.exports=require("knex")},86897:e=>{e.exports=require("lodash/camelCase")},59969:e=>{e.exports=require("lodash/capitalize")},90221:e=>{e.exports=require("lodash/chunk")},89699:e=>{e.exports=require("lodash/isEmpty")},86069:e=>{e.exports=require("lodash/isNil")},15452:e=>{e.exports=require("lodash/isPlainObject")},87413:e=>{e.exports=require("lodash/mapKeys")},9941:e=>{e.exports=require("lodash/mapValues")},84159:e=>{e.exports=require("lodash/pick")},20808:e=>{e.exports=require("lodash/pickBy")},81131:e=>{e.exports=require("lodash/snakeCase")},98094:e=>{e.exports=require("log4js")},20145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},99648:e=>{e.exports=import("axios")},68122:e=>{e.exports=import("posthog-node")},21274:e=>{e.exports=import("sql-formatter")},46555:e=>{e.exports=import("uuid")},6005:e=>{e.exports=require("node:crypto")},56962:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>p,routeModule:()=>l});var o=r(71802),a=r(47153),n=r(56249),i=r(29496),d=e([i]);i=(d.then?(await d)():d)[0];let p=(0,n.l)(i,"default"),u=(0,n.l)(i,"config"),l=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/ask_task/streaming_answer",pathname:"/api/ask_task/streaming_answer",bundlePath:"",filename:""},userland:i});s()}catch(e){s(e)}})},29496:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>d});var o=r(85913),a=r(89179),n=r(10827),i=e([o,a,n]);[o,a,n]=i.then?(await i)():i;let{wrenAIAdaptor:p,askingService:u,telemetry:l}=o.components;class h{appendContent(e,t){this.contentMap[e]||(this.contentMap[e]=""),this.contentMap[e]+=t}getContent(e){return this.contentMap[e]}remove(e){delete this.contentMap[e]}constructor(){this.contentMap={}}}let c=new h;async function d(e,t){if("GET"!==e.method){t.status(405).json({error:"Method Not Allowed"});return}t.setHeader("Content-Type","text/event-stream"),t.setHeader("Cache-Control","no-cache, no-transform"),t.setHeader("Connection","keep-alive"),t.flushHeaders();let{responseId:r}=e.query;if(!r){t.status(400).json({error:"responseId is required"});return}try{let s=await u.getResponse(Number(r));if(!s)throw Error(`Thread response ${r} not found`);if(s.answerDetail?.status!==a.Vr.STREAMING)throw Error(`Thread response ${r} is not in streaming status`);let o=s.answerDetail?.queryId;if(!o)throw Error(`Thread response ${r} does not have queryId`);let i=await p.streamTextBasedAnswer(o);i.on("data",e=>{let r=e.toString("utf-8"),s="",a=r.match(/data: {"message":"([\s\S]*?)"}/);a&&a[1]?s=a[1]:console.log(`not able to match: ${r}`),c.appendContent(o,s),t.write(e)}),i.on("end",()=>{t.write(`data: ${JSON.stringify({done:!0})}

`),t.end(),u.changeThreadResponseAnswerDetailStatus(Number(r),a.Vr.FINISHED,c.getContent(o)).then(()=>{console.log("Thread response answer detail status updated to FINISHED"),c.remove(o),l.sendEvent(n.MW.HOME_ANSWER_QUESTION,{question:s.question})}).catch(e=>{console.error("Failed to update thread response answer detail status",e),c.remove(o),l.sendEvent(n.MW.HOME_ANSWER_QUESTION,{question:s.question,error:e},null,!1)})}),e.on("close",()=>{i.destroy(),u.changeThreadResponseAnswerDetailStatus(Number(r),a.Vr.INTERRUPTED,c.getContent(o)).then(()=>{console.log("Thread response answer detail status updated to INTERRUPTED"),c.remove(o),l.sendEvent(n.MW.HOME_ANSWER_QUESTION_INTERRUPTED,{question:s.question})}).catch(e=>{console.error("Failed to update thread response answer detail status",e),c.remove(o),l.sendEvent(n.MW.HOME_ANSWER_QUESTION_INTERRUPTED,{question:s.question,error:e},null,!1)})})}catch(e){console.error(e),t.status(500).end()}}s()}catch(e){s(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[980],()=>r(56962));module.exports=s})();