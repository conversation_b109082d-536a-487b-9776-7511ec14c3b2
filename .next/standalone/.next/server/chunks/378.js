exports.id=378,exports.ids=[378],exports.modules={9425:(e,t,s)=>{"use strict";s.d(t,{y:()=>a.a});var r=s(80261),a=s.n(r)},37972:(e,t,s)=>{"use strict";s.d(t,{y:()=>a.a});var r=s(17369),a=s.n(r)},58287:(e,t,s)=>{"use strict";s.d(t,{$j:()=>u,OL:()=>x,ZP:()=>g,gb:()=>p,ql:()=>c,u:()=>h});var r=s(20997),a=s(9425),n=s(57518),o=s.n(n),d=s(47263),l=s.n(d);let i=o().div.withConfig({displayName:"PageLoading__Wrapper",componentId:"sc-a5788bf1-0"})(["position:absolute;top:48px;left:0;right:0;bottom:0;z-index:9999;background-color:white;display:none;&.isShow{display:flex;}"]),c=r.jsx(l(),{style:{fontSize:36},spin:!0}),u=({className:e="",size:t=36})=>r.jsx(l(),{className:e,style:{fontSize:t},spin:!0});function g(e){let{visible:t}=e;return r.jsx(i,{className:`align-center justify-center${t?" isShow":""}`,children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(a.y,{indicator:c}),r.jsx("div",{className:"mt-2 geekblue-6",children:"Loading..."})]})})}let x=e=>{let{height:t,tip:s}=e;return(0,r.jsxs)("div",{className:"d-flex align-center justify-center flex-column geekblue-6",style:{height:t||"100%"},children:[c,s&&r.jsx("span",{className:"mt-2",children:s})]})},p=({children:e=null,spinning:t=!1,loading:s=!1,tip:n})=>r.jsx(a.y,{indicator:c,spinning:t||s,tip:n,children:e}),h=e=>{let{loading:t,tip:s,children:a}=e;return t?r.jsx(x,{tip:s}):a}},68378:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(20997),a=s(40968),n=s.n(a),o=s(9425),d=s(98315),l=s.n(d),i=s(29114),c=s(79114),u=s(42698);let g=(0,c.onError)(e=>(0,u.ZP)(e)),x=new i.HttpLink({uri:"/api/graphql"}),p=new i.ApolloClient({link:(0,i.from)([g,x]),cache:new i.InMemoryCache});var h=s(11163),w=s(16689);let E=async()=>{let e=await fetch("/api/config").then(e=>e.json()),t=Buffer.from(e.telemetryKey,"base64").toString();return{...e,telemetryKey:t}},m=e=>{},M=(e,t)=>{let s=()=>{l().capture("$pageview")};return t.isTelemetryEnabled&&(m(t),e.events.on("routeChangeComplete",s)),()=>{e.events.off("routeChangeComplete",s)}},F=(0,w.createContext)({}),f=({children:e})=>{let t=(0,h.useRouter)(),[s,a]=(0,w.useState)(null);return(0,w.useEffect)(()=>{E().then(e=>(a(e),M(t,e))).catch(e=>{console.error("Failed to get user config",e)})},[t]),r.jsx(F.Provider,{value:{config:s},children:e})};var C=s(35487),j=s(58287);s(67602),o.y.setDefaultIndicator(j.ql);let y=function({Component:e,pageProps:t}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n(),{children:[r.jsx("title",{children:"Wren AI"}),r.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),r.jsx(f,{children:r.jsx(i.ApolloProvider,{client:p,children:r.jsx(C.PostHogProvider,{client:l(),children:r.jsx("main",{className:"app",children:r.jsx(e,{...t})})})})})]})}},42698:(e,t,s)=>{"use strict";s.d(t,{Bx:()=>Q,O1:()=>a,ZP:()=>z});var r=s(37972);let a={INVALID_CALCULATED_FIELD:"INVALID_CALCULATED_FIELD",CONNECTION_REFUSED:"CONNECTION_REFUSED",NO_CHART:"NO_CHART"},n=(e,t)=>{let s=/\%\{.+\}/,r=e.match(/(?<=\%\{).+(?=\})/);return null===r?(console.warn("Replace token not found in message:",e),e):t?e.replace(s,`- ${t}`):e.replace(s,r[0])};class o{handle(e){let t=this.getErrorMessage(e);t&&r.y.error(t)}}let d=new Map;class l extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create model(s)."}}class i extends o{getErrorMessage(e){return e.extensions?.code,"Failed to define relations."}}class c extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create asking task."}}class u extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create thread."}}class g extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update thread."}}class x extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete thread."}}class p extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create thread response."}}class h extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update thread response."}}class w extends o{getErrorMessage(e){return e.extensions?.code,"Failed to generate thread response answer."}}class E extends o{getErrorMessage(e){return e.extensions?.code,"Failed to adjust thread response answer."}}class m extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create view."}}class M extends o{getErrorMessage(e){return e.extensions?.code,n("Failed to update %{data source}.",e.message)}}class F extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create model."}}class f extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update model."}}class C extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete model."}}class j extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update model metadata."}}class y extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create calculated field."}}class v extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update calculated field."}}class D extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete calculated field."}}class b extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create relationship."}}class N extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update relationship."}}class I extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete relationship."}}class S extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update view metadata."}}class T extends o{getErrorMessage(e){return e.extensions?.code,"Failed to scan data source."}}class k extends o{getErrorMessage(e){return e.extensions?.code,"Failed to resolve schema change."}}class L extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create dashboard item."}}class U extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update dashboard item."}}class R extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update dashboard item layouts."}}class A extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete dashboard item."}}class q extends o{getErrorMessage(e){return e.extensions?.code,"Failed to set dashboard schedule."}}class P extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create question-sql pair."}}class _ extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update question-sql pair."}}class O extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete question-sql pair."}}class H extends o{getErrorMessage(e){return e.extensions?.code,"Failed to create instruction."}}class V extends o{getErrorMessage(e){return e.extensions?.code,"Failed to update instruction."}}class $ extends o{getErrorMessage(e){return e.extensions?.code,"Failed to delete instruction."}}d.set("SaveTables",new l),d.set("SaveRelations",new i),d.set("CreateAskingTask",new c),d.set("CreateThread",new u),d.set("UpdateThread",new g),d.set("DeleteThread",new x),d.set("CreateThreadResponse",new p),d.set("UpdateThreadResponse",new h),d.set("GenerateThreadResponseAnswer",new w),d.set("AdjustThreadResponse",new E),d.set("CreateView",new m),d.set("UpdateDataSource",new M),d.set("CreateModel",new F),d.set("UpdateModel",new f),d.set("DeleteModel",new C),d.set("UpdateModelMetadata",new j),d.set("UpdateViewMetadata",new S),d.set("CreateCalculatedField",new y),d.set("UpdateCalculatedField",new v),d.set("DeleteCalculatedField",new D),d.set("CreateRelationship",new b),d.set("UpdateRelationship",new N),d.set("DeleteRelationship",new I),d.set("TriggerDataSourceDetection",new T),d.set("ResolveSchemaChange",new k),d.set("CreateDashboardItem",new L),d.set("UpdateDashboardItem",new U),d.set("UpdateDashboardItemLayouts",new R),d.set("DeleteDashboardItem",new A),d.set("SetDashboardSchedule",new q),d.set("CreateSqlPair",new P),d.set("UpdateSqlPair",new _),d.set("DeleteSqlPair",new O),d.set("CreateInstruction",new H),d.set("UpdateInstruction",new V),d.set("DeleteInstruction",new $);let z=e=>{e.networkError&&r.y.error("No internet. Please check your network connection and try again.");let t=e?.operation?.operationName||"";if(e.graphQLErrors)for(let s of e.graphQLErrors)d.get(t)?.handle(s)},Q=e=>{if(!e)return null;let t=e.graphQLErrors?.[0],s=t?.extensions||{};return{message:s.message,shortMessage:s.shortMessage,code:s.code,stacktrace:s?.stacktrace}}},67602:()=>{}};