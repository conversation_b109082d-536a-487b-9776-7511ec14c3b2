{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/setup", "destination": "/setup/connection", "statusCode": 308, "regex": "^(?!/_next)/setup(?:/)?$"}], "headers": [], "dynamicRoutes": [{"page": "/home/<USER>", "regex": "^/home/<USER>/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/home/<USER>/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/api-management/history", "regex": "^/api\\-management/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/api\\-management/history(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/home/<USER>", "regex": "^/home/<USER>/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/)?$"}, {"page": "/knowledge/instructions", "regex": "^/knowledge/instructions(?:/)?$", "routeKeys": {}, "namedRegex": "^/knowledge/instructions(?:/)?$"}, {"page": "/knowledge/question-sql-pairs", "regex": "^/knowledge/question\\-sql\\-pairs(?:/)?$", "routeKeys": {}, "namedRegex": "^/knowledge/question\\-sql\\-pairs(?:/)?$"}, {"page": "/modeling", "regex": "^/modeling(?:/)?$", "routeKeys": {}, "namedRegex": "^/modeling(?:/)?$"}, {"page": "/setup/connection", "regex": "^/setup/connection(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/connection(?:/)?$"}, {"page": "/setup/models", "regex": "^/setup/models(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/models(?:/)?$"}, {"page": "/setup/relationships", "regex": "^/setup/relationships(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/relationships(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}